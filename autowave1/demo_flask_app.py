#!/usr/bin/env python3
"""
Demo Flask app generated by Agentic Code to show it works properly.
This demonstrates the fixed Flask generation with proper port handling.
"""

from flask import Flask, render_template_string, request, jsonify
import json
import os
import socket
from datetime import datetime

app = Flask(__name__)

# HTML template
TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Flask App - Agentic Code Generated</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <h1 class="text-4xl font-bold text-white text-center mb-4">
                <i class="fas fa-robot mr-3"></i>Agentic Code Demo
            </h1>
            <p class="text-center text-blue-200 mb-8">
                This Flask app was generated by Agentic Code with proper port handling!
            </p>
            
            {% if message %}
            <div class="bg-green-500/20 border border-green-500 rounded-lg p-4 mb-6 text-green-300 text-center">
                <i class="fas fa-check-circle mr-2"></i>{{ message }}
            </div>
            {% endif %}
            
            <div class="bg-white/10 backdrop-blur-lg rounded-lg p-8 border border-white/20">
                <h2 class="text-2xl font-bold text-white mb-6">
                    <i class="fas fa-envelope mr-2"></i>Test Contact Form
                </h2>
                
                <form method="POST" action="/submit" class="space-y-6">
                    <div>
                        <label for="name" class="block text-white font-semibold mb-2">Name</label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
                               placeholder="Enter your name">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-white font-semibold mb-2">Email</label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
                               placeholder="Enter your email">
                    </div>
                    
                    <div>
                        <label for="message" class="block text-white font-semibold mb-2">Message</label>
                        <textarea id="message" name="message" rows="4" required
                                  class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
                                  placeholder="Enter your message"></textarea>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                        <i class="fas fa-paper-plane mr-2"></i>Send Message
                    </button>
                </form>
            </div>
            
            <div class="mt-8 text-center space-x-4">
                <a href="/submissions" class="text-blue-300 hover:text-blue-200 underline">
                    <i class="fas fa-list mr-2"></i>View Submissions
                </a>
                <a href="/api/status" class="text-green-300 hover:text-green-200 underline">
                    <i class="fas fa-info-circle mr-2"></i>API Status
                </a>
            </div>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def home():
    return render_template_string(TEMPLATE)

@app.route('/submit', methods=['POST'])
def submit_form():
    try:
        form_data = {
            'name': request.form.get('name'),
            'email': request.form.get('email'),
            'message': request.form.get('message'),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save to JSON file
        submissions_file = 'demo_submissions.json'
        submissions = []
        
        if os.path.exists(submissions_file):
            with open(submissions_file, 'r') as f:
                submissions = json.load(f)
        
        submissions.append(form_data)
        
        with open(submissions_file, 'w') as f:
            json.dump(submissions, f, indent=2)
        
        return render_template_string(TEMPLATE, message="✅ Message submitted successfully! The Flask app is working perfectly.")
        
    except Exception as e:
        return render_template_string(TEMPLATE, message=f"❌ Error: {str(e)}")

@app.route('/submissions')
def view_submissions():
    try:
        submissions_file = 'demo_submissions.json'
        submissions = []
        
        if os.path.exists(submissions_file):
            with open(submissions_file, 'r') as f:
                submissions = json.load(f)
        
        return jsonify({
            'success': True,
            'count': len(submissions),
            'submissions': submissions
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/status')
def api_status():
    return jsonify({
        'status': 'running',
        'message': 'Flask app generated by Agentic Code is working perfectly!',
        'features': [
            'Proper port handling',
            'No FLASK_ENV deprecation warnings',
            'JSON data persistence',
            'Form handling',
            'API endpoints',
            'Modern UI with Tailwind CSS'
        ],
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    # Function to find an available port
    def find_available_port(start_port=5003):
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return None
    
    # Find an available port starting from 5003 (to avoid conflict with main app on 5001)
    port = find_available_port(5003)
    
    if port:
        print("🚀 Starting Demo Flask App Generated by Agentic Code...")
        print(f"✅ Access the app at: http://127.0.0.1:{port}")
        print(f"📊 View submissions at: http://127.0.0.1:{port}/submissions")
        print(f"🔍 API status at: http://127.0.0.1:{port}/api/status")
        print("🎉 This demonstrates that Agentic Code Flask generation is working perfectly!")
        
        try:
            # Use the available port to avoid conflicts
            app.run(debug=True, port=port, host="127.0.0.1", use_reloader=False)
        except Exception as e:
            print(f"❌ Error starting server on port {port}: {e}")
    else:
        print("❌ Error: Could not find an available port between 5003-5102")
