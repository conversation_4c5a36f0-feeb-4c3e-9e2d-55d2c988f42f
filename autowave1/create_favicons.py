#!/usr/bin/env python3
"""
AutoWave Favicon Generator
Creates multiple favicon sizes from the existing logo for browser compatibility
"""

from PIL import Image
import os

def create_favicons():
    """Create favicon files in multiple sizes from the existing logo"""
    
    # Paths
    logo_path = "app/static/images/autowave-logo.png"
    static_path = "app/static"
    
    # Check if logo exists
    if not os.path.exists(logo_path):
        print(f"❌ Logo not found at {logo_path}")
        return False
    
    try:
        # Open the original logo
        original_logo = Image.open(logo_path)
        print(f"✅ Loaded logo: {original_logo.size}")
        
        # Convert to RGBA if not already
        if original_logo.mode != 'RGBA':
            original_logo = original_logo.convert('RGBA')
        
        # Favicon sizes to create
        favicon_sizes = [
            (16, 16),    # Standard favicon
            (32, 32),    # High DPI favicon
            (48, 48),    # Windows site icon
            (64, 64),    # Windows site icon
            (96, 96),    # Android Chrome
            (128, 128),  # Chrome Web Store
            (152, 152),  # iOS touch icon
            (167, 167),  # iOS touch icon (iPad Pro)
            (180, 180),  # iOS touch icon (iPhone 6 Plus)
            (192, 192),  # Android Chrome
            (196, 196),  # Android Chrome
            (256, 256),  # Windows site icon
            (512, 512),  # Android Chrome
        ]
        
        # Create favicons directory if it doesn't exist
        favicon_dir = os.path.join(static_path, "favicons")
        os.makedirs(favicon_dir, exist_ok=True)
        
        created_files = []
        
        # Generate each size
        for width, height in favicon_sizes:
            # Resize the logo
            resized_logo = original_logo.resize((width, height), Image.Resampling.LANCZOS)
            
            # Save as PNG
            png_filename = f"favicon-{width}x{height}.png"
            png_path = os.path.join(favicon_dir, png_filename)
            resized_logo.save(png_path, "PNG", optimize=True)
            created_files.append(png_filename)
            print(f"✅ Created {png_filename}")
        
        # Create the main favicon.ico (16x16 and 32x32 combined)
        favicon_ico_path = os.path.join(static_path, "favicon.ico")
        favicon_16 = original_logo.resize((16, 16), Image.Resampling.LANCZOS)
        favicon_32 = original_logo.resize((32, 32), Image.Resampling.LANCZOS)
        
        # Save as ICO with multiple sizes
        favicon_16.save(
            favicon_ico_path, 
            format='ICO', 
            sizes=[(16, 16), (32, 32)],
            append_images=[favicon_32]
        )
        created_files.append("favicon.ico")
        print(f"✅ Created favicon.ico")
        
        # Create Apple touch icon (180x180)
        apple_touch_icon = original_logo.resize((180, 180), Image.Resampling.LANCZOS)
        apple_touch_path = os.path.join(static_path, "apple-touch-icon.png")
        apple_touch_icon.save(apple_touch_path, "PNG", optimize=True)
        created_files.append("apple-touch-icon.png")
        print(f"✅ Created apple-touch-icon.png")
        
        # Create Android Chrome icons
        android_192 = original_logo.resize((192, 192), Image.Resampling.LANCZOS)
        android_192_path = os.path.join(static_path, "android-chrome-192x192.png")
        android_192.save(android_192_path, "PNG", optimize=True)
        created_files.append("android-chrome-192x192.png")
        
        android_512 = original_logo.resize((512, 512), Image.Resampling.LANCZOS)
        android_512_path = os.path.join(static_path, "android-chrome-512x512.png")
        android_512.save(android_512_path, "PNG", optimize=True)
        created_files.append("android-chrome-512x512.png")
        print(f"✅ Created Android Chrome icons")
        
        print(f"\n🎉 Successfully created {len(created_files)} favicon files!")
        print("📁 Files created:")
        for file in created_files:
            print(f"   - {file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating favicons: {e}")
        return False

def create_web_manifest():
    """Create a web app manifest for PWA support"""
    
    manifest_content = """{
    "name": "AutoWave - AI-Powered Assistant",
    "short_name": "AutoWave",
    "description": "Advanced AI-powered assistant platform with multiple AI models and tools",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#121212",
    "theme_color": "#4a9eff",
    "orientation": "portrait-primary",
    "icons": [
        {
            "src": "/static/android-chrome-192x192.png",
            "sizes": "192x192",
            "type": "image/png",
            "purpose": "maskable any"
        },
        {
            "src": "/static/android-chrome-512x512.png",
            "sizes": "512x512",
            "type": "image/png",
            "purpose": "maskable any"
        }
    ],
    "categories": ["productivity", "utilities", "business"],
    "lang": "en",
    "dir": "ltr"
}"""
    
    manifest_path = "app/static/site.webmanifest"
    
    try:
        with open(manifest_path, 'w') as f:
            f.write(manifest_content)
        print(f"✅ Created web manifest: {manifest_path}")
        return True
    except Exception as e:
        print(f"❌ Error creating web manifest: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AutoWave Favicon Generator")
    print("=" * 50)
    
    # Create favicons
    if create_favicons():
        print("\n📱 Creating web manifest...")
        create_web_manifest()
        print("\n✅ All favicon files created successfully!")
        print("\n📋 Next steps:")
        print("1. Add favicon links to HTML head")
        print("2. Configure Google Search Console")
        print("3. Set up domain verification")
    else:
        print("\n❌ Failed to create favicon files")
