#!/usr/bin/env python3
"""
Create missing billing tables for AutoWave subscription management
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """Create billing tables in Supabase"""
    
    # Get Supabase credentials
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY') or os.getenv('SUPABASE_ANON_KEY')

    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in environment variables")
        sys.exit(1)
    
    print("🚀 Creating billing tables in Supabase...")
    print(f"📍 Supabase URL: {supabase_url}")
    
    try:
        # Initialize Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Connected to Supabase")
        
        # SQL commands to create billing tables
        sql_commands = [
            # 1. Create billing_addresses table
            """
            CREATE TABLE IF NOT EXISTS public.billing_addresses (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
                name TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                state TEXT,
                postal_code TEXT,
                country TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(user_id)
            );
            """,
            
            # 2. Enable RLS on billing_addresses
            """
            ALTER TABLE public.billing_addresses ENABLE ROW LEVEL SECURITY;
            """,
            
            # 3. Create RLS policy for billing_addresses
            """
            CREATE POLICY "Users can manage their own billing address" ON public.billing_addresses
                FOR ALL USING (auth.uid() = user_id);
            """,
            
            # 4. Create updated_at trigger for billing_addresses
            """
            CREATE OR REPLACE FUNCTION public.handle_updated_at()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
            """,
            
            """
            CREATE TRIGGER billing_addresses_updated_at
                BEFORE UPDATE ON public.billing_addresses
                FOR EACH ROW
                EXECUTE FUNCTION public.handle_updated_at();
            """
        ]
        
        # Execute SQL commands
        for i, sql in enumerate(sql_commands, 1):
            try:
                print(f"📝 Executing SQL command {i}/{len(sql_commands)}...")
                result = supabase.rpc('exec_sql', {'sql': sql.strip()}).execute()
                print(f"✅ Command {i} executed successfully")
            except Exception as e:
                print(f"⚠️  Command {i} warning (may already exist): {e}")
                continue
        
        print("\n🔍 Verifying table creation...")
        
        # Verify billing_addresses table exists
        try:
            result = supabase.table('billing_addresses').select('*').limit(1).execute()
            print("✅ billing_addresses - Table exists and accessible")
        except Exception as e:
            print(f"❌ billing_addresses - Error: {e}")
        
        print("\n🎉 Billing tables setup completed!")
        print("\nTables created:")
        print("✅ billing_addresses - Store user billing information")
        print("\nFeatures enabled:")
        print("✅ Row Level Security (RLS)")
        print("✅ Auto-updated timestamps")
        print("✅ User-specific access policies")
        
    except Exception as e:
        print(f"❌ Error setting up billing tables: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
