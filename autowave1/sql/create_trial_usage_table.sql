-- Create trial_usage table for tracking one-time trial limits per page
CREATE TABLE IF NOT EXISTS trial_usage (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    page_name VARCHAR(100) NOT NULL,
    usage_count INTEGER DEFAULT 0,
    first_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one record per user per page
    UNIQUE(user_id, page_name)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trial_usage_user_id ON trial_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_usage_page_name ON trial_usage(page_name);
CREATE INDEX IF NOT EXISTS idx_trial_usage_user_page ON trial_usage(user_id, page_name);

-- Add comments for documentation
COMMENT ON TABLE trial_usage IS 'Tracks one-time trial usage per page for free users';
COMMENT ON COLUMN trial_usage.user_id IS 'User identifier from session';
COMMENT ON COLUMN trial_usage.page_name IS 'Page name (research_lab, agent_wave, agentic_code, prime_agent_task, context7_tools)';
COMMENT ON COLUMN trial_usage.usage_count IS 'Number of prompts used on this page';
COMMENT ON COLUMN trial_usage.first_used IS 'When user first used this page';
COMMENT ON COLUMN trial_usage.last_used IS 'When user last used this page';

-- Example trial limits:
-- research_lab: 2 prompts
-- agent_wave: 2 prompts  
-- agentic_code: 3 prompts
-- prime_agent_task: 2 prompts
-- context7_tools: 2 prompts
-- autowave_chat: handled by credit_service with daily renewal
