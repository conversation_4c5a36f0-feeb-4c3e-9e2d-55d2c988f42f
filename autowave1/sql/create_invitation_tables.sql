-- Create invitation_links table for managing invitation promotions
CREATE TABLE IF NOT EXISTS invitation_links (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) UNIQUE NOT NULL,
    created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    used_by <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    used_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    promotion_type VARCHAR(50) DEFAULT 'plus_3_months',
    credits_total INTEGER DEFAULT 16000,
    description TEXT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create invitation_users table for tracking users who signed up via invitation
CREATE TABLE IF NOT EXISTS invitation_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) UNIQUE NOT NULL,
    invitation_code VA<PERSON><PERSON>R(50) NOT NULL,
    signup_date TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    subscription_date TIMESTAMP NULL,
    credits_used INTEGER DEFAULT 0,
    credits_remaining INTEGER DEFAULT 16000,
    is_expired BOOLEAN DEFAULT FALSE,
    expiry_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_invitation_code 
        FOREIGN KEY (invitation_code) 
        REFERENCES invitation_links(code)
        ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_invitation_links_code ON invitation_links(code);
CREATE INDEX IF NOT EXISTS idx_invitation_links_created_by ON invitation_links(created_by);
CREATE INDEX IF NOT EXISTS idx_invitation_links_used_by ON invitation_links(used_by);
CREATE INDEX IF NOT EXISTS idx_invitation_links_is_active ON invitation_links(is_active);

CREATE INDEX IF NOT EXISTS idx_invitation_users_user_id ON invitation_users(user_id);
CREATE INDEX IF NOT EXISTS idx_invitation_users_invitation_code ON invitation_users(invitation_code);
CREATE INDEX IF NOT EXISTS idx_invitation_users_expiry_date ON invitation_users(expiry_date);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_invitation_links_updated_at 
    BEFORE UPDATE ON invitation_links 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invitation_users_updated_at 
    BEFORE UPDATE ON invitation_users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE invitation_links IS 'Manages invitation links for special promotions (Pay 1 month Plus, get 2 months free)';
COMMENT ON COLUMN invitation_links.code IS 'Unique invitation code (e.g., INV-ABC12345)';
COMMENT ON COLUMN invitation_links.promotion_type IS 'Type of promotion (plus_3_months = Pay 1 month, get 2 free)';
COMMENT ON COLUMN invitation_links.credits_total IS 'Total credits for the promotion (16000 = 2x Plus plan)';
COMMENT ON COLUMN invitation_links.expires_at IS 'When the invitation expires (set when used, 7 days from signup)';

COMMENT ON TABLE invitation_users IS 'Tracks users who signed up via invitation links';
COMMENT ON COLUMN invitation_users.expiry_date IS '7 days from signup to subscribe, or promotion expires';
COMMENT ON COLUMN invitation_users.credits_remaining IS 'Remaining credits from invitation promotion';
COMMENT ON COLUMN invitation_users.subscription_date IS 'When user subscribed to activate the promotion';

-- Example promotion details:
-- plus_3_months: Pay for 1 month Plus ($29), get 2 months free
-- Total value: $87 (3 months × $29)
-- Customer pays: $29
-- Customer saves: $58 (67% savings)
-- Total credits: 16,000 (2x normal Plus plan credits)
-- Expiry: 7 days after signup if no subscription
