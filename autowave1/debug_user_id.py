#!/usr/bin/env python3
"""
Debug script to check what user_id is being generated by the API
"""

import uuid
import hashlib

def test_user_id_generation():
    """Test how the API generates user_ids"""
    
    print("🔍 Testing User ID Generation...")
    
    # Simulate different session scenarios
    test_sessions = [
        {'_id': 'session_123'},
        {'_id': 'session_456'},
        {},  # No session ID
        {'_id': 'anonymous'},
    ]
    
    for i, session in enumerate(test_sessions):
        print(f"\n📋 Test {i+1}: Session = {session}")
        
        # Old method (what was causing the problem)
        old_user_id = f"temp_user_{hash(session.get('_id', 'anonymous'))}"
        print(f"   Old method: {old_user_id}")
        
        # New method (UUID-based)
        session_id = session.get('_id', 'anonymous')
        namespace = uuid.UUID('12345678-1234-5678-1234-123456789abc')
        new_user_id = str(uuid.uuid5(namespace, f"temp_user_{session_id}"))
        print(f"   New method: {new_user_id}")
        
        # Check if they're consistent
        # Test multiple calls with same session
        session_id_2 = session.get('_id', 'anonymous')
        namespace_2 = uuid.UUID('12345678-1234-5678-1234-123456789abc')
        new_user_id_2 = str(uuid.uuid5(namespace_2, f"temp_user_{session_id_2}"))
        
        consistent = new_user_id == new_user_id_2
        print(f"   Consistent: {consistent} ({'✅' if consistent else '❌'})")
    
    print("\n🧪 Testing Session Persistence...")
    
    # Simulate what happens in real API calls
    # First call (activity tracking)
    session_1 = {'_id': 'test_session_abc123'}
    session_id_1 = session_1.get('_id', 'anonymous')
    namespace = uuid.UUID('12345678-1234-5678-1234-123456789abc')
    user_id_1 = str(uuid.uuid5(namespace, f"temp_user_{session_id_1}"))
    
    # Second call (history retrieval) - should be same session
    session_2 = {'_id': 'test_session_abc123'}  # Same session
    session_id_2 = session_2.get('_id', 'anonymous')
    user_id_2 = str(uuid.uuid5(namespace, f"temp_user_{session_id_2}"))
    
    print(f"📊 Activity tracking user_id: {user_id_1}")
    print(f"📊 History retrieval user_id: {user_id_2}")
    print(f"📊 Match: {user_id_1 == user_id_2} ({'✅' if user_id_1 == user_id_2 else '❌'})")
    
    if user_id_1 == user_id_2:
        print("✅ User ID generation is consistent!")
    else:
        print("❌ User ID generation is NOT consistent!")
    
    print("\n🔍 Testing Hash vs UUID Consistency...")
    
    # Test if the issue was hash vs UUID
    test_session = {'_id': 'consistent_test'}
    
    # Hash method (old)
    hash_user_id = f"temp_user_{hash(test_session.get('_id', 'anonymous'))}"
    
    # UUID method (new)
    uuid_user_id = str(uuid.uuid5(namespace, f"temp_user_{test_session.get('_id', 'anonymous')}"))
    
    print(f"📊 Hash method: {hash_user_id}")
    print(f"📊 UUID method: {uuid_user_id}")
    print(f"📊 Same: {hash_user_id == uuid_user_id} ({'✅' if hash_user_id == uuid_user_id else '❌'})")
    
    # The issue: hash() returns different values in different Python sessions!
    print("\n⚠️  IMPORTANT: hash() function returns different values in different Python sessions!")
    print("   This means API calls would generate different user_ids each time!")

if __name__ == "__main__":
    test_user_id_generation()
