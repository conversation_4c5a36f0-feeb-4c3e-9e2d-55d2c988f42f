#!/usr/bin/env python3
"""
Test script for Memory Manager
Run this to test the memory management functionality
"""

import time
import requests
from app.services.memory_manager import MemoryManager

def test_memory_manager():
    """Test the memory manager functionality"""
    print("🧪 Testing Memory Manager...")
    
    # Create memory manager instance
    manager = MemoryManager()
    
    # Test memory usage detection
    print("\n📊 Current Memory Usage:")
    memory_info = manager.get_memory_usage()
    if memory_info:
        print(f"  Usage: {memory_info['usage_percent']:.1f}%")
        print(f"  Used: {memory_info['used_mb']:.1f} MB")
        print(f"  Available: {memory_info['available_mb']:.1f} MB")
        print(f"  Total: {memory_info['total_mb']:.1f} MB")
    else:
        print("  ❌ Could not get memory info")
    
    # Test cleanup
    print("\n🧹 Testing Memory Cleanup...")
    success = manager.cleanup_memory()
    print(f"  Cleanup result: {'✅ Success' if success else '❌ Failed'}")
    
    # Test status
    print("\n📋 Manager Status:")
    status = manager.get_status()
    print(f"  Monitoring: {status['is_monitoring']}")
    print(f"  Last cleanup: {status['last_cleanup']}")
    print(f"  Thresholds: {status['thresholds']}")
    
    print("\n✅ Memory Manager test completed!")

def test_api_endpoints():
    """Test the API endpoints (requires running Flask app)"""
    print("\n🌐 Testing API Endpoints...")
    
    base_url = "http://localhost:5000"  # Adjust if needed
    
    try:
        # Test health check
        response = requests.get(f"{base_url}/api/health-check", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check endpoint working")
            print(f"  App status: {data.get('app_status')}")
            print(f"  Intro video: {data.get('intro_video', {}).get('status')}")
            print(f"  Memory usage: {data.get('memory', {}).get('usage_percent')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Could not connect to Flask app: {e}")
        print("   Make sure the Flask app is running on localhost:5000")
    
    try:
        # Test memory status
        response = requests.get(f"{base_url}/api/memory-status", timeout=5)
        if response.status_code == 200:
            print("✅ Memory status endpoint working")
        else:
            print(f"❌ Memory status failed: {response.status_code}")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Memory status endpoint error: {e}")

if __name__ == "__main__":
    print("🚀 AutoWave Memory Manager Test Suite")
    print("=" * 50)
    
    # Test memory manager directly
    test_memory_manager()
    
    # Test API endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("🎉 Test suite completed!")
    print("\n💡 Tips:")
    print("  - Check /api/health-check for overall status")
    print("  - Check /api/memory-status for detailed memory info")
    print("  - Use /api/cleanup-memory to manually trigger cleanup")
    print("  - Memory manager runs automatically every 5 minutes")
    print("  - Cleanup happens automatically every hour or when usage > 85%")
