#!/usr/bin/env python3
"""
Complete Frontend Test for All 6 Agents
Tests admin <NAME_EMAIL> across all agents
"""

import requests
import json
import time
from urllib.parse import urljoin

def test_all_agents():
    """Test all 6 agents with admin bypass"""
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    print("🚀 COMPLETE AGENT TESTING WITH ADMIN BYPASS")
    print("=" * 60)
    
    # Step 1: Login as admin
    print("1. 🔐 Admin Login")
    login_response = session.post(
        urljoin(base_url, "/auth/login"),
        json={"email": "<EMAIL>", "password": "12345678"},
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code == 200:
        login_result = login_response.json()
        if login_result.get('success'):
            print("   ✅ Login successful!")
        else:
            print(f"   ❌ Login failed: {login_result}")
            return
    else:
        print(f"   ❌ Login request failed: {login_response.status_code}")
        return
    
    # Test all 6 agents
    agents = [
        {
            "name": "AutoWave Chat",
            "endpoint": "/api/chat",
            "payload": {"message": "Hello, test admin access"},
            "success_key": "response"
        },
        {
            "name": "Agent Wave (Super Agent)",
            "endpoint": "/api/super-agent/execute-task",
            "payload": {"task_description": "Test admin access to Agent Wave"},
            "success_key": "success"
        },
        {
            "name": "Agentic Code",
            "endpoint": "/agentic-code",
            "method": "GET",
            "success_key": None  # Just check status code
        },
        {
            "name": "Document Generator",
            "endpoint": "/document-generator",
            "method": "GET",
            "success_key": None
        },
        {
            "name": "Prime Agent",
            "endpoint": "/prime-agent",
            "method": "GET", 
            "success_key": None
        },
        {
            "name": "Call Assistant",
            "endpoint": "/call-assistant",
            "method": "GET",
            "success_key": None
        }
    ]
    
    results = {}
    
    for i, agent in enumerate(agents, 2):
        print(f"\n{i}. 🤖 Testing {agent['name']}")
        
        try:
            if agent.get('method') == 'GET':
                # Test GET endpoints (page access)
                response = session.get(urljoin(base_url, agent['endpoint']))
                
                if response.status_code == 200:
                    print(f"   ✅ {agent['name']}: Page accessible (Status 200)")
                    results[agent['name']] = "✅ WORKING"
                else:
                    print(f"   ❌ {agent['name']}: Failed (Status {response.status_code})")
                    results[agent['name']] = f"❌ FAILED ({response.status_code})"
            
            else:
                # Test POST endpoints (API calls)
                response = session.post(
                    urljoin(base_url, agent['endpoint']),
                    json=agent['payload'],
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        print(f"   Response: {json.dumps(result, indent=2)[:200]}...")
                        
                        # Check for admin bypass indicators
                        admin_indicators = [
                            result.get('admin_bypass'),
                            result.get('remaining_credits') == -1,
                            result.get('credits_consumed') == 0,
                            'admin' in str(result).lower()
                        ]
                        
                        if any(admin_indicators):
                            print(f"   ✅ {agent['name']}: ADMIN BYPASS WORKING!")
                            results[agent['name']] = "✅ ADMIN BYPASS WORKING"
                        elif result.get(agent['success_key']):
                            print(f"   ✅ {agent['name']}: Working (but check admin bypass)")
                            results[agent['name']] = "⚠️ WORKING (check bypass)"
                        else:
                            print(f"   ❌ {agent['name']}: Failed - {result}")
                            results[agent['name']] = f"❌ FAILED - {result.get('error', 'Unknown')}"
                            
                    except json.JSONDecodeError:
                        print(f"   ❌ {agent['name']}: Invalid JSON response")
                        results[agent['name']] = "❌ INVALID RESPONSE"
                        
                else:
                    print(f"   ❌ {agent['name']}: HTTP {response.status_code}")
                    try:
                        error_detail = response.json()
                        print(f"   Error: {error_detail}")
                        results[agent['name']] = f"❌ HTTP {response.status_code} - {error_detail.get('error', 'Unknown')}"
                    except:
                        results[agent['name']] = f"❌ HTTP {response.status_code}"
                        
        except Exception as e:
            print(f"   ❌ {agent['name']}: Exception - {str(e)}")
            results[agent['name']] = f"❌ EXCEPTION - {str(e)}"
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 FINAL RESULTS SUMMARY")
    print("="*60)
    
    working_count = 0
    for agent_name, status in results.items():
        print(f"{agent_name:25} | {status}")
        if "✅" in status:
            working_count += 1
    
    print(f"\n🎯 SUMMARY: {working_count}/{len(results)} agents working")
    
    if working_count == len(results):
        print("🎉 ALL AGENTS WORKING WITH ADMIN BYPASS!")
    elif working_count >= 4:
        print("⚠️  Most agents working - check failed ones")
    else:
        print("❌ Multiple agents failing - needs investigation")
    
    return results

if __name__ == "__main__":
    test_all_agents()
