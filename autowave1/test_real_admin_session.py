#!/usr/bin/env python3
"""
Real Admin Session Test
This script creates a real Flask session and tests admin access
"""

import requests
import json
import time

def test_with_real_session():
    """Test admin access with a real Flask session"""
    base_url = "http://127.0.0.1:5001"
    
    print("🧪 Testing Real Admin Session")
    print("=" * 50)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Get the home page to establish a session
    print("\n1. Establishing session...")
    try:
        response = session.get(base_url)
        print(f"✅ Home page: Status {response.status_code}")
        
        # Check if we got session cookies
        cookies = session.cookies.get_dict()
        print(f"Session cookies: {list(cookies.keys())}")
        
    except Exception as e:
        print(f"❌ Error establishing session: {e}")
        return False
    
    # Step 2: Try to access a protected page to trigger login redirect
    print("\n2. Accessing protected page to get session...")
    try:
        response = session.get(f"{base_url}/autowave", allow_redirects=True)
        print(f"AutoWave page: Status {response.status_code}")
        
        # Check final URL after redirects
        print(f"Final URL: {response.url}")
        
    except Exception as e:
        print(f"❌ Error accessing protected page: {e}")
    
    # Step 3: Create a test endpoint to set admin session
    print("\n3. Creating admin session via direct API call...")
    
    # Let's try to call the chat API directly and see what happens
    try:
        chat_data = {
            'message': 'Test admin access - this should work with admin bypass'
        }
        
        # Add session headers manually
        headers = {
            'Content-Type': 'application/json',
            'X-Admin-Email': '<EMAIL>',  # Custom header for testing
            'X-User-ID': 'admin-test-user'
        }
        
        response = session.post(f"{base_url}/api/chat/message",
                               json=chat_data,
                               headers=headers)
        
        print(f"Chat API with headers: Status {response.status_code}")
        print(f"Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error testing chat API: {e}")
    
    # Step 4: Test the actual Flask session by accessing the app directly
    print("\n4. Testing Flask app session directly...")
    
    try:
        # Import Flask app and test client
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # Set session data
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'
                sess['access_token'] = 'test_admin_token'
                sess['email_confirmed'] = True
            
            # Test chat API
            chat_response = client.post('/api/chat/message',
                                      json={'message': 'Test admin chat access'},
                                      content_type='application/json')
            
            print(f"Flask test client chat: Status {chat_response.status_code}")
            
            if chat_response.status_code == 200:
                result = chat_response.get_json()
                print("✅ Chat API working with admin session!")
                print(f"Response: {result.get('response', 'No response')[:100]}...")
            else:
                print(f"❌ Chat API failed: {chat_response.get_data(as_text=True)}")
            
            # Test document generation API
            doc_response = client.post('/api/document/generate',
                                     json={
                                         'content': 'Generate a test document for admin verification',
                                         'page_count': 1
                                     },
                                     content_type='application/json')
            
            print(f"Flask test client document: Status {doc_response.status_code}")
            
            if doc_response.status_code == 200:
                result = doc_response.get_json()
                print("✅ Document API working with admin session!")
                print(f"Success: {result.get('success', False)}")
            else:
                print(f"❌ Document API failed: {doc_response.get_data(as_text=True)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Flask app directly: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_credit_service_directly():
    """Test the credit service directly to verify admin bypass"""
    print("\n5. Testing Credit Service Directly...")
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(__file__))
        
        from app.services.credit_service import CreditService
        from flask import Flask
        
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'
        
        with app.test_request_context():
            from flask import session
            
            # Set admin session
            session['user_id'] = 'admin-reffynestan-test'
            session['user_email'] = '<EMAIL>'
            
            # Test credit consumption
            credit_service = CreditService()
            
            result = credit_service.consume_credits(
                user_id='admin-reffynestan-test',
                task_type='autowave_chat_basic',
                input_text='Test admin message',
                output_text='Test admin response'
            )
            
            print(f"Credit service result: {result}")
            
            if result.get('success') and result.get('admin_bypass'):
                print("✅ Credit service admin bypass working!")
                return True
            else:
                print("❌ Credit service admin bypass not working")
                return False
                
    except Exception as e:
        print(f"❌ Error testing credit service: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Real Admin Session Test")
    print("=" * 60)
    
    # Test with real session
    session_test = test_with_real_session()
    
    # Test credit service directly
    credit_test = test_credit_service_directly()
    
    print("\n📊 Test Results:")
    print(f"Session test: {'✅ PASS' if session_test else '❌ FAIL'}")
    print(f"Credit service test: {'✅ PASS' if credit_test else '❌ FAIL'}")
    
    if session_test and credit_test:
        print("\n🎉 Admin bypass is working!")
        print("The issue might be with how the frontend maintains sessions.")
        print("Try logging in through the actual login <NAME_EMAIL>")
    else:
        print("\n❌ Admin bypass has issues that need to be fixed.")
