# 🔍 Google Search Console & Domain Configuration Guide

## 📋 Overview
This guide will help you set up Google Search Console for AutoWave and configure your domain (autowave.pro) for optimal search engine visibility.

## 🚀 Step 1: Google Search Console Setup

### 1.1 Access Google Search Console
1. Go to [Google Search Console](https://search.google.com/search-console/)
2. Sign in with your Google account (use the same account you want to manage the site with)

### 1.2 Add Your Property
1. Click **"Add Property"**
2. Choose **"URL prefix"** method
3. Enter your domain: `https://autowave.pro`
4. Click **"Continue"**

### 1.3 Verify Domain Ownership
Google will provide several verification methods. Choose one:

#### Method A: HTML Meta Tag (Recommended)
1. Google will provide a meta tag like:
   ```html
   <meta name="google-site-verification" content="YOUR_VERIFICATION_CODE_HERE">
   ```
2. Copy the verification code
3. Add it to your HTML head section (already prepared in layout.html)
4. Deploy to production
5. Click **"Verify"** in Google Search Console

#### Method B: HTML File Upload
1. Download the HTML verification file from Google
2. Upload it to your static folder: `app/static/`
3. Make sure it's accessible at: `https://autowave.pro/google[verification-code].html`
4. Click **"Verify"**

#### Method C: DNS Verification (Advanced)
1. Add a TXT record to your domain DNS
2. Use the value provided by Google
3. Wait for DNS propagation (up to 24 hours)
4. Click **"Verify"**

## 🌐 Step 2: Namecheap Domain Configuration

### 2.1 Access Namecheap Dashboard
1. Log in to [Namecheap](https://www.namecheap.com/)
2. Go to **"Domain List"**
3. Click **"Manage"** next to autowave.pro

### 2.2 Configure DNS Settings
1. Go to **"Advanced DNS"** tab
2. Add/modify these records:

#### A Records (for apex domain)
```
Type: A Record
Host: @
Value: [Your Heroku IP or use CNAME]
TTL: Automatic
```

#### CNAME Records (for www subdomain)
```
Type: CNAME Record
Host: www
Value: autowave.pro
TTL: Automatic
```

#### For Heroku Deployment:
```
Type: CNAME Record
Host: @
Value: autowave1-0c9df082f22c.herokuapp.com
TTL: Automatic

Type: CNAME Record
Host: www
Value: autowave1-0c9df082f22c.herokuapp.com
TTL: Automatic
```

### 2.3 Add Google Verification (if using DNS method)
```
Type: TXT Record
Host: @
Value: google-site-verification=YOUR_VERIFICATION_CODE
TTL: Automatic
```

## ⚙️ Step 3: Heroku Custom Domain Setup

### 3.1 Add Domain to Heroku
```bash
# Add apex domain
heroku domains:add autowave.pro --app autowave1

# Add www subdomain
heroku domains:add www.autowave.pro --app autowave1

# Check domain status
heroku domains --app autowave1
```

### 3.2 Get Heroku DNS Target
```bash
heroku domains --app autowave1
```
Copy the DNS Target provided by Heroku and use it in your Namecheap CNAME records.

## 📊 Step 4: Submit Sitemap to Google

### 4.1 Access Sitemaps Section
1. In Google Search Console, go to **"Sitemaps"** in the left menu
2. Click **"Add a new sitemap"**
3. Enter: `sitemap.xml`
4. Click **"Submit"**

### 4.2 Verify Sitemap
- Check that your sitemap is accessible at: `https://autowave.pro/sitemap.xml`
- Google will process it within a few hours to days

## 🔧 Step 5: Additional SEO Configuration

### 5.1 Enable Rich Snippets
Add structured data to your pages for better search results:

```html
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "AutoWave",
  "description": "Advanced AI-powered assistant platform",
  "url": "https://autowave.pro",
  "applicationCategory": "ProductivityApplication",
  "operatingSystem": "Web",
  "offers": {
    "@type": "Offer",
    "price": "15",
    "priceCurrency": "USD"
  }
}
</script>
```

### 5.2 Monitor Performance
- Check **"Performance"** tab in Google Search Console
- Monitor **"Coverage"** for indexing issues
- Review **"Enhancements"** for structured data

## 📱 Step 6: Mobile and Core Web Vitals

### 6.1 Mobile Usability
- Google Search Console will test mobile usability
- Fix any issues reported

### 6.2 Core Web Vitals
- Monitor loading speed, interactivity, and visual stability
- Use Google PageSpeed Insights for detailed analysis

## 🔍 Step 7: Verification Checklist

### Before Going Live:
- [ ] Favicon appears in browser tab
- [ ] Meta tags are properly set
- [ ] Robots.txt is accessible
- [ ] Sitemap.xml is accessible
- [ ] Domain points to Heroku app
- [ ] SSL certificate is active
- [ ] Google verification meta tag is added

### After Going Live:
- [ ] Google Search Console verification completed
- [ ] Sitemap submitted and processed
- [ ] No crawl errors in Coverage report
- [ ] Mobile usability test passed
- [ ] Core Web Vitals are good

## 🚨 Troubleshooting

### Common Issues:
1. **Verification Failed**: Check if meta tag is in HTML head
2. **Domain Not Resolving**: Check DNS propagation (use dig or nslookup)
3. **SSL Issues**: Ensure Heroku SSL is enabled
4. **Sitemap Errors**: Validate XML syntax

### Useful Tools:
- [DNS Checker](https://dnschecker.org/)
- [SSL Checker](https://www.sslshopper.com/ssl-checker.html)
- [Google PageSpeed Insights](https://pagespeed.web.dev/)
- [XML Sitemap Validator](https://www.xml-sitemaps.com/validate-xml-sitemap.html)

## 📞 Support
If you encounter issues:
1. Check Heroku logs: `heroku logs --tail --app autowave1`
2. Verify DNS settings in Namecheap
3. Contact Heroku support for domain issues
4. Use Google Search Console Help for verification problems
