#!/usr/bin/env python3
"""
Fix Admin Access Script
This script will grant admin <NAME_EMAIL>
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_admin_access():
    """Fix admin <NAME_EMAIL>"""
    try:
        # Import services
        from app.services.admin_service import admin_service
        from app.services.subscription_service import SubscriptionService
        
        admin_email = '<EMAIL>'
        
        print(f"🔧 Fixing admin access for {admin_email}")
        
        # Check if email is recognized as admin
        is_admin = admin_service.is_admin(admin_email)
        print(f"   Admin email check: {'✅ Recognized' if is_admin else '❌ Not recognized'}")
        
        if not is_admin:
            print(f"❌ {admin_email} is not in the admin emails list")
            return False
        
        # Initialize subscription service
        subscription_service = SubscriptionService()
        
        # Check if using Supabase
        if not admin_service.use_supabase:
            print("✅ Using SQLite mode - admin access should work automatically")
            print("   Admin bypass will work based on email in session")
            return True
        
        print("🔍 Using Supabase mode - checking database...")
        
        # Find user by email in Supabase
        try:
            user_response = subscription_service.supabase.table('user_profiles').select('*').eq('email', admin_email).execute()
            
            if not user_response.data:
                print(f"❌ User profile not found for {admin_email}")
                print("   Please log in to the application first to create your profile")
                return False
            
            user = user_response.data[0]
            user_id = user['id']
            
            print(f"✅ Found user profile: {user_id}")
            
            # Grant admin access
            print("🔑 Granting admin access...")
            success = admin_service.grant_admin_access(user_id, admin_email)
            
            if success:
                print(f"✅ Admin access granted successfully to {admin_email}")
                
                # Verify the setup
                admin_status = admin_service.check_user_admin_status(user_id)
                print(f"   Admin status: {admin_status.get('is_admin', False)}")
                print(f"   Subscription tier: {admin_status.get('subscription_tier', 'unknown')}")
                print(f"   Has unlimited credits: {admin_status.get('has_unlimited_credits', False)}")
                
                return True
            else:
                print(f"❌ Failed to grant admin access to {admin_email}")
                return False
                
        except Exception as e:
            print(f"❌ Error accessing Supabase: {str(e)}")
            print("   This might be due to missing API keys or connection issues")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing admin access: {str(e)}")
        return False

def setup_local_admin():
    """Set up local admin access for testing"""
    try:
        print("🔧 Setting up local admin access...")
        
        # Create data directory if it doesn't exist
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        os.makedirs(data_dir, exist_ok=True)
        
        # Run the local database setup
        from setup_subscription_tables import setup_local_database
        
        if setup_local_database():
            print("✅ Local admin database setup complete")
            return True
        else:
            print("❌ Failed to set up local admin database")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up local admin: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 AutoWave Admin Access Fix")
    print("=" * 50)
    
    # Try to fix admin access
    success = fix_admin_access()
    
    if not success:
        print("\n🔄 Trying local database setup as fallback...")
        success = setup_local_admin()
    
    if success:
        print("\n✅ Admin access should now work!")
        print("   Please refresh your browser and try accessing the agent pages again.")
        print("   Your email (<EMAIL>) should now have unlimited access.")
    else:
        print("\n❌ Could not fix admin access automatically.")
        print("   Please ensure you're logged in to the application first.")
        print("   Then try running this script again.")
