# Agen911 Environment Variables

# Google Gemini API Keys
# Primary API Key
# Get your API key from the Google AI Developer Portal
GEMINI_API_KEY=your_gemini_api_key_here

# Backup API Keys (add your additional keys here)
GEMINI_API_KEY_BACKUP1=your_gemini_backup_key_1_here
GEMINI_API_KEY_BACKUP2=your_gemini_backup_key_2_here

# Groq API Key (fallback for Gemini)
# Get your API key from https://console.groq.com/
GROQ_API_KEY=your_groq_api_key_here
# MCP Server API Keys
# Serper API Key for web and image search
# Get your API key from https://serper.dev/
SERPER_API_KEY=your_serper_api_key_here

# Google Custom Search API Key and CX
# Get your API key from https://developers.google.com/custom-search/v1/overview
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CX=your_google_cx_here

# Unsplash API Key for image search
# Get your API key from https://unsplash.com/developers
UNSPLASH_API_KEY=your_unsplash_api_key_here

# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development

# Live Browser Configuration
LIGHTWEIGHT_MODE=false
DISABLE_SCREENSHOTS=false
DISABLE_SCREEN_RECORDING=false
SCREENSHOT_INTERVAL_MS=300
