# CORE DEPENDENCIES - <PERSON><PERSON><PERSON>U COMPATIBLE
Flask==3.0.0
Flask-CORS==4.0.0
python-dotenv==1.0.0
requests==2.31.0
PyJWT==2.8.0
psutil==5.9.5

# AI DEPENDENCIES
google-generativeai==0.3.2

# WEB SCRAPING
beautifulsoup4==4.12.2

# IMAGE PROCESSING
Pillow==10.0.1

# PDF PROCESSING
PyPDF2==3.0.1

# DOCX PROCESSING
python-docx==0.8.11

# MARKDOWN PROCESSING
markdown==3.4.4

# DATA ANALYSIS DEPENDENCIES - FOR AGENT WAVE
numpy==1.24.3
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2

# INFOGRAPHICS DEPENDENCIES - FOR AGENT WAVE
plotly==5.17.0
reportlab==4.0.4
svglib==1.5.1

# SELENIUM FOR WEB AUTOMATION
selenium==4.15.2
webdriver-manager==4.0.1

# SUPABASE - HEROKU COMPATIBLE (UPDATED FOR PROXY FIX)
supabase==2.8.0
realtime==2.0.5
websockets==11.0.3

# ADDITIONAL DEPENDENCIES FOR AUTOWAVE
gunicorn==21.2.0
urllib3==2.0.7
certifi==2023.7.22
cryptography==41.0.7
stripe==7.8.0
groq==0.4.1

# BUILD TOOLS - HEROKU COMPATIBILITY
setuptools>=65.0.0
wheel>=0.38.0
pip>=22.0.0
