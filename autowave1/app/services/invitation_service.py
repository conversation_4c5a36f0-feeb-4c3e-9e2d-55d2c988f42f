"""
AutoWave Invitation System
Special promotion: Pay 1 month Plus, get 2 months free (total 3 months)
Each invitation link can only be used once and expires after 7 days if no subscription
"""

import os
import json
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    logging.warning("Supabase not available. Invitation features will use local storage.")

logger = logging.getLogger(__name__)

@dataclass
class InvitationLink:
    """Data class for invitation link information"""
    id: str
    code: str
    created_by: str
    created_at: datetime
    expires_at: Optional[datetime] = None
    used_by: Optional[str] = None
    used_at: Optional[datetime] = None
    is_active: bool = True
    promotion_type: str = "plus_3_months"  # Pay 1 month, get 2 free
    credits_total: int = 16000  # 2x Plus plan credits
    description: Optional[str] = None

@dataclass
class InvitationUser:
    """Data class for users who signed up via invitation"""
    user_id: str
    invitation_code: str
    signup_date: datetime
    subscription_date: Optional[datetime] = None
    credits_used: int = 0
    credits_remaining: int = 16000
    is_expired: bool = False
    expiry_date: datetime = None

class InvitationService:
    """Service for managing invitation links and promotions"""
    
    def __init__(self):
        self.supabase = None
        self.invitation_data_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'data', 'invitations.json'
        )
        self.invitation_users_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'data', 'invitation_users.json'
        )
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.invitation_data_file), exist_ok=True)
        
        # Try to initialize Supabase
        try:
            if SUPABASE_AVAILABLE:
                supabase_url = os.environ.get('SUPABASE_URL')
                supabase_key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
                
                if supabase_url and supabase_key:
                    self.supabase = create_client(supabase_url, supabase_key)
                    logger.info("✅ Invitation service initialized with Supabase")
                else:
                    logger.warning("⚠️ Supabase not configured, using local file storage")
        except Exception as e:
            logger.warning(f"⚠️ Supabase initialization failed: {e}")
    
    def _load_invitations(self) -> Dict[str, Any]:
        """Load invitation data from local file"""
        try:
            if os.path.exists(self.invitation_data_file):
                with open(self.invitation_data_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading invitation data: {e}")
        return {}
    
    def _save_invitations(self, data: Dict[str, Any]) -> None:
        """Save invitation data to local file"""
        try:
            with open(self.invitation_data_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving invitation data: {e}")
    
    def _load_invitation_users(self) -> Dict[str, Any]:
        """Load invitation users data from local file"""
        try:
            if os.path.exists(self.invitation_users_file):
                with open(self.invitation_users_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading invitation users data: {e}")
        return {}
    
    def _save_invitation_users(self, data: Dict[str, Any]) -> None:
        """Save invitation users data to local file"""
        try:
            with open(self.invitation_users_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving invitation users data: {e}")
    
    def generate_invitation_link(self, created_by: str, description: str = None) -> InvitationLink:
        """
        Generate a new invitation link
        
        Args:
            created_by: User ID who created the link
            description: Optional description for the link
            
        Returns:
            InvitationLink object
        """
        invitation_id = str(uuid.uuid4())
        invitation_code = f"INV-{uuid.uuid4().hex[:8].upper()}"
        
        invitation = InvitationLink(
            id=invitation_id,
            code=invitation_code,
            created_by=created_by,
            created_at=datetime.now(),
            description=description or f"Invitation link created on {datetime.now().strftime('%Y-%m-%d')}"
        )
        
        # Try Supabase first
        if self.supabase:
            try:
                self.supabase.table('invitation_links').insert({
                    'id': invitation.id,
                    'code': invitation.code,
                    'created_by': invitation.created_by,
                    'created_at': invitation.created_at.isoformat(),
                    'is_active': invitation.is_active,
                    'promotion_type': invitation.promotion_type,
                    'credits_total': invitation.credits_total,
                    'description': invitation.description
                }).execute()
                
                logger.info(f"✅ Invitation link created in Supabase: {invitation.code}")
                return invitation
                
            except Exception as e:
                logger.error(f"Error creating invitation in Supabase: {e}")
        
        # Fallback to local storage
        invitations_data = self._load_invitations()
        invitations_data[invitation.code] = {
            'id': invitation.id,
            'code': invitation.code,
            'created_by': invitation.created_by,
            'created_at': invitation.created_at.isoformat(),
            'expires_at': invitation.expires_at.isoformat() if invitation.expires_at else None,
            'used_by': invitation.used_by,
            'used_at': invitation.used_at.isoformat() if invitation.used_at else None,
            'is_active': invitation.is_active,
            'promotion_type': invitation.promotion_type,
            'credits_total': invitation.credits_total,
            'description': invitation.description
        }
        
        self._save_invitations(invitations_data)
        logger.info(f"✅ Invitation link created locally: {invitation.code}")
        return invitation
    
    def get_invitation_by_code(self, code: str) -> Optional[InvitationLink]:
        """
        Get invitation link by code
        
        Args:
            code: Invitation code
            
        Returns:
            InvitationLink object or None
        """
        # Try Supabase first
        if self.supabase:
            try:
                response = self.supabase.table('invitation_links').select('*').eq('code', code).execute()
                
                if response.data:
                    data = response.data[0]
                    return InvitationLink(
                        id=data['id'],
                        code=data['code'],
                        created_by=data['created_by'],
                        created_at=datetime.fromisoformat(data['created_at']),
                        expires_at=datetime.fromisoformat(data['expires_at']) if data.get('expires_at') else None,
                        used_by=data.get('used_by'),
                        used_at=datetime.fromisoformat(data['used_at']) if data.get('used_at') else None,
                        is_active=data.get('is_active', True),
                        promotion_type=data.get('promotion_type', 'plus_3_months'),
                        credits_total=data.get('credits_total', 16000),
                        description=data.get('description')
                    )
                    
            except Exception as e:
                logger.error(f"Error getting invitation from Supabase: {e}")
        
        # Fallback to local storage
        invitations_data = self._load_invitations()
        if code in invitations_data:
            data = invitations_data[code]
            return InvitationLink(
                id=data['id'],
                code=data['code'],
                created_by=data['created_by'],
                created_at=datetime.fromisoformat(data['created_at']),
                expires_at=datetime.fromisoformat(data['expires_at']) if data.get('expires_at') else None,
                used_by=data.get('used_by'),
                used_at=datetime.fromisoformat(data['used_at']) if data.get('used_at') else None,
                is_active=data.get('is_active', True),
                promotion_type=data.get('promotion_type', 'plus_3_months'),
                credits_total=data.get('credits_total', 16000),
                description=data.get('description')
            )
        
        return None

    def use_invitation_link(self, code: str, user_id: str) -> bool:
        """
        Use an invitation link for user signup

        Args:
            code: Invitation code
            user_id: User ID who is using the link

        Returns:
            True if successful, False otherwise
        """
        invitation = self.get_invitation_by_code(code)

        if not invitation:
            logger.warning(f"Invitation code not found: {code}")
            return False

        if not invitation.is_active:
            logger.warning(f"Invitation code is inactive: {code}")
            return False

        if invitation.used_by:
            logger.warning(f"Invitation code already used: {code}")
            return False

        # Mark invitation as used
        now = datetime.now()
        expiry_date = now + timedelta(days=7)  # 7 days to subscribe

        # Try Supabase first
        if self.supabase:
            try:
                # Update invitation link
                self.supabase.table('invitation_links').update({
                    'used_by': user_id,
                    'used_at': now.isoformat(),
                    'expires_at': expiry_date.isoformat()
                }).eq('code', code).execute()

                # Create invitation user record
                self.supabase.table('invitation_users').insert({
                    'user_id': user_id,
                    'invitation_code': code,
                    'signup_date': now.isoformat(),
                    'credits_used': 0,
                    'credits_remaining': 16000,
                    'is_expired': False,
                    'expiry_date': expiry_date.isoformat()
                }).execute()

                logger.info(f"✅ Invitation used in Supabase: {code} by {user_id}")
                return True

            except Exception as e:
                logger.error(f"Error using invitation in Supabase: {e}")

        # Fallback to local storage
        invitations_data = self._load_invitations()
        if code in invitations_data:
            invitations_data[code]['used_by'] = user_id
            invitations_data[code]['used_at'] = now.isoformat()
            invitations_data[code]['expires_at'] = expiry_date.isoformat()
            self._save_invitations(invitations_data)

        # Create invitation user record
        users_data = self._load_invitation_users()
        users_data[user_id] = {
            'user_id': user_id,
            'invitation_code': code,
            'signup_date': now.isoformat(),
            'subscription_date': None,
            'credits_used': 0,
            'credits_remaining': 16000,
            'is_expired': False,
            'expiry_date': expiry_date.isoformat()
        }
        self._save_invitation_users(users_data)

        logger.info(f"✅ Invitation used locally: {code} by {user_id}")
        return True

    def get_invitation_user(self, user_id: str) -> Optional[InvitationUser]:
        """
        Get invitation user data

        Args:
            user_id: User ID

        Returns:
            InvitationUser object or None
        """
        # Try Supabase first
        if self.supabase:
            try:
                response = self.supabase.table('invitation_users').select('*').eq('user_id', user_id).execute()

                if response.data:
                    data = response.data[0]
                    return InvitationUser(
                        user_id=data['user_id'],
                        invitation_code=data['invitation_code'],
                        signup_date=datetime.fromisoformat(data['signup_date']),
                        subscription_date=datetime.fromisoformat(data['subscription_date']) if data.get('subscription_date') else None,
                        credits_used=data.get('credits_used', 0),
                        credits_remaining=data.get('credits_remaining', 16000),
                        is_expired=data.get('is_expired', False),
                        expiry_date=datetime.fromisoformat(data['expiry_date']) if data.get('expiry_date') else None
                    )

            except Exception as e:
                logger.error(f"Error getting invitation user from Supabase: {e}")

        # Fallback to local storage
        users_data = self._load_invitation_users()
        if user_id in users_data:
            data = users_data[user_id]
            return InvitationUser(
                user_id=data['user_id'],
                invitation_code=data['invitation_code'],
                signup_date=datetime.fromisoformat(data['signup_date']),
                subscription_date=datetime.fromisoformat(data['subscription_date']) if data.get('subscription_date') else None,
                credits_used=data.get('credits_used', 0),
                credits_remaining=data.get('credits_remaining', 16000),
                is_expired=data.get('is_expired', False),
                expiry_date=datetime.fromisoformat(data['expiry_date']) if data.get('expiry_date') else None
            )

        return None

    def activate_invitation_subscription(self, user_id: str) -> bool:
        """
        Activate invitation subscription when user pays

        Args:
            user_id: User ID

        Returns:
            True if successful, False otherwise
        """
        invitation_user = self.get_invitation_user(user_id)

        if not invitation_user:
            logger.warning(f"No invitation user found: {user_id}")
            return False

        if invitation_user.is_expired:
            logger.warning(f"Invitation expired for user: {user_id}")
            return False

        # Check if still within 7-day window
        if invitation_user.expiry_date and datetime.now() > invitation_user.expiry_date:
            logger.warning(f"Invitation expired (7 days passed) for user: {user_id}")
            return False

        now = datetime.now()

        # Try Supabase first
        if self.supabase:
            try:
                self.supabase.table('invitation_users').update({
                    'subscription_date': now.isoformat()
                }).eq('user_id', user_id).execute()

                logger.info(f"✅ Invitation subscription activated in Supabase: {user_id}")
                return True

            except Exception as e:
                logger.error(f"Error activating invitation subscription in Supabase: {e}")

        # Fallback to local storage
        users_data = self._load_invitation_users()
        if user_id in users_data:
            users_data[user_id]['subscription_date'] = now.isoformat()
            self._save_invitation_users(users_data)

        logger.info(f"✅ Invitation subscription activated locally: {user_id}")
        return True

    def get_all_invitations(self, created_by: str = None) -> List[InvitationLink]:
        """
        Get all invitation links, optionally filtered by creator

        Args:
            created_by: Optional user ID to filter by

        Returns:
            List of InvitationLink objects
        """
        invitations = []

        # Try Supabase first
        if self.supabase:
            try:
                query = self.supabase.table('invitation_links').select('*')
                if created_by:
                    query = query.eq('created_by', created_by)

                response = query.execute()

                for data in response.data:
                    invitations.append(InvitationLink(
                        id=data['id'],
                        code=data['code'],
                        created_by=data['created_by'],
                        created_at=datetime.fromisoformat(data['created_at']),
                        expires_at=datetime.fromisoformat(data['expires_at']) if data.get('expires_at') else None,
                        used_by=data.get('used_by'),
                        used_at=datetime.fromisoformat(data['used_at']) if data.get('used_at') else None,
                        is_active=data.get('is_active', True),
                        promotion_type=data.get('promotion_type', 'plus_3_months'),
                        credits_total=data.get('credits_total', 16000),
                        description=data.get('description')
                    ))

                return invitations

            except Exception as e:
                logger.error(f"Error getting invitations from Supabase: {e}")

        # Fallback to local storage
        invitations_data = self._load_invitations()
        for code, data in invitations_data.items():
            if not created_by or data.get('created_by') == created_by:
                invitations.append(InvitationLink(
                    id=data['id'],
                    code=data['code'],
                    created_by=data['created_by'],
                    created_at=datetime.fromisoformat(data['created_at']),
                    expires_at=datetime.fromisoformat(data['expires_at']) if data.get('expires_at') else None,
                    used_by=data.get('used_by'),
                    used_at=datetime.fromisoformat(data['used_at']) if data.get('used_at') else None,
                    is_active=data.get('is_active', True),
                    promotion_type=data.get('promotion_type', 'plus_3_months'),
                    credits_total=data.get('credits_total', 16000),
                    description=data.get('description')
                ))

        return invitations

# Global instance
invitation_service = InvitationService()
