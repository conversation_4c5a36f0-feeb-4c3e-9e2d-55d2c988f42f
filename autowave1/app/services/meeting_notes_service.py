"""
Meeting Notes Service
Handles voice transcription and meeting notes functionality using multiple AI providers
"""

import os
import base64
import tempfile
import logging
import requests
import json
from datetime import datetime
from flask import session
from app.services.admin_service import AdminService

# Set up logging
logger = logging.getLogger(__name__)

class MeetingNotesService:
    def __init__(self):
        self.admin_service = AdminService()

        # Initialize transcription providers in order of preference
        self.transcription_providers = []

        # Initialize text processing providers (for enhancement and summarization)
        self.text_providers = []

        # 1. Try Groq Whisper (fastest and cheapest)
        groq_api_key = os.getenv('GROQ_API_KEY')
        if groq_api_key:
            self.transcription_providers.append({
                'name': 'groq',
                'api_key': groq_api_key,
                'base_url': 'https://api.groq.com/openai/v1'
            })
            # Also add Groq for text processing
            self.text_providers.append({
                'name': 'groq',
                'api_key': groq_api_key,
                'base_url': 'https://api.groq.com/openai/v1'
            })
            logger.info("✅ Groq available for transcription and text processing")

        # 2. Try Gemini API (excellent for text processing)
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            self.text_providers.append({
                'name': 'gemini',
                'api_key': gemini_api_key
            })
            logger.info("✅ Gemini API available for text processing")

        # 3. Try OpenAI (fallback)
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if openai_api_key:
            import openai
            self.transcription_providers.append({
                'name': 'openai',
                'api_key': openai_api_key,
                'client': openai.OpenAI(api_key=openai_api_key)
            })
            self.text_providers.append({
                'name': 'openai',
                'api_key': openai_api_key,
                'client': openai.OpenAI(api_key=openai_api_key)
            })
            logger.info("✅ OpenAI available for transcription and text processing")

        if not self.transcription_providers:
            logger.error("No transcription providers available. Please set GROQ_API_KEY or OPENAI_API_KEY")
            raise ValueError("At least one transcription API key is required (GROQ_API_KEY or OPENAI_API_KEY)")

        if not self.text_providers:
            logger.error("No text processing providers available. Please set GROQ_API_KEY, GEMINI_API_KEY, or OPENAI_API_KEY")
            raise ValueError("At least one text processing API key is required")

        logger.info(f"Initialized with {len(self.transcription_providers)} transcription providers and {len(self.text_providers)} text processing providers")
    
    def transcribe_audio(self, audio_data, audio_format='webm'):
        """
        Transcribe audio using available AI providers (Groq, OpenAI)

        Args:
            audio_data (str): Base64 encoded audio data
            audio_format (str): Audio format (webm, mp3, wav, etc.)

        Returns:
            dict: Result with success status and transcription or error
        """
        try:
            # Decode base64 audio data
            audio_bytes = base64.b64decode(audio_data)

            # Create temporary file for audio
            with tempfile.NamedTemporaryFile(suffix=f'.{audio_format}', delete=False) as temp_file:
                temp_file.write(audio_bytes)
                temp_file_path = temp_file.name

            # Try each transcription provider in order
            last_error = None
            for provider in self.transcription_providers:
                try:
                    logger.info(f"Attempting transcription with {provider['name']}")

                    if provider['name'] == 'groq':
                        result = self._transcribe_with_groq(temp_file_path, provider)
                    elif provider['name'] == 'openai':
                        result = self._transcribe_with_openai(temp_file_path, provider)
                    else:
                        continue

                    # Clean up temporary file on success
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

                    if result['success']:
                        logger.info(f"✅ Transcription successful with {provider['name']}")
                        return result
                    else:
                        last_error = result.get('error', 'Unknown error')
                        logger.warning(f"❌ {provider['name']} failed: {last_error}")

                except Exception as e:
                    last_error = str(e)
                    logger.error(f"❌ {provider['name']} error: {last_error}")
                    continue

            # Clean up temporary file if all providers failed
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

            return {
                'success': False,
                'error': f'All transcription providers failed. Last error: {last_error}'
            }

        except Exception as e:
            logger.error(f"Error in transcribe_audio: {str(e)}")
            return {
                'success': False,
                'error': f'Transcription failed: {str(e)}'
            }
    
    def save_notes(self, title, content):
        """
        Save meeting notes to user's account
        
        Args:
            title (str): Meeting title
            content (str): Notes content
        
        Returns:
            dict: Result with success status
        """
        try:
            user_email = session.get('user_email')
            if not user_email:
                return {
                    'success': False,
                    'error': 'User not authenticated'
                }
            
            # For now, we'll just return success
            # In a full implementation, you would save to a database
            logger.info(f"Saving meeting notes for user {user_email}: {title}")
            
            return {
                'success': True,
                'message': 'Notes saved successfully',
                'note_id': f"note_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }
            
        except Exception as e:
            logger.error(f"Error saving notes: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to save notes: {str(e)}'
            }
    
    def list_user_notes(self):
        """
        List user's saved meeting notes
        
        Returns:
            dict: Result with success status and notes list
        """
        try:
            user_email = session.get('user_email')
            if not user_email:
                return {
                    'success': False,
                    'error': 'User not authenticated'
                }
            
            # For now, return empty list
            # In a full implementation, you would fetch from database
            return {
                'success': True,
                'notes': []
            }
            
        except Exception as e:
            logger.error(f"Error listing notes: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to list notes: {str(e)}'
            }
    
    def enhance_transcription(self, raw_transcription):
        """
        Enhance raw transcription with AI to improve formatting and clarity

        Args:
            raw_transcription (str): Raw transcription text

        Returns:
            dict: Enhanced transcription result
        """
        try:
            if not raw_transcription or not raw_transcription.strip():
                return {
                    'success': False,
                    'error': 'No transcription to enhance'
                }

            enhancement_prompt = """Please enhance this meeting transcription by:
1. Adding proper punctuation and capitalization
2. Breaking it into logical paragraphs
3. Identifying and formatting action items with bullet points
4. Correcting obvious speech-to-text errors
5. Maintaining the original meaning and content

Raw transcription:
{transcription}

Enhanced transcription:"""

            # Try each text processing provider in order
            last_error = None
            for provider in self.text_providers:
                try:
                    logger.info(f"Attempting transcription enhancement with {provider['name']}")

                    if provider['name'] == 'groq':
                        result = self._enhance_with_groq(raw_transcription, enhancement_prompt, provider)
                    elif provider['name'] == 'gemini':
                        result = self._enhance_with_gemini(raw_transcription, enhancement_prompt, provider)
                    elif provider['name'] == 'openai':
                        result = self._enhance_with_openai(raw_transcription, enhancement_prompt, provider)
                    else:
                        continue

                    if result['success']:
                        logger.info(f"✅ Enhancement successful with {provider['name']}")
                        return result
                    else:
                        last_error = result.get('error', 'Unknown error')
                        logger.warning(f"❌ {provider['name']} enhancement failed: {last_error}")

                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"❌ {provider['name']} enhancement error: {last_error}")
                    continue

            # All providers failed
            logger.error(f"All text processing providers failed for enhancement. Last error: {last_error}")
            return {
                'success': False,
                'error': f'Enhancement failed with all providers: {last_error}',
                'fallback_transcription': raw_transcription
            }

        except Exception as e:
            logger.error(f"Error in enhance_transcription: {str(e)}")
            return {
                'success': False,
                'error': f'Enhancement failed: {str(e)}',
                'fallback_transcription': raw_transcription
            }
    
    def generate_meeting_summary(self, notes_content):
        """
        Generate a summary of meeting notes

        Args:
            notes_content (str): Full meeting notes content

        Returns:
            dict: Summary result
        """
        try:
            if not notes_content or not notes_content.strip():
                return {
                    'success': False,
                    'error': 'No notes content to summarize'
                }

            summary_prompt = """Please create a concise summary of these meeting notes including:
1. Key discussion points
2. Decisions made
3. Action items and who is responsible
4. Next steps

Meeting notes:
{notes_content}

Summary:"""

            # Try each text processing provider in order
            last_error = None
            for provider in self.text_providers:
                try:
                    logger.info(f"Attempting summary generation with {provider['name']}")

                    if provider['name'] == 'groq':
                        result = self._summarize_with_groq(notes_content, summary_prompt, provider)
                    elif provider['name'] == 'gemini':
                        result = self._summarize_with_gemini(notes_content, summary_prompt, provider)
                    elif provider['name'] == 'openai':
                        result = self._summarize_with_openai(notes_content, summary_prompt, provider)
                    else:
                        continue

                    if result['success']:
                        logger.info(f"✅ Summary generation successful with {provider['name']}")
                        return result
                    else:
                        last_error = result.get('error', 'Unknown error')
                        logger.warning(f"❌ {provider['name']} summary failed: {last_error}")

                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"❌ {provider['name']} summary error: {last_error}")
                    continue

            # All providers failed
            logger.error(f"All text processing providers failed for summary. Last error: {last_error}")
            return {
                'success': False,
                'error': f'Summary generation failed with all providers: {last_error}'
            }

        except Exception as e:
            logger.error(f"Error in generate_meeting_summary: {str(e)}")
            return {
                'success': False,
                'error': f'Summary generation failed: {str(e)}'
            }

    def _transcribe_with_groq(self, audio_file_path, provider):
        """
        Transcribe audio using Groq Whisper API

        Args:
            audio_file_path (str): Path to audio file
            provider (dict): Provider configuration

        Returns:
            dict: Transcription result
        """
        try:
            # Groq uses OpenAI-compatible Whisper API
            headers = {
                'Authorization': f'Bearer {provider["api_key"]}'
            }

            # Open and send the audio file
            with open(audio_file_path, 'rb') as audio_file:
                files = {
                    'file': (os.path.basename(audio_file_path), audio_file, 'audio/webm'),
                    'model': (None, 'whisper-large-v3'),
                    'response_format': (None, 'json'),
                    'language': (None, 'en')  # Can be made configurable
                }

                response = requests.post(
                    f"{provider['base_url']}/audio/transcriptions",
                    headers=headers,
                    files=files,
                    timeout=60
                )

                response.raise_for_status()
                result = response.json()

                if 'text' in result:
                    transcription = result['text'].strip()
                    if transcription:
                        logger.info(f"Groq transcription successful: {len(transcription)} characters")
                        return {
                            'success': True,
                            'transcription': transcription,
                            'provider': 'groq'
                        }
                    else:
                        return {
                            'success': False,
                            'error': 'Empty transcription from Groq'
                        }
                else:
                    return {
                        'success': False,
                        'error': f'Unexpected Groq response format: {result}'
                    }

        except requests.exceptions.RequestException as e:
            logger.error(f"Groq API request error: {str(e)}")
            return {
                'success': False,
                'error': f'Groq API error: {str(e)}'
            }
        except Exception as e:
            logger.error(f"Groq transcription error: {str(e)}")
            return {
                'success': False,
                'error': f'Groq transcription failed: {str(e)}'
            }

    def _transcribe_with_openai(self, audio_file_path, provider):
        """
        Transcribe audio using OpenAI Whisper API

        Args:
            audio_file_path (str): Path to audio file
            provider (dict): Provider configuration

        Returns:
            dict: Transcription result
        """
        try:
            client = provider['client']

            with open(audio_file_path, 'rb') as audio_file:
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="text"
                )

                if transcript and transcript.strip():
                    logger.info(f"OpenAI transcription successful: {len(transcript)} characters")
                    return {
                        'success': True,
                        'transcription': transcript.strip(),
                        'provider': 'openai'
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Empty transcription from OpenAI'
                    }

        except Exception as e:
            logger.error(f"OpenAI transcription error: {str(e)}")
            return {
                'success': False,
                'error': f'OpenAI transcription failed: {str(e)}'
            }

    def _enhance_with_groq(self, transcription, prompt_template, provider):
        """Enhance transcription using Groq API"""
        try:
            import requests

            headers = {
                'Authorization': f'Bearer {provider["api_key"]}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': 'llama-3.1-8b-instant',  # Fast and cheap model
                'messages': [
                    {
                        'role': 'system',
                        'content': 'You are a professional meeting notes assistant. Enhance transcriptions while preserving all original content and meaning.'
                    },
                    {
                        'role': 'user',
                        'content': prompt_template.format(transcription=transcription)
                    }
                ],
                'max_tokens': 2000,
                'temperature': 0.3
            }

            response = requests.post(
                f'{provider["base_url"]}/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            if 'choices' in result and len(result['choices']) > 0:
                enhanced_text = result['choices'][0]['message']['content'].strip()
                if enhanced_text:
                    return {
                        'success': True,
                        'enhanced_transcription': enhanced_text,
                        'provider': 'groq'
                    }

            return {
                'success': False,
                'error': 'Empty response from Groq'
            }

        except Exception as e:
            logger.error(f"Error enhancing with Groq: {str(e)}")
            return {
                'success': False,
                'error': f'Groq enhancement failed: {str(e)}'
            }

    def _enhance_with_gemini(self, transcription, prompt_template, provider):
        """Enhance transcription using Gemini API"""
        try:
            import requests

            url = f'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key={provider["api_key"]}'

            headers = {
                'Content-Type': 'application/json'
            }

            data = {
                'contents': [{
                    'parts': [{
                        'text': f"""You are a professional meeting notes assistant. Enhance transcriptions while preserving all original content and meaning.

{prompt_template.format(transcription=transcription)}"""
                    }]
                }],
                'generationConfig': {
                    'temperature': 0.3,
                    'maxOutputTokens': 2000
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()

            if 'candidates' in result and len(result['candidates']) > 0:
                content = result['candidates'][0]['content']['parts'][0]['text'].strip()
                if content:
                    return {
                        'success': True,
                        'enhanced_transcription': content,
                        'provider': 'gemini'
                    }

            return {
                'success': False,
                'error': 'Empty response from Gemini'
            }

        except Exception as e:
            logger.error(f"Error enhancing with Gemini: {str(e)}")
            return {
                'success': False,
                'error': f'Gemini enhancement failed: {str(e)}'
            }

    def _enhance_with_openai(self, transcription, prompt_template, provider):
        """Enhance transcription using OpenAI API"""
        try:
            client = provider['client']

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional meeting notes assistant. Enhance transcriptions while preserving all original content and meaning."
                    },
                    {
                        "role": "user",
                        "content": prompt_template.format(transcription=transcription)
                    }
                ],
                max_tokens=2000,
                temperature=0.3
            )

            enhanced_text = response.choices[0].message.content.strip()

            return {
                'success': True,
                'enhanced_transcription': enhanced_text,
                'provider': 'openai'
            }

        except Exception as e:
            logger.error(f"Error enhancing with OpenAI: {str(e)}")
            return {
                'success': False,
                'error': f'OpenAI enhancement failed: {str(e)}'
            }

    def _summarize_with_groq(self, notes_content, prompt_template, provider):
        """Summarize notes using Groq API"""
        try:
            import requests

            headers = {
                'Authorization': f'Bearer {provider["api_key"]}',
                'Content-Type': 'application/json'
            }

            data = {
                'model': 'llama-3.1-8b-instant',  # Fast and cheap model
                'messages': [
                    {
                        'role': 'system',
                        'content': 'You are a professional meeting summarizer. Create clear, actionable summaries.'
                    },
                    {
                        'role': 'user',
                        'content': prompt_template.format(notes_content=notes_content)
                    }
                ],
                'max_tokens': 1000,
                'temperature': 0.3
            }

            response = requests.post(
                f'{provider["base_url"]}/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            if 'choices' in result and len(result['choices']) > 0:
                summary = result['choices'][0]['message']['content'].strip()
                if summary:
                    return {
                        'success': True,
                        'summary': summary,
                        'provider': 'groq'
                    }

            return {
                'success': False,
                'error': 'Empty response from Groq'
            }

        except Exception as e:
            logger.error(f"Error summarizing with Groq: {str(e)}")
            return {
                'success': False,
                'error': f'Groq summarization failed: {str(e)}'
            }

    def _summarize_with_gemini(self, notes_content, prompt_template, provider):
        """Summarize notes using Gemini API"""
        try:
            import requests

            url = f'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent?key={provider["api_key"]}'

            headers = {
                'Content-Type': 'application/json'
            }

            data = {
                'contents': [{
                    'parts': [{
                        'text': f"""You are a professional meeting summarizer. Create clear, actionable summaries.

{prompt_template.format(notes_content=notes_content)}"""
                    }]
                }],
                'generationConfig': {
                    'temperature': 0.3,
                    'maxOutputTokens': 1000
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()

            if 'candidates' in result and len(result['candidates']) > 0:
                summary = result['candidates'][0]['content']['parts'][0]['text'].strip()
                if summary:
                    return {
                        'success': True,
                        'summary': summary,
                        'provider': 'gemini'
                    }

            return {
                'success': False,
                'error': 'Empty response from Gemini'
            }

        except Exception as e:
            logger.error(f"Error summarizing with Gemini: {str(e)}")
            return {
                'success': False,
                'error': f'Gemini summarization failed: {str(e)}'
            }

    def _summarize_with_openai(self, notes_content, prompt_template, provider):
        """Summarize notes using OpenAI API"""
        try:
            client = provider['client']

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional meeting summarizer. Create clear, actionable summaries."
                    },
                    {
                        "role": "user",
                        "content": prompt_template.format(notes_content=notes_content)
                    }
                ],
                max_tokens=1000,
                temperature=0.3
            )

            summary = response.choices[0].message.content.strip()

            return {
                'success': True,
                'summary': summary,
                'provider': 'openai'
            }

        except Exception as e:
            logger.error(f"Error summarizing with OpenAI: {str(e)}")
            return {
                'success': False,
                'error': f'OpenAI summarization failed: {str(e)}'
            }
