"""
Trial Management Service for AutoWave
Handles one-time trial limits per page for free users
"""

import os
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class TrialService:
    """Service for managing one-time trial limits per page"""
    
    # Trial limits per page (one-time only, no renewal)
    TRIAL_LIMITS = {
        'research_lab': 2,          # Research Lab: 2 prompts
        'agent_wave': 2,            # Agent Wave: 2 prompts  
        'agentic_code': 3,          # Agentic Code: 3 prompts
        'prime_agent_task': 2,      # Prime Agent Task (AutoWave): 2 prompts
        'context7_tools': 2,        # Prime Agent Tools (Context7): 2 prompts
        # Note: autowave_chat is handled by credit_service with daily renewal
    }
    
    def __init__(self):
        self.supabase = None
        self.trial_data_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'data', 'trial_usage.json'
        )
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.trial_data_file), exist_ok=True)
        
        # Try to initialize Supabase for trial tracking
        try:
            from supabase import create_client, Client
            supabase_url = os.environ.get('SUPABASE_URL')
            supabase_key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
            
            if supabase_url and supabase_key:
                self.supabase = create_client(supabase_url, supabase_key)
                logger.info("✅ Trial service initialized with Supabase")
            else:
                logger.warning("⚠️ Supabase not configured, using local file storage for trials")
        except Exception as e:
            logger.warning(f"⚠️ Supabase initialization failed, using local file storage: {e}")
    
    def _load_trial_data(self) -> Dict[str, Any]:
        """Load trial usage data from local file"""
        try:
            if os.path.exists(self.trial_data_file):
                with open(self.trial_data_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error loading trial data: {e}")
        return {}
    
    def _save_trial_data(self, data: Dict[str, Any]) -> None:
        """Save trial usage data to local file"""
        try:
            with open(self.trial_data_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving trial data: {e}")
    
    def check_trial_limit(self, user_id: str, page_name: str) -> Tuple[bool, int, int]:
        """
        Check if user has exceeded trial limit for a specific page
        
        Args:
            user_id: User identifier
            page_name: Page name (research_lab, agent_wave, etc.)
            
        Returns:
            Tuple of (can_use, used_count, limit)
        """
        if page_name not in self.TRIAL_LIMITS:
            logger.warning(f"Unknown page name: {page_name}")
            return True, 0, 0  # Allow if page not in trial system
        
        limit = self.TRIAL_LIMITS[page_name]
        
        # Try Supabase first
        if self.supabase:
            try:
                response = self.supabase.table('trial_usage').select('*').eq('user_id', user_id).eq('page_name', page_name).execute()
                
                if response.data:
                    used_count = response.data[0].get('usage_count', 0)
                else:
                    used_count = 0
                
                can_use = used_count < limit
                logger.info(f"🔍 Trial check for {user_id} on {page_name}: {used_count}/{limit} (can_use: {can_use})")
                return can_use, used_count, limit
                
            except Exception as e:
                logger.error(f"Error checking trial limit in Supabase: {e}")
        
        # Fallback to local file storage
        trial_data = self._load_trial_data()
        user_trials = trial_data.get(user_id, {})
        used_count = user_trials.get(page_name, 0)
        
        can_use = used_count < limit
        logger.info(f"🔍 Trial check (local) for {user_id} on {page_name}: {used_count}/{limit} (can_use: {can_use})")
        return can_use, used_count, limit
    
    def consume_trial(self, user_id: str, page_name: str) -> Tuple[bool, int, int]:
        """
        Consume one trial usage for a specific page
        
        Args:
            user_id: User identifier
            page_name: Page name (research_lab, agent_wave, etc.)
            
        Returns:
            Tuple of (success, new_used_count, limit)
        """
        if page_name not in self.TRIAL_LIMITS:
            logger.warning(f"Unknown page name: {page_name}")
            return True, 0, 0  # Allow if page not in trial system
        
        limit = self.TRIAL_LIMITS[page_name]
        
        # Check current usage first
        can_use, used_count, _ = self.check_trial_limit(user_id, page_name)
        
        if not can_use:
            logger.warning(f"🚫 Trial limit exceeded for {user_id} on {page_name}: {used_count}/{limit}")
            return False, used_count, limit
        
        new_used_count = used_count + 1
        
        # Try Supabase first
        if self.supabase:
            try:
                # Check if record exists
                response = self.supabase.table('trial_usage').select('*').eq('user_id', user_id).eq('page_name', page_name).execute()
                
                if response.data:
                    # Update existing record
                    self.supabase.table('trial_usage').update({
                        'usage_count': new_used_count,
                        'last_used': datetime.now().isoformat()
                    }).eq('user_id', user_id).eq('page_name', page_name).execute()
                else:
                    # Create new record
                    self.supabase.table('trial_usage').insert({
                        'user_id': user_id,
                        'page_name': page_name,
                        'usage_count': new_used_count,
                        'first_used': datetime.now().isoformat(),
                        'last_used': datetime.now().isoformat()
                    }).execute()
                
                logger.info(f"✅ Trial consumed for {user_id} on {page_name}: {new_used_count}/{limit}")
                return True, new_used_count, limit
                
            except Exception as e:
                logger.error(f"Error consuming trial in Supabase: {e}")
        
        # Fallback to local file storage
        trial_data = self._load_trial_data()
        
        if user_id not in trial_data:
            trial_data[user_id] = {}
        
        trial_data[user_id][page_name] = new_used_count
        trial_data[user_id][f"{page_name}_last_used"] = datetime.now().isoformat()
        
        self._save_trial_data(trial_data)
        
        logger.info(f"✅ Trial consumed (local) for {user_id} on {page_name}: {new_used_count}/{limit}")
        return True, new_used_count, limit
    
    def get_trial_status(self, user_id: str) -> Dict[str, Dict[str, Any]]:
        """
        Get trial status for all pages for a user
        
        Args:
            user_id: User identifier
            
        Returns:
            Dict with trial status for each page
        """
        status = {}
        
        for page_name in self.TRIAL_LIMITS:
            can_use, used_count, limit = self.check_trial_limit(user_id, page_name)
            status[page_name] = {
                'used': used_count,
                'limit': limit,
                'remaining': max(0, limit - used_count),
                'exhausted': not can_use
            }
        
        return status
    
    def is_trial_exhausted(self, user_id: str, page_name: str) -> bool:
        """
        Check if trial is exhausted for a specific page
        
        Args:
            user_id: User identifier
            page_name: Page name
            
        Returns:
            True if trial is exhausted, False otherwise
        """
        can_use, _, _ = self.check_trial_limit(user_id, page_name)
        return not can_use

# Global instance
trial_service = TrialService()
