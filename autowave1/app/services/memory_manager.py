"""
Memory Management Service for Heroku
Automatically monitors and manages memory usage to prevent crashes
"""

import os
import gc
import psutil
import threading
import time
import logging
from datetime import datetime, timedelta
from flask import current_app

# Set up logging
logger = logging.getLogger(__name__)

class MemoryManager:
    def __init__(self):
        self.memory_threshold = 85  # Percentage threshold for memory cleanup
        self.critical_threshold = 95  # Critical threshold for emergency cleanup
        self.check_interval = 300  # Check every 5 minutes (300 seconds)
        self.cleanup_interval = 3600  # Force cleanup every hour (3600 seconds)
        self.last_cleanup = datetime.now()
        self.is_monitoring = False
        self.monitor_thread = None
        
    def get_memory_usage(self):
        """Get current memory usage percentage"""
        try:
            # Get process memory info
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            # Get system memory info
            system_memory = psutil.virtual_memory()
            
            # Calculate memory usage percentage
            memory_usage_percent = (memory_info.rss / system_memory.total) * 100
            
            return {
                'usage_percent': memory_usage_percent,
                'used_mb': memory_info.rss / (1024 * 1024),
                'available_mb': system_memory.available / (1024 * 1024),
                'total_mb': system_memory.total / (1024 * 1024)
            }
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return None
    
    def cleanup_memory(self, force=False):
        """Perform memory cleanup operations"""
        try:
            logger.info("🧹 Starting memory cleanup...")
            
            # Force garbage collection
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")
            
            # Clear any cached data if available
            try:
                # Clear Flask-Session cache if it exists
                if hasattr(current_app, 'session_interface'):
                    logger.info("Clearing session cache...")
                
                # Clear any other caches
                self._clear_application_caches()
                
            except Exception as e:
                logger.warning(f"Error clearing caches: {e}")
            
            # Update last cleanup time
            self.last_cleanup = datetime.now()
            
            # Get memory usage after cleanup
            memory_info = self.get_memory_usage()
            if memory_info:
                logger.info(f"✅ Memory cleanup completed. Usage: {memory_info['usage_percent']:.1f}% ({memory_info['used_mb']:.1f}MB)")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")
            return False
    
    def _clear_application_caches(self):
        """Clear application-specific caches"""
        try:
            # Clear any global variables or caches
            # This is where you can add specific cache clearing logic
            
            # Example: Clear sessions cache if it exists
            from app.api.agentic_code import sessions
            if len(sessions) > 100:  # Keep only recent sessions
                # Sort by timestamp and keep only the 50 most recent
                sorted_sessions = sorted(sessions.items(), 
                                       key=lambda x: x[1].get('created_at', 0), 
                                       reverse=True)
                sessions.clear()
                for session_id, session_data in sorted_sessions[:50]:
                    sessions[session_id] = session_data
                logger.info(f"Cleared old sessions, kept 50 most recent")
            
        except Exception as e:
            logger.warning(f"Error clearing application caches: {e}")
    
    def monitor_memory(self):
        """Main memory monitoring loop"""
        logger.info("🔍 Memory monitoring started")
        
        while self.is_monitoring:
            try:
                memory_info = self.get_memory_usage()
                
                if memory_info:
                    usage_percent = memory_info['usage_percent']
                    
                    # Log memory status every 30 minutes
                    if int(time.time()) % 1800 == 0:  # Every 30 minutes
                        logger.info(f"📊 Memory usage: {usage_percent:.1f}% ({memory_info['used_mb']:.1f}MB)")
                    
                    # Check if cleanup is needed
                    should_cleanup = False
                    
                    # Force cleanup every hour
                    if datetime.now() - self.last_cleanup > timedelta(seconds=self.cleanup_interval):
                        logger.info("⏰ Scheduled hourly cleanup")
                        should_cleanup = True
                    
                    # Cleanup if memory usage is high
                    elif usage_percent > self.memory_threshold:
                        logger.warning(f"⚠️ High memory usage detected: {usage_percent:.1f}%")
                        should_cleanup = True
                    
                    # Emergency cleanup if critical
                    elif usage_percent > self.critical_threshold:
                        logger.error(f"🚨 Critical memory usage: {usage_percent:.1f}%")
                        should_cleanup = True
                        # Also force more aggressive cleanup
                        self._emergency_cleanup()
                    
                    if should_cleanup:
                        self.cleanup_memory()
                
                # Sleep for the check interval
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def _emergency_cleanup(self):
        """Perform emergency cleanup operations"""
        try:
            logger.warning("🚨 Performing emergency memory cleanup...")
            
            # More aggressive garbage collection
            for _ in range(3):
                gc.collect()
            
            # Clear more caches
            self._clear_application_caches()
            
            # Force Python to release memory back to OS
            try:
                import ctypes
                libc = ctypes.CDLL("libc.so.6")
                libc.malloc_trim(0)
            except:
                pass  # Not available on all systems
                
        except Exception as e:
            logger.error(f"Error in emergency cleanup: {e}")
    
    def start_monitoring(self):
        """Start the memory monitoring thread"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_memory, daemon=True)
            self.monitor_thread.start()
            logger.info("✅ Memory manager started")
    
    def stop_monitoring(self):
        """Stop the memory monitoring thread"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("🛑 Memory manager stopped")
    
    def get_status(self):
        """Get current memory manager status"""
        memory_info = self.get_memory_usage()
        return {
            'is_monitoring': self.is_monitoring,
            'last_cleanup': self.last_cleanup.isoformat(),
            'memory_info': memory_info,
            'thresholds': {
                'cleanup': self.memory_threshold,
                'critical': self.critical_threshold
            }
        }

# Global memory manager instance
memory_manager = MemoryManager()

def init_memory_manager(app):
    """Initialize memory manager with Flask app"""
    with app.app_context():
        memory_manager.start_monitoring()
        logger.info("🚀 Memory manager initialized")

def cleanup_memory_now():
    """Manual memory cleanup function"""
    return memory_manager.cleanup_memory(force=True)
