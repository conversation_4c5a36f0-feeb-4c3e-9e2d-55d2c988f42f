{% extends "layout.html" %}

{#
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
WARNING: THIS IS THE ONLY TEMPLATE THAT SHOULD BE MODIFIED FOR THE PRIME AGENT UI
- Do NOT create new templates
- Do NOT modify any other templates
- All UI changes should be made ONLY to this autowave.html template
- All other templates should be considered archived
!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
#}

{% block title %}Prime Agent - AutoWave{% endblock %}

{% block header %}Prime Agent{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/super_agent.css">
<link rel="stylesheet" href="/static/css/booking_results.css">
<style>
    /* Fixed input container styles */
    .fixed-input-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: transparent;
        padding: 16px;
        z-index: 100;
        margin-left: 16rem; /* Match sidebar width */
        transition: margin-left 0.3s ease;
    }

    .input-wrapper {
        max-width: 1000px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        gap: 10px;
        background-color: transparent;
    }

    /* Style the input and button to stand alone */
    #taskDescription {
        background-color: #1e293b;
        border: 1px solid #4a5568;
        color: #e2e8f0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* File upload styles */
    .file-preview {
        background-color: #1e293b;
        border: 1px solid #4a5568;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
    }

    .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        background-color: #2d3748;
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
        border: 1px solid #4a5568;
    }

    .file-item:last-child {
        margin-bottom: 0;
    }

    .file-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .file-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.75rem;
        color: #60a5fa;
    }

    .file-details {
        flex: 1;
    }

    .file-name {
        font-weight: 500;
        color: #e2e8f0;
        font-size: 0.875rem;
    }

    .file-size {
        color: #9ca3af;
        font-size: 0.75rem;
    }

    .file-remove {
        background: none;
        border: none;
        color: #ef4444;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s;
    }

    .file-remove:hover {
        background-color: #374151;
    }

    .file-image-preview {
        width: 3rem;
        height: 3rem;
        object-fit: cover;
        border-radius: 0.375rem;
        margin-right: 0.75rem;
        border: 1px solid #4a5568;
    }

    #executeTaskBtn {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    @media (max-width: 768px) {
        .fixed-input-container {
            margin-left: 0;
        }

        .input-wrapper {
            margin: 0 auto;
            max-width: 95%;
        }
    }

    /* Adjust main content to prevent overlap with fixed input */
    .content-wrapper {
        padding-bottom: 100px;
    }

    /* Collapsed sidebar adjustment */
    body.collapsed-sidebar .fixed-input-container {
        margin-left: 4rem;
    }

    /* Task Summary toggle styles */
    #taskSummaryContainer.collapsed #summaryContainers {
        display: none;
    }

    #taskSummaryContainer.collapsed {
        min-height: 60px;
    }

    #toggleSummaryBtn svg.rotate-90 {
        transform: rotate(90deg);
    }

    /* Dark scrollbar styles for all elements */
    * {
        scrollbar-width: thin;
        scrollbar-color: rgba(75, 85, 99, 0.5) rgba(17, 24, 39, 0.1);
    }

    /* WebKit browsers (Chrome, Safari, etc.) */
    *::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    *::-webkit-scrollbar-track {
        background: rgba(17, 24, 39, 0.1);
        border-radius: 4px;
    }

    *::-webkit-scrollbar-thumb {
        background-color: rgba(75, 85, 99, 0.5);
        border-radius: 4px;
        border: 2px solid rgba(17, 24, 39, 0.1);
    }

    *::-webkit-scrollbar-thumb:hover {
        background-color: rgba(75, 85, 99, 0.7);
    }

    /* Specific scrollbar styles for task results and thinking process */
    #taskResults, .thinking-process, .summary-container-content, .results-content {
        scrollbar-width: thin;
        scrollbar-color: rgba(75, 85, 99, 0.5) rgba(17, 24, 39, 0.1);
    }

    #taskResults::-webkit-scrollbar,
    .thinking-process::-webkit-scrollbar,
    .summary-container-content::-webkit-scrollbar,
    .results-content::-webkit-scrollbar {
        width: 8px;
    }

    #taskResults::-webkit-scrollbar-track,
    .thinking-process::-webkit-scrollbar-track,
    .summary-container-content::-webkit-scrollbar-track,
    .results-content::-webkit-scrollbar-track {
        background: rgba(17, 24, 39, 0.1);
        border-radius: 4px;
    }

    #taskResults::-webkit-scrollbar-thumb,
    .thinking-process::-webkit-scrollbar-thumb,
    .summary-container-content::-webkit-scrollbar-thumb,
    .results-content::-webkit-scrollbar-thumb {
        background-color: rgba(75, 85, 99, 0.5);
        border-radius: 4px;
        border: 2px solid rgba(17, 24, 39, 0.1);
    }

    #taskResults::-webkit-scrollbar-thumb:hover,
    .thinking-process::-webkit-scrollbar-thumb:hover,
    .summary-container-content::-webkit-scrollbar-thumb:hover,
    .results-content::-webkit-scrollbar-thumb:hover {
        background-color: rgba(75, 85, 99, 0.7);
    }
</style>
{% endblock %}

{% block content %}
<div class="p-6">
    <div class="mb-6 bg-gradient-to-r from-gray-900 to-gray-800 p-4 rounded-lg border-l-4 border-gray-600 shadow-sm">
        <p class="text-gray-300">Prime Agent can browse the web, book travel, generate code, and complete complex tasks - all with enhanced AI capabilities.</p>
    </div>

    <!-- Tabs Navigation -->
    <div class="border-b border-gray-700 mb-6">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-white text-white" data-tab="task">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Task
                </span>
            </button>
            <!-- Code tab removed - use the Code IDE in the sidebar instead -->
            <!--
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-400 hover:text-white hover:border-gray-500" data-tab="code">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    Code
                </span>
            </button>
            -->

            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-400 hover:text-white hover:border-gray-500" data-tab="history">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    History
                </span>
            </button>


        </nav>
    </div>

    <!-- Task Tab -->
    <div class="tab-content content-wrapper" id="task-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Execute a Task</h3>
                <p class="text-gray-600 mb-4">Describe a task for the agent to perform. The agent will break it down into steps and execute them.</p>

                <!-- Hidden Advanced Options - Automatically enabled -->
                <div class="hidden">
                    <input id="useAdvancedBrowser" type="checkbox" checked>
                </div>
            </div>
        </div>

        <!-- Task Progress and Results Container -->
        <div class="mt-8 hidden" id="resultsContainer">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column: Task Progress -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Task Progress</h3>
                        <div class="flex space-x-2">
                            <!-- Clear All Button -->
                            <button id="clearAllTasksBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Clear all tasks">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Progress Bar -->
                        <div class="mb-6" id="taskProgress">
                            <div class="relative pt-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <div id="processingIndicator" class="relative inline-block">
                                            <div class="h-8 w-8 rounded-full bg-gray-700 animate-spin border-4 border-gray-500 border-t-transparent"></div>
                                            <span class="checkmark absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-sm">✓</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-700">
                                    <div class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gray-400 transition-all duration-500 ease-in-out" id="progressFill" style="width: 0%"></div>
                                </div>
                                <p id="stepDescription" class="text-sm text-gray-400">Analyzing your request and preparing execution plan</p>
                            </div>
                        </div>

                        <!-- Thinking Process Container -->
                        <div id="thinkingContainers" class="space-y-6">
                            <!-- Initial thinking process container -->
                            <div class="thinking-container" id="thinking-container-initial">
                                <div class="bg-gray-50 px-4 py-3 rounded-t-md border border-gray-200 flex justify-between items-center">
                                    <div class="flex items-center">
                                        <button class="toggle-thinking-btn p-1 mr-2 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Toggle thinking process" data-container-id="thinking-container-initial">
                                            <svg class="w-4 h-4 transform rotate-0 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </button>
                                        <h4 class="font-medium text-gray-700">Thinking Process</h4>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button class="goto-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Go to summary" data-task-id="summary-container-initial">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="thinking-process bg-gray-50 p-4 rounded-b-md border-l border-r border-b border-gray-200 h-64 overflow-auto">
                                    <div id="thinkingContent" class="prose prose-sm max-w-none text-sm text-gray-500">
                                        <div class="thinking-step">
                                            <p>Waiting for task input...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Task Summary -->
                <div id="taskSummaryContainer" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Task Summary</h3>
                        <div class="flex space-x-2">
                            <!-- Toggle Icon -->
                            <button id="toggleSummaryBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Expand task summary section">
                                <svg class="w-5 h-5 transform rotate-0 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-6 overflow-y-scroll h-[800px]" id="summaryContainers">
                        <!-- Initial summary container -->
                        <div class="summary-container" id="summary-container-initial">
                            <div class="bg-gray-50 px-4 py-3 rounded-t-md border border-gray-200 flex justify-between items-center summary-container-header">
                                <h4 class="font-medium text-gray-700">Task Summary</h4>
                                <div class="flex space-x-1">
                                    <!-- Download Icon -->
                                    <button class="download-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Download summary" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                    </button>
                                    <!-- Copy Icon -->
                                    <button class="copy-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Copy to clipboard" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                        </svg>
                                    </button>
                                    <!-- Share Icon -->
                                    <button class="share-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Share summary" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                                        </svg>
                                    </button>
                                    <!-- Mobile Menu Button (only visible on small screens) -->
                                    <button class="mobile-menu-btn md:hidden p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <!-- Mobile Dropdown Menu -->
                                <div class="mobile-dropdown absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden" data-task-id="summary-container-initial" style="top: 2.5rem;">
                                    <button class="download-summary-btn block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" data-task-id="summary-container-initial">Download</button>
                                    <button class="copy-summary-btn block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" data-task-id="summary-container-initial">Copy to clipboard</button>
                                    <button class="share-summary-btn block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" data-task-id="summary-container-initial">Share</button>
                                </div>
                            </div>
                            <div class="summary-container-content prose prose-img:rounded-lg prose-img:shadow-md prose-img:mx-auto prose-img:max-w-full p-4 border-l border-r border-b border-gray-200 rounded-b-md">
                                <p class="text-center text-gray-500">Task results will appear here</p>
                            </div>
                        </div>
                        <!-- Remove the non-functional container that appears below -->
                        <div id="taskResults" class="hidden"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Browse Tab Removed -->

    <!-- Code Tab - Archived (use the Code IDE in the sidebar instead) -->
    <!--
    <div class="tab-content hidden" id="code-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Build with Code</h3>
                <p class="text-gray-600 mb-4">Describe what you want to build, and the agent will create it for you with live code generation.</p>
                <div class="mb-6">
                    <textarea id="codePrompt" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 min-h-[120px]" placeholder="Example: Create a to-do list app with HTML, CSS, and JavaScript"></textarea>
                </div>

                <div class="flex flex-wrap items-center gap-4 mb-6">
                    <div class="flex items-center">
                        <label for="projectType" class="block text-sm font-medium text-gray-700 mr-2">Project Type:</label>
                        <select id="projectType" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black rounded-md">
                            <option value="web">Web Application</option>
                            <option value="script">Script/Program</option>
                            <option value="api">API/Backend</option>
                            <option value="data">Data Analysis</option>
                            <option value="mobile">Mobile App</option>
                        </select>
                    </div>
                    <div class="flex items-center ml-4">
                        <label for="complexity" class="block text-sm font-medium text-gray-700 mr-2">Complexity:</label>
                        <select id="complexity" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black rounded-md">
                            <option value="simple">Simple</option>
                            <option value="moderate">Moderate</option>
                            <option value="complex">Complex</option>
                        </select>
                    </div>
                </div>

                <button id="buildProjectBtn" class="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md shadow-sm text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    Build Project
                </button>
            </div>
        </div>

        <!-- Code Generation Process -->
        <div class="mt-8 hidden" id="codeGenerationProcess">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column: Code Generation -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                            Code Generation
                        </h3>
                        <div class="flex space-x-2">
                            <button id="copyAllCodeBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Copy All Code">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                            </button>
                            <button id="downloadCodeBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Download Code">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-0 overflow-hidden">
                        <!-- File Tabs -->
                        <div class="bg-gray-100 border-b border-gray-200 overflow-x-auto whitespace-nowrap" id="fileTabs">
                            <!-- Tabs will be dynamically added here -->
                            <button class="px-4 py-2 text-sm font-medium text-gray-700 border-b-2 border-transparent hover:text-black hover:border-gray-300 active-file-tab" data-file="main.js">main.js</button>
                        </div>
                        <!-- Code Editor -->
                        <div class="p-0 relative">
                            <pre id="codeEditor" class="language-javascript text-sm p-4 m-0 h-[500px] overflow-auto bg-gray-50 font-mono"><code id="codeContent">// Code will appear here as it's being generated...</code></pre>
                            <div id="typingIndicator" class="absolute bottom-4 left-4 flex items-center">
                                <div class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Preview -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            Preview
                        </h3>
                        <div class="flex space-x-2">
                            <button id="refreshPreviewBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Refresh Preview">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                            <button id="openPreviewBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Open in New Window">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-0 h-[542px] overflow-hidden">
                        <iframe id="previewFrame" class="w-full h-full border-0" sandbox="allow-scripts allow-same-origin allow-forms" srcdoc="<html><body><div style='display:flex;justify-content:center;align-items:center;height:100vh;font-family:sans-serif;color:#666;'>Preview will appear here when code is ready</div></body></html>"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Files List -->
        <div class="mt-6 hidden" id="projectFilesList">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                        </svg>
                        Project Files
                    </h3>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="filesList">
                        <!-- Files will be dynamically added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    -->



    <!-- History Tab -->
    <div class="tab-content hidden" id="history-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Session History</h3>
                    <button id="clearHistoryBtn" class="inline-flex items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                        <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Clear History
                    </button>
                </div>

                <div id="historyResults" class="text-center text-gray-500">
                    <p>Session history will appear here</p>
                </div>
            </div>
        </div>
    </div>




</div>

<!-- Fixed Input Container for Task Tab -->
<div class="fixed-input-container" id="taskInputContainer" style="display: none;">
    <div class="input-wrapper" style="max-width: 1050px; margin: 0 auto;">
        <div class="relative w-full">
            <textarea id="taskDescription" class="w-full px-4 py-3 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200" rows="2" placeholder="Example: Go to wikipedia.org, search for 'artificial intelligence', and find the section about machine learning"></textarea>

            <!-- File Upload Button -->
            <button id="taskFileUploadBtn" class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-300 transition-colors duration-200 bg-transparent border-0" title="Upload file or image">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
                </svg>
            </button>

            <button id="executeTaskBtn" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-300 transition-colors duration-200 bg-transparent border-0">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
            </button>

            <!-- Hidden File Input -->
            <input type="file" id="taskFileInput" class="hidden" accept="image/*,.txt,.py,.js,.html,.css,.json,.md,.pdf,.doc,.docx" multiple>

            <!-- File Preview -->
            <div id="taskFilePreview" class="file-preview mt-2" style="display: none;">
                <!-- File previews will be added here dynamically -->
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/markdown-it@12.0.6/dist/markdown-it.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="/static/css/task_summary_enhanced.css">
<link rel="stylesheet" href="/static/css/screenshot_styles.css">
<script src="/static/js/markdown_processor.js"></script>
<script src="/static/js/task_container_handlers.js"></script>
<script src="/static/js/simple_input_fixed.js?v=llm-tools-2024"></script>
<!-- Code generator script removed - use the Code IDE in the sidebar instead -->
<!-- <script src="/static/js/code_generator.js"></script> -->
<script src="/static/js/execute_python.js"></script>
<script src="/static/js/history.js"></script>
<script src="/static/js/tab_switcher.js"></script>
<script src="/static/js/modal_handlers.js"></script>
<script src="/static/js/design_task_preview.js"></script>
<script src="/static/js/image_processor.js"></script>
<script src="/static/js/screenshot_handler.js"></script>
<script src="/static/js/caption_cleaner.js"></script>
<script src="/static/js/autowave_thinking_process.js"></script>
<script src="/static/js/task_summary_toggle.js"></script>
<script src="/static/js/super_agent.js"></script>

<script src="/static/js/document_handler.js"></script>
<script src="/static/js/universal_file_upload.js"></script>

<!-- Fixed Input Container Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Fixed Input Container Script loaded');

        // Show/hide fixed input container based on active tab
        const tabButtons = document.querySelectorAll('.tab-button');
        const taskInputContainer = document.getElementById('taskInputContainer');

        console.log('Fixed Input Container elements:');
        console.log('- tabButtons:', tabButtons.length);
        console.log('- taskInputContainer:', !!taskInputContainer);

        // Function to update input container visibility
        function updateInputContainerVisibility() {
            const activeTab = document.querySelector('.tab-button[data-tab="task"]');
            if (activeTab && activeTab.classList.contains('border-white')) {
                if (taskInputContainer) {
                    taskInputContainer.style.display = 'block';
                    console.log('Fixed input container shown by tab script');
                }
            } else {
                if (taskInputContainer) {
                    taskInputContainer.style.display = 'none';
                    console.log('Fixed input container hidden by tab script');
                }
            }
        }

        // Initial check
        updateInputContainerVisibility();

        // Add event listeners to tab buttons
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                setTimeout(updateInputContainerVisibility, 100);
            });
        });
    });
</script>

<!-- Automatic caption removal script (no visible button) -->
<script>
    // Automatically remove captions without showing a button
    document.addEventListener('DOMContentLoaded', function() {
        // Function to remove captions
        function removeCaptions() {
            console.log('Removing all captions');

            // Get all image captions
            const captions = document.querySelectorAll('.image-caption');
            console.log(`Found ${captions.length} captions to remove`);

            // Remove all captions
            captions.forEach(caption => {
                if (caption.parentNode) {
                    caption.parentNode.removeChild(caption);
                }
            });
        }

        // Run automatically after a delay
        setTimeout(function() {
            console.log('Auto-running caption removal');
            removeCaptions();
        }, 1000);

        // And run periodically
        setInterval(function() {
            console.log('Periodic caption removal');
            const captions = document.querySelectorAll('.image-caption');
            if (captions.length > 0) {
                removeCaptions();
            }
        }, 5000);
    });
</script>

<style>
    /* Processing indicator styles */
    #processingIndicator.completed .checkmark {
        display: block;
    }

    #processingIndicator.completed .animate-spin {
        animation: none;
    }

    #processingIndicator .checkmark {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 0.75rem;
    }

    /* Thinking content styles */
    #thinkingContent p {
        margin-bottom: 0.75rem;
        line-height: 1.5;
        animation: fadeIn 0.5s ease-in-out;
    }

    #thinkingContent ul {
        margin-top: 0.5rem;
        margin-bottom: 0.75rem;
        padding-left: 1.5rem;
        list-style-type: disc;
    }

    #thinkingContent li {
        margin-bottom: 0.25rem;
        line-height: 1.5;
    }

    #thinkingContent strong {
        font-weight: 600;
        color: #111827;
    }

    #thinkingContent h1, #thinkingContent h2, #thinkingContent h3, #thinkingContent h4 {
        font-weight: 600;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }

    #thinkingContent code {
        font-family: monospace;
        background-color: #f3f4f6;
        padding: 0.1rem 0.2rem;
        border-radius: 0.25rem;
    }

    /* Enhanced thinking process styles */
    .thinking-step {
        margin-bottom: 10px;
        padding-left: 10px;
        border-left: 3px solid #4B5563;
        animation: fadeIn 0.5s ease-in-out;
    }

    .thinking-step.success {
        color: #10B981;
        font-weight: bold;
        border-left: 3px solid #10B981;
    }

    .thinking-step.search {
        border-left: 3px solid #3B82F6;
    }

    .thinking-step.processing {
        border-left: 3px solid #6366F1;
    }

    .thinking-step.analysis {
        border-left: 3px solid #8B5CF6;
    }

    .thinking-step.insight {
        border-left: 3px solid #F59E0B;
        font-weight: bold;
    }

    /* Task summary styles */
    #taskResults {
        overflow-y: scroll !important;
        height: 800px !important;
        /* Scrollbar styles are now defined globally */
    }

    /* Task Summary toggle styles */
    #toggleSummaryBtn svg.rotate-90 {
        transform: rotate(90deg);
    }

    #toggleSummaryBtn svg.rotate-180 {
        transform: rotate(180deg);
    }

    /* Expanded summary styles */
    #resultsContainer .grid > div {
        transition: all 0.3s ease;
    }

    #resultsContainer.summary-expanded .grid {
        display: block;
    }

    #resultsContainer.summary-expanded #taskSummaryContainer {
        width: 100%;
        transition: all 0.3s ease;
    }

    /* Adjust the height of the summary container when expanded */
    #resultsContainer.summary-expanded #summaryContainers {
        height: calc(100vh - 250px) !important;
        max-height: 1000px;
    }

    #taskResults .task-summary {
        overflow: visible;
    }

    #taskResults img {
        max-width: 100%;
        height: auto;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin: 1rem auto;
        display: block;
    }

    /* Screenshot container styles */
    .screenshot-container {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1.5rem 0;
    }

    .screenshot-container img {
        border: 1px solid #e5e7eb;
        max-height: 500px;
        object-fit: contain;
    }

    .screenshot-zoom-btn, .screenshot-download-btn {
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: all 0.2s;
    }

    .screenshot-zoom-btn:hover, .screenshot-download-btn:hover {
        background-color: #f3f4f6;
    }

    /* Multiple container styles */
    .thinking-container, .summary-container {
        margin-bottom: 1.5rem;
        position: relative;
        background-color: white;
        border-radius: 0.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .thinking-container:last-child, .summary-container:last-child {
        margin-bottom: 0;
    }

    .thinking-container:hover, .summary-container:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .summary-container-header {
        position: relative;
    }

    .mobile-dropdown {
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 10;
    }

    .highlight-container {
        animation: highlight-pulse 2s ease-in-out;
    }

    @keyframes highlight-pulse {
        0%, 100% {
            box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
        }
        50% {
            box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2);
        }
    }

    /* Enhanced Task Summary Text Styling */
    .summary-container-content {
        font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: #333;
        letter-spacing: 0.01em;
    }

    .summary-container-content h1 {
        font-family: 'Outfit', sans-serif;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.25rem;
        margin-top: 1.75rem;
        line-height: 1.2;
        color: #111;
        letter-spacing: -0.02em;
        border-bottom: none;
    }

    .summary-container-content h2 {
        font-family: 'Outfit', sans-serif;
        font-size: 1.4rem;
        font-weight: 600;
        margin-top: 1.75rem;
        margin-bottom: 1rem;
        color: #222;
        letter-spacing: -0.01em;
        border-bottom: none;
    }

    .summary-container-content h3 {
        font-family: 'Outfit', sans-serif;
        font-size: 1.2rem;
        font-weight: 500;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
        color: #333;
        letter-spacing: -0.01em;
    }

    .summary-container-content p {
        margin-bottom: 1.25rem;
        line-height: 1.6;
        font-size: 1rem;
        color: #444;
    }

    .summary-container-content strong,
    .summary-container-content b {
        font-weight: 600;
        color: #222;
    }

    .summary-container-content ul,
    .summary-container-content ol {
        margin-bottom: 1.25rem;
        padding-left: 1.25rem;
    }

    .summary-container-content li {
        margin-bottom: 0.5rem;
        line-height: 1.6;
        font-size: 1rem;
        position: relative;
    }

    .summary-container-content ul li {
        list-style-type: none;
        padding-left: 0.5rem;
    }

    .summary-container-content ul li::before {
        content: '•';
        position: absolute;
        left: -1rem;
        color: #555;
        font-weight: bold;
    }

    .summary-container-content ol li {
        list-style-type: decimal;
        padding-left: 0.25rem;
    }

    /* Links */
    .summary-container-content a {
        color: #0066cc;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(0, 102, 204, 0.2);
        padding-bottom: 1px;
    }

    .summary-container-content a:hover {
        color: #004499;
        border-bottom: 1px solid rgba(0, 68, 153, 0.6);
    }

    /* Code blocks */
    .summary-container-content pre {
        background-color: #f5f5f5;
        padding: 1rem;
        border-radius: 0.5rem;
        overflow-x: auto;
        margin-bottom: 1.25rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .summary-container-content code {
        font-family: 'SF Mono', 'Consolas', 'Monaco', 'Andale Mono', monospace;
        font-size: 0.9rem;
        color: #333;
    }

    /* Inline code */
    .summary-container-content p code,
    .summary-container-content li code {
        background-color: #f0f0f0;
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
        font-size: 0.9rem;
        color: #333;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    /* Dark mode support */
    [data-theme="dark"] .summary-container-content {
        color: #e0e0e0;
    }

    [data-theme="dark"] .summary-container-content h1 {
        color: #f0f0f0;
    }

    [data-theme="dark"] .summary-container-content h2 {
        color: #e0e0e0;
    }

    [data-theme="dark"] .summary-container-content h3 {
        color: #d0d0d0;
    }

    [data-theme="dark"] .summary-container-content p {
        color: #c0c0c0;
    }

    [data-theme="dark"] .summary-container-content strong,
    [data-theme="dark"] .summary-container-content b {
        color: #f0f0f0;
    }

    [data-theme="dark"] .summary-container-content a {
        color: #4d9fff;
        border-bottom: 1px solid rgba(77, 159, 255, 0.2);
    }

    [data-theme="dark"] .summary-container-content a:hover {
        color: #77b7ff;
        border-bottom: 1px solid rgba(119, 183, 255, 0.6);
    }

    [data-theme="dark"] .summary-container-content pre {
        background-color: #2a2a2a;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] .summary-container-content code {
        color: #e0e0e0;
    }

    [data-theme="dark"] .summary-container-content p code,
    [data-theme="dark"] .summary-container-content li code {
        background-color: #2a2a2a;
        color: #e0e0e0;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Legacy styles for backward compatibility */
    #taskResults h1, #taskResults h2, #taskResults h3, #taskResults h4 {
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    #taskResults p {
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    #taskResults ul, #taskResults ol {
        margin-top: 0.5rem;
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    #taskResults li {
        margin-bottom: 0.5rem;
    }

    .typing-cursor::after {
        content: '|';
        display: inline-block;
        animation: blink 1s step-end infinite;
    }

    @keyframes blink {
        from, to { opacity: 1; }
        50% { opacity: 0; }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
    }

    .typing-cursor {
        display: inline-block;
        width: 2px;
        height: 1em;
        background-color: #4B5563;
        margin-left: 2px;
        animation: blink 1s infinite;
        vertical-align: middle;
    }

    /* Animation classes */
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    /* Modal animation */
    #summaryModal {
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    }

    #summaryModal.hidden {
        opacity: 0;
        transform: scale(0.95);
        pointer-events: none;
    }

    #summaryModal:not(.hidden) {
        opacity: 1;
        transform: scale(1);
    }
</style>


<!-- Full Screen Modal for Task Summary -->
<div id="summaryModal" class="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center hidden">
    <div style="background-color: #1a202c;" class="rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[90vh] flex flex-col border border-gray-800">
        <div style="background-color: #1a202c;" class="px-6 py-4 border-b border-gray-800 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-white">Task Summary</h3>
            <button id="closeSummaryModal" class="p-1 rounded-md text-gray-400 hover:text-white hover:bg-gray-800" onclick="document.getElementById('summaryModal').classList.add('hidden'); console.log('Close button clicked directly');">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="flex-1 p-6 overflow-auto text-gray-200" id="modalTaskResults" style="background-color: #2d3748 !important;">
            <div class="prose prose-invert max-w-none task-summary dark-mode-content" style="overflow: visible;"></div>
        </div>
    </div>
</div>

<style>
    /* Dark mode styles for modal content */
    .dark-mode-content {
        font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        color: #e5e7eb !important; /* Light gray text */
        letter-spacing: 0.01em;
    }

    /* Modal scrollbar styles */
    #modalTaskResults {
        scrollbar-width: thin;
        scrollbar-color: rgba(75, 85, 99, 0.5) rgba(17, 24, 39, 0.1);
    }

    #modalTaskResults::-webkit-scrollbar {
        width: 8px;
    }

    #modalTaskResults::-webkit-scrollbar-track {
        background: rgba(17, 24, 39, 0.1);
        border-radius: 4px;
    }

    #modalTaskResults::-webkit-scrollbar-thumb {
        background-color: rgba(75, 85, 99, 0.5);
        border-radius: 4px;
        border: 2px solid rgba(17, 24, 39, 0.1);
    }

    #modalTaskResults::-webkit-scrollbar-thumb:hover {
        background-color: rgba(75, 85, 99, 0.7);
    }

    .dark-mode-content h1,
    .dark-mode-content h2,
    .dark-mode-content h3,
    .dark-mode-content h4,
    .dark-mode-content h5,
    .dark-mode-content h6 {
        font-family: 'Outfit', sans-serif;
        color: #f3f4f6 !important; /* Lighter gray for headings */
        letter-spacing: -0.01em;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .dark-mode-content h1 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.25rem;
        margin-top: 1.75rem;
        line-height: 1.2;
        letter-spacing: -0.02em;
    }

    .dark-mode-content h2 {
        font-size: 1.4rem;
        margin-top: 1.75rem;
    }

    .dark-mode-content h3 {
        font-size: 1.2rem;
        font-weight: 500;
        margin-top: 1.5rem;
    }

    .dark-mode-content p,
    .dark-mode-content li,
    .dark-mode-content blockquote {
        color: #d1d5db !important; /* Medium gray for paragraphs */
        margin-bottom: 1.25rem;
        line-height: 1.6;
    }

    .dark-mode-content a {
        color: #93c5fd !important; /* Light blue for links */
    }

    .dark-mode-content strong {
        color: #f9fafb !important; /* Almost white for bold text */
        font-weight: 600;
    }

    .dark-mode-content code {
        background-color: #1f2937 !important; /* Dark gray for code background */
        color: #e5e7eb !important; /* Light gray for code text */
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
    }

    .dark-mode-content pre {
        background-color: #1f2937 !important; /* Dark gray for pre background */
        border: 1px solid #374151 !important; /* Border for pre blocks */
    }

    .dark-mode-content pre code {
        background-color: transparent !important;
    }

    .dark-mode-content blockquote {
        border-left-color: #4b5563 !important; /* Medium gray for blockquote border */
        background-color: #1f2937 !important; /* Dark gray for blockquote background */
        padding: 1rem;
    }

    .dark-mode-content table {
        border-color: #4b5563 !important; /* Medium gray for table borders */
    }

    .dark-mode-content th {
        background-color: #1f2937 !important; /* Dark gray for table header */
        color: #f3f4f6 !important; /* Light gray for table header text */
    }

    .dark-mode-content td {
        background-color: #111827 !important; /* Very dark gray for table cells */
        color: #d1d5db !important; /* Medium gray for table cell text */
    }

    .dark-mode-content hr {
        border-color: #4b5563 !important; /* Medium gray for horizontal rules */
    }

    /* Fix any white backgrounds that might be in the content */
    .dark-mode-content * {
        background-color: transparent !important;
    }

    .dark-mode-content .summary-container,
    .dark-mode-content .summary-container-content,
    .dark-mode-content .summary-container-header {
        background-color: #2d3748 !important; /* Research Lab content background */
        border-color: #4a5568 !important; /* Research Lab border color */
    }

    .dark-mode-content .summary-container h4 {
        color: #f3f4f6 !important; /* Light gray for container headers */
    }

    /* Additional styles for dark summary sections */
    .dark-summary-section {
        margin-bottom: 2rem;
        padding: 0;
        background-color: #2d3748 !important;
        border-radius: 0.5rem;
        border: 1px solid #4a5568;
        overflow: hidden;
    }

    .dark-summary-section .section-header {
        background-color: #1a202c !important;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #4a5568;
    }

    .dark-summary-section .section-header h2 {
        color: #f3f4f6 !important;
        font-weight: bold;
        margin: 0;
        font-size: 1.125rem;
    }

    .dark-summary-section .section-content {
        padding: 1rem;
    }

    .dark-summary-content {
        color: #e5e7eb !important;
    }

    .dark-summary-content p {
        color: #d1d5db !important;
        margin-bottom: 1rem;
    }

    .dark-summary-content a {
        color: #93c5fd !important;
        text-decoration: underline;
    }

    .dark-summary-content strong {
        color: #f9fafb !important;
        font-weight: 600;
    }

    .dark-summary-content ul,
    .dark-summary-content ol {
        color: #d1d5db !important;
        margin-left: 1.5rem;
        margin-bottom: 1rem;
    }

    .dark-summary-content li {
        color: #d1d5db !important;
        margin-bottom: 0.5rem;
    }

    .dark-summary-content h1,
    .dark-summary-content h2,
    .dark-summary-content h3,
    .dark-summary-content h4,
    .dark-summary-content h5,
    .dark-summary-content h6 {
        color: #f3f4f6 !important;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .dark-summary-content code {
        background-color: #1f2937 !important;
        color: #e5e7eb !important;
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
    }

    .dark-summary-content pre {
        background-color: #1f2937 !important;
        border: 1px solid #374151 !important;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        overflow-x: auto;
    }

    .dark-summary-content img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }

    /* Override any white backgrounds or black text */
    #summaryModal * {
        background-color: transparent;
    }

    #summaryModal .bg-white,
    #summaryModal .bg-gray-50,
    #summaryModal .bg-gray-100,
    #summaryModal .bg-gray-200 {
        background-color: #2d3748 !important;
    }

    #summaryModal .text-black,
    #summaryModal .text-gray-900,
    #summaryModal .text-gray-800 {
        color: #e5e7eb !important;
    }

    /* Fix for white glitch in the middle */
    #summaryModal .task-summary > * {
        background-color: #2d3748 !important;
    }

    #summaryModal .task-summary > .dark-summary-section .section-header {
        background-color: #1a202c !important;
        border-bottom: 1px solid #4a5568;
    }

    #summaryModal .task-summary > .dark-summary-section .section-content {
        background-color: #2d3748 !important;
    }

    /* Ensure no white backgrounds in the content */
    #summaryModal .dark-summary-content * {
        background-color: #2d3748 !important;
    }

    #summaryModal .dark-summary-content p,
    #summaryModal .dark-summary-content div,
    #summaryModal .dark-summary-content span,
    #summaryModal .dark-summary-content li,
    #summaryModal .dark-summary-content a {
        background-color: #2d3748 !important;
    }

    /* Additional fixes for specific elements */
    #summaryModal .task-summary li,
    #summaryModal .task-summary p,
    #summaryModal .task-summary a,
    #summaryModal .task-summary span {
        background-color: #2d3748 !important;
    }

    /* Fix for links and text with white backgrounds */
    #summaryModal a[href],
    #summaryModal a[href] * {
        background-color: #2d3748 !important;
    }

    /* Fix for inline styles */
    #summaryModal [style*="background"] {
        background-color: #2d3748 !important;
        background: #2d3748 !important;
    }

    /* Clean, classic styling for the modal content */
    #summaryModal .task-summary {
        font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.6;
        letter-spacing: 0.015em;
    }

    #summaryModal .task-summary h1,
    #summaryModal .task-summary h2,
    #summaryModal .task-summary h3 {
        font-weight: 600;
        letter-spacing: -0.02em;
        margin-top: 1.5em;
        margin-bottom: 0.75em;
    }

    #summaryModal .task-summary p {
        margin-bottom: 1.2em;
    }

    #summaryModal .task-summary ul,
    #summaryModal .task-summary ol {
        margin-bottom: 1.2em;
        padding-left: 1.5em;
    }

    #summaryModal .task-summary li {
        margin-bottom: 0.5em;
    }

    #summaryModal .task-summary a {
        text-decoration: none;
        border-bottom: 1px solid rgba(147, 197, 253, 0.5);
        transition: border-color 0.2s ease;
    }

    #summaryModal .task-summary a:hover {
        border-color: rgba(147, 197, 253, 1);
    }

    /* Fix for white glitch at the top of the modal */
    #summaryModal::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #1a202c;
        z-index: 10;
    }

    /* Additional fix for the white line */
    #summaryModal::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #000000;
        z-index: 9;
    }

    #modalTaskResults::before {
        content: none !important;
    }

    /* Ensure the modal content has no white edges */
    #summaryModal .task-summary {
        position: relative;
        margin-top: -1px;
        padding-top: 1px;
    }
</style>

{% endblock %}
