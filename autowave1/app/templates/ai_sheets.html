{% extends "layout.html" %}

{% block title %}AI Sheets - Interactive Spreadsheet Generator{% endblock %}

{% block header %}AI Sheets{% endblock %}

{% block extra_css %}
<!-- Tailwind CSS CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    darkMode: 'class',
    theme: {
      extend: {
        colors: {
          dark: {
            100: '#e0e0e0',
            200: '#a0a0a0',
            300: '#717171',
            400: '#4a4a4a',
            500: '#2d2d2d',
            600: '#1e1e1e',
            700: '#1a1a1a',
            800: '#121212',
            900: '#0a0a0a',
          },
          primary: {
            500: '#4299e1',
            600: '#3182ce',
          },
        }
      }
    }
  }
</script>
{% endblock %}

{% block content %}
<style>
    body {
        background-color: #121212;
        color: #e0e0e0;
    }

    /* AI Sheets specific layout - completely static with overlay sidebar */
    .ai-sheets-container {
        margin-left: 0; /* No margin - full width */
        min-height: 100vh;
        display: flex;
        flex-direction: row;
        position: relative;
        width: 100vw; /* Full viewport width */
    }

    /* Remove sidebar adjustment - page stays static */
    body.collapsed-sidebar .ai-sheets-container {
        margin-left: 0; /* Keep static - no movement */
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .ai-sheets-container {
            margin-left: 0 !important;
        }
    }

    /* Conversation sidebar - positioned within the main content area */
    .conversation-sidebar {
        width: 300px;
        height: calc(100vh - 70px);
        background-color: #121212;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #333;
        overflow: hidden;
        flex-shrink: 0;
        position: relative;
        z-index: 10;
    }

    .conversation-sidebar.collapsed {
        width: 0;
        overflow: hidden;
        border-right: none;
    }

    /* New Session Button */
    .new-session-btn {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        margin: 15px;
        margin-bottom: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(66, 153, 225, 0.2);
    }

    .new-session-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
    }



    .conversation-container {
        overflow-y: auto;
        flex: 1;
        padding: 15px;
        padding-bottom: 0;
        scrollbar-color: #333 #1a1a1a;
        scrollbar-width: thin;
    }

    /* AI Reasoning styling */
    .reasoning-step {
        background-color: #1a1a1a;
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 12px;
        border: 1px solid #333;
        border-left: 3px solid #4299e1;
        opacity: 0;
        transform: translateY(10px);
        animation: slideIn 0.3s ease forwards;
    }

    @keyframes slideIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .conversation-footer {
        border-top: 1px solid #121212; /* Match background color to hide the line */
        width: 100%;
        padding: 15px;
        background-color: #121212;
        margin-top: auto;
    }

    .conversation-input-container {
        position: relative;
        display: flex;
        align-items: flex-end;
        gap: 8px;
    }

    /* Conversation Input Styling */
    .conversation-input {
        width: 100%;
        padding: 12px 15px;
        padding-right: 50px;
        border: 1px solid #333;
        border-radius: 8px;
        font-size: 14px;
        background-color: #1a1a1a;
        color: #e2e8f0;
        resize: none;
        min-height: 80px;
        max-height: 120px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.4;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        box-sizing: border-box;
    }

    .conversation-input:focus {
        outline: none;
        border-color: #4299e1;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }

    .conversation-input::placeholder {
        color: #6b7280;
        font-size: 13px;
    }

    .conversation-send-btn {
        position: absolute;
        bottom: 12px;
        right: 12px;
        background: transparent;
        color: #4299e1;
        border: none;
        border-radius: 50%;
        padding: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        z-index: 1;
    }

    .conversation-send-btn:hover {
        color: #3182ce;
        background: rgba(66, 153, 225, 0.1);
        transform: scale(1.1);
    }

    .conversation-send-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    /* Main content area - positioned within the container */
    .fixed-preview-container {
        flex: 1;
        height: calc(100vh - 70px);
        padding: 20px; /* Static padding - no changes on sidebar toggle */
        display: flex;
        flex-direction: column;
        position: relative;
        /* Remove transition to make it static */
        margin-left: 0; /* Ensure no margin */
    }

    /* Remove the sidebar-collapsed class effect to keep canvas static */
    .fixed-preview-container.sidebar-collapsed {
        /* No changes - keep canvas static */
    }

    /* Sheets Canvas styling */
    .sheets-canvas {
        background-color: white;
        border-radius: 12px;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }

    .sheets-header {
        background-color: #f8fafc;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .sheets-header .icon {
        color: #6b7280;
        font-size: 18px;
    }

    .sheets-header .title {
        color: #374151;
        font-weight: 500;
        font-size: 14px;
    }

    .sheets-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fafafa;
        color: #6b7280;
        text-align: center;
        padding: 40px;
        overflow-y: auto;
        overflow-x: auto;
        /* Dark scrollbar styles like AutoWave */
        scrollbar-width: thin;
        scrollbar-color: rgba(75, 85, 99, 0.5) rgba(17, 24, 39, 0.1);
    }

    /* WebKit browsers (Chrome, Safari, etc.) scrollbar styling */
    .sheets-content::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .sheets-content::-webkit-scrollbar-track {
        background: rgba(17, 24, 39, 0.1);
        border-radius: 4px;
    }

    .sheets-content::-webkit-scrollbar-thumb {
        background: rgba(75, 85, 99, 0.5);
        border-radius: 4px;
    }

    .sheets-content::-webkit-scrollbar-thumb:hover {
        background: rgba(75, 85, 99, 0.7);
    }

    /* Spreadsheet table styling */
    .spreadsheet-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin: 0;
    }

    .spreadsheet-table th,
    .spreadsheet-table td {
        border: 1px solid #e5e7eb;
        padding: 8px 12px;
        text-align: left;
        font-size: 14px;
        color: #374151;
    }

    .spreadsheet-table th {
        background-color: #f9fafb;
        font-weight: 600;
        color: #111827;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .spreadsheet-table tr:nth-child(even) {
        background-color: #f9fafb;
    }

    .spreadsheet-table tr:hover {
        background-color: #f3f4f6;
    }

    .spreadsheet-table td[contenteditable="true"] {
        cursor: text;
        transition: background-color 0.2s ease;
    }

    .spreadsheet-table td[contenteditable="true"]:focus {
        background-color: #dbeafe;
        outline: 2px solid #3b82f6;
        outline-offset: -2px;
    }

    .spreadsheet-table td[contenteditable="true"]:hover {
        background-color: #f0f9ff;
    }

    .sheets-empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
    }

    .sheets-empty-state .icon {
        font-size: 48px;
        color: #d1d5db;
    }

    .sheets-empty-state .title {
        font-size: 16px;
        font-weight: 500;
        color: #374151;
    }

    .sheets-empty-state .subtitle {
        font-size: 14px;
        color: #6b7280;
    }

    /* Export buttons */
    .export-buttons {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        gap: 8px;
        z-index: 10;
    }

    .export-btn {
        padding: 8px 12px;
        background-color: #4299e1;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .export-btn:hover {
        background-color: #3182ce;
        transform: translateY(-1px);
    }

    /* Spreadsheet styling */
    .spreadsheet-container {
        display: none;
        flex-direction: column;
        height: 100%;
        background-color: white;
    }

    .spreadsheet-header {
        padding: 20px;
        border-bottom: 1px solid #e5e7eb;
        background-color: #f8fafc;
    }

    .spreadsheet-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 4px;
    }

    .spreadsheet-description {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .spreadsheet-table-container {
        flex: 1;
        overflow: auto;
        background-color: white;
    }

    .spreadsheet-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }

    .spreadsheet-table th {
        background-color: #f3f4f6;
        color: #374151;
        padding: 12px 8px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #e5e7eb;
        border-right: 1px solid #e5e7eb;
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .spreadsheet-table td {
        padding: 10px 8px;
        border-bottom: 1px solid #e5e7eb;
        border-right: 1px solid #e5e7eb;
        color: #374151;
        background-color: white;
    }

    .spreadsheet-table tr:hover td {
        background-color: #f9fafb;
    }

    .spreadsheet-table td input {
        width: 100%;
        background: transparent;
        border: none;
        color: inherit;
        font-size: inherit;
        padding: 4px;
        border-radius: 4px;
    }

    .spreadsheet-table td input:focus {
        outline: none;
        background-color: #eff6ff;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    }

    /* Loading state */
    .loading-state {
        display: none;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #4299e1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #374151;
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .loading-subtext {
        color: #6b7280;
        font-size: 0.9rem;
    }

    /* Sidebar toggle button positioned between canvas and reasoning */
    .sidebar-toggle {
        position: absolute;
        left: 300px; /* Position at the edge of the sidebar */
        top: 50%;
        transform: translateY(-50%);
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 0 6px 6px 0;
        padding: 8px 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 20;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 48px;
        color: #888;
    }

    .sidebar-toggle:hover {
        background: #2a2a2a;
        color: #ccc;
        border-color: #555;
    }

    .sidebar-toggle.collapsed {
        left: 0; /* Move to the left edge when sidebar is collapsed */
        border-radius: 0 6px 6px 0;
    }

    /* Mobile responsive adjustments */
    @media (max-width: 768px) {
        .ai-sheets-container {
            flex-direction: column;
        }

        .conversation-sidebar {
            width: 100%;
            height: 200px;
            border-right: none;
            border-bottom: 1px solid #333;
        }

        .fixed-preview-container {
            padding: 10px !important;
        }

        .sidebar-toggle {
            display: none; /* Hide toggle on mobile */
        }

        /* Input container adjustments */
        .input-container {
            padding: 12px;
            margin: 8px;
        }

        .input-container textarea {
            font-size: 16px !important;
            padding: 12px;
            min-height: 80px;
        }

        /* Button adjustments */
        .send-button {
            padding: 8px 12px;
            font-size: 14px;
        }

        /* Spreadsheet container */
        .spreadsheet-container {
            padding: 8px;
            overflow-x: auto;
        }

        /* Table adjustments */
        table {
            font-size: 12px;
            min-width: 600px; /* Ensure horizontal scroll on small screens */
        }

        th, td {
            padding: 4px 6px;
            min-width: 80px;
        }

        /* Reasoning panel */
        .reasoning-panel {
            padding: 12px;
            font-size: 14px;
        }

        .reasoning-content {
            font-size: 13px;
            line-height: 1.4;
        }

        /* Action buttons */
        .action-buttons {
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
        }

        .action-buttons button {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        /* Download buttons */
        .download-buttons {
            flex-direction: column;
            gap: 8px;
        }

        .download-buttons button {
            width: 100%;
            padding: 8px;
            font-size: 14px;
        }

        /* Loading states */
        .loading-spinner {
            width: 20px;
            height: 20px;
        }
    }

    @media (max-width: 480px) {
        /* Extra small screens */
        .fixed-preview-container {
            padding: 5px !important;
        }

        .input-container {
            padding: 8px;
            margin: 4px;
        }

        .input-container textarea {
            font-size: 14px !important;
            padding: 10px;
        }

        table {
            font-size: 11px;
            min-width: 500px;
        }

        th, td {
            padding: 3px 4px;
            min-width: 60px;
        }

        .reasoning-panel {
            padding: 8px;
            font-size: 13px;
        }

        .reasoning-content {
            font-size: 12px;
        }

        .action-buttons button {
            padding: 4px 8px;
            font-size: 11px;
        }

        .download-buttons button {
            padding: 6px;
            font-size: 12px;
        }

        .conversation-sidebar {
            height: 150px;
        }
    }
</style>

<!-- AI Sheets Container -->
<div class="ai-sheets-container">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle" title="Toggle Reasoning Panel">
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M15 18l-6-6 6-6"/>
        </svg>
    </button>

    <!-- Conversation Sidebar -->
    <div class="conversation-sidebar" id="conversationSidebar">
        <!-- Sidebar Header -->
        <div style="display: flex; justify-content: center; align-items: center; padding: 15px; border-bottom: 1px solid #121212;"> <!-- Match background color to hide the line -->
            <button class="new-session-btn" id="newSessionBtn">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 5v14m-7-7h14"/>
                </svg>
                New Session
            </button>
        </div>

    <!-- Conversation Container -->
    <div class="conversation-container" id="conversationContainer">
        <!-- AI Reasoning steps will appear here -->
    </div>

    <!-- Input Footer -->
    <div class="conversation-footer">
        <div class="conversation-input-container">
            <textarea
                id="conversationInput"
                class="conversation-input"
                placeholder="Describe the spreadsheet you want to create..."
                rows="3"></textarea>
            <button class="conversation-send-btn" id="conversationSendBtn">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
                </svg>
            </button>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="fixed-preview-container" id="previewContainer">
    <!-- Export Buttons -->
    <div class="export-buttons">
        <button class="export-btn" id="exportCsvBtn">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
            </svg>
            Export CSV
        </button>
        <button class="export-btn" id="exportExcelBtn">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="10" y1="11" x2="14" y2="17"/>
                <line x1="14" y1="11" x2="10" y2="17"/>
            </svg>
            Export Excel
        </button>
        <button class="export-btn" id="exportJsonBtn">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="16 18 22 12 16 6"/>
                <polyline points="8 6 2 12 8 18"/>
            </svg>
            Export JSON
        </button>
    </div>

    <!-- Sheets Canvas -->
    <div class="sheets-canvas" id="sheetsCanvas">
        <!-- Header -->
        <div class="sheets-header">
            <div class="icon">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="7" height="7"/>
                    <rect x="14" y="3" width="7" height="7"/>
                    <rect x="14" y="14" width="7" height="7"/>
                    <rect x="3" y="14" width="7" height="7"/>
                </svg>
            </div>
            <div class="title">Sheets Canvas</div>
        </div>

        <!-- Content -->
        <div class="sheets-content" id="sheetsContent">
            <!-- Empty State -->
            <div class="sheets-empty-state" id="emptyState">
                <div class="icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <rect x="3" y="3" width="7" height="7"/>
                        <rect x="14" y="3" width="7" height="7"/>
                        <rect x="14" y="14" width="7" height="7"/>
                        <rect x="3" y="14" width="7" height="7"/>
                    </svg>
                </div>
                <div class="title">Preview of Your Sheets</div>
                <div class="subtitle">Your AI-generated spreadsheet will appear here</div>
            </div>

            <!-- Loading State -->
            <div class="loading-state" id="loadingState">
                <div class="loading-spinner"></div>
                <div class="loading-text">Generating your spreadsheet...</div>
                <div class="loading-subtext">AI is analyzing your request and creating the perfect sheet</div>
            </div>

            <!-- Spreadsheet Container -->
            <div class="spreadsheet-container" id="spreadsheetContainer">
                <div class="spreadsheet-header">
                    <div class="spreadsheet-title" id="spreadsheetTitle">Spreadsheet Title</div>
                    <div class="spreadsheet-description" id="spreadsheetDescription">Description will appear here</div>
                </div>
                <div class="spreadsheet-table-container">
                    <table class="spreadsheet-table" id="spreadsheetTable">
                        <!-- Table content will be generated dynamically -->
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div> <!-- Close ai-sheets-container -->

{% endblock %}

{% block extra_js %}
<script>
class AISheets {
    constructor() {
        this.currentSession = null;
        this.isGenerating = false;
        this.sidebarCollapsed = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupMainSidebarIntegration();
        this.handleURLParameters();
    }

    bindEvents() {
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleReasoningSidebar();
        });

        // New session
        document.getElementById('newSessionBtn').addEventListener('click', () => {
            this.newSession();
        });

        // Send button and Enter key
        document.getElementById('conversationSendBtn').addEventListener('click', () => {
            this.generateSpreadsheet();
        });

        document.getElementById('conversationInput').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.generateSpreadsheet();
            }
        });

        // Export buttons
        document.getElementById('exportCsvBtn').addEventListener('click', () => {
            this.exportData('csv');
        });

        document.getElementById('exportExcelBtn').addEventListener('click', () => {
            this.exportData('excel');
        });

        document.getElementById('exportJsonBtn').addEventListener('click', () => {
            this.exportData('json');
        });
    }

    setupMainSidebarIntegration() {
        // Auto-resize textarea
        const textarea = document.getElementById('conversationInput');
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Listen for main sidebar changes
        this.observeMainSidebar();
    }

    observeMainSidebar() {
        // Watch for changes to the main sidebar state
        const mainSidebar = document.getElementById('sidebar');
        const collapsedSidebar = document.getElementById('collapsed-sidebar');
        const aiSheetsContainer = document.querySelector('.ai-sheets-container');

        if (!mainSidebar || !collapsedSidebar || !aiSheetsContainer) return;

        // Create a mutation observer to watch for sidebar changes
        const observer = new MutationObserver(() => {
            const isCollapsed = mainSidebar.style.display === 'none';
            if (isCollapsed) {
                document.body.classList.add('collapsed-sidebar');
            } else {
                document.body.classList.remove('collapsed-sidebar');
            }
        });

        // Observe changes to the main sidebar
        observer.observe(mainSidebar, {
            attributes: true,
            attributeFilter: ['style']
        });

        // Initial state check
        const isCollapsed = mainSidebar.style.display === 'none';
        if (isCollapsed) {
            document.body.classList.add('collapsed-sidebar');
        }
    }

    toggleReasoningSidebar() {
        const sidebar = document.getElementById('conversationSidebar');
        const toggleBtn = document.getElementById('sidebarToggle');
        const previewContainer = document.getElementById('previewContainer');

        sidebar.classList.toggle('collapsed');
        toggleBtn.classList.toggle('collapsed');

        // Update toggle button icon and position
        const icon = toggleBtn.querySelector('svg path');
        if (sidebar.classList.contains('collapsed')) {
            icon.setAttribute('d', 'M9 18l6-6-6-6'); // Right arrow
            // Expand canvas when reasoning sidebar is collapsed
            if (previewContainer) {
                previewContainer.style.width = '100%';
            }
        } else {
            icon.setAttribute('d', 'M15 18l-6-6 6-6'); // Left arrow
            // Restore canvas width when reasoning sidebar is expanded
            if (previewContainer) {
                previewContainer.style.width = '70%';
            }
        }
    }

    newSession() {
        // Clear conversation
        document.getElementById('conversationContainer').innerHTML = '';
        document.getElementById('conversationInput').value = '';

        // Reset to empty state
        this.showEmptyState();

        // Focus input
        document.getElementById('conversationInput').focus();

        this.currentSession = null;
    }

    handleURLParameters() {
        // Check for task parameter from homepage
        const urlParams = new URLSearchParams(window.location.search);
        const task = urlParams.get('task');

        if (task) {
            // Set the input value
            const input = document.getElementById('conversationInput');
            if (input) {
                input.value = task;
                // Automatically generate the spreadsheet
                setTimeout(() => {
                    this.generateSpreadsheet();
                }, 500); // Small delay to ensure UI is ready
            }

            // Clean up URL
            const url = new URL(window.location);
            url.searchParams.delete('task');
            window.history.replaceState({}, document.title, url.toString());
        }
    }

    showEmptyState() {
        document.getElementById('emptyState').style.display = 'flex';
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('spreadsheetContainer').style.display = 'none';
    }

    showLoadingState() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'flex';
        document.getElementById('spreadsheetContainer').style.display = 'none';
    }

    showSpreadsheetState() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('spreadsheetContainer').style.display = 'flex';
    }

    addReasoningStep(text, icon = '🤔') {
        const container = document.getElementById('conversationContainer');
        const step = document.createElement('div');
        step.className = 'reasoning-step';
        step.innerHTML = `
            <div class="step-icon">${icon}</div>
            <div class="step-text">${text}</div>
        `;
        container.appendChild(step);
        container.scrollTop = container.scrollHeight;
    }

    async generateSpreadsheet() {
        const input = document.getElementById('conversationInput');
        const prompt = input.value.trim();

        if (!prompt || this.isGenerating) return;

        this.isGenerating = true;
        input.value = '';

        // Show loading state
        this.showLoadingState();

        // Add reasoning steps
        this.addReasoningStep('Analyzing your request...', '🔍');

        try {
            const response = await fetch('/api/ai-sheets/generate-sheet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt,
                    sheet_type: 'general',
                    rows: 15,
                    columns: 6,
                    is_follow_up: false,
                    session_id: Date.now() + '_' + Math.random().toString(36).substr(2, 9), // Unique session ID
                    timestamp: new Date().toISOString()
                })
            });

            if (!response.ok) {
                throw new Error('Failed to generate spreadsheet');
            }

            const response_data = await response.json();

            if (response_data.success) {
                // Add more reasoning steps
                this.addReasoningStep('Understanding data structure...', '📊');
                this.addReasoningStep('Creating spreadsheet layout...', '🏗️');
                this.addReasoningStep('Populating with data...', '✨');

                // Render the spreadsheet
                this.renderSpreadsheet(response_data.data);
            } else {
                throw new Error(response_data.error || 'Failed to generate spreadsheet');
            }

        } catch (error) {
            console.error('Error generating spreadsheet:', error);
            this.addReasoningStep('Error: ' + error.message, '❌');
        } finally {
            this.isGenerating = false;
        }
    }

    renderSpreadsheet(data) {
        // Update title and description
        document.getElementById('spreadsheetTitle').textContent = data.title || 'Generated Spreadsheet';
        document.getElementById('spreadsheetDescription').textContent = data.description || 'AI-generated spreadsheet';

        // Render table
        const table = document.getElementById('spreadsheetTable');
        table.innerHTML = '';

        if (data.headers && data.rows) {
            // Create header row
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');

            data.headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });

            thead.appendChild(headerRow);
            table.appendChild(thead);

            // Create body rows
            const tbody = document.createElement('tbody');

            data.rows.forEach(row => {
                const tr = document.createElement('tr');

                row.forEach(cell => {
                    const td = document.createElement('td');
                    td.textContent = cell || '';
                    td.contentEditable = true;
                    td.addEventListener('blur', () => {
                        // Handle cell changes
                        this.onCellChange(td);
                    });
                    td.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            td.blur();
                        }
                    });
                    tr.appendChild(td);
                });

                tbody.appendChild(tr);
            });

            table.appendChild(tbody);
        }

        // Show spreadsheet
        this.showSpreadsheetState();
        this.addReasoningStep('Spreadsheet generated successfully!', '✅');

        // Store current data
        this.currentSession = data;
    }

    onCellChange(cell) {
        // Handle individual cell changes
        console.log('Cell changed:', cell.textContent);
        // You can add auto-save functionality here
    }

    exportData(format) {
        if (!this.currentSession) {
            alert('No spreadsheet to export');
            return;
        }

        const data = this.currentSession;

        switch (format) {
            case 'csv':
                this.exportCSV(data);
                break;
            case 'excel':
                this.exportExcel(data);
                break;
            case 'json':
                this.exportJSON(data);
                break;
        }
    }

    exportCSV(data) {
        let csv = '';

        // Add headers
        if (data.headers) {
            csv += data.headers.join(',') + '\n';
        }

        // Add rows
        if (data.rows) {
            data.rows.forEach(row => {
                csv += row.map(cell => `"${cell || ''}"`).join(',') + '\n';
            });
        }

        this.downloadFile(csv, 'spreadsheet.csv', 'text/csv');
    }

    exportJSON(data) {
        const json = JSON.stringify(data, null, 2);
        this.downloadFile(json, 'spreadsheet.json', 'application/json');
    }

    exportExcel(data) {
        // For now, export as CSV with .xlsx extension
        // In a real implementation, you'd use a library like SheetJS
        this.exportCSV(data);
    }

    downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AISheets();
});
</script>
{% endblock %}
