{% extends "layout.html" %}

{% block title %}AutoWave - AI-Powered Assistant{% endblock %}

{% block header %}Welcome to AutoWave{% endblock %}

{% block extra_css %}
<style>
    /* Dark theme styles */
    body {
        background-color: #121212;
        color: #e0e0e0;
    }

    #main-content {
        background-color: #121212;
    }

    main {
        background-color: #121212;
    }

    .bg-white {
        background-color: #121212 !important;
    }

    .text-gray-700, .text-gray-800 {
        color: #e0e0e0 !important;
    }

    .text-gray-600 {
        color: #aaa !important;
    }

    .text-gray-500 {
        color: #888 !important;
    }

    .border-gray-200 {
        border-color: #333 !important;
    }

    .shadow-sm {
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5) !important;
    }

    /* Container styles */
    .feature-container {
        background-color: #1e1e1e;
        border-radius: 8px;
        border: 1px solid #333;
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Modern Expandable Input Styles */
    .modern-input-container {
        position: relative;
        background-color: #2d2d2d;
        border: 1px solid #444;
        border-radius: 12px;
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .modern-input-container:focus-within {
        border-color: #555;
        box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
    }

    .modern-expandable-input {
        background: transparent;
        border: none;
        color: #e0e0e0;
        padding: 16px 16px 50px 16px;
        width: 100%;
        font-size: 16px;
        line-height: 1.5;
        resize: none;
        outline: none;
        min-height: 24px;
        max-height: 200px;
        overflow-y: auto;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transition: all 0.2s ease;
    }

    .modern-expandable-input::placeholder {
        color: #888;
    }

    .modern-expandable-input::-webkit-scrollbar {
        width: 4px;
    }

    .modern-expandable-input::-webkit-scrollbar-track {
        background: transparent;
    }

    .modern-expandable-input::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 2px;
    }

    .modern-expandable-input::-webkit-scrollbar-thumb:hover {
        background: #666;
    }

    .input-bottom-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: transparent;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 40px;
        pointer-events: none;
        z-index: 10;
    }

    .input-bottom-bar > * {
        pointer-events: auto;
    }

    .input-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .input-actions-left {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .input-action-btn {
        background: transparent;
        border: none;
        color: #888;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .input-action-btn:hover {
        color: #e0e0e0;
        background: rgba(255, 255, 255, 0.1);
    }

    .input-mic-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        padding: 6px;
        border-radius: 50%;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
    }

    .input-mic-btn:hover {
        color: #e0e0e0;
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Pulse animation for recording */
    @keyframes pulse {
        0% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.7;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* Button styles */
    .prime-button {
        background-color: #2d2d2d;
        color: #e0e0e0;
        border: 1px solid #444;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .prime-button:hover {
        background-color: #3d3d3d;
    }

    .prime-button:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
    }

    /* Feature card styles */
    .feature-card {
        background-color: #1e1e1e;
        border-radius: 8px;
        border: 1px solid #333;
        padding: 20px;
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        border-color: #444;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .feature-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
    }

    .feature-icon {
        margin-right: 12px;
        color: #aaa;
        font-size: 20px;
    }

    .feature-title {
        color: #e0e0e0;
        font-size: 18px;
        font-weight: 500;
    }

    .feature-description {
        color: #aaa;
        font-size: 14px;
        margin-bottom: 16px;
    }

    .feature-activity {
        background-color: #2d2d2d;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 16px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #333;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        margin-right: 12px;
        color: #888;
        width: 20px;
        text-align: center;
    }

    .activity-text {
        color: #ccc;
        flex: 1;
    }

    .activity-time {
        color: #777;
        font-size: 12px;
    }

    .feature-button {
        background-color: #2d2d2d;
        color: #e0e0e0;
        border: none;
        border-radius: 8px;
        padding: 10px 16px;
        font-size: 14px;
        text-align: center;
        display: block;
        width: 100%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .feature-button:hover {
        background-color: #3d3d3d;
    }

    .feature-button {
        text-decoration: none;
        display: block;
    }

    /* Content wrapper */
    .content-wrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding-bottom: 20px;
    }

    /* Web Images Section */
    .web-image-card {
        background-color: #1e1e1e;
        border-radius: 8px;
        border: 1px solid #333;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .web-image-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
        border-color: #444;
    }

    .web-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .web-image:hover {
        opacity: 0.9;
    }

    .web-image-caption {
        padding: 12px;
        color: #ccc;
        font-size: 14px;
        text-align: center;
        border-top: 1px solid #333;
    }

    /* File upload styles */
    .file-preview {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
        backdrop-filter: blur(10px);
    }

    .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .file-item:last-child {
        margin-bottom: 0;
    }

    .file-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .file-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.75rem;
        color: #60a5fa;
    }

    .file-details {
        flex: 1;
    }

    .file-name {
        font-weight: 500;
        color: white;
        font-size: 0.875rem;
    }

    .file-size {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.75rem;
    }

    .file-remove {
        background: none;
        border: none;
        color: #ef4444;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s;
    }

    .file-remove:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .file-image-preview {
        width: 3rem;
        height: 3rem;
        object-fit: cover;
        border-radius: 0.375rem;
        margin-right: 0.75rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Footer styles */
    .footer {
        color: #666;
        text-align: center;
        padding-top: 20px;
        font-size: 12px;
    }

    .footer-nav {
        display: flex;
        justify-content: center;
        gap: 32px;
        align-items: center;
    }

    .footer-link {
        color: #666;
        text-decoration: none;
        font-size: 14px;
        font-weight: 400;
        transition: color 0.3s ease;
        padding: 8px 0;
    }

    .footer-link:hover {
        color: #aaa;
        text-decoration: none;
    }

    /* Footer styles */
    .footer {
        color: #666;
        text-align: center;
        padding-top: 20px;
        font-size: 12px;
    }

    .footer-nav {
        display: flex;
        justify-content: center;
        gap: 32px;
        align-items: center;
    }

    .footer-link {
        color: #666;
        text-decoration: none;
        font-size: 14px;
        font-weight: 400;
        transition: color 0.3s ease;
        padding: 8px 0;
    }

    .footer-link:hover {
        color: #aaa;
        text-decoration: none;
    }

    /* Tab selection styles */
    .tab-selector {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 16px;
        margin-bottom: 8px;
    }

    .tab-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.3s ease;
        color: #aaa;
        font-size: 14px;
        font-weight: 500;
        background: transparent;
        border: none;
    }

    .tab-option:hover {
        color: #e0e0e0;
        background-color: rgba(255, 255, 255, 0.05);
    }

    .tab-option.selected {
        color: #e0e0e0;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .tab-option .tab-icon {
        font-size: 16px;
    }

    /* Homepage Title Styling */
    .homepage-title {
        color: #ffffff;
        font-size: 32px;
        font-weight: 800;
        margin-bottom: 16px;
        text-align: center;
        letter-spacing: -0.5px;
        line-height: 1.2;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    /* Homepage Subtitle Styling */
    .homepage-subtitle {
        color: #f8f9fa;
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 24px;
        text-align: center;
        line-height: 1.5;
        opacity: 0.9;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    /* Auth Overlay Styles */
    .auth-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .auth-card {
        background: #1a1a1a;
        border: 1px solid #333;
        border-radius: 16px;
        padding: 40px;
        max-width: 420px;
        width: 100%;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .auth-logo {
        width: 64px;
        height: 64px;
        margin: 0 auto 24px;
        background: #2d2d2d;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #444;
    }

    .auth-logo img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
    }

    .auth-title {
        color: #ffffff;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    .auth-subtitle {
        color: #aaa;
        font-size: 16px;
        margin-bottom: 32px;
        line-height: 1.4;
    }

    .auth-button {
        width: 100%;
        padding: 14px 20px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        margin-bottom: 12px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    .auth-button-primary {
        background: #007bff;
        color: white;
        font-weight: 600;
    }

    .auth-button-primary:hover {
        background: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .auth-button-google {
        background: white;
        color: #333;
        border: 1px solid #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }

    .auth-button-google:hover {
        background: #f8f9fa;
        transform: translateY(-1px);
    }

    .google-logo {
        width: 20px;
        height: 20px;
    }

    /* Disabled state for page content */
    .content-disabled {
        pointer-events: none;
        user-select: none;
    }

    /* Video Overlay Styles */
    .video-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.95);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .video-container {
        position: relative;
        max-width: 90vw;
        max-height: 90vh;
        background: #000;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    }

    .video-close-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.95);
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        color: #333;
        z-index: 10001;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .video-close-btn:hover {
        background: white;
        transform: scale(1.1);
    }

    .intro-video {
        width: 100%;
        height: auto;
        display: block;
        max-height: 80vh;
    }

    /* Hide any large footer sections on homepage but keep user email section */
    footer[class*="grid"],
    footer[class*="border-t"],
    .footer-section,
    .seo-footer,
    div[class*="grid-cols-4"],
    section[class*="footer"] {
        display: none !important;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        /* Main container adjustments */
        .container, .max-w-6xl, .max-w-4xl {
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Content wrapper */
        .content-wrapper {
            padding: 0 !important;
            margin: 0 !important;
        }

        /* Feature container adjustments */
        .feature-container {
            margin: 0 0 24px 0 !important;
            padding: 20px 12px !important;
            width: 100% !important;
            box-sizing: border-box;
        }

        /* Homepage title and subtitle */
        .homepage-title {
            font-size: 24px !important;
            line-height: 1.3;
            margin-bottom: 12px;
            text-align: center;
        }

        .homepage-subtitle {
            font-size: 16px !important;
            line-height: 1.4;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Input container */
        .modern-input-container {
            margin: 0 0 24px 0 !important;
            width: 100% !important;
            box-sizing: border-box;
            padding: 0 12px !important;
        }

        .modern-expandable-input {
            font-size: 16px !important;
            padding: 12px !important;
            min-height: 80px !important;
            width: 100% !important;
            box-sizing: border-box;
        }

        /* Feature cards and sections */
        .feature-card {
            padding: 20px 12px;
            margin: 0 0 24px 0 !important;
            width: 100% !important;
            box-sizing: border-box;
        }

        .feature-title {
            font-size: 16px !important;
            text-align: center;
        }

        .feature-description {
            font-size: 14px !important;
            line-height: 1.4;
            text-align: center;
        }

        /* Research Lab section */
        .research-section, .research-assistant-section {
            margin: 0 0 24px 0 !important;
            padding: 20px 12px !important;
            width: 100% !important;
            box-sizing: border-box;
        }

        /* Grid adjustments */
        .grid.grid-cols-1.md\\:grid-cols-2 {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
            margin: 0 12px !important;
            width: calc(100% - 24px) !important;
        }

        .grid.grid-cols-2.md\\:grid-cols-3 {
            grid-template-columns: 1fr 1fr !important;
            gap: 12px !important;
            margin: 0 12px !important;
            width: calc(100% - 24px) !important;
        }

        /* Button adjustments */
        .prime-button {
            padding: 10px 20px;
            font-size: 14px;
            width: 100%;
            margin-bottom: 8px;
        }

        /* Input actions */
        .input-actions {
            flex-wrap: wrap;
            gap: 4px;
            justify-content: center;
        }

        .input-actions-left {
            flex-wrap: wrap;
            gap: 4px;
        }

        .input-action-btn {
            padding: 4px;
            font-size: 12px;
        }

        /* Tab selector adjustments */
        .tab-selector {
            margin: 0 8px !important;
            padding: 0 4px !important;
        }

        .tab-option {
            padding: 10px 12px !important;
            font-size: 14px !important;
            margin: 2px 1px !important;
            min-width: auto !important;
        }

        .tab-option span {
            font-size: 13px !important;
        }

        .tab-icon {
            width: 18px !important;
            height: 18px !important;
        }

        /* Web images section */
        .web-images-section {
            margin: 0 0 24px 0 !important;
            padding: 20px 12px !important;
            width: 100% !important;
            box-sizing: border-box;
        }

        .web-image-card {
            margin-bottom: 12px;
        }

        .web-image {
            border-radius: 6px;
        }

        .web-image-caption {
            font-size: 12px !important;
            padding: 6px;
        }

        /* Video overlay */
        .video-container {
            margin: 0 0 24px 0 !important;
            max-width: 100vw !important;
            width: 100% !important;
            box-sizing: border-box;
            padding: 0 12px !important;
        }

        .intro-video-container {
            max-width: 100% !important;
            margin: 0 !important;
            width: 100% !important;
            padding: 0 12px !important;
        }

        /* Sidebar adjustments for mobile */
        .sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .sidebar.open {
            transform: translateX(0);
        }

        /* Main content area */
        .main-content {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
        }

        /* All sections alignment */
        section, .section {
            margin: 0 0 24px 0 !important;
            padding: 20px 12px !important;
            width: 100% !important;
            box-sizing: border-box;
        }
    }

    @media (max-width: 480px) {
        /* Extra small screens */
        .container, .max-w-6xl, .max-w-4xl {
            margin: 0 8px !important;
        }

        .feature-container {
            margin: 0 8px 20px 8px !important;
            padding: 16px 12px !important;
            width: calc(100% - 16px) !important;
        }

        .homepage-title {
            font-size: 20px !important;
        }

        .homepage-subtitle {
            font-size: 14px !important;
        }

        .modern-input-container {
            margin: 0 0 20px 0 !important;
        }

        .modern-expandable-input {
            font-size: 14px !important;
            padding: 10px !important;
        }

        .feature-card, .research-section, .research-assistant-section, .web-images-section {
            margin: 0 0 20px 0 !important;
            padding: 16px 8px !important;
            width: 100% !important;
        }

        .grid.grid-cols-1.md\\:grid-cols-2, .grid.grid-cols-2.md\\:grid-cols-3 {
            grid-template-columns: 1fr !important;
            margin: 0 8px !important;
            width: calc(100% - 16px) !important;
        }

        section, .section {
            margin: 0 0 20px 0 !important;
            padding: 16px 8px !important;
            width: 100% !important;
        }

        .video-container, .intro-video-container {
            margin: 0 0 20px 0 !important;
            width: 100% !important;
            padding: 0 8px !important;
        }

        .tab-option {
            padding: 8px 10px !important;
            font-size: 13px !important;
            margin: 1px !important;
        }

        .tab-option span {
            font-size: 12px !important;
        }

        .tab-icon {
            width: 16px !important;
            height: 16px !important;
        }
    }

    /* Ensure user email section is visible */
    #user-email-section {
        display: block !important;
    }

    /* Hide specific footer content patterns */
    div:has(h4:contains("Product")),
    div:has(h4:contains("Company")),
    div:has(h4:contains("Legal")),
    div:has(h4:contains("AI Tools")) {
        display: none !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Auth Overlay -->
<div id="authOverlay" class="auth-overlay">
    <div class="auth-card">
        <div class="auth-logo">
            <img src="{{ url_for('static', filename='images/autowave-logo.png') }}" alt="AutoWave Logo">
        </div>
        <h2 class="auth-title">Explore AutoWave Prime Agent</h2>
        <p class="auth-subtitle">AI Web Browser • AI Code Editor • Document Generator & more</p>

        <button class="auth-button auth-button-primary" onclick="redirectToAuth('login')">
            Log in / Sign up
        </button>

        <button class="auth-button auth-button-google" onclick="redirectToAuth('google')">
            <svg class="google-logo" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
        </button>
    </div>
</div>

<!-- Video Intro Overlay -->
<div id="videoOverlay" class="video-overlay" style="display: none;">
    <div class="video-container">
        <button class="video-close-btn" onclick="closeVideoOverlay()">&times;</button>
        <div class="intro-video-container" style="position: relative; width: 100%; max-width: 800px; margin: 0 auto; border-radius: 12px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
            <!-- YouTube Embed -->
            <div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">
                <iframe
                    src="https://www.youtube.com/embed/PTDXhvek-P4?si=24RPZx8YNsALbJFh&autoplay=1&mute=1&controls=1&modestbranding=1&rel=0&showinfo=0"
                    style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                </iframe>
            </div>

            <!-- Video Overlay Controls -->
            <div style="position: absolute; top: 20px; right: 20px; z-index: 10;">
                <button onclick="closeVideoOverlay()" style="background: rgba(0,0,0,0.7); border: none; color: white; width: 40px; height: 40px; border-radius: 50%; font-size: 20px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(0,0,0,0.9)'" onmouseout="this.style.background='rgba(0,0,0,0.7)'">
                    ×
                </button>
            </div>

            <!-- Welcome Message Below Video -->
            <div style="background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%); color: white; padding: 30px; text-align: center; border-top: 1px solid rgba(255,255,255,0.1);">
                <h2 style="font-size: 24px; margin-bottom: 12px; font-weight: 600; color: #ffffff;">Welcome to AutoWave!</h2>
                <p style="font-size: 16px; opacity: 0.8; line-height: 1.5; margin-bottom: 20px; color: #e0e0e0;">
                    Your AI-powered productivity suite is ready to transform how you work!
                </p>
                <button onclick="closeVideoOverlay()" style="background: white; border: 2px solid #1a1a2e; color: #000000; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(26, 26, 46, 0.2)'; this.style.background='#f8f9fa'" onmouseout="this.style.transform='translateY(0px)'; this.style.boxShadow='0 4px 15px rgba(0, 0, 0, 0.1)'; this.style.background='white'">
                    Get Started →
                </button>
            </div>
        </div>
    </div>
</div>

<div class="content-wrapper content-disabled" id="mainContent">
    <div class="feature-container">
        <h2 class="homepage-title">Your AutoWave Prime Agent</h2>
        <p class="homepage-subtitle">Get comprehensive answers, browse the web, and complete complex tasks</p>

        <!-- Input box inside the first section -->
        <div class="mt-6 mb-4">
            <form id="mainForm" action="/dark-chat" method="get" class="relative">
                <div class="modern-input-container">
                    <textarea
                        name="message"
                        class="modern-expandable-input"
                        placeholder="Enter a task for Prime Agent..."
                        required
                        rows="1"
                        id="homeMainInput"
                    ></textarea>

                    <!-- Bottom Bar with Actions Only -->
                    <div class="input-bottom-bar">
                        <!-- Left side actions -->
                        <div class="input-actions-left">
                            <!-- Speech to Text Button -->
                            <button type="button" id="homeMicBtn" class="input-mic-btn" title="Speech to text">
                                🎤
                            </button>
                        </div>

                        <!-- Right side actions -->
                        <div class="input-actions">
                            <!-- File Upload Button -->
                            <button type="button" id="homeFileUploadBtn" class="input-action-btn" title="Upload file or image">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
                                </svg>
                            </button>

                            <!-- Submit Button -->
                            <button type="submit" class="input-action-btn" title="Send message">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 2L11 13"></path>
                                    <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="homeFileInput" class="hidden" accept="image/*,.txt,.py,.js,.html,.css,.json,.md,.pdf,.doc,.docx" multiple>
            </form>

            <!-- File Preview -->
            <div id="homeFilePreview" class="file-preview mt-2" style="display: none;">
                <!-- File previews will be added here dynamically -->
            </div>

            <!-- Tab Selector -->
            <div class="tab-selector">
                <button class="tab-option" data-target="/autowave" data-name="Task">
                    <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="16" height="16">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                    <span>Task</span>
                </button>

                <button class="tab-option" data-target="/agentic-code" data-name="Design">
                    <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="16" height="16">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    <span>Design</span>
                </button>

                <button class="tab-option" data-target="/document-generator" data-name="Wave">
                    <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="16" height="16">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                    </svg>
                    <span>Wave</span>
                </button>

                <button class="tab-option" data-target="/ai-sheets" data-name="AI Sheets">
                    <svg class="tab-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="16" height="16">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v4" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 5v4" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 11h18" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 15h10" />
                    </svg>
                    <span>AI Sheets</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Research Assistant -->
        <div class="feature-card">
            <div class="feature-header">
                <div class="feature-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="feature-title">Research Assistant</h3>
            </div>
            <p class="feature-description">Actively browsing and collecting data</p>

            <div class="feature-activity">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="activity-text">Visiting knowledge sources</div>
                    <div class="activity-time">2 seconds ago</div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-save"></i>
                    </div>
                    <div class="activity-text">Saving research data</div>
                    <div class="activity-time">10 seconds ago</div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="activity-text">Processing information</div>
                    <div class="activity-time">30 seconds ago</div>
                </div>
            </div>

            <a href="/deep-research" class="feature-button">
                Go to Research Lab
            </a>
        </div>

        <!-- AI Chat -->
        <div class="feature-card">
            <div class="feature-header">
                <div class="feature-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <h3 class="feature-title">AI Chat</h3>
            </div>
            <p class="feature-description">Having intelligent conversations</p>

            <div class="feature-activity">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-comment"></i>
                    </div>
                    <div class="activity-text">Answering questions</div>
                    <div class="activity-time">5 seconds ago</div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="activity-text">Processing context</div>
                    <div class="activity-time">15 seconds ago</div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="activity-text">Generating insights</div>
                    <div class="activity-time">25 seconds ago</div>
                </div>
            </div>

            <a href="/dark-chat" class="feature-button">
                Go to AutoWave Chat
            </a>
        </div>
    </div>

    <!-- Images from Web Section -->
    <div class="feature-container mt-8" style="max-width: 950px; margin-left: auto; margin-right: auto;">
        <h2 class="text-2xl font-bold mb-4">Images from Web</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-6">
            <!-- Image 1 - Woman on Beach -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/1382731/pexels-photo-1382731.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Beach Woman" class="web-image">
                <div class="web-image-caption">Woman enjoying beach vacation</div>
            </div>
            <!-- Image 2 - Hawaii -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/1268855/pexels-photo-1268855.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Hawaii" class="web-image">
                <div class="web-image-caption">Beautiful Hawaii beach</div>
            </div>
            <!-- Image 3 - Fancy Man -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/1300550/pexels-photo-1300550.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Fancy Man" class="web-image">
                <div class="web-image-caption">Stylish gentleman in suit</div>
            </div>
            <!-- Image 4 -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/546819/pexels-photo-546819.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Programming" class="web-image">
                <div class="web-image-caption">Modern programming</div>
            </div>
            <!-- Image 5 -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/8566526/pexels-photo-8566526.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Robotics" class="web-image">
                <div class="web-image-caption">Robotics advancements</div>
            </div>
            <!-- Image 6 -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/2599244/pexels-photo-2599244.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Future Tech" class="web-image">
                <div class="web-image-caption">Future technologies</div>
            </div>
            <!-- Image 7 - Beautiful Map -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/697662/pexels-photo-697662.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Travel Map" class="web-image">
                <div class="web-image-caption">Vintage world travel map</div>
            </div>
            <!-- Image 8 - Luxury Car -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/3802510/pexels-photo-3802510.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Luxury Car" class="web-image">
                <div class="web-image-caption">Luxury sports car</div>
            </div>
            <!-- Image 9 - Modern Architecture -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Modern House" class="web-image">
                <div class="web-image-caption">Modern architecture home</div>
            </div>
            <!-- Image 10 - Beautiful Young Lady -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Beautiful Young Lady" class="web-image">
                <div class="web-image-caption">Beautiful young woman</div>
            </div>
            <!-- Image 11 - Luxury Yacht -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/163236/luxury-yacht-boat-speed-water-163236.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Luxury Yacht" class="web-image">
                <div class="web-image-caption">Luxury yacht experience</div>
            </div>
            <!-- Image 12 - Tropical Waterfall -->
            <div class="web-image-card">
                <img src="https://images.pexels.com/photos/460621/pexels-photo-460621.jpeg?auto=compress&cs=tinysrgb&w=800" alt="Tropical Waterfall" class="web-image">
                <div class="web-image-caption">Tropical waterfall paradise</div>
            </div>
        </div>
    </div>

    <!-- Footer Navigation -->
    <div class="footer mt-8">
        <div class="footer-nav">
            <a href="/about" class="footer-link">About Us</a>
            <a href="/blog" class="footer-link">Blog</a>
            <a href="/privacy" class="footer-link">Privacy</a>
            <a href="/terms" class="footer-link">Terms</a>
        </div>
    </div>
</div>



<script src="/static/js/universal_file_upload.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('mainForm');
    const tabOptions = document.querySelectorAll('.tab-option');
    let selectedTarget = null;

    // Handle tab selection
    tabOptions.forEach(tab => {
        tab.addEventListener('click', function() {
            const target = this.getAttribute('data-target');
            const tabName = this.getAttribute('data-name');

            // If it's the Task tab, handle selection for form submission (don't redirect immediately)
            if (tabName === 'Task') {
                // Remove selected class from all tabs
                tabOptions.forEach(t => t.classList.remove('selected'));

                // Add selected class to clicked tab
                this.classList.add('selected');

                // Store the selected target - redirect to prime agent task page instead of context7-tools
                selectedTarget = '/autowave';

                // Update placeholder text
                const input = form.querySelector('input[name="message"]');
                input.placeholder = `Enter a task for ${tabName}...`;
                return;
            }

            // For other tabs, handle selection for form submission
            // Remove selected class from all tabs
            tabOptions.forEach(t => t.classList.remove('selected'));

            // Add selected class to clicked tab
            this.classList.add('selected');

            // Store the selected target
            selectedTarget = target;

            // Update placeholder text based on selection
            const input = form.querySelector('input[name="message"]');
            input.placeholder = `Enter a task for ${tabName}...`;
        });
    });

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const message = form.querySelector('textarea[name="message"]').value;

        // Get uploaded files if available
        let fileContent = '';
        if (window.universalFileUpload) {
            fileContent = window.universalFileUpload.getFileContentForAI('home');
        }

        // Determine target URL and parameter name
        let targetUrl;
        let paramName = 'message'; // Default parameter name

        if (selectedTarget) {
            targetUrl = selectedTarget;

            // Use correct parameter names for each page
            if (targetUrl === '/autowave') {
                paramName = 'task'; // Prime Agent expects 'task' parameter
            } else if (targetUrl === '/context7-tools') {
                paramName = 'task'; // Prime Agent Tools expects 'task' parameter
            } else if (targetUrl === '/agentic-code') {
                paramName = 'message'; // Agentic Code (Design) will expect 'message' parameter
            } else if (targetUrl === '/document-generator') {
                paramName = 'content'; // Document Generator (now "Wave") will expect 'content' parameter
            } else if (targetUrl === '/ai-sheets') {
                paramName = 'task'; // AI Sheets will expect 'task' parameter
            }
        } else {
            // Default to AutoWave Chat if no tab selected
            targetUrl = '/dark-chat';
            paramName = 'message'; // AutoWave Chat expects 'message' parameter
        }

        // Add message as query parameter and redirect
        const url = new URL(targetUrl, window.location.origin);
        if (message || fileContent) {
            url.searchParams.set(paramName, message + fileContent);
        }

        // Clear uploaded files before navigation
        if (window.universalFileUpload) {
            window.universalFileUpload.clearFiles('home');
        }

        window.location.href = url.toString();
    });

    // Modern Expandable Input Functionality
    function initializeExpandableInput(inputId) {
        const textarea = document.getElementById(inputId);
        if (!textarea) return;

        // Auto-expand functionality
        function autoExpand() {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        }

        // Event listeners
        textarea.addEventListener('input', autoExpand);
        textarea.addEventListener('paste', () => setTimeout(autoExpand, 0));

        // Initial setup
        autoExpand();

        // Handle Enter key (submit on Enter, new line on Shift+Enter)
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const form = textarea.closest('form');
                if (form && textarea.value.trim()) {
                    form.submit();
                }
            }
        });
    }

    // Suggestion function removed - clean input design

    // Initialize expandable input
    initializeExpandableInput('homeMainInput');

    // Speech-to-Text functionality for Homepage input
    let homeRecognition = null;
    let isHomeRecording = false;
    const homeMicBtn = document.getElementById('homeMicBtn');
    const homeTextInput = document.getElementById('homeMainInput');

    // Check if browser supports speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        homeRecognition = new SpeechRecognition();

        // Configure speech recognition
        homeRecognition.continuous = true;
        homeRecognition.interimResults = true;
        homeRecognition.lang = 'en-US';

        let finalTranscript = '';

        homeRecognition.onresult = function(event) {
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript + ' ';
                } else {
                    interimTranscript += transcript;
                }
            }

            // Update the textarea with the transcribed text
            homeTextInput.value = finalTranscript + interimTranscript;

            // Auto-resize the textarea
            homeTextInput.style.height = 'auto';
            homeTextInput.style.height = Math.min(homeTextInput.scrollHeight, 200) + 'px';
        };

        homeRecognition.onstart = function() {
            isHomeRecording = true;
            homeMicBtn.style.color = '#ef4444'; // Red color when recording
            homeMicBtn.style.animation = 'pulse 1.5s infinite';
            console.log('Speech recognition started for home input');
        };

        homeRecognition.onend = function() {
            isHomeRecording = false;
            homeMicBtn.style.color = '';
            homeMicBtn.style.animation = '';
            console.log('Speech recognition ended for home input');
        };

        homeRecognition.onerror = function(event) {
            console.error('Speech recognition error for home input:', event.error);
            isHomeRecording = false;
            homeMicBtn.style.color = '';
            homeMicBtn.style.animation = '';
        };

        // Add click event listener to microphone button
        if (homeMicBtn) {
            homeMicBtn.addEventListener('click', function() {
                if (isHomeRecording) {
                    homeRecognition.stop();
                } else {
                    finalTranscript = homeTextInput.value || '';
                    homeRecognition.start();
                }
            });
        }
    } else {
        // Hide microphone button if speech recognition is not supported
        if (homeMicBtn) {
            homeMicBtn.style.display = 'none';
        }
        console.log('Speech recognition not supported in this browser');
    }

    // Remove any large footer sections that might appear
    function removeFooterSections() {
        // Remove footer elements with specific patterns
        const footerSelectors = [
            'footer[class*="grid"]',
            'footer[class*="border-t"]',
            '.footer-section',
            '.seo-footer',
            'div[class*="grid-cols-4"]',
            'section[class*="footer"]'
        ];

        footerSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                // Don't remove the user email section or any element containing it
                if (el && el.parentNode &&
                    el.id !== 'user-email-section' &&
                    !el.querySelector('#user-email-section') &&
                    !el.closest('#user-email-section')) {
                    el.parentNode.removeChild(el);
                }
            });
        });

        // Remove elements containing specific footer headings
        const footerHeadings = ['Product', 'Company', 'Legal', 'AI Tools'];
        footerHeadings.forEach(heading => {
            const h4Elements = document.querySelectorAll('h4');
            h4Elements.forEach(h4 => {
                if (h4.textContent.includes(heading)) {
                    const parentDiv = h4.closest('div');
                    if (parentDiv && parentDiv.parentNode) {
                        parentDiv.parentNode.removeChild(parentDiv);
                    }
                }
            });
        });
    }

    // Run footer removal immediately and after a short delay
    removeFooterSections();
    setTimeout(removeFooterSections, 100);
    setTimeout(removeFooterSections, 500);

    // Auth Overlay Functionality
    function redirectToAuth(type) {
        console.log('Redirecting to auth type:', type);
        if (type === 'signup') {
            window.location.href = '/auth/register';
        } else if (type === 'login') {
            window.location.href = '/auth/login';
        } else if (type === 'google') {
            // Google button should redirect to login page for easier sign-in
            window.location.href = '/auth/login';
        }
    }

    // Make redirectToAuth globally accessible
    window.redirectToAuth = redirectToAuth;

    // Make redirectToAuth globally accessible
    window.redirectToAuth = redirectToAuth;

    // Check if user is authenticated
    function checkAuthStatus() {
        // Get authentication status from backend
        return {{ 'true' if is_authenticated else 'false' }};
    }

    // Check if this is a new login session and set flag for intro video
    const isNewLogin = {{ 'true' if new_login else 'false' }};
    if (isNewLogin) {
        sessionStorage.setItem('autowave_new_session', 'true');
    }

    // Show/hide auth overlay based on authentication status
    function initAuthOverlay() {
        const overlay = document.getElementById('authOverlay');
        const mainContent = document.getElementById('mainContent');

        if (!checkAuthStatus()) {
            // User not authenticated - show overlay and disable content
            overlay.style.display = 'flex';
            mainContent.classList.add('content-disabled');
        } else {
            // User authenticated - hide overlay and enable content
            overlay.style.display = 'none';
            mainContent.classList.remove('content-disabled');

            // Show video overlay for authenticated users (check if they've seen it before)
            // Also check if this is a new session (just logged in)
            const isNewSession = sessionStorage.getItem('autowave_new_session') === 'true';
            const hasSeenVideo = localStorage.getItem('autowave_intro_seen') === 'true';

            if (!hasSeenVideo || isNewSession) {
                setTimeout(() => {
                    showVideoOverlay();
                    // Clear the new session flag
                    sessionStorage.removeItem('autowave_new_session');
                }, 1000); // Show video after 1 second
            }
        }
    }

    // Video overlay functions
    function showVideoOverlay() {
        const videoOverlay = document.getElementById('videoOverlay');

        // Show video overlay for authenticated users
        videoOverlay.style.display = 'flex';
    }

    function closeVideoOverlay() {
        const videoOverlay = document.getElementById('videoOverlay');
        const video = videoOverlay.querySelector('video');

        // Pause video
        if (video) {
            video.pause();
        }

        // Hide video overlay
        videoOverlay.style.display = 'none';

        // Mark as seen so it doesn't show again
        localStorage.setItem('autowave_intro_seen', 'true');
    }

    // Make closeVideoOverlay globally accessible
    window.closeVideoOverlay = closeVideoOverlay;

    // Initialize auth overlay on page load
    initAuthOverlay();

    // Override the layout's checkAuthStatus function for homepage
    window.checkAuthStatus = function() {
        // On homepage, we handle auth differently - don't redirect
        console.log('Homepage auth check - showing overlay instead of redirecting');

        // Ensure auth overlay is shown for unauthenticated users
        const isAuth = {{ 'true' if is_authenticated else 'false' }};
        if (!isAuth) {
            const overlay = document.getElementById('authOverlay');
            const mainContent = document.getElementById('mainContent');
            if (overlay) overlay.style.display = 'flex';
            if (mainContent) mainContent.classList.add('content-disabled');
        }
        return;
    };

    // Add a function to manually show video (for testing)
    window.showIntroVideo = function() {
        showVideoOverlay();
    };

    // Add a function to reset video viewing status (for testing)
    window.resetVideoStatus = function() {
        localStorage.removeItem('autowave_intro_seen');
        console.log('Video viewing status reset. Refresh page to see video again.');
    };

    // Make closeVideoOverlay globally accessible
    window.closeVideoOverlay = closeVideoOverlay;
});
</script>

{% endblock %}
