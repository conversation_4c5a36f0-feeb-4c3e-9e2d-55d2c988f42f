{% extends "base.html" %}

{% block title %}Billing Information - AutoWave{% endblock %}

{% block content %}
<div class="min-h-screen" style="background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);">
    <!-- Header -->
    <div class="container mx-auto px-6 py-8">
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold" style="color: #e0e0e0;">Billing Information</h1>
                <p class="text-sm mt-2" style="color: #aaa;">Manage your payment methods and billing details</p>
            </div>
            <div class="flex space-x-4">
                <a href="/payment-history" class="px-4 py-2 rounded-lg border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors">
                    Payment History
                </a>
                <a href="/pricing" class="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                    View Plans
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Current Subscription -->
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h2 class="text-xl font-semibold mb-4" style="color: #e0e0e0;">Current Subscription</h2>
                <div id="subscription-details" class="space-y-4">
                    <!-- Will be populated by JavaScript -->
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                        <div class="text-gray-400 mt-2">Loading...</div>
                    </div>
                </div>
            </div>

            <!-- Payment Method -->
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h2 class="text-xl font-semibold mb-4" style="color: #e0e0e0;">Payment Method</h2>
                <div id="payment-method-details" class="space-y-4">
                    <!-- Will be populated by JavaScript -->
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                        <div class="text-gray-400 mt-2">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Billing Address -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mt-8">
            <h2 class="text-xl font-semibold mb-4" style="color: #e0e0e0;">Billing Address</h2>
            <div id="billing-address-form">
                <form id="update-billing-form" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="billing-name" class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                        <input type="text" id="billing-name" name="name" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="billing-email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                        <input type="email" id="billing-email" name="email" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="md:col-span-2">
                        <label for="billing-address" class="block text-sm font-medium text-gray-300 mb-2">Address</label>
                        <input type="text" id="billing-address" name="address" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="billing-city" class="block text-sm font-medium text-gray-300 mb-2">City</label>
                        <input type="text" id="billing-city" name="city" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="billing-country" class="block text-sm font-medium text-gray-300 mb-2">Country</label>
                        <select id="billing-country" name="country" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select Country</option>
                            <option value="US">United States</option>
                            <option value="NG">Nigeria</option>
                            <option value="GB">United Kingdom</option>
                            <option value="CA">Canada</option>
                            <option value="AU">Australia</option>
                            <!-- Add more countries as needed -->
                        </select>
                    </div>
                    <div class="md:col-span-2 flex space-x-4">
                        <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            Update Billing Info
                        </button>
                        <button type="button" onclick="updatePaymentMethod()" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            Update Payment Method
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Plan Switching Section -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mt-8">
            <h2 class="text-xl font-semibold mb-4" style="color: #e0e0e0;">Switch Plan</h2>
            <p class="text-gray-400 mb-6">Need a different plan? Switch anytime or contact us for unlimited options.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Free Plan -->
                <div class="border border-gray-600 rounded-lg p-4 text-center">
                    <h3 class="text-lg font-semibold text-gray-300 mb-2">Free Plan</h3>
                    <div class="text-2xl font-bold text-gray-300 mb-2">$0</div>
                    <div class="text-sm text-gray-400 mb-4">50 credits/day</div>
                    <button onclick="switchPlan('free')" class="w-full px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                        Downgrade
                    </button>
                </div>

                <!-- Plus Plan -->
                <div class="border border-blue-500 rounded-lg p-4 text-center bg-blue-900/20">
                    <h3 class="text-lg font-semibold text-blue-300 mb-2">Plus Plan</h3>
                    <div class="text-2xl font-bold text-blue-300 mb-2">$15</div>
                    <div class="text-sm text-gray-400 mb-4">8,000 credits/month</div>
                    <button onclick="switchPlan('plus')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Switch to Plus
                    </button>
                </div>

                <!-- Pro Plan -->
                <div class="border border-purple-500 rounded-lg p-4 text-center bg-purple-900/20">
                    <h3 class="text-lg font-semibold text-purple-300 mb-2">Pro Plan</h3>
                    <div class="text-2xl font-bold text-purple-300 mb-2">$169</div>
                    <div class="text-sm text-gray-400 mb-4">200,000 credits/month</div>
                    <button onclick="switchPlan('pro')" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        Switch to Pro
                    </button>
                </div>
            </div>

            <!-- Contact for Unlimited -->
            <div class="mt-6 p-4 bg-gray-700 rounded-lg text-center">
                <h4 class="text-lg font-semibold text-gray-300 mb-2">Need Unlimited Access?</h4>
                <p class="text-gray-400 mb-4">Contact our sales team for custom enterprise solutions</p>
                <a href="mailto:<EMAIL>" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors">
                    Contact Sales
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Load billing information
document.addEventListener('DOMContentLoaded', async function() {
    await loadSubscriptionDetails();
    await loadPaymentMethodDetails();
    await loadBillingAddress();
});

async function loadSubscriptionDetails() {
    try {
        const response = await fetch('/payment/subscription-details');
        const data = await response.json();
        
        const container = document.getElementById('subscription-details');
        
        if (data.success && data.subscription) {
            const sub = data.subscription;
            const nextBilling = new Date(sub.current_period_end).toLocaleDateString();
            const status = sub.cancel_at_period_end ? 'Cancelling at period end' : 'Active';
            
            container.innerHTML = `
                <div class="flex justify-between items-center py-2 border-b border-gray-700">
                    <span class="text-gray-400">Plan:</span>
                    <span class="text-white font-medium">${sub.plan_name}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700">
                    <span class="text-gray-400">Status:</span>
                    <span class="text-white font-medium">${status}</span>
                </div>
                <div class="flex justify-between items-center py-2 border-b border-gray-700">
                    <span class="text-gray-400">Next Billing:</span>
                    <span class="text-white font-medium">${nextBilling}</span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-400">Gateway:</span>
                    <span class="text-white font-medium capitalize">${sub.payment_gateway}</span>
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-gray-400">Free Plan</div>
                    <div class="text-sm text-gray-500 mt-2">No subscription required</div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading subscription details:', error);
        document.getElementById('subscription-details').innerHTML = `
            <div class="text-center py-4 text-red-400">
                Error loading subscription details
            </div>
        `;
    }
}

async function loadPaymentMethodDetails() {
    try {
        const response = await fetch('/payment/payment-method');
        const data = await response.json();
        
        const container = document.getElementById('payment-method-details');
        
        if (data.success && data.payment_method) {
            const pm = data.payment_method;
            container.innerHTML = `
                <div class="flex items-center space-x-4 p-4 bg-gray-700 rounded-lg">
                    <div class="flex-1">
                        <div class="text-white font-medium">${pm.type} ending in ${pm.last4}</div>
                        <div class="text-gray-400 text-sm">Expires ${pm.exp_month}/${pm.exp_year}</div>
                    </div>
                    <button onclick="updatePaymentMethod()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Update
                    </button>
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-gray-400">No payment method on file</div>
                    <button onclick="updatePaymentMethod()" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Add Payment Method
                    </button>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading payment method:', error);
        document.getElementById('payment-method-details').innerHTML = `
            <div class="text-center py-4 text-red-400">
                Error loading payment method
            </div>
        `;
    }
}

async function loadBillingAddress() {
    try {
        const response = await fetch('/payment/billing-address');
        const data = await response.json();
        
        if (data.success && data.address) {
            const addr = data.address;
            document.getElementById('billing-name').value = addr.name || '';
            document.getElementById('billing-email').value = addr.email || '';
            document.getElementById('billing-address').value = addr.address || '';
            document.getElementById('billing-city').value = addr.city || '';
            document.getElementById('billing-country').value = addr.country || '';
        }
    } catch (error) {
        console.error('Error loading billing address:', error);
    }
}

// Handle billing form submission
document.getElementById('update-billing-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const billingData = Object.fromEntries(formData);
    
    try {
        const response = await fetch('/payment/update-billing-address', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(billingData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccessMessage('Billing information updated successfully');
        } else {
            throw new Error(result.error || 'Failed to update billing information');
        }
    } catch (error) {
        console.error('Error updating billing info:', error);
        showErrorMessage('Failed to update billing information: ' + error.message);
    }
});

async function updatePaymentMethod() {
    try {
        const response = await fetch('/payment/update-payment-method-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success && result.update_url) {
            window.location.href = result.update_url;
        } else {
            throw new Error(result.error || 'Failed to get payment method update URL');
        }
    } catch (error) {
        console.error('Error updating payment method:', error);
        showErrorMessage('Failed to update payment method: ' + error.message);
    }
}

async function switchPlan(planName) {
    if (!confirm(`Are you sure you want to switch to the ${planName} plan?`)) {
        return;
    }
    
    try {
        const response = await fetch('/payment/switch-plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ plan_name: planName })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccessMessage(`Successfully switched to ${planName} plan`);
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            throw new Error(result.error || 'Failed to switch plan');
        }
    } catch (error) {
        console.error('Error switching plan:', error);
        showErrorMessage('Failed to switch plan: ' + error.message);
    }
}

function showSuccessMessage(message) {
    // Create and show success notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function showErrorMessage(message) {
    // Create and show error notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}
</script>
{% endblock %}
