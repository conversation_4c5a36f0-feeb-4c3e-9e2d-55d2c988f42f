{% extends "layout.html" %}

{% block title %}Meeting Notes - AutoWave{% endblock %}

{% block header %}Meeting Notes{% endblock %}

{% block extra_css %}
<!-- Tailwind CSS CDN -->
<script src="https://cdn.tailwindcss.com"></script>
<script>
  tailwind.config = {
    darkMode: 'class',
    theme: {
      extend: {
        colors: {
          dark: {
            100: '#e0e0e0',
            200: '#a0a0a0',
            300: '#717171',
            400: '#4a4a4a',
            500: '#2d2d2d',
            600: '#1e1e1e',
            700: '#1a1a1a',
            800: '#121212',
            900: '#0a0a0a',
          },
          primary: {
            500: '#4299e1',
            600: '#3182ce',
          },
        }
      }
    }
  }
</script>

<style>
    /* Research Lab Theme Colors */
    :root {
        --research-primary: #2d2d2d;
        --research-secondary: #444;
        --research-accent: #555;
        --research-bg: #1e1e1e;
        --research-text: #e0e0e0;
        --research-border: #333;
        --paper-bg: #f8f8f8;
        --paper-line: #d0d0d0;
        --paper-text: #000000;
        --paper-margin: #ff6b6b;
    }

    /* Main container */
    .meeting-notes-container {
        background-color: var(--research-bg);
        min-height: 100vh;
        color: var(--research-text);
    }

    /* Header section */
    .meeting-header {
        background-color: var(--research-primary);
        border-bottom: 1px solid var(--research-border);
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .meeting-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--research-text);
        margin: 0;
    }

    /* Control panel */
    .control-panel {
        background-color: var(--research-primary);
        border-bottom: 1px solid var(--research-border);
        padding: 1rem 1.5rem;
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    /* Recording button */
    .record-button {
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .record-button:hover {
        background-color: #c82333;
        transform: scale(1.05);
    }

    .record-button.recording {
        background-color: #28a745;
        animation: pulse 2s infinite;
    }

    .record-button.recording:hover {
        background-color: #218838;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
    }

    /* Status indicator */
    .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: var(--research-text);
    }

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #6c757d;
    }

    .status-dot.recording {
        background-color: #28a745;
        animation: blink 1s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }

    /* Main content area */
    .meeting-content {
        display: flex;
        height: calc(100vh - 140px);
    }

    /* Notes section - notebook style */
    .notes-section {
        flex: 1;
        background-color: var(--paper-bg);
        position: relative;
        overflow: hidden;
    }

    /* Notebook paper background */
    .notebook-paper {
        width: 100%;
        height: 100%;
        background-color: var(--paper-bg);
        background-image: 
            linear-gradient(to right, var(--paper-margin) 80px, transparent 80px),
            repeating-linear-gradient(
                transparent,
                transparent 24px,
                var(--paper-line) 24px,
                var(--paper-line) 26px
            );
        background-size: 100% 26px;
        padding: 2rem 1rem 2rem 100px;
        position: relative;
    }

    /* Notes textarea */
    .notes-textarea {
        width: 100%;
        height: 100%;
        background: transparent;
        border: none;
        outline: none;
        font-family: 'Georgia', serif;
        font-size: 16px;
        line-height: 26px;
        color: var(--paper-text);
        resize: none;
        padding: 0;
        margin: 0;
    }

    .notes-textarea::placeholder {
        color: #999;
        font-style: italic;
    }

    /* Action buttons */
    .action-buttons {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
    }

    .action-button {
        background-color: var(--research-primary);
        color: var(--research-text);
        border: 1px solid var(--research-border);
        border-radius: 0.375rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-button:hover {
        background-color: var(--research-accent);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* Transcription status */
    .transcription-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: rgba(45, 45, 45, 0.9);
        color: var(--research-text);
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        display: none;
    }

    .transcription-status.show {
        display: block;
    }

    /* Loading animation */
    .loading-dots {
        display: inline-flex;
        gap: 2px;
    }

    .loading-dots span {
        width: 4px;
        height: 4px;
        background-color: var(--research-text);
        border-radius: 50%;
        animation: loading 1.4s infinite ease-in-out both;
    }

    .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
    .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
    .loading-dots span:nth-child(3) { animation-delay: 0s; }

    @keyframes loading {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .meeting-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
            padding: 1rem;
        }

        .control-panel {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
        }

        .notebook-paper {
            padding: 1rem 0.5rem 1rem 60px;
            margin: 0 0.5rem;
        }

        .action-buttons {
            flex-direction: column;
            bottom: 0.5rem;
            right: 0.5rem;
            gap: 0.5rem;
        }

        /* Input adjustments */
        .input-container {
            margin: 0.5rem;
        }

        .input-container textarea {
            font-size: 16px !important;
            padding: 12px;
            min-height: 80px;
        }

        /* Button adjustments */
        button {
            padding: 8px 16px;
            font-size: 14px;
        }

        .action-buttons button {
            padding: 10px;
            font-size: 12px;
            min-width: 40px;
            height: 40px;
        }

        /* Meeting title */
        .meeting-title {
            font-size: 20px;
            margin-bottom: 0.5rem;
        }

        /* Notes content */
        .notes-content {
            font-size: 14px;
            line-height: 1.5;
            padding: 12px;
        }

        /* Download buttons */
        .download-buttons {
            flex-wrap: wrap;
            gap: 8px;
        }

        .download-buttons button {
            flex: 1;
            min-width: 120px;
            padding: 8px 12px;
            font-size: 13px;
        }

        /* Loading states */
        .loading-spinner {
            width: 20px;
            height: 20px;
        }

        /* Notebook lines adjustment */
        .notebook-paper::before {
            left: 50px; /* Adjust red margin line for mobile */
        }
    }

    @media (max-width: 480px) {
        /* Extra small screens */
        .meeting-header {
            padding: 0.5rem;
        }

        .control-panel {
            padding: 0.5rem;
        }

        .notebook-paper {
            padding: 0.5rem 0.25rem 0.5rem 50px;
            margin: 0 0.25rem;
        }

        .input-container {
            margin: 0.25rem;
        }

        .input-container textarea {
            font-size: 14px !important;
            padding: 10px;
        }

        .meeting-title {
            font-size: 18px;
        }

        .notes-content {
            font-size: 13px;
            padding: 8px;
        }

        .action-buttons {
            bottom: 0.25rem;
            right: 0.25rem;
        }

        .action-buttons button {
            padding: 8px;
            font-size: 11px;
            min-width: 35px;
            height: 35px;
        }

        .download-buttons button {
            min-width: 100px;
            padding: 6px 8px;
            font-size: 12px;
        }

        /* Adjust notebook lines for very small screens */
        .notebook-paper::before {
            left: 40px;
        }

        .notebook-paper {
            padding-left: 45px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="meeting-notes-container">
    <!-- Header -->
    <div class="meeting-header">
        <h1 class="meeting-title">Meeting Notes</h1>
        <div class="text-sm text-gray-400">
            Voice Recording & AI Transcription
        </div>
    </div>

    <!-- Control Panel -->
    <div class="control-panel">
        <button id="recordButton" class="record-button" title="Start/Stop Recording">
            <i class="fas fa-microphone"></i>
        </button>
        
        <div class="status-indicator">
            <div id="statusDot" class="status-dot"></div>
            <span id="statusText">Ready to record</span>
        </div>

        <div class="flex-1"></div>

        <div class="text-sm text-gray-400">
            <span id="recordingTime">00:00</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="meeting-content">
        <!-- Notes Section -->
        <div class="notes-section">
            <div class="notebook-paper">
                <textarea 
                    id="notesTextarea" 
                    class="notes-textarea" 
                    placeholder="Click 'Start' to begin recording. Your meeting notes will appear here as you speak..."
                ></textarea>
            </div>

            <!-- Transcription Status -->
            <div id="transcriptionStatus" class="transcription-status">
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="ml-2">Transcribing...</span>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button id="copyButton" class="action-button" title="Copy notes to clipboard">
                    <i class="fas fa-copy"></i>
                    Copy
                </button>
                <button id="downloadButton" class="action-button" title="Download notes as text file">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class MeetingNotes {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.recordingStartTime = null;
        this.recordingTimer = null;

        this.recordButton = document.getElementById('recordButton');
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        this.recordingTime = document.getElementById('recordingTime');
        this.notesTextarea = document.getElementById('notesTextarea');
        this.transcriptionStatus = document.getElementById('transcriptionStatus');
        this.copyButton = document.getElementById('copyButton');
        this.downloadButton = document.getElementById('downloadButton');

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        this.recordButton.addEventListener('click', () => this.toggleRecording());
        this.copyButton.addEventListener('click', () => this.copyNotes());
        this.downloadButton.addEventListener('click', () => this.downloadNotes());
    }

    async toggleRecording() {
        if (!this.isRecording) {
            await this.startRecording();
        } else {
            this.stopRecording();
        }
    }

    async startRecording() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });

            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start(1000); // Collect data every second

            this.isRecording = true;
            this.recordingStartTime = Date.now();

            this.updateUI();
            this.startTimer();

        } catch (error) {
            console.error('Error starting recording:', error);
            this.showError('Failed to start recording. Please check microphone permissions.');
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            this.mediaRecorder.stop();

            // Stop all tracks to release microphone
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }

        this.isRecording = false;
        this.stopTimer();
        this.updateUI();
    }

    async processRecording() {
        if (this.audioChunks.length === 0) return;

        this.showTranscriptionStatus(true);

        try {
            const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
            const transcription = await this.transcribeAudio(audioBlob);

            if (transcription) {
                this.appendToNotes(transcription);
            }
        } catch (error) {
            console.error('Error processing recording:', error);
            this.showError('Failed to transcribe audio. Please try again.');
        } finally {
            this.showTranscriptionStatus(false);
        }
    }

    async transcribeAudio(audioBlob) {
        // Convert blob to base64 for sending to server
        const base64Audio = await this.blobToBase64(audioBlob);

        try {
            const response = await fetch('/api/meeting-notes/transcribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    audio_data: base64Audio,
                    format: 'webm'
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success) {
                return result.transcription;
            } else {
                throw new Error(result.error || 'Transcription failed');
            }
        } catch (error) {
            console.error('Transcription error:', error);
            return null;
        }
    }

    blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    appendToNotes(text) {
        const currentNotes = this.notesTextarea.value;
        const timestamp = new Date().toLocaleTimeString();
        const newText = currentNotes ? `${currentNotes}\n\n[${timestamp}] ${text}` : `[${timestamp}] ${text}`;

        this.notesTextarea.value = newText;
        this.notesTextarea.scrollTop = this.notesTextarea.scrollHeight;
    }

    updateUI() {
        if (this.isRecording) {
            this.recordButton.classList.add('recording');
            this.recordButton.innerHTML = '<i class="fas fa-stop"></i>';
            this.recordButton.title = 'Stop Recording';

            this.statusDot.classList.add('recording');
            this.statusText.textContent = 'Recording...';
        } else {
            this.recordButton.classList.remove('recording');
            this.recordButton.innerHTML = '<i class="fas fa-microphone"></i>';
            this.recordButton.title = 'Start Recording';

            this.statusDot.classList.remove('recording');
            this.statusText.textContent = 'Ready to record';
        }
    }

    startTimer() {
        this.recordingTimer = setInterval(() => {
            if (this.recordingStartTime) {
                const elapsed = Date.now() - this.recordingStartTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                this.recordingTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }

    stopTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
        this.recordingTime.textContent = '00:00';
    }

    showTranscriptionStatus(show) {
        if (show) {
            this.transcriptionStatus.classList.add('show');
        } else {
            this.transcriptionStatus.classList.remove('show');
        }
    }

    showError(message) {
        // Create a temporary error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-4 py-2 rounded shadow-lg z-50';
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 5000);
    }

    copyNotes() {
        const notes = this.notesTextarea.value;
        if (!notes.trim()) {
            this.showError('No notes to copy');
            return;
        }

        navigator.clipboard.writeText(notes).then(() => {
            // Show success message
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded shadow-lg z-50';
            successDiv.textContent = 'Notes copied to clipboard!';

            document.body.appendChild(successDiv);

            setTimeout(() => {
                document.body.removeChild(successDiv);
            }, 3000);
        }).catch(() => {
            this.showError('Failed to copy notes to clipboard');
        });
    }

    downloadNotes() {
        const notes = this.notesTextarea.value;
        if (!notes.trim()) {
            this.showError('No notes to download');
            return;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `meeting-notes-${timestamp}.txt`;

        const blob = new Blob([notes], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
    }
}

// Initialize the meeting notes app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new MeetingNotes();
});
</script>
{% endblock %}
