{% extends "layout.html" %}

{% block title %}AutoWave - AI-Powered Assistant{% endblock %}

{% block header %}Welcome to AutoWave{% endblock %}

{% block content %}
<div class="hero-section">
    <h2>Your AI-powered Assistants</h2>
    <p class="subtitle">Get comprehensive answers, browse the web, book travel, and complete complex tasks</p>

    <div class="search-container">
        <form action="/autowave" method="get" class="main-search-form">
            <input type="text" name="task" class="search-box" placeholder="Enter a task for AutoWave..." required>
            <button type="submit" class="search-button autowave-button">Use AutoWave</button>
        </form>
    </div>


</div>

<div class="features-section">
    <div class="feature-card">
        <div class="card-header">
            <div class="icon-container">
                <i class="fas fa-search"></i>
            </div>
            <h3>Research Assistant</h3>
        </div>
        <p class="card-description">Actively browsing and collecting data</p>

        <div class="activity-list">
            <div class="activity-item">
                <i class="fas fa-globe"></i>
                <span>Visiting knowledge sources</span>
                <span class="activity-time">2 seconds ago</span>
            </div>
            <div class="activity-item">
                <i class="fas fa-save"></i>
                <span>Saving research data</span>
                <span class="activity-time">10 seconds ago</span>
            </div>
            <div class="activity-item">
                <i class="fas fa-cogs"></i>
                <span>Processing information</span>
                <span class="activity-time">30 seconds ago</span>
            </div>
        </div>

        <a href="/search" class="feature-link">Start Researching</a>
    </div>

    <div class="feature-card">
        <div class="card-header">
            <div class="icon-container">
                <i class="fas fa-comments"></i>
            </div>
            <h3>AI Chat</h3>
        </div>
        <p class="card-description">Having intelligent conversations</p>

        <div class="activity-list">
            <div class="activity-item">
                <i class="fas fa-comment"></i>
                <span>Answering questions</span>
                <span class="activity-time">5 seconds ago</span>
            </div>
            <div class="activity-item">
                <i class="fas fa-brain"></i>
                <span>Processing context</span>
                <span class="activity-time">15 seconds ago</span>
            </div>
            <div class="activity-item">
                <i class="fas fa-lightbulb"></i>
                <span>Generating insights</span>
                <span class="activity-time">25 seconds ago</span>
            </div>
        </div>

        <a href="/chat" class="feature-link">Start Chatting</a>
    </div>
</div>

<style>
    .hero-section {
        text-align: center;
        margin-bottom: 40px;
    }

    .subtitle {
        color: #aaaaaa;
        margin-bottom: 30px;
        font-size: 18px;
    }

    .main-search-form {
        max-width: 600px;
        margin: 0 auto;
    }

    .features-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 40px;
    }

    .feature-card {
        background-color: #1f1f1f;
        color: #fff;
        padding: 25px;
        border-radius: 8px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .icon-container {
        margin-right: 10px;
        font-size: 18px;
    }

    .feature-card h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
    }

    .card-description {
        color: #bbb;
        font-size: 14px;
        margin-bottom: 20px;
    }

    .activity-list {
        margin-bottom: 20px;
    }

    .activity-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .activity-item i {
        width: 20px;
        margin-right: 10px;
        color: #bbb;
    }

    .activity-time {
        margin-left: auto;
        color: #999;
        font-size: 12px;
    }

    .feature-link {
        display: block;
        text-align: center;
        padding: 10px;
        background-color: #2a2a2a;
        color: #fff;
        border-radius: 4px;
        margin-top: 15px;
        font-weight: 500;
        transition: background-color 0.3s ease;
    }

    .feature-link:hover {
        background-color: #444;
        color: #fff;
    }

    @media (max-width: 768px) {
        .features-section {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}
