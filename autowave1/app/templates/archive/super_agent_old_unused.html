{% extends "layout.html" %}

{% block title %}AutoWave - AI Assistant{% endblock %}

{% block header %}AutoWave{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/super_agent.css">
<link rel="stylesheet" href="/static/css/booking_results.css">
{% endblock %}

{% block content %}
<div class="p-6">
    <div class="mb-6 bg-gradient-to-r from-gray-900 to-gray-800 p-4 rounded-lg border-l-4 border-gray-600 shadow-sm">
        <p class="text-gray-300">AutoWave can browse the web, book travel, generate code, and complete complex tasks - all with enhanced AI capabilities.</p>
    </div>

    <!-- Tabs Navigation -->
    <div class="border-b border-gray-700 mb-6">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-white text-white" data-tab="task">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Task
                </span>
            </button>
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-400 hover:text-white hover:border-gray-500" data-tab="browse">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                    </svg>
                    Browse
                </span>
            </button>
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-400 hover:text-white hover:border-gray-500" data-tab="code">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    Code
                </span>
            </button>
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-400 hover:text-white hover:border-gray-500" data-tab="booking">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Booking
                </span>
            </button>
            <button class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-400 hover:text-white hover:border-gray-500" data-tab="history">
                <span class="flex items-center">
                    <svg class="-ml-0.5 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    History
                </span>
            </button>
        </nav>
    </div>

    <!-- Task Tab -->
    <div class="tab-content" id="task-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Execute a Task</h3>
                <p class="text-gray-600 mb-4">Describe a task for the agent to perform. The agent will break it down into steps and execute them.</p>
                <div class="mb-6">
                    <textarea id="taskDescription" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 min-h-[120px]" placeholder="Example: Go to wikipedia.org, search for 'artificial intelligence', and find the section about machine learning"></textarea>
                </div>

                <div class="space-y-4 mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h4 class="font-medium text-gray-700 mb-2">Advanced Options</h4>



                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="useAdvancedBrowser" type="checkbox" class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="useAdvancedBrowser" class="font-medium text-gray-700">Use advanced browser (Playwright/Selenium with screenshots)</label>
                            <p class="text-gray-500">Enable this option for tasks that require visual browsing and screenshots. Works best for booking flights, ordering rides, etc.</p>
                        </div>
                    </div>
                </div>

                <button id="executeTaskBtn" class="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md shadow-sm text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Execute Task
                </button>
            </div>
        </div>

        <!-- Task Progress and Results Container -->
        <div class="mt-8 hidden" id="resultsContainer">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column: Task Progress -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Task Progress</h3>
                        <div class="flex space-x-2">
                            <!-- Clear All Button -->
                            <button id="clearAllTasksBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Clear all tasks">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <!-- Progress Bar -->
                        <div class="mb-6" id="taskProgress">
                            <div class="relative pt-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <div id="processingIndicator" class="relative inline-block">
                                            <div class="h-8 w-8 rounded-full bg-gray-700 animate-spin border-4 border-gray-500 border-t-transparent"></div>
                                            <span class="checkmark absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-sm">✓</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-700">
                                    <div class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gray-400 transition-all duration-500 ease-in-out" id="progressFill" style="width: 0%"></div>
                                </div>
                                <p id="stepDescription" class="text-sm text-gray-400">Analyzing your request and preparing execution plan</p>
                            </div>
                        </div>

                        <!-- Thinking Process Container -->
                        <div id="thinkingContainers" class="space-y-6">
                            <!-- Initial thinking process container -->
                            <div class="thinking-container" id="thinking-container-initial">
                                <div class="bg-gray-50 px-4 py-3 rounded-t-md border border-gray-200 flex justify-between items-center">
                                    <h4 class="font-medium text-gray-700">Thinking Process</h4>
                                    <div class="flex space-x-1">
                                        <button class="goto-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Go to summary" data-task-id="summary-container-initial">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="thinking-process bg-gray-50 p-4 rounded-b-md border-l border-r border-b border-gray-200 h-64 overflow-auto">
                                    <div id="thinkingContent" class="prose prose-sm max-w-none text-sm text-gray-500">
                                        <p class="typing-cursor">Starting analysis and planning process...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Task Summary -->
                <div id="taskSummaryContainer" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Task Summary</h3>
                        <div class="flex space-x-2">
                            <!-- Expand Icon -->
                            <button id="expandSummaryBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Expand to full screen">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-6 overflow-y-scroll h-[800px]" id="summaryContainers">
                        <!-- Initial summary container -->
                        <div class="summary-container" id="summary-container-initial">
                            <div class="bg-gray-50 px-4 py-3 rounded-t-md border border-gray-200 flex justify-between items-center summary-container-header">
                                <h4 class="font-medium text-gray-700">Task Summary</h4>
                                <div class="flex space-x-1">
                                    <!-- Download Icon -->
                                    <button class="download-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Download summary" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                    </button>
                                    <!-- Copy Icon -->
                                    <button class="copy-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Copy to clipboard" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                        </svg>
                                    </button>
                                    <!-- Share Icon -->
                                    <button class="share-summary-btn p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Share summary" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                                        </svg>
                                    </button>
                                    <!-- Mobile Menu Button (only visible on small screens) -->
                                    <button class="mobile-menu-btn md:hidden p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" data-task-id="summary-container-initial">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <!-- Mobile Dropdown Menu -->
                                <div class="mobile-dropdown absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 hidden" data-task-id="summary-container-initial" style="top: 2.5rem;">
                                    <button class="download-summary-btn block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" data-task-id="summary-container-initial">Download</button>
                                    <button class="copy-summary-btn block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" data-task-id="summary-container-initial">Copy to clipboard</button>
                                    <button class="share-summary-btn block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" data-task-id="summary-container-initial">Share</button>
                                </div>
                            </div>
                            <div class="summary-container-content prose prose-img:rounded-lg prose-img:shadow-md prose-img:mx-auto prose-img:max-w-full p-4 border-l border-r border-b border-gray-200 rounded-b-md">
                                <p class="text-center text-gray-500">Task results will appear here</p>
                            </div>
                        </div>
                        <!-- Remove the non-functional container that appears below -->
                        <div id="taskResults" class="hidden"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Browse Tab -->
    <div class="tab-content hidden" id="browse-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Browse the Web</h3>
                <div class="mb-6">
                    <div class="flex">
                        <input type="text" id="urlInput" class="flex-grow px-4 py-3 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200" placeholder="Enter URL (e.g., https://example.com)">
                        <button id="browseBtn" class="px-6 py-3 border border-gray-600 text-base font-medium rounded-r-md shadow-sm text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                            <span class="flex items-center">
                                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                </svg>
                                Browse
                            </span>
                        </button>
                    </div>
                </div>
                <div class="space-y-4 mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h4 class="font-medium text-gray-700 mb-2">Browser Options</h4>

                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="useBrowserUse" type="checkbox" class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="useBrowserUse" class="font-medium text-gray-700">Use browser-use (advanced browser with JavaScript support)</label>
                            <p class="text-gray-500">Enable this option for complex web tasks that require JavaScript. May be slower on older systems.</p>
                        </div>
                    </div>
                </div>

                <div class="flex flex-wrap gap-2">
                    <button id="analyzeBtn" disabled class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                        <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        Analyze Page
                    </button>
                </div>
            </div>
        </div>

        <!-- Browse Results -->
        <div class="mt-8 hidden" id="browseResults">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-6 text-center text-gray-500">
                <p>Browse results will appear here</p>
            </div>
        </div>
    </div>

    <!-- Code Tab -->
    <div class="tab-content hidden" id="code-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Build with Code</h3>
                <p class="text-gray-600 mb-4">Describe what you want to build, and the agent will create it for you with live code generation.</p>
                <div class="mb-6">
                    <textarea id="codePrompt" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200 min-h-[120px]" placeholder="Example: Create a to-do list app with HTML, CSS, and JavaScript"></textarea>
                </div>

                <div class="flex flex-wrap items-center gap-4 mb-6">
                    <div class="flex items-center">
                        <label for="projectType" class="block text-sm font-medium text-gray-700 mr-2">Project Type:</label>
                        <select id="projectType" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black rounded-md">
                            <option value="web">Web Application</option>
                            <option value="script">Script/Program</option>
                            <option value="api">API/Backend</option>
                            <option value="data">Data Analysis</option>
                            <option value="mobile">Mobile App</option>
                        </select>
                    </div>
                    <div class="flex items-center ml-4">
                        <label for="complexity" class="block text-sm font-medium text-gray-700 mr-2">Complexity:</label>
                        <select id="complexity" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-black focus:border-black rounded-md">
                            <option value="simple">Simple</option>
                            <option value="moderate">Moderate</option>
                            <option value="complex">Complex</option>
                        </select>
                    </div>
                </div>

                <button id="buildProjectBtn" class="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md shadow-sm text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    Build Project
                </button>
            </div>
        </div>

        <!-- Code Generation Process -->
        <div class="mt-8 hidden" id="codeGenerationProcess">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column: Code Generation -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                            Code Generation
                        </h3>
                        <div class="flex space-x-2">
                            <button id="copyAllCodeBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Copy All Code">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                            </button>
                            <button id="downloadCodeBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Download Code">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-0 overflow-hidden">
                        <!-- File Tabs -->
                        <div class="bg-gray-100 border-b border-gray-200 overflow-x-auto whitespace-nowrap" id="fileTabs">
                            <!-- Tabs will be dynamically added here -->
                            <button class="px-4 py-2 text-sm font-medium text-gray-700 border-b-2 border-transparent hover:text-black hover:border-gray-300 active-file-tab" data-file="main.js">main.js</button>
                        </div>
                        <!-- Code Editor -->
                        <div class="p-0 relative">
                            <pre id="codeEditor" class="language-javascript text-sm p-4 m-0 h-[500px] overflow-auto bg-gray-50 font-mono"><code id="codeContent">// Code will appear here as it's being generated...</code></pre>
                            <div id="typingIndicator" class="absolute bottom-4 left-4 flex items-center">
                                <div class="typing-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Preview -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            Preview
                        </h3>
                        <div class="flex space-x-2">
                            <button id="refreshPreviewBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Refresh Preview">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                            <button id="openPreviewBtn" class="text-gray-500 hover:text-gray-700 focus:outline-none" title="Open in New Window">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-0 h-[542px] overflow-hidden">
                        <iframe id="previewFrame" class="w-full h-full border-0" sandbox="allow-scripts allow-same-origin allow-forms" srcdoc="<html><body><div style='display:flex;justify-content:center;align-items:center;height:100vh;font-family:sans-serif;color:#666;'>Preview will appear here when code is ready</div></body></html>"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Files List -->
        <div class="mt-6 hidden" id="projectFilesList">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                        </svg>
                        Project Files
                    </h3>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="filesList">
                        <!-- Files will be dynamically added here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Tab -->
    <div class="tab-content hidden" id="booking-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Book Travel & Accommodations</h3>
                <p class="text-gray-600 mb-4">Search for flights, hotels, car rentals, and ride services with real-time pricing and availability.</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Booking Type Selection -->
                    <div>
                        <label for="bookingType" class="block text-sm font-medium text-gray-700 mb-2">What would you like to book?</label>
                        <select id="bookingType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200">
                            <option value="flight">Flight</option>
                            <option value="hotel">Hotel</option>
                            <option value="car">Car Rental</option>
                            <option value="ride">Ride Service</option>
                        </select>
                    </div>

                    <!-- Date Selection -->
                    <div id="dateSelectionContainer">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="startDate" class="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                                <input type="date" id="startDate" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200">
                            </div>
                            <div>
                                <label for="endDate" class="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                                <input type="date" id="endDate" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Origin/Location -->
                    <div id="originContainer">
                        <label for="origin" class="block text-sm font-medium text-gray-700 mb-2">Origin/Location</label>
                        <input type="text" id="origin" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200" placeholder="City or Airport">
                    </div>

                    <!-- Destination (for flights and rides) -->
                    <div id="destinationContainer">
                        <label for="destination" class="block text-sm font-medium text-gray-700 mb-2">Destination</label>
                        <input type="text" id="destination" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200" placeholder="City or Airport">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Travelers/Guests -->
                    <div id="travelersContainer">
                        <label for="travelers" class="block text-sm font-medium text-gray-700 mb-2">Travelers/Guests</label>
                        <select id="travelers" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200">
                            <option value="1">1 Person</option>
                            <option value="2" selected>2 People</option>
                            <option value="3">3 People</option>
                            <option value="4">4 People</option>
                            <option value="5">5+ People</option>
                        </select>
                    </div>

                    <!-- Car Type (for car rentals) -->
                    <div id="carTypeContainer" class="hidden">
                        <label for="carType" class="block text-sm font-medium text-gray-700 mb-2">Car Type</label>
                        <select id="carType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200">
                            <option value="economy">Economy</option>
                            <option value="compact">Compact</option>
                            <option value="midsize">Midsize</option>
                            <option value="standard">Standard</option>
                            <option value="fullsize">Full Size</option>
                            <option value="premium">Premium</option>
                            <option value="luxury">Luxury</option>
                            <option value="suv">SUV</option>
                            <option value="minivan">Minivan</option>
                        </select>
                    </div>

                    <!-- Ride Type (for rides) -->
                    <div id="rideTypeContainer" class="hidden">
                        <label for="rideType" class="block text-sm font-medium text-gray-700 mb-2">Ride Type</label>
                        <select id="rideType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent transition-all duration-200">
                            <option value="standard">Standard</option>
                            <option value="economy">Economy</option>
                            <option value="premium">Premium</option>
                            <option value="xl">XL</option>
                        </select>
                    </div>
                </div>

                <div class="space-y-4 mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h4 class="font-medium text-gray-700 mb-2">Advanced Options</h4>

                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="compareAllProviders" type="checkbox" class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded" checked>
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="compareAllProviders" class="font-medium text-gray-700">Compare all providers</label>
                            <p class="text-gray-500">Search across multiple booking sites to find the best deals</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="flex items-center h-5">
                            <input id="showRealTimeResults" type="checkbox" class="focus:ring-black h-4 w-4 text-black border-gray-300 rounded" checked>
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="showRealTimeResults" class="font-medium text-gray-700">Show real-time results</label>
                            <p class="text-gray-500">Display actual pricing and availability from booking providers</p>
                        </div>
                    </div>
                </div>

                <button id="searchBookingBtn" class="inline-flex items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-md shadow-sm text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Search
                </button>
            </div>
        </div>

        <!-- Booking Results Container -->
        <div class="mt-8 hidden" id="bookingResultsContainer">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column: Search Process -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Search Process</h3>
                    </div>
                    <div class="p-6">
                        <!-- Progress Bar -->
                        <div class="mb-6" id="bookingProgress">
                            <div class="relative pt-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <div id="bookingProcessingIndicator" class="relative inline-block">
                                            <div class="h-8 w-8 rounded-full bg-gray-700 animate-spin border-4 border-gray-500 border-t-transparent"></div>
                                            <span class="checkmark absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-sm">✓</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-700">
                                    <div class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gray-400 transition-all duration-500 ease-in-out" id="bookingProgressFill" style="width: 0%"></div>
                                </div>
                                <p id="bookingStepDescription" class="text-sm text-gray-400">Preparing to search for the best options...</p>
                            </div>
                        </div>

                        <!-- Search Process Container -->
                        <div id="searchProcessContainer" class="space-y-6">
                            <div class="bg-gray-50 px-4 py-3 rounded-md border border-gray-200">
                                <h4 class="font-medium text-gray-700 mb-2">Search Process</h4>
                                <div class="prose prose-sm max-w-none text-sm text-gray-500">
                                    <p>The search process will appear here...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Booking Results -->
                <div id="bookingResultsDisplay" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Booking Results</h3>
                        <div class="flex space-x-2">
                            <!-- Expand Icon -->
                            <button id="expandBookingResultsBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Expand to full screen">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"></path>
                                </svg>
                            </button>
                            <!-- Download Icon -->
                            <button id="downloadBookingResultsBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Download results">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-6 overflow-y-scroll h-[800px]" id="bookingResultsContent">
                        <p class="text-center text-gray-500">Booking results will appear here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Tab -->
    <div class="tab-content hidden" id="history-content">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Session History</h3>
                    <button id="clearHistoryBtn" class="inline-flex items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                        <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Clear History
                    </button>
                </div>

                <div id="historyResults" class="text-center text-gray-500">
                    <p>Session history will appear here</p>
                </div>
            </div>
        </div>
    </div>
</div>




{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/markdown-it@12.0.6/dist/markdown-it.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="/static/css/task_summary_enhanced.css">
<script src="/static/js/markdown_processor.js"></script>
<script src="/static/js/task_container_handlers.js"></script>
<script src="/static/js/simple_input_fixed.js"></script>
<script src="/static/js/code_generator.js"></script>
<script src="/static/js/execute_python.js"></script>
<script src="/static/js/history.js"></script>
<script src="/static/js/tab_switcher.js"></script>
<script src="/static/js/booking_results.js"></script>
<script src="/static/js/booking_handler.js"></script>

<style>
    /* Processing indicator styles */
    #processingIndicator.completed .checkmark {
        display: block;
    }

    #processingIndicator.completed .animate-spin {
        animation: none;
    }

    #processingIndicator .checkmark {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 0.75rem;
    }

    /* Thinking content styles */
    #thinkingContent p {
        margin-bottom: 0.75rem;
        line-height: 1.5;
        animation: fadeIn 0.5s ease-in-out;
    }

    #thinkingContent ul {
        margin-top: 0.5rem;
        margin-bottom: 0.75rem;
        padding-left: 1.5rem;
        list-style-type: disc;
    }

    #thinkingContent li {
        margin-bottom: 0.25rem;
        line-height: 1.5;
    }

    #thinkingContent strong {
        font-weight: 600;
        color: #111827;
    }

    #thinkingContent h1, #thinkingContent h2, #thinkingContent h3, #thinkingContent h4 {
        font-weight: 600;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }

    #thinkingContent code {
        font-family: monospace;
        background-color: #f3f4f6;
        padding: 0.1rem 0.2rem;
        border-radius: 0.25rem;
    }

    /* Task summary styles */
    #taskResults {
        overflow-y: scroll !important;
        height: 800px !important;
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
    }

    #taskResults::-webkit-scrollbar {
        width: 8px;
    }

    #taskResults::-webkit-scrollbar-track {
        background: transparent;
    }

    #taskResults::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
    }

    #taskResults .task-summary {
        overflow: visible;
    }

    #taskResults img {
        max-width: 100%;
        height: auto;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin: 1rem auto;
        display: block;
    }

    /* Screenshot container styles */
    .screenshot-container {
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1.5rem 0;
    }

    .screenshot-container img {
        border: 1px solid #e5e7eb;
        max-height: 500px;
        object-fit: contain;
    }

    .screenshot-zoom-btn, .screenshot-download-btn {
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: all 0.2s;
    }

    .screenshot-zoom-btn:hover, .screenshot-download-btn:hover {
        background-color: #f3f4f6;
    }

    /* Multiple container styles */
    .thinking-container, .summary-container {
        margin-bottom: 1.5rem;
        position: relative;
        background-color: white;
        border-radius: 0.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .thinking-container:last-child, .summary-container:last-child {
        margin-bottom: 0;
    }

    .thinking-container:hover, .summary-container:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .summary-container-header {
        position: relative;
    }

    .mobile-dropdown {
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 10;
    }

    .highlight-container {
        animation: highlight-pulse 2s ease-in-out;
    }

    @keyframes highlight-pulse {
        0%, 100% {
            box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
        }
        50% {
            box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.2);
        }
    }

    #taskResults h1, #taskResults h2, #taskResults h3, #taskResults h4 {
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    #taskResults p {
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    #taskResults ul, #taskResults ol {
        margin-top: 0.5rem;
        margin-bottom: 1rem;
        padding-left: 1.5rem;
    }

    #taskResults li {
        margin-bottom: 0.5rem;
    }

    .typing-cursor::after {
        content: '|';
        display: inline-block;
        animation: blink 1s step-end infinite;
    }

    @keyframes blink {
        from, to { opacity: 1; }
        50% { opacity: 0; }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Animation classes */
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    /* Modal animation */
    #summaryModal {
        transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    }

    #summaryModal.hidden {
        opacity: 0;
        transform: scale(0.95);
        pointer-events: none;
    }

    #summaryModal:not(.hidden) {
        opacity: 1;
        transform: scale(1);
    }
</style>

        });
    });
</script>
<!-- Full Screen Modal for Task Summary -->
<div id="summaryModal" class="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center hidden">
    <div class="bg-gray-900 rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[90vh] flex flex-col border border-gray-700">
        <div class="bg-gray-800 px-6 py-4 border-b border-gray-700 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-white">Task Summary</h3>
            <button id="closeSummaryModal" class="p-1 rounded-md text-gray-400 hover:text-white hover:bg-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="flex-1 p-6 overflow-auto bg-gray-900 text-gray-200" id="modalTaskResults">
            <div class="prose prose-invert max-w-none task-summary" style="overflow: visible;"></div>
        </div>
    </div>
</div>

{% endblock %}
