{% extends "layout.html" %}

{% block title %}Super Agent{% endblock %}

{% block header %}Welcome to Super Agent{% endblock %}

{% block extra_head %}
<style>
    /* Ensure full width for Super Agent container */
    .container.mx-auto {
        max-width: 100% !important;
        width: 100% !important;
    }

    /* Ensure grid maintains full width */
    #resultsContainer .grid {
        width: 100% !important;
    }

    /* Fix for mobile view */
    @media (max-width: 768px) {
        .container.mx-auto {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }
    }

    /* Typing animation styles */
    .typing-cursor {
        border-right: 2px solid #000;
        animation: blink 0.75s step-end infinite;
        white-space: pre-wrap;
        display: inline-block;
    }

    @keyframes blink {
        from, to { border-color: transparent }
        50% { border-color: #000; }
    }

    /* Thinking process styles */
    #thinkingProcess {
        font-family: inherit;
        line-height: 1.6;
    }

    .thinking-step {
        margin-bottom: 0.75rem;
    }

    #thinkingContent p {
        margin-bottom: 0.5rem;
        margin-top: 0;
        font-size: 0.875rem;
        color: #6b7280;
    }

    #thinkingContent ul {
        list-style-type: disc;
        margin-left: 1.5rem;
        margin-bottom: 0.5rem;
        margin-top: 0.5rem;
    }

    #thinkingContent li {
        margin-bottom: 0.25rem;
    }

    #thinkingContent strong {
        font-weight: 600;
        color: #4b5563;
    }

    #thinkingContent h1, #thinkingContent h2, #thinkingContent h3, #thinkingContent h4 {
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    #thinkingContent code {
        background-color: #f1f1f1;
        padding: 0.1rem 0.3rem;
        border-radius: 0.25rem;
        font-family: inherit;
    }

    /* Word-by-word animation */
    .word-appear {
        animation: fadeIn 0.1s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Rotating animation for thinking icon */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    #thinkingIcon {
        animation: spin 1.5s linear infinite;
    }

    /* HTML content styles */
    .html-content img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        margin: 1rem 0;
        display: block;
    }

    .html-content div.my-4 {
        margin: 1.5rem 0;
        text-align: center;
    }

    .html-content p.text-sm {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.5rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 w-full max-w-full">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8 w-full">
        <div class="bg-black text-white px-6 py-4">
            <h2 class="text-xl font-bold">Super Agent</h2>
            <p class="text-gray-300 text-sm mt-1">Your AI assistant that can browse the web, execute tasks, and more.</p>
        </div>

        <div class="border-b border-gray-200">
            <nav class="flex -mb-px">
                <button class="tab-button px-6 py-3 border-b-2 border-black text-black font-medium text-sm" data-tab="task">
                    Task
                </button>
                <button class="tab-button px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" data-tab="browse">
                    Browse
                </button>
                <button class="tab-button px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" data-tab="code">
                    Code
                </button>
            </nav>
        </div>

        <div id="task-content" class="tab-content p-6">
            <div class="mb-6">
                <label for="taskDescription" class="block text-sm font-medium text-gray-700 mb-2">Task Description</label>
                <textarea id="taskDescription" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black" placeholder="Describe the task you want the agent to perform..."></textarea>
            </div>

            <div class="mb-6">
                <div class="flex items-center mb-2">
                    <input id="useBrowserUse" type="checkbox" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded">
                    <label for="useBrowserUse" class="ml-2 block text-sm text-gray-700">Use Browser-Use for web browsing</label>
                </div>
                <div class="flex items-center">
                    <input id="useAdvancedBrowser" type="checkbox" class="h-4 w-4 text-black focus:ring-black border-gray-300 rounded">
                    <label for="useAdvancedBrowser" class="ml-2 block text-sm text-gray-700">Use advanced browser capabilities</label>
                </div>
            </div>

            <div class="flex justify-start">
                <button id="executeTaskBtn" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors duration-200">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Execute Task
                </button>
                <button id="testButton" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors duration-200">
                    Test Button
                </button>
            </div>

            <!-- Task Results Container - Side by Side Layout -->
            <div class="hidden mt-8" id="resultsContainer">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
                <!-- Task Progress Section -->
                <div id="taskProgress" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Task Progress</h3>
                    </div>
                    <div class="p-6">
                        <!-- Progress indicator removed as requested -->
                        <div class="mt-4 flex items-center">
                            <div class="step-icon mr-3 w-8 h-8 flex items-center justify-center bg-black text-white rounded-full">
                                <svg id="thinkingIcon" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 id="stepTitle" class="font-medium">Initializing</h4>
                                <p id="stepDescription" class="text-sm text-gray-500">Preparing to execute your task</p>
                            </div>
                        </div>

                        <!-- Thinking Process Section -->
                        <div class="mt-6">
                            <h4 class="font-medium mb-2">Thinking Process</h4>
                            <div id="thinkingProcess" class="bg-gray-50 p-4 rounded-md border border-gray-200 h-64 overflow-auto">
                                <div id="thinkingContent" class="prose prose-sm max-w-none text-sm text-gray-500">
                                    <p class="typing-cursor">Initializing thinking process...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Task Summary Section -->
                <div id="taskSummaryContainer" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Task Summary</h3>
                        <div class="flex space-x-2">
                            <!-- Expand Icon -->
                            <button id="expandSummaryBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Expand to full screen">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"></path>
                                </svg>
                            </button>
                            <!-- Download Icon -->
                            <button id="downloadSummaryBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Download summary">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                            </button>
                            <!-- Copy Icon -->
                            <button id="copySummaryBtn" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100" title="Copy to clipboard">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="p-6 overflow-auto max-h-[500px] prose prose-img:rounded-lg prose-img:shadow-md prose-img:mx-auto prose-img:max-w-full" id="taskResults"></div>
                </div>
                </div>
            </div>

            <!-- Full Screen Modal for Task Summary -->
            <div id="summaryModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
                <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[90vh] flex flex-col">
                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Task Summary</h3>
                        <button id="closeSummaryModal" class="p-1 rounded-md text-gray-500 hover:text-black hover:bg-gray-100">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-6 overflow-auto flex-grow prose prose-img:rounded-lg prose-img:shadow-md prose-img:mx-auto prose-img:max-w-full" id="modalTaskResults"></div>
                </div>
            </div>
        </div>

        <div id="browse-content" class="tab-content p-6 hidden">
            <div class="mb-6">
                <label for="urlInput" class="block text-sm font-medium text-gray-700 mb-2">URL</label>
                <div class="flex">
                    <input id="urlInput" type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-black focus:border-black" placeholder="Enter a URL to browse...">
                    <button id="browseBtn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors duration-200">
                        Browse
                    </button>
                </div>
            </div>

            <div class="mb-6">
                <button id="analyzeBtn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors duration-200">
                    Analyze Page
                </button>
            </div>

            <div id="browseResults" class="hidden mt-4"></div>
        </div>

        <div id="code-content" class="tab-content p-6 hidden">
            <div class="mb-6">
                <label for="codePrompt" class="block text-sm font-medium text-gray-700 mb-2">Code Prompt</label>
                <textarea id="codePrompt" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black" placeholder="Describe the code you want to generate..."></textarea>
            </div>

            <div class="mb-6">
                <label for="language" class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                <select id="language" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-black focus:border-black">
                    <option value="python">Python</option>
                    <option value="javascript">JavaScript</option>
                    <option value="html">HTML</option>
                    <option value="css">CSS</option>
                    <option value="java">Java</option>
                    <option value="cpp">C++</option>
                    <option value="csharp">C#</option>
                    <option value="php">PHP</option>
                    <option value="ruby">Ruby</option>
                    <option value="go">Go</option>
                    <option value="swift">Swift</option>
                    <option value="kotlin">Kotlin</option>
                </select>
            </div>

            <div class="flex justify-start">
                <button id="generateCodeBtn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors duration-200">
                    Generate Code
                </button>
            </div>

            <div id="codeResults" class="hidden mt-6"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Make sure markdown-it is loaded before our script -->
<script src="https://cdn.jsdelivr.net/npm/markdown-it@12.0.6/dist/markdown-it.min.js"></script>
<!-- Add Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
<script>
    // Verify markdown-it is loaded
    console.log('Markdown-it loaded:', typeof window.markdownit === 'function');
</script>
<script src="/static/js/super_agent.js"></script>

<style>
    /* Additional styling for markdown content */
    .prose h1 { font-size: 1.8rem; font-weight: 700; margin-top: 1.5rem; margin-bottom: 1rem; }
    .prose h2 { font-size: 1.5rem; font-weight: 600; margin-top: 1.5rem; margin-bottom: 0.75rem; }
    .prose h3 { font-size: 1.25rem; font-weight: 600; margin-top: 1.25rem; margin-bottom: 0.5rem; }
    .prose p { margin-bottom: 1rem; line-height: 1.6; }
    .prose ul { list-style-type: disc; margin-left: 1.5rem; margin-bottom: 1rem; }
    .prose ol { list-style-type: decimal; margin-left: 1.5rem; margin-bottom: 1rem; }
    .prose li { margin-bottom: 0.5rem; }
    .prose blockquote { border-left: 4px solid #e5e7eb; padding-left: 1rem; font-style: italic; margin: 1rem 0; }
    .prose code { background-color: #f3f4f6; padding: 0.2rem 0.4rem; border-radius: 0.25rem; font-family: monospace; }
    .prose pre { background-color: #f3f4f6; padding: 1rem; border-radius: 0.25rem; overflow-x: auto; margin: 1rem 0; }
    .prose img { max-width: 100%; height: auto; border-radius: 0.5rem; margin: 1.5rem auto; display: block; transition: all 0.3s ease; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
    .prose img:hover { transform: scale(1.02); box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); cursor: pointer; }
    .prose a { color: #2563eb; text-decoration: none; transition: all 0.3s ease; }
    .prose a:hover { color: #1d4ed8; text-decoration: underline; }
    .prose .my-6 { position: relative; margin-top: 2rem; margin-bottom: 2rem; }
    .prose .my-6 a { display: block; text-decoration: none; }
    .prose .my-6 p { background-color: rgba(255, 255, 255, 0.9); padding: 0.5rem; border-radius: 0.25rem; position: relative; margin-top: -2rem; margin-bottom: 1rem; width: 80%; margin-left: auto; margin-right: auto; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
    .prose hr { border: 0; border-top: 1px solid #e5e7eb; margin: 2rem 0; }
    .prose table { border-collapse: collapse; width: 100%; margin: 1rem 0; }
    .prose th, .prose td { border: 1px solid #e5e7eb; padding: 0.5rem; }
    .prose th { background-color: #f3f4f6; }

    /* Image placeholder styling */
    .image-placeholder {
        width: 100%;
        height: 300px;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 1.5rem auto;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .image-placeholder:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .image-placeholder i {
        font-size: 4rem;
        color: #6b7280;
        margin-bottom: 1rem;
    }

    .image-placeholder .title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        text-align: center;
        padding: 0 1rem;
    }

    .image-placeholder .overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 0.75rem;
        transform: translateY(100%);
        transition: transform 0.3s ease;
    }

    .image-placeholder:hover .overlay {
        transform: translateY(0);
    }

    .image-placeholder .link-text {
        color: #2563eb;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-placeholder .link-text i {
        font-size: 0.875rem;
        margin-left: 0.5rem;
        margin-bottom: 0;
    }
</style>
{% endblock %}
