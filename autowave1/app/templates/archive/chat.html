{% extends "layout.html" %}

{% block title %}Agen911 - Chat{% endblock %}

{% block header %}Welcome to Chat{% endblock %}

{% block content %}
<div class="chat-page">
    <div class="chat-description">
        <p>Have a natural conversation with our AI assistant to get answers to your questions in a conversational format.</p>
    </div>

    {% if error %}
    <div class="error-message">{{ error }}</div>
    {% endif %}

    <div class="chat-interface">
        <div class="chat-messages" id="chatMessages">
            <div class="message ai welcome-message">
                <p>Hello! I'm your AI assistant. How can I help you today?</p>
            </div>
            <!-- Messages will appear here -->
        </div>

        <form method="post" class="chat-input-form">
            <div class="chat-input-container">
                <input type="text" name="message" id="messageInput" placeholder="Type your message here..." required>
                <button type="submit" class="send-button">Send</button>
            </div>
        </form>
    </div>

    <div class="chat-tips">
        <h3>Chat Tips</h3>
        <ul>
            <li>Ask specific questions for more accurate answers</li>
            <li>You can ask follow-up questions to get more details</li>
            <li>For complex topics, consider using the Research feature instead</li>
        </ul>
    </div>
</div>

<style>
    .chat-page {
        max-width: 700px;
        margin: 0 auto;
    }

    .chat-description {
        margin-bottom: 25px;
        color: var(--dark-gray);
        font-size: 17px;
    }

    .error-message {
        background-color: #331111;
        color: #ff6666;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid #662222;
    }

    .chat-interface {
        border: 1px solid #333333;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 30px;
        background-color: #1a1a1a;
    }

    .chat-messages {
        height: 400px;
        overflow-y: auto;
        padding: 20px;
    }

    .message {
        margin-bottom: 20px;
        max-width: 80%;
    }

    .message p {
        margin: 0;
        padding: 0;
    }

    .message.user {
        margin-left: auto;
        background-color: #333333;
        color: white;
        padding: 12px 18px;
        border-radius: 18px 18px 0 18px;
    }

    .message.ai {
        background-color: #222222;
        padding: 12px 18px;
        border-radius: 18px 18px 18px 0;
        border: 1px solid #333333;
        color: #e0e0e0;
    }

    .welcome-message {
        border-left: 3px solid #444444;
    }

    .chat-input-form {
        margin: 0;
    }

    .chat-input-container {
        display: flex;
        padding: 15px;
        background-color: #1a1a1a;
        border-top: 1px solid #333333;
    }

    #messageInput {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #333333;
        border-radius: 8px;
        font-size: 16px;
        background-color: #222222;
        color: #ffffff;
    }

    #messageInput:focus {
        outline: none;
        border-color: #444444;
    }

    .send-button {
        background-color: #333333;
        color: white;
        border: 1px solid #444444;
        padding: 12px 20px;
        border-radius: 8px;
        margin-left: 10px;
        cursor: pointer;
        font-weight: 500;
    }

    .send-button:hover {
        background-color: #444444;
    }

    .chat-tips {
        background-color: #222222;
        padding: 20px;
        border-radius: 8px;
        margin-top: 30px;
        border: 1px solid #333333;
    }

    .chat-tips h3 {
        margin-top: 0;
        font-size: 18px;
        margin-bottom: 15px;
        color: #ffffff;
    }

    .chat-tips ul {
        padding-left: 20px;
    }

    .chat-tips li {
        margin-bottom: 10px;
        color: #aaaaaa;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('.chat-input-form');
        const input = document.getElementById('messageInput');
        const messagesContainer = document.getElementById('chatMessages');

        form.addEventListener('submit', function(e) {
            // Don't prevent default as we want the form to submit normally

            // Add user message to the chat
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.innerHTML = `<p>${input.value}</p>`;
            messagesContainer.appendChild(userMessage);

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        });
    });
</script>
{% endblock %}
