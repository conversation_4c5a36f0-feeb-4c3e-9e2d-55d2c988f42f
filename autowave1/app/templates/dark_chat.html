<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Chat - AutoWave</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .chat-header {
            padding: 1rem 1.5rem;
            background-color: #1e1e1e;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            padding-bottom: 5rem; /* Add extra padding at the bottom to account for fixed input container */
            display: flex;
            flex-direction: column;
        }

        .message {
            margin-bottom: 1.5rem;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            align-self: flex-end;
            background-color: #2d3748;
            color: #e0e0e0;
            border-radius: 18px 18px 4px 18px;
            padding: 12px 16px;
        }

        .ai-message {
            align-self: flex-start;
            background-color: transparent;
            color: #e0e0e0;
            padding: 0;
            max-width: 95%;
        }

        .message-input-container {
            padding: 1rem 1.5rem;
            background-color: #1e1e1e;
            border-top: 1px solid #333;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 100;
            display: flex;
            justify-content: center;
        }

        .message-input-container-inner {
            width: 100%;
            max-width: 1200px;
        }

        .message-input-form {
            position: relative;
        }

        /* Modern Expandable Input Styles for Chat - Research Lab Theme */
        .modern-chat-input-container {
            background-color: #2d2d2d;
            border: 1px solid #444;
            border-radius: 12px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .modern-chat-input-container:focus-within {
            border-color: #555;
            box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
        }

        .modern-chat-expandable-input {
            background: transparent;
            border: none;
            color: #e0e0e0;
            padding: 16px 16px 50px 16px;
            width: 100%;
            font-size: 16px;
            line-height: 1.5;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 200px;
            overflow-y: auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.2s ease;
        }

        .modern-chat-expandable-input::placeholder {
            color: #888;
        }

        .modern-chat-expandable-input::-webkit-scrollbar {
            width: 4px;
        }

        .modern-chat-expandable-input::-webkit-scrollbar-track {
            background: transparent;
        }

        .modern-chat-expandable-input::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 2px;
        }

        .modern-chat-expandable-input::-webkit-scrollbar-thumb:hover {
            background: #666;
        }

        .chat-input-bottom-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: transparent;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-height: 40px;
        }

        .chat-input-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-input-action-btn {
            background: transparent;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px;
            border-radius: 6px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-input-action-btn:hover {
            color: #e0e0e0;
            background: rgba(255, 255, 255, 0.1);
        }

        .send-button {
            background-color: transparent;
            border: none;
            color: #4299e1;
            margin-left: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .send-button:hover {
            background-color: rgba(66, 153, 225, 0.1);
        }

        .file-upload-button {
            position: absolute;
            right: 50px;
            top: 50%;
            transform: translateY(-50%);
            background-color: transparent;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .file-upload-button:hover {
            color: #4299e1;
            background-color: rgba(66, 153, 225, 0.1);
        }

        .hidden {
            display: none !important;
        }

        .typing-indicator {
            align-self: flex-start;
            background-color: #1a202c;
            color: #e0e0e0;
            border-radius: 18px 18px 18px 4px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
        }

        .typing-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #e0e0e0;
            margin: 0 2px;
            opacity: 0.6;
            animation: typing-animation 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing-animation {
            0%, 100% {
                transform: translateY(0);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-4px);
                opacity: 1;
            }
        }

        .markdown-content pre {
            background-color: #2d3748;
            border-radius: 4px;
            padding: 1rem;
            overflow-x: auto;
        }

        .markdown-content code {
            font-family: 'Courier New', monospace;
            color: #e0e0e0;
        }

        .markdown-content p {
            margin-bottom: 1rem;
        }

        .markdown-content ul, .markdown-content ol {
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .home-button {
            color: #e0e0e0;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: color 0.2s;
        }

        .home-button:hover {
            color: #4299e1;
        }

        /* Dark scrollbar styles for AutoWave chat */
        /* Webkit browsers (Chrome, Safari, Edge) */
        ::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        ::-webkit-scrollbar-track {
            background: #1a1a1a;
            border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb {
            background: #444444;
            border-radius: 6px;
            border: 2px solid #1a1a1a;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555555;
        }

        ::-webkit-scrollbar-corner {
            background: #1a1a1a;
        }

        /* Firefox */
        html {
            scrollbar-width: thin;
            scrollbar-color: #444444 #1a1a1a;
        }

        /* Specific dark scrollbar for chat messages */
        .chat-messages {
            scrollbar-width: thin;
            scrollbar-color: #444444 #1a1a1a;
        }

        .chat-messages::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #1a1a1a;
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #444444;
            border-radius: 4px;
            border: 1px solid #1a1a1a;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #555555;
        }

        /* File upload styles for AutoWave Chat */
        .file-preview {
            background-color: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin-top: 0.5rem;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem;
            background-color: #1a202c;
            border-radius: 0.375rem;
            margin-bottom: 0.5rem;
            border: 1px solid #4a5568;
        }

        .file-item:last-child {
            margin-bottom: 0;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .file-icon {
            width: 1.5rem;
            height: 1.5rem;
            margin-right: 0.75rem;
            color: #60a5fa;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: #e0e0e0;
            font-size: 0.875rem;
        }

        .file-size {
            color: #9ca3af;
            font-size: 0.75rem;
        }

        .file-remove {
            background: none;
            border: none;
            color: #ef4444;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }

        .file-remove:hover {
            background-color: #374151;
        }

        .file-image-preview {
            width: 3rem;
            height: 3rem;
            object-fit: cover;
            border-radius: 0.375rem;
            margin-right: 0.75rem;
            border: 1px solid #4a5568;
        }




    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <a href="/" class="home-button">
                <i class="fas fa-arrow-left mr-2"></i>
                <span>Back to Home</span>
            </a>
            <h1 class="text-xl font-semibold">AutoWave Chat</h1>
            <div></div> <!-- Empty div for flex spacing -->
        </div>

        <div id="chat-messages" class="chat-messages">
            <!-- Messages will be added here dynamically -->
        </div>

        <div class="message-input-container">
            <div class="message-input-container-inner">
                <form id="message-form" class="message-input-form">
                    <div class="modern-chat-input-container">
                        <textarea
                            id="message-input"
                            class="modern-chat-expandable-input"
                            placeholder="Type your message here..."
                            autocomplete="off"
                            rows="1"
                        ></textarea>

                        <!-- Bottom Bar with Actions Only -->
                        <div class="chat-input-bottom-bar">
                            <div class="chat-input-actions">
                                <!-- File Upload Button -->
                                <button type="button" id="chatFileUploadBtn" class="chat-input-action-btn" title="Upload file or image">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"></path>
                                    </svg>
                                </button>

                                <!-- Submit Button -->
                                <button type="submit" class="chat-input-action-btn" title="Send message">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M22 2L11 13"></path>
                                        <path d="M22 2L15 22L11 13L2 9L22 2Z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden File Input -->
                    <input type="file" id="chatFileInput" class="hidden" accept="image/*,.txt,.py,.js,.html,.css,.json,.md,.pdf,.doc,.docx" multiple>
                </form>

                <!-- File Preview -->
                <div id="chatFilePreview" class="file-preview mt-2" style="display: none;">
                    <!-- File previews will be added here dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- History Sidebar removed - not functional -->

    <!-- History Sidebar and overlay removed - not functional -->

    <script src="https://cdn.jsdelivr.net/npm/markdown-it@12.0.6/dist/markdown-it.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
    <script src="/static/js/universal_file_upload.js"></script>
    <script src="/static/js/professional_history.js"></script>
    <script>
        // Modern Expandable Input Functionality for Chat
        function initializeChatExpandableInput(inputId) {
            const textarea = document.getElementById(inputId);
            if (!textarea) return;

            // Auto-expand functionality
            function autoExpand() {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
            }

            // Event listeners
            textarea.addEventListener('input', autoExpand);
            textarea.addEventListener('paste', () => setTimeout(autoExpand, 0));

            // Initial setup
            autoExpand();

            // Handle Enter key (submit on Enter, new line on Shift+Enter)
            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const form = textarea.closest('form');
                    if (form && textarea.value.trim()) {
                        form.dispatchEvent(new Event('submit'));
                    }
                }
            });
        }

        // Suggestion function removed - clean input design

        document.addEventListener('DOMContentLoaded', function() {
            const messageForm = document.getElementById('message-form');
            const messageInput = document.getElementById('message-input');
            const chatMessages = document.getElementById('chat-messages');

            // Initialize expandable input
            initializeChatExpandableInput('message-input');

            // Track previous questions to detect repeats
            const previousQuestions = [];

            // Initialize markdown-it with fallback
            let md;
            try {
                if (window.markdownit) {
                    md = window.markdownit({
                        html: true,
                        breaks: true,
                        linkify: true
                    });
                } else {
                    console.warn('markdown-it not loaded, using fallback');
                    md = {
                        render: function(text) {
                            // Simple fallback: convert line breaks to <br> and wrap in <p>
                            return '<p>' + text.replace(/\n/g, '<br>') + '</p>';
                        }
                    };
                }
            } catch (error) {
                console.error('Error initializing markdown-it:', error);
                md = {
                    render: function(text) {
                        // Simple fallback: convert line breaks to <br> and wrap in <p>
                        return '<p>' + text.replace(/\n/g, '<br>') + '</p>';
                    }
                };
            }

            // Add typing cursor style if it doesn't exist
            if (!document.querySelector('style#typing-cursor-style')) {
                const style = document.createElement('style');
                style.id = 'typing-cursor-style';
                style.textContent = `
                    @keyframes blink {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0; }
                    }

                    .typing-cursor {
                        display: inline-block;
                        width: 2px;
                        height: 1em;
                        background-color: #e0e0e0;
                        margin-left: 2px;
                        animation: blink 0.7s infinite;
                        vertical-align: middle;
                    }
                `;
                document.head.appendChild(style);
            }

            // Function to add a message to the chat
            function addMessage(content, isUser = false) {
                try {
                    // Create message element
                    const messageElement = document.createElement('div');
                    messageElement.className = isUser ? 'message user-message' : 'message ai-message';

                    if (isUser) {
                        messageElement.textContent = content;

                        // Add message to chat immediately for user messages
                        chatMessages.appendChild(messageElement);

                        // Scroll to bottom
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    } else {
                        // For AI messages, add empty container first
                        messageElement.className += ' markdown-content';
                        chatMessages.appendChild(messageElement);

                        // Then animate the text word by word (very fast)
                        animateText(messageElement, content, function() {
                            // Add action buttons after animation completes
                            addActionButtons(messageElement, content);
                        });
                    }
                } catch (error) {
                    console.error('Error in addMessage:', error);
                    // Fallback: create a simple message
                    const fallbackElement = document.createElement('div');
                    fallbackElement.className = isUser ? 'message user-message' : 'message ai-message';
                    fallbackElement.textContent = content;
                    chatMessages.appendChild(fallbackElement);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            }

            // Function to animate text word by word
            function animateText(element, content, callback) {
                try {
                    // First render the markdown to get the HTML
                    const renderedHTML = md.render(content);

                // Create a temporary div to hold the rendered HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = renderedHTML;

                // Extract text nodes and their parent elements
                const textNodesMap = [];

                function extractTextNodes(node, parent) {
                    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                        textNodesMap.push({
                            text: node.textContent,
                            parent: parent || node.parentNode
                        });
                    } else if (node.nodeType === Node.ELEMENT_NODE) {
                        // Clone the node without its children
                        const clone = node.cloneNode(false);

                        // Process children
                        for (let child of node.childNodes) {
                            extractTextNodes(child, clone);
                        }

                        // If this is a top-level element, add it to the element
                        if (!parent) {
                            element.appendChild(clone);
                        } else {
                            parent.appendChild(clone);
                        }
                    }
                }

                // Process all nodes
                for (let child of tempDiv.childNodes) {
                    extractTextNodes(child);
                }

                // Now animate the text
                let currentTextNodeIndex = 0;
                let currentWordIndex = 0;
                let currentCharIndex = 0;
                let typingSpeed = 2; // milliseconds per character (faster but still readable)

                // Create typing cursor
                const cursor = document.createElement('span');
                cursor.className = 'typing-cursor';

                function typeNextChar() {
                    if (currentTextNodeIndex >= textNodesMap.length) {
                        // All text has been typed
                        cursor.remove();

                        // Add syntax highlighting if Prism is available
                        if (window.Prism) {
                            element.querySelectorAll('pre code').forEach((block) => {
                                Prism.highlightElement(block);
                            });
                        }

                        // Call callback if provided
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                        return;
                    }

                    const currentTextNode = textNodesMap[currentTextNodeIndex];
                    const text = currentTextNode.text;

                    // If we're at the start of a text node, create a new text node
                    if (currentCharIndex === 0) {
                        currentTextNode.actualTextNode = document.createTextNode('');
                        currentTextNode.parent.appendChild(currentTextNode.actualTextNode);

                        // Add cursor after this text node
                        if (cursor.parentNode) {
                            cursor.parentNode.removeChild(cursor);
                        }
                        currentTextNode.parent.appendChild(cursor);
                    }

                    // Process multiple characters at once for fast but readable typing
                    const charsToProcess = Math.min(3, text.length - currentCharIndex);
                    if (charsToProcess > 0) {
                        const nextChars = text.substring(currentCharIndex, currentCharIndex + charsToProcess);
                        currentTextNode.actualTextNode.textContent += nextChars;
                        currentCharIndex += charsToProcess;
                    }

                    // Move to next text node if needed
                    if (currentCharIndex >= text.length) {
                        currentTextNodeIndex++;
                        currentCharIndex = 0;
                    }

                    // Scroll to bottom
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                    // Schedule next batch of characters
                    setTimeout(typeNextChar, typingSpeed);
                }

                    // Start typing
                    typeNextChar();
                } catch (error) {
                    console.error('Error in animateText:', error);
                    // Fallback: just set the content directly
                    element.innerHTML = content.replace(/\n/g, '<br>');
                }
            }

            // Function to show typing indicator
            function showTypingIndicator() {
                const indicator = document.createElement('div');
                indicator.id = 'typing-indicator';
                indicator.className = 'typing-indicator';
                indicator.innerHTML = `
                    <span class="typing-dot"></span>
                    <span class="typing-dot"></span>
                    <span class="typing-dot"></span>
                `;
                chatMessages.appendChild(indicator);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Function to hide typing indicator
            function hideTypingIndicator() {
                const indicator = document.getElementById('typing-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }

            // Function to add copy button below AI messages
            function addActionButtons(messageElement, messageContent) {
                const copyBtn = document.createElement('button');
                copyBtn.className = 'copy-btn';
                copyBtn.title = 'Copy response';
                copyBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: #9ca3af;
                    cursor: pointer;
                    padding: 6px;
                    border-radius: 6px;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-top: 12px;
                    opacity: 0.7;
                `;

                copyBtn.innerHTML = `
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style="width: 18px; height: 18px;">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184"/>
                    </svg>
                `;

                // Add hover effect
                copyBtn.addEventListener('mouseenter', function() {
                    this.style.color = '#e0e0e0';
                    this.style.backgroundColor = '#2d2d2d';
                    this.style.opacity = '1';
                });

                copyBtn.addEventListener('mouseleave', function() {
                    this.style.color = '#9ca3af';
                    this.style.backgroundColor = 'transparent';
                    this.style.opacity = '0.7';
                });

                // Add copy functionality
                copyBtn.addEventListener('click', function() {
                    const textContent = messageContent || messageElement.textContent;

                    navigator.clipboard.writeText(textContent).then(() => {
                        // Visual feedback
                        const originalTitle = this.getAttribute('title');
                        this.setAttribute('title', 'Copied!');
                        this.style.color = '#60a5fa';

                        setTimeout(() => {
                            this.setAttribute('title', originalTitle);
                            this.style.color = '#9ca3af';
                        }, 1500);

                        showToast('✅ Response copied to clipboard!', 'success');
                    }).catch(err => {
                        console.error('Failed to copy text: ', err);

                        // Fallback for older browsers
                        try {
                            const textArea = document.createElement('textarea');
                            textArea.value = textContent;
                            document.body.appendChild(textArea);
                            textArea.select();
                            document.execCommand('copy');
                            document.body.removeChild(textArea);

                            showToast('✅ Response copied to clipboard!', 'success');
                        } catch (fallbackErr) {
                            console.error('Fallback copy method also failed:', fallbackErr);
                            showToast('❌ Failed to copy response. Please try again.', 'error');
                        }
                    });
                });

                messageElement.appendChild(copyBtn);
            }



            // Function to send message to API
            async function sendMessage(message) {
                console.log('=== sendMessage called with:', message);

                // Check credits before sending message
                if (window.creditSystem) {
                    const canProceed = await window.creditSystem.enforceCredits('autowave_chat_basic');
                    if (!canProceed) {
                        console.log('Insufficient credits for AutoWave Chat');
                        return;
                    }
                }

                // Get uploaded files if available
                let fileContent = '';
                let currentUploadedFiles = [];
                if (window.universalFileUpload) {
                    try {
                        fileContent = window.universalFileUpload.getFileContentForAI('chat');
                        // Get file information for tracking
                        currentUploadedFiles = window.universalFileUpload.getFiles('chat') || [];
                    } catch (fileError) {
                        console.warn('Error getting file content:', fileError);
                        fileContent = '';
                        currentUploadedFiles = [];
                    }
                }

                // Combine message with file content
                const fullMessage = message + fileContent;
                console.log('=== Full message prepared, files:', currentUploadedFiles.length);

                // Check if this is a repeat question
                const isRepeat = previousQuestions.includes(fullMessage.toLowerCase());

                // Add to previous questions
                previousQuestions.push(fullMessage.toLowerCase());

                // Show typing indicator
                showTypingIndicator();

                console.log("Sending message to API:", message);

                // Prepare files for API
                const filesForAPI = currentUploadedFiles.map(file => ({
                    name: file.name,
                    type: file.type,
                    content: file.content,
                    contentType: file.contentType
                }));

                // Send message to API
                fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,  // Send original message, not fullMessage
                        files: filesForAPI
                    }),
                })
                .then(response => {
                    console.log("API response status:", response.status);

                    // Handle credit errors (402 status)
                    if (response.status === 402) {
                        return response.json().then(errorData => {
                            hideTypingIndicator();

                            const creditError = `
## 💳 Insufficient Credits

You need **${errorData.credits_needed || 'more'}** credits to use this feature, but you only have **${errorData.credits_available || 0}** credits remaining.

### 🚀 Get More Credits:
- **Free Plan**: Get 50 free credits every 24 hours
- **Plus Plan ($15/month)**: 1,000 credits monthly
- **Pro Plan ($169/month)**: Unlimited credits

[Upgrade Your Plan](/pricing) to continue using AutoWave!`;

                            addMessage(creditError, false);
                            return null; // Don't process further
                        });
                    }

                    return response.json();
                })
                .then(data => {
                    try {
                        // Skip processing if data is null (credit error already handled)
                        if (!data) {
                            return;
                        }

                        // Hide typing indicator
                        hideTypingIndicator();

                        console.log("API response data:", data);

                        // Update credits if provided in response
                        if (data.credits_consumed && data.remaining_credits !== undefined) {
                            console.log(`💳 Credits consumed: ${data.credits_consumed}, Remaining: ${data.remaining_credits}`);

                            // Update credit display in sidebar if it exists
                            const creditDisplay = document.querySelector('.credit-count, .credits-remaining, [data-credits]');
                            if (creditDisplay) {
                                creditDisplay.textContent = data.remaining_credits;
                                console.log(`💳 Updated credit display to: ${data.remaining_credits}`);
                            }

                            // Update universal credit system if available
                            if (window.universalCreditSystem) {
                                window.universalCreditSystem.updateCredits(data.remaining_credits);
                            }
                        }

                        // Add AI response
                        if (data.response) {
                        // Check if the response is a debug or error message
                        if (data.response.includes("Debug info:") ||
                            data.response.includes("I'm currently experiencing technical difficulties") ||
                            data.response.includes("I'm currently in a simplified mode") ||
                            data.response.includes("I'm currently operating with limited knowledge") ||
                            data.response.includes("API limitations") ||
                            data.response.includes("offline mode") ||
                            data.response.includes("I'd like to help with that, but I'm currently operating with limited functionality")) {

                            // If it's a technical error, provide a more helpful message
                            let friendlyResponse;

                            if (isRepeat) {
                                // Different response for repeat questions
                                friendlyResponse = `
## I'm working on reconnecting

I notice you've asked about "${message}" again. I'm still working on reconnecting to my knowledge sources.

While I'm reconnecting, you can:
- Try a different question
- Wait a few moments and try again
- Refresh the page to establish a new connection

I'll be fully operational soon. Thank you for your patience!`;
                            } else {
                                // Standard friendly response
                                friendlyResponse = `
## Connecting to knowledge sources

I'm currently connecting to my knowledge sources to answer your question about "${message}". This might take a moment.

In the meantime:
- You can try a simpler question
- Wait a few seconds and ask again
- Try refreshing the page if this persists

I'm designed to provide helpful, accurate information and I'll be fully operational shortly.`;
                            }

                            addMessage(friendlyResponse, false);

                            // Automatically retry after a delay (only for first-time questions)
                            if (!isRepeat) {
                                setTimeout(() => {
                                    // Show a follow-up message
                                    addMessage("Let me try again to answer your question...", false);

                                    // Show typing indicator
                                    showTypingIndicator();

                                    // Retry the API call
                                    fetch('/api/chat', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                        body: JSON.stringify({
                                            message: message,
                                            files: filesForAPI
                                        }),
                                    })
                                    .then(response => response.json())
                                    .then(retryData => {
                                        // Hide typing indicator
                                        hideTypingIndicator();

                                        // If we got a proper response this time, show it
                                        if (retryData.response &&
                                            !retryData.response.includes("Debug info:") &&
                                            !retryData.response.includes("I'm currently experiencing technical difficulties") &&
                                            !retryData.response.includes("I'm currently in a simplified mode") &&
                                            !retryData.response.includes("I'm currently operating with limited knowledge") &&
                                            !retryData.response.includes("API limitations") &&
                                            !retryData.response.includes("offline mode") &&
                                            !retryData.response.includes("I'd like to help with that, but I'm currently operating with limited functionality")) {

                                            addMessage(retryData.response, false);
                                        } else {
                                            // If still getting errors, don't show another error message
                                            console.log("Retry also failed, not showing another error message");
                                        }
                                    })
                                    .catch(error => {
                                        // Hide typing indicator
                                        hideTypingIndicator();
                                        console.error('Error in retry:', error);
                                    });
                                }, 5000); // Wait 5 seconds before retrying
                            }
                        } else {
                            // If it's a normal response, display it as is
                            addMessage(data.response, false);

                            // Consume credits after successful response
                            if (window.creditSystem) {
                                window.creditSystem.consumeCredits('autowave_chat_basic').then(result => {
                                    if (result.success) {
                                        console.log('Credits consumed successfully:', result.consumed);
                                    } else {
                                        console.warn('Failed to consume credits:', result.error);
                                    }
                                }).catch(error => {
                                    console.error('Error consuming credits:', error);
                                });
                            }

                            // Track activity in enhanced history
                            if (window.trackActivity) {
                                try {
                                    // Get uploaded files if available
                                    let currentUploadedFiles = [];
                                    if (window.universalFileUpload) {
                                        currentUploadedFiles = window.universalFileUpload.getFiles('chat') || [];
                                    }

                                    window.trackActivity('autowave_chat', 'chat', {
                                        message: message,
                                        files: currentUploadedFiles
                                    }, {
                                        response: data.response,
                                        model_used: data.model_used || 'unknown'
                                    });
                                } catch (trackError) {
                                    console.warn('Error tracking activity:', trackError);
                                    // Don't throw error, just log it
                                }
                            }
                        }
                        } else {
                            addMessage('Sorry, I encountered an error processing your request. Please try again in a moment.', false);

                            // Track failed activity
                            if (window.trackActivity) {
                                try {
                                    // Get uploaded files if available
                                    let currentUploadedFiles = [];
                                    if (window.universalFileUpload) {
                                        currentUploadedFiles = window.universalFileUpload.getFiles('chat') || [];
                                    }

                                    window.trackActivity('autowave_chat', 'chat', {
                                        message: message,
                                        files: currentUploadedFiles
                                    }, null, null, false, 'API request failed');
                                } catch (trackError) {
                                    console.warn('Error tracking failed activity:', trackError);
                                    // Don't throw error, just log it
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Error processing API response:', error);
                        hideTypingIndicator();
                        addMessage('Sorry, I encountered an error processing your request. Please try again in a moment.', false);
                    }
                })
                .catch(error => {
                    // Hide typing indicator
                    hideTypingIndicator();

                    console.error('Catch block error details:', {
                        message: error.message,
                        stack: error.stack,
                        name: error.name,
                        line: error.lineNumber || 'unknown',
                        column: error.columnNumber || 'unknown'
                    });

                    // Add error message
                    addMessage('Sorry, an error occurred while sending your message. Please try again later.', false);
                });
            }

            // Handle form submission
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const message = messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                addMessage(message, true);

                // Clear input
                messageInput.value = '';

                // Clear uploaded files after sending
                if (window.universalFileUpload) {
                    window.universalFileUpload.clearFiles('chat');
                }

                // Send message to API
                sendMessage(message);
            });

            // Check for initial message in URL
            const urlParams = new URLSearchParams(window.location.search);
            let initialMessage = urlParams.get('message');

            if (initialMessage) {
                // Decode the URL parameter properly
                initialMessage = decodeURIComponent(initialMessage);
                console.log("Decoded initial message:", initialMessage);

                // Add user message to chat
                addMessage(initialMessage, true);

                // Send initial message to API
                sendMessage(initialMessage);
            }

            // Add a welcome message if there's no initial message
            if (!initialMessage) {
                const welcomeMessage = `
## Welcome to AutoWave Chat

I'm your AI assistant powered by advanced language models. How can I help you today?

You can ask me about:
- General knowledge questions
- Research topics
- Creative writing
- Code assistance
- Planning and organization
- And much more!

I'm connected to multiple AI providers to ensure reliable responses. Feel free to ask any question, and I'll do my best to assist you.
`;

                setTimeout(() => {
                    addMessage(welcomeMessage, false);
                }, 500);
            }



            // Toast notification function
            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 text-white font-medium max-w-sm`;

                // Set background color based on type
                if (type === 'success') {
                    toast.style.backgroundColor = '#10b981'; // green-500
                } else if (type === 'error') {
                    toast.style.backgroundColor = '#ef4444'; // red-500
                } else {
                    toast.style.backgroundColor = '#3b82f6'; // blue-500
                }

                toast.textContent = message;
                document.body.appendChild(toast);

                // Animate in
                toast.style.transform = 'translateX(100%)';
                toast.style.transition = 'transform 0.3s ease-in-out';
                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 10);

                // Remove after 3 seconds
                setTimeout(() => {
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }
        });
    </script>
</body>
</html>
