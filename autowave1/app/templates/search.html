{% extends "layout.html" %}

{% block title %}ARCHIVED - Agen911 - Research{% endblock %}

{% block header %}ARCHIVED - Welcome to Research{% endblock %}

<!--
ARCHIVED PAGE - MOVED TO ARCHIVE
This research page has been archived because Research Lab now handles all research functionality.
Use Research Lab instead of this page.
-->

{% block content %}
<div class="search-page">
    <div class="search-description">
        <p>Access PhD-level academic research on any topic with comprehensive analysis, theoretical frameworks, and scholarly citations.</p>
    </div>

    {% if error %}
    <div class="error-message">{{ error }}</div>
    {% endif %}

    <div class="search-container">
        <form id="searchForm" class="search-form">
            <input type="text" name="query" id="queryInput" class="search-box" placeholder="Enter an academic topic for PhD-level research..." required>
            <button type="submit" class="search-button">Conduct Research</button>
        </form>
    </div>

    <div id="searchResults" class="results-container" style="display: none;">
        <div class="results-header">
            <div class="results-title">Academic Research Report</div>
            <div class="results-actions">
                <button class="action-button" id="copyButton" title="Copy to clipboard">
                    <span class="action-icon">📋</span> Copy
                </button>
            </div>
        </div>
        <div class="results-content" id="resultsContent">
            <!-- Results will appear here -->
        </div>
        <div class="results-footer">
            <div class="citation">
                Academic Research Report Generated by AutoWave using Llama 3 70B
            </div>
        </div>
    </div>

    <div id="loadingIndicator" style="display: none; text-align: center; margin: 30px 0;">
        <div class="loading-spinner"></div>
        <p class="loading-text">Conducting PhD-level research on your query...</p>
        <div id="researchSteps" class="research-steps">
            <p class="research-step">Generating sophisticated research questions...</p>
        </div>
    </div>

    <div class="search-tips">
        <h3>PhD-Level Research Tips</h3>
        <ul>
            <li>Enter complex academic topics to receive comprehensive, scholarly analysis</li>
            <li>Research includes historical context, theoretical frameworks, and current debates</li>
            <li>Results are backed by academic citations and scholarly perspectives</li>
            <li>For interdisciplinary topics, specify the field (e.g., "quantum computing in economics")</li>
            <li>Allow 30-60 seconds for thorough research to be conducted</li>
        </ul>
    </div>
</div>

<style>
    .search-page {
        max-width: 700px;
        margin: 0 auto;
    }

    .search-description {
        margin-bottom: 25px;
        color: #666;
        font-size: 17px;
    }

    .error-message {
        background-color: #fff1f1;
        color: #e74c3c;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid #ffd1d1;
    }

    .search-form {
        margin-bottom: 30px;
    }

    .search-tips {
        background-color: #f9f9f9;
        padding: 20px;
        border-radius: 8px;
        margin-top: 40px;
        border: 1px solid #eee;
    }

    .search-tips h3 {
        margin-top: 0;
        font-size: 18px;
        margin-bottom: 15px;
        color: #333;
    }

    .search-tips ul {
        padding-left: 20px;
    }

    .search-tips li {
        margin-bottom: 10px;
        color: #666;
    }

    .search-box {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        margin-bottom: 15px;
    }

    .search-button {
        background-color: #4a90e2;
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
    }

    .search-button:hover {
        background-color: #3a7bc8;
    }

    .loading-spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top: 4px solid #4299e1;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    .loading-text {
        color: #a0aec0;
        font-size: 16px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .action-button {
        background-color: #1a202c;
        border: 1px solid #4a5568;
        border-radius: 5px;
        padding: 5px 10px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: background-color 0.3s ease;
        color: #e2e8f0;
        text-decoration: none;
    }

    .action-button:hover {
        background-color: #2d3748;
    }

    .results-container {
        background-color: #2d3748;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        margin: 30px 0;
        border: 1px solid #4a5568;
    }

    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #1a202c;
        border-bottom: 1px solid #4a5568;
    }

    .results-title {
        font-weight: 600;
        font-size: 16px;
        color: #e2e8f0;
    }

    .results-content {
        padding: 25px;
        line-height: 1.8;
        white-space: pre-wrap;
        color: #e2e8f0;
    }

    .results-footer {
        padding: 15px 25px;
        border-top: 1px solid #4a5568;
        background-color: #1a202c;
    }

    .citation {
        font-size: 14px;
        color: #a0aec0;
    }

    .research-steps {
        margin-top: 20px;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        text-align: left;
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 15px;
        border: 1px solid #eee;
    }

    .research-step {
        margin-bottom: 10px;
        padding: 8px 12px;
        border-radius: 4px;
        background-color: #f0f0f0;
        color: #333;
        font-size: 14px;
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchForm = document.getElementById('searchForm');
        const queryInput = document.getElementById('queryInput');
        const searchResults = document.getElementById('searchResults');
        const resultsContent = document.getElementById('resultsContent');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const copyButton = document.getElementById('copyButton');

        // Function to simulate research steps
        function simulateResearchSteps() {
            const researchSteps = document.getElementById('researchSteps');
            const initialSteps = [
                "Generating sophisticated research questions...",
                "Exploring historical context and theoretical frameworks...",
                "Analyzing empirical evidence and research findings...",
                "Examining different scholarly perspectives and debates...",
                "Synthesizing findings into a comprehensive report..."
            ];

            const extendedSteps = [
                "Evaluating methodological approaches in the literature...",
                "Identifying gaps in current research...",
                "Comparing conflicting theoretical models...",
                "Assessing the quality of empirical evidence...",
                "Integrating interdisciplinary perspectives...",
                "Analyzing statistical significance of key findings...",
                "Examining ethical implications of the research...",
                "Evaluating practical applications in various contexts...",
                "Considering limitations of current research paradigms...",
                "Formulating recommendations for future research..."
            ];

            // Combine all steps for longer research sessions
            const allSteps = [...initialSteps, ...extendedSteps];

            researchSteps.innerHTML = ''; // Clear existing steps

            // Add first step immediately
            const firstStep = document.createElement('p');
            firstStep.className = 'research-step';
            firstStep.textContent = allSteps[0];
            researchSteps.appendChild(firstStep);

            // Variable to track if we should continue adding steps
            let continueSteps = true;

            // Function to stop adding new steps
            window.stopResearchSteps = function() {
                continueSteps = false;
            };

            // Add remaining steps with delays, and loop if needed
            let stepIndex = 1;

            function addNextStep() {
                if (!continueSteps) return;

                if (stepIndex < allSteps.length) {
                    const step = document.createElement('p');
                    step.className = 'research-step';
                    step.textContent = allSteps[stepIndex];
                    researchSteps.appendChild(step);

                    // Auto-scroll to the bottom of the steps container
                    researchSteps.scrollTop = researchSteps.scrollHeight;

                    stepIndex++;
                } else {
                    // If we've used all steps, start mixing them up and reusing
                    const randomStep = document.createElement('p');
                    randomStep.className = 'research-step';

                    // Get a random step from all steps
                    const randomIndex = Math.floor(Math.random() * allSteps.length);
                    let stepText = allSteps[randomIndex];

                    // Add some variety by occasionally modifying the step text
                    if (Math.random() > 0.7) {
                        const modifiers = [
                            "Continuing to ", "Further ", "Additionally ", "In depth ",
                            "Thoroughly ", "Systematically ", "Comprehensively "
                        ];
                        const randomModifier = modifiers[Math.floor(Math.random() * modifiers.length)];
                        stepText = randomModifier + stepText.toLowerCase();
                    }

                    randomStep.textContent = stepText;
                    researchSteps.appendChild(randomStep);

                    // Auto-scroll to the bottom of the steps container
                    researchSteps.scrollTop = researchSteps.scrollHeight;
                }

                // Schedule the next step
                if (continueSteps) {
                    setTimeout(addNextStep, 3000); // Show a new step every 3 seconds
                }
            }

            // Start adding steps
            setTimeout(addNextStep, 3000);

            return function() {
                continueSteps = false;
            };
        }

        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const query = queryInput.value.trim();
            if (!query) return;

            // Show loading indicator
            loadingIndicator.style.display = 'block';
            searchResults.style.display = 'none';

            // Start simulating research steps and get the stop function
            const stopResearchSteps = simulateResearchSteps();

            // Make AJAX request
            fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query }),
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Hide loading indicator and stop research steps
                loadingIndicator.style.display = 'none';
                if (stopResearchSteps) stopResearchSteps();

                // Display results
                if (data && data.results) {
                    resultsContent.innerHTML = data.results;
                    searchResults.style.display = 'block';

                    // Update the title to include the query
                    const resultsTitle = document.querySelector('.results-title');
                    if (resultsTitle) {
                        resultsTitle.textContent = `Academic Research: ${query}`;
                    }
                } else if (data && data.error) {
                    resultsContent.innerHTML = `<p class="error">${data.error}</p>`;
                    searchResults.style.display = 'block';
                } else {
                    // Handle unexpected response format
                    resultsContent.innerHTML = `<p class="error">Received an unexpected response format from the server.</p>`;
                    searchResults.style.display = 'block';
                }

                // Scroll to results
                searchResults.scrollIntoView({ behavior: 'smooth' });
            })
            .catch(error => {
                // Hide loading indicator and stop research steps
                loadingIndicator.style.display = 'none';
                if (stopResearchSteps) stopResearchSteps();

                // Display error message
                resultsContent.innerHTML = `
                <h1>Research Error</h1>
                <p class="error">I encountered a technical issue while researching "${query}". This could be due to:</p>
                <ul>
                    <li>High server load at the moment</li>
                    <li>Complexity of the research topic</li>
                    <li>Temporary API limitations</li>
                </ul>
                <p>Please try again in a few moments or try a different research topic.</p>
                <p><small>Error details: ${error.message}</small></p>
                `;
                searchResults.style.display = 'block';

                // Scroll to error message
                searchResults.scrollIntoView({ behavior: 'smooth' });

                console.error('Error fetching research results:', error);
            });
        });

        // Copy button functionality
        if (copyButton) {
            copyButton.addEventListener('click', function() {
                const textToCopy = resultsContent.innerText;
                navigator.clipboard.writeText(textToCopy).then(function() {
                    copyButton.innerHTML = '<span class="action-icon">✓</span> Copied';
                    setTimeout(function() {
                        copyButton.innerHTML = '<span class="action-icon">📋</span> Copy';
                    }, 2000);
                });
            });
        }
    });
</script>
{% endblock %}
