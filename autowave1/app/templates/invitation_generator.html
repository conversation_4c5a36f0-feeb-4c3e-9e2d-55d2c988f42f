<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoWave - Invitation Link Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh;
        }
        .glass { 
            background: rgba(255, 255, 255, 0.1); 
            backdrop-filter: blur(10px); 
            border: 1px solid rgba(255, 255, 255, 0.2); 
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="text-white">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-white bg-opacity-20 p-4 rounded-full mr-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold">🎁 Invitation Link Generator</h1>
            </div>
            <p class="text-white text-opacity-80 text-lg">Create special invitation links for Plus plan promotion</p>
            <div class="mt-4 glass rounded-lg p-4 max-w-2xl mx-auto">
                <h2 class="text-xl font-semibold mb-2">🚀 Special Promotion</h2>
                <p class="text-white text-opacity-90">Pay for 1 month Plus, get 2 months FREE!</p>
                <p class="text-sm text-white text-opacity-70 mt-2">Total: 3 months • 16,000 credits • One-time use per link</p>
            </div>
        </div>

        <!-- Current Invitations -->
        <div class="glass rounded-xl p-6 mb-8 card-hover">
            <div class="flex items-center mb-4">
                <svg class="w-6 h-6 mr-3 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <h2 class="text-2xl font-semibold">📊 Your Invitation Links</h2>
            </div>
            <div id="invitations-list" class="space-y-4">
                <div class="text-center py-8 text-white text-opacity-60">
                    <div class="text-4xl mb-4">🔄</div>
                    <p>Loading your invitation links...</p>
                </div>
            </div>
        </div>

        <!-- Generate New Invitation -->
        <div class="glass rounded-xl p-6 mb-8 card-hover">
            <div class="flex items-center mb-4">
                <svg class="w-6 h-6 mr-3 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <h2 class="text-2xl font-semibold">➕ Generate New Invitation Link</h2>
            </div>
            <form id="generate-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2 text-white text-opacity-90">Description (Optional)</label>
                    <input type="text" id="description" placeholder="e.g., Beta tester invitation for feedback" 
                           class="w-full px-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg focus:border-white focus:border-opacity-50 text-white placeholder-white placeholder-opacity-50 backdrop-blur-sm">
                    <p class="text-xs text-white text-opacity-60 mt-1">Help you identify this invitation link later</p>
                </div>
                
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-20">
                    <h3 class="font-semibold mb-2 text-white">🎯 Promotion Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-300">3</div>
                            <div class="text-white text-opacity-70">Total Months</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-300">16,000</div>
                            <div class="text-white text-opacity-70">Total Credits</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-300">67%</div>
                            <div class="text-white text-opacity-70">Savings</div>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 px-6 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                    🚀 Generate Invitation Link
                </button>
            </form>
        </div>

        <!-- Generated Link -->
        <div id="generated-link" class="glass rounded-xl p-6 card-hover" style="display: none;">
            <div class="flex items-center mb-4">
                <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h2 class="text-2xl font-semibold">✅ Invitation Link Generated!</h2>
            </div>
            <div class="bg-white bg-opacity-10 p-4 rounded-lg mb-4 border border-white border-opacity-20">
                <div class="flex items-center justify-between flex-wrap gap-4">
                    <code id="invitation-url" class="text-green-300 break-all flex-1 min-w-0"></code>
                    <button onclick="copyToClipboard()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded text-sm font-medium transition-colors whitespace-nowrap">
                        📋 Copy Link
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="bg-white bg-opacity-10 p-3 rounded border border-white border-opacity-20">
                    <div class="text-white text-opacity-70">Invitation Code</div>
                    <div id="invitation-code" class="text-lg font-bold text-blue-300"></div>
                </div>
                <div class="bg-white bg-opacity-10 p-3 rounded border border-white border-opacity-20">
                    <div class="text-white text-opacity-70">Status</div>
                    <div class="text-lg font-bold text-green-300">Active</div>
                </div>
            </div>
            <div class="mt-4 p-4 bg-yellow-500 bg-opacity-20 rounded-lg border border-yellow-500 border-opacity-30">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-300 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <div class="font-semibold text-yellow-300">Important Notes:</div>
                        <ul class="text-sm text-white text-opacity-80 mt-1 space-y-1">
                            <li>• Each link can only be used by one person</li>
                            <li>• User has 7 days to subscribe after signup</li>
                            <li>• Promotion applies to Plus plan only</li>
                            <li>• Total credits: 16,000 (2x normal Plus plan)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="text-center mt-8">
            <a href="/" class="inline-flex items-center px-6 py-3 bg-white bg-opacity-10 hover:bg-opacity-20 rounded-lg transition-all duration-300 border border-white border-opacity-20">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <script>
        let currentInvitations = [];

        // Load existing invitations on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadInvitations();
        });

        // Generate form submission
        document.getElementById('generate-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const description = document.getElementById('description').value.trim();
            const submitBtn = e.target.querySelector('button[type="submit"]');
            
            // Show loading state
            submitBtn.innerHTML = '🔄 Generating...';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch('/invitation/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        description: description || `Invitation created on ${new Date().toLocaleDateString()}`
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Show generated link
                    document.getElementById('invitation-url').textContent = data.invitation.url;
                    document.getElementById('invitation-code').textContent = data.invitation.code;
                    document.getElementById('generated-link').style.display = 'block';
                    
                    // Scroll to generated link
                    document.getElementById('generated-link').scrollIntoView({ behavior: 'smooth' });
                    
                    // Reset form
                    document.getElementById('description').value = '';
                    
                    // Reload invitations list
                    loadInvitations();
                    
                    showToast('✅ Invitation link generated successfully!', 'success');
                } else {
                    showToast('❌ Error: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Error generating invitation:', error);
                showToast('❌ Failed to generate invitation link', 'error');
            } finally {
                // Reset button
                submitBtn.innerHTML = '🚀 Generate Invitation Link';
                submitBtn.disabled = false;
            }
        });

        async function loadInvitations() {
            try {
                const response = await fetch('/invitation/list');
                const data = await response.json();
                
                if (data.success) {
                    currentInvitations = data.invitations;
                    displayInvitations(data.invitations);
                } else {
                    console.error('Error loading invitations:', data.error);
                    document.getElementById('invitations-list').innerHTML = `
                        <div class="text-center py-8 text-red-300">
                            <div class="text-4xl mb-4">❌</div>
                            <p>Error loading invitations: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading invitations:', error);
                document.getElementById('invitations-list').innerHTML = `
                    <div class="text-center py-8 text-red-300">
                        <div class="text-4xl mb-4">❌</div>
                        <p>Failed to load invitations</p>
                    </div>
                `;
            }
        }

        function displayInvitations(invitations) {
            const container = document.getElementById('invitations-list');
            
            if (invitations.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-white text-opacity-60">
                        <div class="text-4xl mb-4">📝</div>
                        <p>No invitation links created yet</p>
                        <p class="text-sm mt-2">Generate your first invitation link below!</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = invitations.map(invitation => `
                <div class="bg-white bg-opacity-10 rounded-lg p-4 border border-white border-opacity-20">
                    <div class="flex items-center justify-between flex-wrap gap-4">
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center mb-2">
                                <span class="font-semibold text-white">${invitation.code}</span>
                                <span class="ml-3 px-2 py-1 text-xs rounded-full ${invitation.status === 'Used' ? 'bg-red-500 bg-opacity-30 text-red-200' : 'bg-green-500 bg-opacity-30 text-green-200'}">
                                    ${invitation.status}
                                </span>
                            </div>
                            <p class="text-sm text-white text-opacity-70 mb-2">${invitation.description}</p>
                            <div class="text-xs text-white text-opacity-50">
                                Created: ${new Date(invitation.created_at).toLocaleDateString()}
                                ${invitation.used_at ? ` • Used: ${new Date(invitation.used_at).toLocaleDateString()}` : ''}
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${invitation.status === 'Active' ? `
                                <button onclick="copyInvitationLink('${invitation.url}')" class="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm transition-colors">
                                    📋 Copy
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function copyInvitationLink(url) {
            copyToClipboard(url);
        }

        function copyToClipboard(text = null) {
            const textToCopy = text || document.getElementById('invitation-url').textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(textToCopy).then(() => {
                    showToast('📋 Link copied to clipboard!', 'success');
                }).catch(() => {
                    fallbackCopy(textToCopy);
                });
            } else {
                fallbackCopy(textToCopy);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showToast('📋 Link copied to clipboard!', 'success');
            } catch (err) {
                showToast('❌ Failed to copy link', 'error');
            }
            document.body.removeChild(textArea);
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
                type === 'success' ? 'bg-green-600' : 
                type === 'error' ? 'bg-red-600' : 'bg-blue-600'
            } text-white`;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
