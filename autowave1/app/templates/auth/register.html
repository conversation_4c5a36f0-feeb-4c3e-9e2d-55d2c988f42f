<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - AutoWave</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* AutoWave Theme Colors */
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .auth-container {
            background-color: #1e1e1e;
            border: 1px solid #333;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .auth-input {
            background-color: #2d2d2d;
            border: 1px solid #444;
            color: #e0e0e0;
            border-radius: 8px;
            padding: 12px 16px;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .auth-input:focus {
            border-color: #555;
            outline: none;
            box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
        }

        .auth-input::placeholder {
            color: #888;
        }

        .auth-button {
            background-color: #2d2d2d;
            color: #e0e0e0;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .auth-button:hover {
            background-color: #3d3d3d;
            border-color: #555;
        }

        .auth-button:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
        }

        .auth-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .google-button {
            background-color: #1e1e1e;
            border: 1px solid #444;
            color: #e0e0e0;
        }

        .google-button:hover {
            background-color: #2d2d2d;
        }

        .auth-link {
            color: #aaa;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .auth-link:hover {
            color: #e0e0e0;
        }

        .error-message {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .success-message {
            background-color: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #86efac;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
        }

        .loading-spinner {
            border: 2px solid #333;
            border-top: 2px solid #e0e0e0;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .brand-logo {
            background: linear-gradient(135deg, #e0e0e0, #aaa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 2rem;
        }

        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 8px;
            transition: all 0.3s ease;
        }

        .strength-weak { background-color: #ef4444; }
        .strength-medium { background-color: #f59e0b; }
        .strength-strong { background-color: #10b981; }

        .checkbox-custom {
            appearance: none;
            width: 16px;
            height: 16px;
            border: 1px solid #444;
            border-radius: 3px;
            background-color: #2d2d2d;
            position: relative;
            cursor: pointer;
        }

        .checkbox-custom:checked {
            background-color: #3d3d3d;
            border-color: #555;
        }

        .checkbox-custom:checked::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 2px;
            color: #e0e0e0;
            font-size: 12px;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="auth-container w-full max-w-md p-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <img src="/static/images/autowave-logo.png" alt="AutoWave Logo" class="w-12 h-12" onerror="this.style.display='none'">
                <h1 class="brand-logo">AutoWave</h1>
            </div>
            <p class="text-gray-400">Create your account</p>
        </div>

        <!-- Messages -->
        <div id="message-container"></div>

        <!-- Registration Form -->
        <form id="registerForm" class="space-y-6">
            <!-- Full Name Input -->
            <div>
                <label for="fullName" class="block text-sm font-medium text-gray-300 mb-2">
                    Full Name (Optional)
                </label>
                <div class="relative">
                    <input
                        type="text"
                        id="fullName"
                        name="full_name"
                        class="auth-input pl-10"
                        placeholder="Enter your full name"
                    >
                    <i class="fas fa-user absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"></i>
                </div>
            </div>

            <!-- Email Input -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                </label>
                <div class="relative">
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="auth-input pl-10"
                        placeholder="Enter your email"
                        required
                    >
                    <i class="fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"></i>
                </div>
            </div>

            <!-- Password Input -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                    Password
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="auth-input pl-10 pr-10"
                        placeholder="Create a password"
                        required
                    >
                    <i class="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"></i>
                    <button
                        type="button"
                        id="togglePassword"
                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-300"
                    >
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div id="passwordStrength" class="password-strength"></div>
                <p class="text-xs text-gray-500 mt-1">At least 6 characters</p>
            </div>

            <!-- Confirm Password Input -->
            <div>
                <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">
                    Confirm Password
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="confirmPassword"
                        name="confirm_password"
                        class="auth-input pl-10 pr-10"
                        placeholder="Confirm your password"
                        required
                    >
                    <i class="fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"></i>
                    <button
                        type="button"
                        id="toggleConfirmPassword"
                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-300"
                    >
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div id="passwordMatch" class="text-xs mt-1 hidden"></div>
            </div>

            <!-- Terms and Privacy -->
            <div class="flex items-start">
                <input type="checkbox" id="terms" class="checkbox-custom mt-1 mr-3" required>
                <label for="terms" class="text-sm text-gray-400">
                    I agree to the <a href="#" class="auth-link">Terms of Service</a> and
                    <a href="#" class="auth-link">Privacy Policy</a>
                </label>
            </div>

            <!-- Sign Up Button -->
            <button type="submit" id="signUpBtn" class="auth-button">
                <span id="signUpText">Create Account</span>
                <div id="signUpSpinner" class="loading-spinner mx-auto hidden"></div>
            </button>
        </form>

        <!-- Divider -->
        <div class="my-6 flex items-center">
            <div class="flex-1 border-t border-gray-600"></div>
            <span class="px-4 text-sm text-gray-500">or</span>
            <div class="flex-1 border-t border-gray-600"></div>
        </div>

        <!-- Google Sign Up -->
        <button id="googleSignUp" class="auth-button google-button mb-6">
            <i class="fab fa-google mr-2"></i>
            Continue with Google
        </button>

        <!-- Sign In Link -->
        <div class="text-center">
            <p class="text-gray-400">
                Already have an account?
                <a href="/auth/login" class="auth-link font-medium">Sign in</a>
            </p>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-6">
            <a href="/" class="text-sm auth-link">
                <i class="fas fa-arrow-left mr-1"></i>
                Back to AutoWave
            </a>
        </div>
    </div>

    <script>
        // DOM Elements
        const registerForm = document.getElementById('registerForm');
        const signUpBtn = document.getElementById('signUpBtn');
        const signUpText = document.getElementById('signUpText');
        const signUpSpinner = document.getElementById('signUpSpinner');
        const togglePassword = document.getElementById('togglePassword');
        const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const passwordStrength = document.getElementById('passwordStrength');
        const passwordMatch = document.getElementById('passwordMatch');
        const googleSignUp = document.getElementById('googleSignUp');
        const messageContainer = document.getElementById('message-container');

        // Show message function
        function showMessage(message, type = 'error') {
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
            messageDiv.innerHTML = `<i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'} mr-2"></i>${message}`;

            messageContainer.innerHTML = '';
            messageContainer.appendChild(messageDiv);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;

            return strength;
        }

        // Update password strength indicator
        passwordInput.addEventListener('input', () => {
            const password = passwordInput.value;
            const strength = checkPasswordStrength(password);

            passwordStrength.style.width = `${(strength / 5) * 100}%`;

            if (strength <= 2) {
                passwordStrength.className = 'password-strength strength-weak';
            } else if (strength <= 3) {
                passwordStrength.className = 'password-strength strength-medium';
            } else {
                passwordStrength.className = 'password-strength strength-strong';
            }
        });

        // Check password match
        function checkPasswordMatch() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (confirmPassword.length > 0) {
                if (password === confirmPassword) {
                    passwordMatch.textContent = '✓ Passwords match';
                    passwordMatch.className = 'text-xs mt-1 text-green-400';
                    passwordMatch.classList.remove('hidden');
                } else {
                    passwordMatch.textContent = '✗ Passwords do not match';
                    passwordMatch.className = 'text-xs mt-1 text-red-400';
                    passwordMatch.classList.remove('hidden');
                }
            } else {
                passwordMatch.classList.add('hidden');
            }
        }

        confirmPasswordInput.addEventListener('input', checkPasswordMatch);
        passwordInput.addEventListener('input', checkPasswordMatch);

        // Toggle password visibility
        togglePassword.addEventListener('click', () => {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            const icon = togglePassword.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        });

        toggleConfirmPassword.addEventListener('click', () => {
            const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            confirmPasswordInput.setAttribute('type', type);

            const icon = toggleConfirmPassword.querySelector('i');
            icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
        });

        // Handle registration form submission
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(registerForm);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');

            // Validate passwords match
            if (password !== confirmPassword) {
                showMessage('Passwords do not match');
                return;
            }

            // Validate password strength
            if (password.length < 6) {
                showMessage('Password must be at least 6 characters long');
                return;
            }

            // Show loading state
            signUpBtn.disabled = true;
            signUpText.classList.add('hidden');
            signUpSpinner.classList.remove('hidden');

            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        full_name: formData.get('full_name'),
                        email: formData.get('email'),
                        password: password,
                        confirm_password: confirmPassword
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage(result.message || 'Account created successfully!', 'success');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 2000);
                } else {
                    showMessage(result.error || 'Registration failed');
                }
            } catch (error) {
                showMessage('An error occurred. Please try again.');
            } finally {
                // Reset loading state
                signUpBtn.disabled = false;
                signUpText.classList.remove('hidden');
                signUpSpinner.classList.add('hidden');
            }
        });

        // Handle Google sign up
        googleSignUp.addEventListener('click', () => {
            window.location.href = '/auth/google';
        });
    </script>
</body>
</html>
