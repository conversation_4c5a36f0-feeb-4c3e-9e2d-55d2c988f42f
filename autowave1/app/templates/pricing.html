{% extends "layout.html" %}

{% block header %}AutoWave Pricing{% endblock %}

{% block content %}
<div class="min-h-screen" style="background-color: #121212;">
    <!-- Header Section -->
    <div class="text-center py-12" style="background-color: #1e1e1e;">
        <h1 class="text-4xl font-bold mb-4" style="color: #e0e0e0;">Choose Your AutoWave Plan</h1>
        <p class="text-xl max-w-3xl mx-auto" style="color: #aaa;">
            Unlock the full potential of AI-powered automation with our flexible credit-based pricing
        </p>
    </div>

    <!-- Current Usage Section -->
    <div class="max-w-4xl mx-auto px-6 py-8">
        <div class="rounded-lg p-6 mb-8" style="background-color: #1e1e1e; border: 1px solid #333;">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold" style="color: #e0e0e0;">Current Usage</h2>
                <span class="px-3 py-1 text-sm font-medium rounded-full" style="background-color: #f5f5f5; color: #000;" id="current-plan-display">Loading...</span>
            </div>

            <!-- Credits Display -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium" style="color: #aaa;">Credits Remaining</span>
                    <span class="text-2xl font-bold" style="color: #e0e0e0;" id="credits-display">--</span>
                </div>
                <div class="w-full rounded-full h-3" style="background-color: #333;">
                    <div class="h-3 rounded-full transition-all duration-300 bg-gradient-to-r from-green-500 to-emerald-500" id="credits-progress-bar" style="width: 0%"></div>
                </div>
                <p class="text-sm mt-2" style="color: #888;" id="credits-text">Loading credit information...</p>
            </div>


        </div>
    </div>

    <!-- Billing Toggle -->
    <div class="max-w-4xl mx-auto px-6 py-4">
        <div class="flex justify-center">
            <div class="rounded-full p-1" style="background-color: #2d2d2d;">
                <div class="flex">
                    <button id="monthly-tab" class="px-6 py-2 rounded-full text-sm font-medium transition-all duration-300" style="background-color: #000; color: white;">
                        Monthly
                    </button>
                    <button id="annual-tab" class="px-6 py-2 rounded-full text-sm font-medium transition-all duration-300" style="background: transparent; color: #aaa;">
                        Annual
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pricing Plans -->
    <div class="max-w-6xl mx-auto px-6 py-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
            
            <!-- Free Plan -->
            <div class="rounded-lg p-6 w-full max-w-sm mx-auto" style="background-color: #1e1e1e; border: 1px solid #333;">
                <div class="text-center mb-4">
                    <h3 class="text-xl font-semibold mb-2" style="color: #e0e0e0;">Free Plan</h3>
                    <div class="text-3xl font-bold mb-1" style="color: #e0e0e0;">$0</div>
                    <div class="text-sm mb-4" style="color: #aaa;">per month</div>

                    <button class="w-full py-2 px-4 rounded-lg font-medium cursor-not-allowed mb-6" style="background-color: #2d2d2d; color: #888;">
                        Current Plan
                    </button>
                </div>

                <ul class="space-y-3">
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            50 daily credits
                        </div>
                        <div class="ml-6 mt-1" style="font-style: italic;">
                            (resets daily)
                        </div>
                    </li>
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            AI Agents:
                        </div>
                        <div class="ml-6 mt-1" style="font-style: italic;">
                            (AutoWave Chat)
                        </div>
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Community support
                    </li>
                </ul>
            </div>

            <!-- Plus Plan -->
            <div class="rounded-lg p-6 relative w-full max-w-sm mx-auto" data-plan="plus" style="background-color: #1e1e1e; border: 2px solid; border-image: linear-gradient(90deg, #3b82f6, #8b5cf6) 1;">
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span class="px-3 py-1 rounded-full text-xs font-medium" style="background: linear-gradient(90deg, #3b82f6, #8b5cf6); color: white;">Most Popular</span>
                </div>

                <div class="text-center mb-4">
                    <h3 class="text-xl font-semibold mb-2" style="color: #e0e0e0;">Plus Plan</h3>
                    <div class="monthly-price">
                        <div class="text-3xl font-bold mb-1" style="color: #e0e0e0;">$15</div>
                        <div class="text-sm mb-1" style="color: #aaa;">per month</div>
                        <div class="text-xs mb-4" style="color: #888;">$15/month</div>
                    </div>
                    <div class="annual-price" style="display: none;">
                        <div class="text-3xl font-bold mb-1" style="color: #e0e0e0;">$171</div>
                        <div class="text-sm mb-1" style="color: #aaa;">per year</div>
                        <div class="text-xs mb-4" style="color: #888;">₦282,150/year, billed annually</div>
                    </div>

                    <!-- Referral discount automatically applied via URL parameters -->
                    <div id="referral-discount-plus" class="mb-3" style="display: none;">
                        <div class="text-sm text-green-400 bg-green-900/20 px-3 py-2 rounded-lg border border-green-500/30">
                            <span class="font-semibold">🎉 Referral Discount Applied!</span>
                            <div class="text-xs mt-1" id="referral-details-plus"></div>
                        </div>
                    </div>



                    <!-- Apple Pay removed - Available on Paystack payment page -->

                    <button class="w-full py-2 px-4 rounded-lg font-medium transition-colors mb-6" style="background: linear-gradient(90deg, #3b82f6, #8b5cf6); color: white;" onmouseover="this.style.background='linear-gradient(90deg, #2563eb, #7c3aed)'" onmouseout="this.style.background='linear-gradient(90deg, #3b82f6, #8b5cf6)'" onclick="upgradeToPlan('plus', this)">
                        Upgrade to Plus
                    </button>

                    <!-- Cancel Subscription Option (only shown for subscribed users) -->
                    <div id="plus-cancel-option" class="text-center mb-4 hidden">
                        <span class="text-red-400 hover:text-red-300 cursor-pointer text-sm underline transition-colors"
                              onclick="cancelSubscription('plus')">
                            Cancel subscription
                        </span>
                    </div>
                </div>

                <ul class="space-y-3">
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            8,000 credits per month
                        </div>
                        <div class="ml-6 mt-1" style="font-style: italic;">
                            (~267/day)
                        </div>
                    </li>
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            All 5 AI Agents:
                        </div>
                        <div class="ml-6 mt-1" style="font-style: italic;">
                            (Prime Agent, AutoWave Chat, Code Wave, Agent Wave, Research Lab)
                        </div>
                    </li>
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            12 Prime Agent Tools:
                        </div>
                        <div class="ml-6 mt-1">
                            Job Search, Flight Searching, Hotel Planning, Travel Planning, Real Estate, etc.
                        </div>
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Advanced file upload (100 files)
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Email support
                    </li>
                </ul>
            </div>

            <!-- Pro Plan -->
            <div class="rounded-lg p-6 w-full max-w-sm mx-auto" data-plan="pro" style="background-color: #1e1e1e; border: 1px solid #333;">
                <div class="text-center mb-4">
                    <h3 class="text-xl font-semibold mb-2" style="color: #e0e0e0;">Pro Plan</h3>
                    <div class="monthly-price">
                        <div class="text-3xl font-bold mb-1" style="color: #e0e0e0;">$169</div>
                        <div class="text-sm mb-1" style="color: #aaa;">per month</div>
                        <div class="text-xs mb-4" style="color: #888;">$169/month</div>
                    </div>
                    <div class="annual-price" style="display: none;">
                        <div class="text-3xl font-bold mb-1" style="color: #e0e0e0;">$2,028</div>
                        <div class="text-sm mb-1" style="color: #aaa;">per year</div>
                        <div class="text-xs mb-4" style="color: #888;">$2,028/year, billed annually</div>
                    </div>

                    <!-- Referral discount automatically applied via URL parameters -->
                    <div id="referral-discount-pro" class="mb-3" style="display: none;">
                        <div class="text-sm text-green-400 bg-green-900/20 px-3 py-2 rounded-lg border border-green-500/30">
                            <span class="font-semibold">🎉 Referral Discount Applied!</span>
                            <div class="text-xs mt-1" id="referral-details-pro"></div>
                        </div>
                    </div>



                    <!-- Apple Pay removed - Available on Paystack payment page -->

                    <button class="w-full py-2 px-4 rounded-lg font-medium transition-colors mb-4" style="background: linear-gradient(90deg, #3b82f6, #8b5cf6); color: white;" onmouseover="this.style.background='linear-gradient(90deg, #2563eb, #7c3aed)'" onmouseout="this.style.background='linear-gradient(90deg, #3b82f6, #8b5cf6)'" onclick="upgradeToPlan('pro', this)">
                        Upgrade to Pro
                    </button>

                    <!-- Cancel Subscription Option (only shown for subscribed users) -->
                    <div id="pro-cancel-option" class="text-center mb-4 hidden">
                        <span class="text-red-400 hover:text-red-300 cursor-pointer text-sm underline transition-colors"
                              onclick="cancelSubscription('pro')">
                            Cancel subscription
                        </span>
                    </div>

                    <!-- Refund Policy Link -->
                    <div class="text-center mb-6">
                        <a href="/refund-policy" class="text-xs" style="color: #888; text-decoration: none;" onmouseover="this.style.color='#aaa'" onmouseout="this.style.color='#888'">
                            📋 View Refund Policy
                        </a>
                    </div>
                </div>

                <ul class="space-y-3">
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            200,000 credits per month
                        </div>
                        <div class="ml-6 mt-1" style="font-style: italic;">
                            (~6,667/day)
                        </div>
                    </li>
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            All 5 AI Agents:
                        </div>
                        <div class="ml-6 mt-1" style="font-style: italic;">
                            (Prime Agent, AutoWave Chat, Code Wave, Agent Wave, Research Lab)
                        </div>
                    </li>
                    <li class="text-sm" style="color: #aaa;">
                        <div class="flex items-center">
                            <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                            All 18+ Prime Agent Tools:
                        </div>
                        <div class="ml-6 mt-1">
                            Job Search, Flight Searching, Hotel Planning, Travel Planning, Real Estate, Car Rental, Weather, News, etc.
                        </div>
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Unlimited file upload
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Real-time web browsing
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Priority support
                    </li>
                </ul>
            </div>

            <!-- Enterprise Plan - ARCHIVED FOR PADDLE.COM COMPLIANCE -->
            <!--
            <div class="rounded-lg p-6" style="background-color: #1e1e1e; border: 1px solid #333;">
                <div class="text-center mb-4">
                    <h3 class="text-xl font-semibold mb-2" style="color: #e0e0e0;">Enterprise</h3>
                    <div class="text-3xl font-bold mb-1" style="color: #e0e0e0;">Custom</div>
                    <div class="text-sm mb-4" style="color: #aaa;">pricing</div>

                    <a href="mailto:<EMAIL>" class="w-full py-2 px-4 rounded-lg font-medium transition-colors mb-6 block text-center" style="background: linear-gradient(90deg, #3b82f6, #8b5cf6); color: white; text-decoration: none;" onmouseover="this.style.background='linear-gradient(90deg, #2563eb, #7c3aed)'" onmouseout="this.style.background='linear-gradient(90deg, #3b82f6, #8b5cf6)'">
                        Contact Sales
                    </a>
                </div>

                <ul class="space-y-3">
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Everything in Pro
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Unlimited users & team collaboration
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Custom integrations & API access
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        White-label solutions
                    </li>
                    <li class="flex items-center text-sm" style="color: #aaa;">
                        <i class="fas fa-check mr-2" style="color: #4ade80;"></i>
                        Dedicated support & SLA guarantees
                    </li>
                </ul>
            </div>
            -->
        </div>
    </div>

    <!-- Payment Methods Section -->
    <div class="max-w-4xl mx-auto px-6 py-12">
        <h2 class="text-2xl font-bold text-center mb-8" style="color: #e0e0e0;">Secure Payment Options</h2>

        <div class="rounded-lg p-8" style="background-color: #1e1e1e; border: 1px solid #333;">
            <div class="text-center mb-8">
                <p style="color: #aaa; font-size: 16px;">We accept multiple payment methods for your convenience and security</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                <!-- Apple Pay -->
                <div class="text-center p-4 rounded-lg" style="background-color: #2a2a2a; border: 1px solid #444;">
                    <div class="mb-3">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" style="color: #e0e0e0; margin: 0 auto;">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                    </div>
                    <h4 style="color: #e0e0e0; font-weight: 600; margin-bottom: 8px;">Apple Pay</h4>
                    <p style="color: #aaa; font-size: 12px;">Fast & secure payments with Face ID or Touch ID</p>
                </div>

                <!-- Credit/Debit Cards -->
                <div class="text-center p-4 rounded-lg" style="background-color: #2a2a2a; border: 1px solid #444;">
                    <div class="mb-3">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" style="color: #e0e0e0; margin: 0 auto;">
                            <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
                        </svg>
                    </div>
                    <h4 style="color: #e0e0e0; font-weight: 600; margin-bottom: 8px;">Cards</h4>
                    <p style="color: #aaa; font-size: 12px;">Visa, Mastercard, American Express</p>
                </div>

                <!-- Bank Transfer -->
                <div class="text-center p-4 rounded-lg" style="background-color: #2a2a2a; border: 1px solid #444;">
                    <div class="mb-3">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" style="color: #e0e0e0; margin: 0 auto;">
                            <path d="M11.5 1L2 6v2h20V6m-5 4v7h3v-7M2 17v2h20v-2M6 9v7h3V9m7 0v7h3V9"/>
                        </svg>
                    </div>
                    <h4 style="color: #e0e0e0; font-weight: 600; margin-bottom: 8px;">Bank Transfer</h4>
                    <p style="color: #aaa; font-size: 12px;">Direct bank transfers & USSD</p>
                </div>

                <!-- Mobile Money -->
                <div class="text-center p-4 rounded-lg" style="background-color: #2a2a2a; border: 1px solid #444;">
                    <div class="mb-3">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" style="color: #e0e0e0; margin: 0 auto;">
                            <path d="M17 18c0 .55-.45 1-1 1H8c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h8c.55 0 1 .45 1 1v12zM16 1H8C6.34 1 5 2.34 5 4v16c0 1.66 1.34 3 3 3h8c1.66 0 3-1.34 3-3V4c0-1.66-1.34-3-3-3z"/>
                        </svg>
                    </div>
                    <h4 style="color: #e0e0e0; font-weight: 600; margin-bottom: 8px;">Mobile Money</h4>
                    <p style="color: #aaa; font-size: 12px;">MTN, Airtel, and other providers</p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-8">
                <!-- International Payments -->
                <div class="text-center">
                    <h3 style="color: #4CAF50; font-weight: 600; margin-bottom: 16px; font-size: 18px;">🌍 International Users</h3>
                    <div style="color: #aaa; text-align: left;">
                        <div style="margin-bottom: 12px;">
                            <strong style="color: #e0e0e0;">✓ Apple Pay Available:</strong><br>
                            <span style="font-size: 14px;">Use Apple Pay on iPhone, iPad, or Mac for instant payments</span>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong style="color: #e0e0e0;">✓ Local Currency:</strong><br>
                            <span style="font-size: 14px;">Pay in your local currency with automatic conversion</span>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong style="color: #e0e0e0;">✓ Global Cards:</strong><br>
                            <span style="font-size: 14px;">International Visa, Mastercard, and Amex accepted</span>
                        </div>
                    </div>
                </div>

                <!-- Security Features -->
                <div class="text-center">
                    <h3 style="color: #4CAF50; font-weight: 600; margin-bottom: 16px; font-size: 18px;">🔒 Security & Trust</h3>
                    <div style="color: #aaa; text-align: left;">
                        <div style="margin-bottom: 12px;">
                            <strong style="color: #e0e0e0;">✓ PCI DSS Compliant:</strong><br>
                            <span style="font-size: 14px;">Bank-level security for all transactions</span>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong style="color: #e0e0e0;">✓ SSL Encryption:</strong><br>
                            <span style="font-size: 14px;">256-bit encryption protects your payment data</span>
                        </div>
                        <div style="margin-bottom: 12px;">
                            <strong style="color: #e0e0e0;">✓ No Stored Cards:</strong><br>
                            <span style="font-size: 14px;">We don't store your payment information</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8 p-4 rounded-lg" style="background-color: #0f3460; border: 1px solid #1e5a96;">
                <p style="color: #60a5fa; font-size: 14px; margin-bottom: 8px;">
                    <strong>💡 Pro Tip for International Users:</strong>
                </p>
                <p style="color: #aaa; font-size: 13px;">
                    Apple Pay offers the fastest checkout experience with automatic currency conversion and enhanced security.
                    No need to enter card details - just use Face ID or Touch ID!
                </p>
            </div>
        </div>
    </div>

    <!-- Plan Switching Section -->
    <div class="max-w-4xl mx-auto px-6 py-12" id="plan-switching-section" style="display: none;">
        <div class="bg-gray-800 rounded-lg p-8 border border-gray-700">
            <h2 class="text-2xl font-bold text-center mb-6" style="color: #e0e0e0;">Switch Your Plan</h2>
            <p class="text-center text-gray-400 mb-8">Need a different plan? Switch anytime or contact us for unlimited options.</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Free Plan Switch -->
                <div class="border border-gray-600 rounded-lg p-6 text-center bg-gray-700/30">
                    <h3 class="text-lg font-semibold text-gray-300 mb-2">Free Plan</h3>
                    <div class="text-3xl font-bold text-gray-300 mb-2">$0</div>
                    <div class="text-sm text-gray-400 mb-4">50 credits/day</div>
                    <ul class="text-xs text-gray-400 mb-6 space-y-1">
                        <li>• Daily credit reset</li>
                        <li>• Basic features</li>
                        <li>• Community support</li>
                    </ul>
                    <button onclick="switchPlanFromPricing('free')" class="w-full px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                        Downgrade to Free
                    </button>
                </div>

                <!-- Plus Plan Switch -->
                <div class="border border-blue-500 rounded-lg p-6 text-center bg-blue-900/20">
                    <h3 class="text-lg font-semibold text-blue-300 mb-2">Plus Plan</h3>
                    <div class="text-3xl font-bold text-blue-300 mb-2">$15</div>
                    <div class="text-sm text-gray-400 mb-4">8,000 credits/month</div>
                    <ul class="text-xs text-gray-400 mb-6 space-y-1">
                        <li>• Monthly credits</li>
                        <li>• All agent features</li>
                        <li>• Priority support</li>
                    </ul>
                    <button onclick="switchPlanFromPricing('plus')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Switch to Plus
                    </button>
                </div>

                <!-- Pro Plan Switch -->
                <div class="border border-purple-500 rounded-lg p-6 text-center bg-purple-900/20">
                    <h3 class="text-lg font-semibold text-purple-300 mb-2">Pro Plan</h3>
                    <div class="text-3xl font-bold text-purple-300 mb-2">$169</div>
                    <div class="text-sm text-gray-400 mb-4">200,000 credits/month</div>
                    <ul class="text-xs text-gray-400 mb-6 space-y-1">
                        <li>• Massive credit allowance</li>
                        <li>• All premium features</li>
                        <li>• Dedicated support</li>
                    </ul>
                    <button onclick="switchPlanFromPricing('pro')" class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        Switch to Pro
                    </button>
                </div>
            </div>

            <!-- Contact for Unlimited -->
            <div class="p-6 bg-gray-700 rounded-lg text-center">
                <h4 class="text-xl font-semibold text-gray-300 mb-3">Need Unlimited Access?</h4>
                <p class="text-gray-400 mb-6">Contact our sales team for custom enterprise solutions with unlimited credits, dedicated support, and custom integrations.</p>
                <a href="mailto:<EMAIL>" class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-colors font-semibold">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Contact Sales
                </a>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="max-w-4xl mx-auto px-6 py-12">
        <h2 class="text-2xl font-bold text-center mb-8" style="color: #e0e0e0;">Frequently Asked Questions</h2>

        <div class="space-y-6">
            <div class="rounded-lg p-6" style="background-color: #1e1e1e; border: 1px solid #333;">
                <h3 class="text-lg font-semibold mb-3" style="color: #e0e0e0;">How do credits work?</h3>
                <p style="color: #aaa; margin-bottom: 12px;">Credits are consumed based on task complexity and AI processing requirements:</p>
                <div style="color: #aaa; font-size: 14px;">
                    <div style="margin-bottom: 8px;"><strong style="color: #e0e0e0;">Basic Tasks:</strong></div>
                    <ul style="margin-left: 20px; margin-bottom: 12px;">
                        <li>• Simple chat messages: 1-2 credits</li>
                        <li>• Basic web search: 3-5 credits</li>
                        <li>• Text generation: 2-4 credits</li>
                    </ul>
                    <div style="margin-bottom: 8px;"><strong style="color: #e0e0e0;">Advanced Tasks:</strong></div>
                    <ul style="margin-left: 20px; margin-bottom: 12px;">
                        <li>• Code Wave (website creation): 15-25 credits</li>
                        <li>• Agent Wave (document processing): 10-20 credits</li>
                        <li>• Prime Agent tools: 8-15 credits</li>
                        <li>• Research Lab (deep analysis): 20-30 credits</li>
                    </ul>
                    <div style="margin-bottom: 8px;"><strong style="color: #e0e0e0;">Credit Rollover:</strong></div>
                    <p>• Plus Plan: Unused monthly credits roll over (up to 50% of monthly allocation)</p>
                    <p>• Pro Plan: Unused monthly credits roll over (up to 30% of monthly allocation)</p>
                    <p>• Free Plan: Daily credits reset each day</p>
                </div>
            </div>

            <div class="rounded-lg p-6" style="background-color: #1e1e1e; border: 1px solid #333;">
                <h3 class="text-lg font-semibold mb-2" style="color: #e0e0e0;">Can I upgrade or downgrade anytime?</h3>
                <p style="color: #aaa;">Yes! You can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at the start of your next billing cycle.</p>
            </div>

            <div class="rounded-lg p-6" style="background-color: #1e1e1e; border: 1px solid #333;">
                <h3 class="text-lg font-semibold mb-2" style="color: #e0e0e0;">What happens if I run out of credits?</h3>
                <p style="color: #aaa;">You can purchase additional credits or upgrade to a higher plan. Your account won't be suspended, but you'll need more credits to continue using premium features.</p>
            </div>
        </div>
    </div>
</div>

<!-- Apple Pay styles removed - Using Paystack's native Apple Pay -->

<script>
// Billing toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const monthlyTab = document.getElementById('monthly-tab');
    const annualTab = document.getElementById('annual-tab');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const annualPrices = document.querySelectorAll('.annual-price');

    function showMonthly() {
        // Update tab styles
        monthlyTab.style.backgroundColor = '#000';
        monthlyTab.style.color = 'white';
        annualTab.style.background = 'transparent';
        annualTab.style.color = '#aaa';

        // Show/hide prices
        monthlyPrices.forEach(price => price.style.display = 'block');
        annualPrices.forEach(price => price.style.display = 'none');
    }

    function showAnnual() {
        // Update tab styles
        annualTab.style.backgroundColor = '#000';
        annualTab.style.color = 'white';
        monthlyTab.style.background = 'transparent';
        monthlyTab.style.color = '#aaa';

        // Show/hide prices
        monthlyPrices.forEach(price => price.style.display = 'none');
        annualPrices.forEach(price => price.style.display = 'block');
    }

    monthlyTab.addEventListener('click', showMonthly);
    annualTab.addEventListener('click', showAnnual);
});

// Credit indicator functionality
function updateCreditsDisplay(credits, maxCredits, creditType = 'monthly') {
    console.log('Updating credits display:', { credits, maxCredits, creditType });

    const creditsDisplay = document.getElementById('credits-display');
    const progressBar = document.getElementById('credits-progress-bar');

    // Check if this is an admin user with unlimited credits
    const isUnlimited = credits === -1 || maxCredits === -1 || creditType === 'unlimited';

    if (creditsDisplay) {
        if (isUnlimited) {
            creditsDisplay.textContent = 'Unlimited';
            creditsDisplay.style.color = '#7c3aed'; // Purple color for admin
            console.log('Updated credits display text to: Unlimited (Admin)');
        } else {
            creditsDisplay.textContent = credits;
            console.log('Updated credits display text to:', credits);
        }
    }

    if (progressBar) {
        if (isUnlimited) {
            // Admin users get full purple progress bar
            progressBar.style.width = '100%';
            progressBar.className = 'h-3 rounded-full transition-all duration-300 bg-gradient-to-r from-purple-500 to-blue-500';
            console.log('Applied purple color scheme for admin unlimited credits');
        } else {
            const percentage = (credits / maxCredits) * 100;
            progressBar.style.width = percentage + '%';
            console.log('Updated progress bar width to:', percentage + '%');

            // Change color based on credit level
            if (credits <= 10) {
                progressBar.className = 'h-3 rounded-full transition-all duration-300 bg-gradient-to-r from-red-600 to-red-800';
                if (creditsDisplay) creditsDisplay.style.color = '#dc2626';
                console.log('Applied red color scheme for low credits');
            } else if (credits <= 25) {
                progressBar.className = 'h-3 rounded-full transition-all duration-300 bg-gradient-to-r from-yellow-500 to-orange-500';
                if (creditsDisplay) creditsDisplay.style.color = '#d97706';
                console.log('Applied yellow color scheme for medium credits');
            } else {
                progressBar.className = 'h-3 rounded-full transition-all duration-300 bg-gradient-to-r from-green-500 to-emerald-500';
                if (creditsDisplay) creditsDisplay.style.color = '#059669';
                console.log('Applied green color scheme for high credits');
            }
        }
    }
}

// Update plan buttons based on current user plan
function updatePlanButtons(currentPlan) {
    // Reset all buttons first
    const allButtons = document.querySelectorAll('[data-plan] button');
    allButtons.forEach(button => {
        button.style.backgroundColor = '';
        button.style.background = 'linear-gradient(90deg, #3b82f6, #8b5cf6)';
        button.style.color = 'white';
        button.disabled = false;
        button.textContent = button.textContent.includes('Upgrade') ? button.textContent : 'Upgrade to ' + button.closest('[data-plan]').getAttribute('data-plan').charAt(0).toUpperCase() + button.closest('[data-plan]').getAttribute('data-plan').slice(1);
    });

    // Update current plan button
    if (currentPlan && currentPlan !== 'free') {
        const currentPlanCard = document.querySelector(`[data-plan="${currentPlan}"]`);
        if (currentPlanCard) {
            const button = currentPlanCard.querySelector('button');
            if (button) {
                button.style.background = '#2d2d2d';
                button.style.color = '#888';
                button.disabled = true;
                button.textContent = 'Current Plan';
            }
        }
    }

    // Update free plan button
    const freePlanButton = document.querySelector('.rounded-lg:not([data-plan]) button');
    if (freePlanButton) {
        if (currentPlan === 'free') {
            freePlanButton.style.background = '#2d2d2d';
            freePlanButton.style.color = '#888';
            freePlanButton.disabled = true;
            freePlanButton.textContent = 'Current Plan';
        } else {
            freePlanButton.style.background = '#2d2d2d';
            freePlanButton.style.color = '#888';
            freePlanButton.disabled = true;
            freePlanButton.textContent = 'Downgrade to Free';
        }
    }
}

// Initialize with current user credits (this would come from the backend)
document.addEventListener('DOMContentLoaded', function() {
    // Load user's current plan and credit information
    loadUserPlanInfo();
    // Load pricing with currency conversion
    loadPricingWithCurrency();

    // Integrate with Universal Credit System
    initializeUniversalCreditIntegration();
});

// Integration with Universal Credit System
function initializeUniversalCreditIntegration() {
    console.log('Initializing Universal Credit System integration for pricing page...');

    // Wait for Universal Credit System to be ready
    const checkCreditSystem = setInterval(() => {
        if (window.creditSystem && window.creditSystem.isInitialized) {
            console.log('Universal Credit System is ready, updating pricing page...');

            // Update pricing page with current credits
            const creditStatus = window.creditSystem.getCreditStatus();
            updateCreditsDisplay(creditStatus.remaining, creditStatus.total, creditStatus.type);

            // Update plan display
            const planDisplay = document.getElementById('current-plan-display');
            if (planDisplay) {
                // Check if admin user with unlimited credits
                const isUnlimited = creditStatus.remaining === 'unlimited' || creditStatus.remaining === -1 || creditStatus.type === 'unlimited';

                if (isUnlimited) {
                    planDisplay.textContent = 'Admin Plan';
                    planDisplay.style.backgroundColor = '#7c3aed'; // Purple for admin
                    planDisplay.style.color = '#fff';
                } else {
                    planDisplay.textContent = creditStatus.plan.charAt(0).toUpperCase() + creditStatus.plan.slice(1) + ' Plan';

                    // Update plan display styling based on plan
                    if (creditStatus.plan === 'pro') {
                        planDisplay.style.backgroundColor = '#059669';
                        planDisplay.style.color = '#fff';
                    } else if (creditStatus.plan === 'plus') {
                        planDisplay.style.backgroundColor = '#fbbf24';
                        planDisplay.style.color = '#000';
                    } else {
                        planDisplay.style.backgroundColor = '#f5f5f5';
                        planDisplay.style.color = '#000';
                    }
                }
            }

            clearInterval(checkCreditSystem);
        }
    }, 100);

    // Clear interval after 10 seconds if credit system doesn't load
    setTimeout(() => {
        clearInterval(checkCreditSystem);
    }, 10000);
}

// Load user plan and credit information
async function loadUserPlanInfo() {
    try {
        console.log('Loading user plan info...');
        const response = await fetch('/payment/user-info');
        const data = await response.json();
        console.log('User info response:', data);

        if (data.success) {
            const userInfo = data.user_info;
            console.log('Processing user info:', userInfo);

            // Update current plan display
            const planDisplay = document.getElementById('current-plan-display');
            if (planDisplay) {
                // Check if admin user with unlimited credits
                const isUnlimited = userInfo.credits && (userInfo.credits.remaining === -1 || userInfo.credits.total === -1 || userInfo.credits.type === 'unlimited') || userInfo.plan_name === 'admin';

                if (isUnlimited) {
                    planDisplay.textContent = 'Admin Plan';
                    planDisplay.style.backgroundColor = '#7c3aed'; // Purple for admin
                    planDisplay.style.color = '#fff';
                    console.log('Updated plan display to: Admin Plan');
                } else {
                    planDisplay.textContent = userInfo.display_name || 'Free Plan';
                    console.log('Updated plan display to:', userInfo.display_name);

                    // Update plan badge color based on plan
                    if (userInfo.plan_name === 'free') {
                        planDisplay.style.backgroundColor = '#f5f5f5';
                        planDisplay.style.color = '#000';
                    } else if (userInfo.plan_name === 'plus') {
                        planDisplay.style.background = 'linear-gradient(90deg, #3b82f6, #8b5cf6)';
                        planDisplay.style.color = 'white';
                    } else if (userInfo.plan_name === 'pro') {
                        planDisplay.style.backgroundColor = '#fbbf24';
                        planDisplay.style.color = '#000';
                    }
                }
            }

            // Update credits display
            if (userInfo.credits) {
                const credits = userInfo.credits;
                console.log('Updating credits display:', credits);
                updateCreditsDisplay(credits.remaining, credits.total, credits.type);

                // Update credits text based on type
                const creditsText = document.getElementById('credits-text');
                if (creditsText) {
                    // Check if admin user with unlimited credits
                    const isUnlimited = credits.remaining === -1 || credits.total === -1 || credits.type === 'unlimited';

                    if (isUnlimited) {
                        creditsText.textContent = 'Admin Plan - Unlimited credits available';
                        creditsText.style.color = '#7c3aed'; // Purple color for admin
                    } else if (credits.type === 'daily') {
                        creditsText.textContent = `${credits.remaining} credits remaining today (resets daily)`;
                        creditsText.style.color = '#888'; // Reset to default color
                    } else {
                        creditsText.textContent = `${credits.remaining} credits remaining this month`;
                        creditsText.style.color = '#888'; // Reset to default color
                    }
                    console.log('Updated credits text to:', creditsText.textContent);
                }
            }

            // Update plan buttons based on current plan
            updatePlanButtons(userInfo.plan_name);
            console.log('User plan info loaded successfully');
        } else {
            console.error('API returned success: false', data);
        }
    } catch (error) {
        console.error('Error loading user plan info:', error);
        // Fallback to free plan data
        const planDisplay = document.getElementById('current-plan-display');
        if (planDisplay) {
            planDisplay.textContent = 'Free Plan';
            planDisplay.style.backgroundColor = '#f5f5f5';
            planDisplay.style.color = '#000';
        }

        updateCreditsDisplay(50, 50, 'daily');
        const creditsText = document.getElementById('credits-text');
        if (creditsText) {
            creditsText.textContent = '50 credits remaining today (resets daily)';
        }
        console.log('Applied fallback user info');
    }
}

// Load pricing with currency conversion
async function loadPricingWithCurrency() {
    try {
        // Load dynamic pricing from API
        console.log('Loading dynamic pricing from API');

        // Force USD currency for Apple Pay compatibility
        let userLocation = 'US';
        console.log('Using USD currency for Apple Pay compatibility');

        const response = await fetch(`/payment/plans?location=${userLocation}`);
        const data = await response.json();

        if (data.success) {
            updatePricingDisplay(data.plans, data.provider, data.currency_info);
        }
    } catch (error) {
        console.error('Error loading pricing:', error);
    }
}

// Update pricing display with converted currencies
function updatePricingDisplay(plans, provider, currencyInfo) {
    plans.forEach(plan => {
        if (plan.name === 'free') return; // Skip free plan

        const planCard = document.querySelector(`[data-plan="${plan.name}"]`);
        if (!planCard) return;

        // Update monthly prices with USD + Naira display
        const monthlyPrice = planCard.querySelector('.monthly-price');
        if (monthlyPrice && plan.pricing.monthly) {
            const pricing = plan.pricing.monthly;
            // Calculate Naira equivalent (1 USD = 1650 NGN)
            const nairaAmount = pricing.usd * 1650;

            monthlyPrice.innerHTML = `
                <div class="price-display">
                    <span class="text-3xl font-bold">$${pricing.usd}</span>
                    <div class="text-sm text-gray-400 mt-1">₦${nairaAmount.toLocaleString()}</div>
                </div>
            `;
        }

        // Update annual prices with USD + Naira display
        const annualPrice = planCard.querySelector('.annual-price');
        if (annualPrice && plan.pricing.annual) {
            const pricing = plan.pricing.annual;
            // Calculate Naira equivalent (1 USD = 1650 NGN)
            const nairaAmount = pricing.usd * 1650;

            annualPrice.innerHTML = `
                <div class="price-display">
                    <span class="text-3xl font-bold">$${pricing.usd}</span>
                    <div class="text-sm text-gray-400 mt-1">₦${nairaAmount.toLocaleString()}</div>
                </div>
            `;
        }
    });

    // Always show currency info for dual display
    showCurrencyInfo({
        currency: 'USD',
        symbol: '$',
        secondary_currency: 'NGN',
        secondary_symbol: '₦',
        exchange_rate: 1650
    }, provider);
}

// Show currency conversion info
function showCurrencyInfo(currencyInfo, provider) {
    const existingInfo = document.getElementById('currency-info');
    if (existingInfo) existingInfo.remove();

    const currencyDiv = document.createElement('div');
    currencyDiv.id = 'currency-info';
    currencyDiv.className = 'bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mb-6 text-center';
    currencyDiv.innerHTML = `
        <div class="flex items-center justify-center space-x-2">
            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-blue-300">
                Prices shown in ${currencyInfo.currency} (${currencyInfo.symbol}) for ${provider === 'paystack' ? 'African' : 'local'} payments
            </span>
        </div>
        <div class="text-sm text-gray-400 mt-1">
            USD prices shown for reference • Exchange rates updated daily
        </div>
    `;

    const pricingContainer = document.querySelector('.pricing-container') || document.querySelector('main');
    if (pricingContainer) {
        pricingContainer.insertBefore(currencyDiv, pricingContainer.firstChild);
    }
}

// Validate referral code
async function validateReferralCode(referralCode) {
    try {
        const response = await fetch('/referral/apply-code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                referral_code: referralCode
            })
        });

        return await response.json();
    } catch (error) {
        console.error('Error validating referral code:', error);
        return { success: false, error: 'Failed to validate referral code' };
    }
}

// Show referral feedback
function showReferralFeedback(inputElement, isValid, message, discountPercentage = 0, bonusCredits = 0) {
    // Get the feedback div for this input
    const feedbackId = inputElement.id.replace('referral-', 'referral-feedback-');
    const feedbackDiv = document.getElementById(feedbackId);

    if (!feedbackDiv) return;

    // Update feedback content
    if (isValid && discountPercentage > 0) {
        feedbackDiv.innerHTML = `<span style="color: #4ade80;">✓ ${discountPercentage}% off + ${bonusCredits} bonus credits!</span>`;
        inputElement.style.borderColor = '#4ade80';

        // Update pricing display
        updatePricingWithDiscount(discountPercentage);
    } else if (isValid) {
        feedbackDiv.innerHTML = `<span style="color: #4ade80;">✓ Referral code applied!</span>`;
        inputElement.style.borderColor = '#4ade80';
    } else {
        feedbackDiv.innerHTML = `<span style="color: #ef4444;">✗ ${message}</span>`;
        inputElement.style.borderColor = '#ef4444';
    }

    // Clear feedback after 5 seconds if valid
    if (isValid) {
        setTimeout(() => {
            if (feedbackDiv && !inputElement.value.trim()) {
                feedbackDiv.innerHTML = '';
                inputElement.style.borderColor = '#444';
            }
        }, 5000);
    }
}

// Handle plan upgrade
async function upgradeToPlan(planName, buttonElement) {
    let button = null;
    let originalText = '';

    try {
        // Show loading state
        button = buttonElement || event.target;
        originalText = button.textContent;
        button.textContent = 'Processing...';
        button.disabled = true;

        // Get billing cycle (monthly or annual)
        const isAnnual = document.getElementById('annual-tab').style.backgroundColor === 'rgb(0, 0, 0)';
        const billingCycle = isAnnual ? 'annual' : 'monthly';

        // Get subscription plans
        const plansResponse = await fetch('/payment/plans');
        const plansData = await plansResponse.json();

        if (!plansData.success) {
            throw new Error('Failed to load subscription plans');
        }

        console.log('Available plans:', plansData.plans);
        console.log('Looking for plan:', planName);

        // Find the selected plan
        const selectedPlan = plansData.plans.find(plan => plan.name === planName);
        if (!selectedPlan) {
            console.error('Plan not found. Available plans:', plansData.plans.map(p => p.name));
            throw new Error(`Selected plan "${planName}" not found. Available plans: ${plansData.plans.map(p => p.name).join(', ')}`);
        }

        // Get referral code if provided
        const referralInput = document.getElementById(`referral-${planName}`);
        const referralCode = referralInput ? referralInput.value.trim() : '';

        // Apply referral code if provided
        if (referralCode) {
            const referralResult = await validateReferralCode(referralCode);
            if (!referralResult.success) {
                showReferralFeedback(referralInput, false, referralResult.error || 'Invalid referral code');
                throw new Error('Invalid referral code');
            }
        }

        // Create subscription
        const subscriptionData = {
            plan_id: selectedPlan.id,
            billing_cycle: billingCycle,
            payment_provider: 'auto', // Auto-detect based on user location
            user_location: await getUserLocation()
        };

        const response = await fetch('/payment/create-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(subscriptionData)
        });

        const result = await response.json();

        if (result.success) {
            // Handle successful subscription creation
            if (result.authorization_url) {
                // For Paystack - redirect to payment page (more reliable)
                showSuccessMessage('Redirecting to secure payment...');
                setTimeout(() => {
                    window.location.href = result.authorization_url;
                }, 1000);

            } else if (result.client_secret) {
                // For Stripe - redirect to payment confirmation
                window.location.href = `/payment/confirm?client_secret=${result.client_secret}`;
            } else {
                // For immediate activation (test mode)
                showSuccessMessage(`Successfully upgraded to ${selectedPlan.display_name}!`);
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        } else {
            throw new Error(result.error || 'Failed to create subscription');
        }

    } catch (error) {
        console.error('Error upgrading plan:', error);
        showErrorMessage(error.message || 'Failed to upgrade plan. Please try again.');

        // Reset button if it exists
        if (button) {
            button.textContent = originalText;
            button.disabled = false;
        }
    }
}

// Get user's approximate location for payment provider selection
async function getUserLocation() {
    try {
        const response = await fetch('https://ipapi.co/country_code/');
        const countryCode = await response.text();
        return countryCode.trim();
    } catch (error) {
        console.error('Error getting user location:', error);
        return 'US'; // Default to US
    }
}

// Show success message
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    alertDiv.textContent = message;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Show error message
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    alertDiv.textContent = message;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Automatic referral detection and discount application
document.addEventListener('DOMContentLoaded', function() {
    // Check for UTM parameters in URL
    const urlParams = new URLSearchParams(window.location.search);
    const utmSource = urlParams.get('utm_source');
    const utmMedium = urlParams.get('utm_medium');
    const utmCampaign = urlParams.get('utm_campaign');

    if (utmSource) {
        // Track the referral visit
        trackReferralVisit(utmSource, utmMedium, utmCampaign);

        // Apply referral discount automatically
        applyReferralDiscount(utmSource);
    }
});

// Track referral visit
async function trackReferralVisit(utmSource, utmMedium, utmCampaign) {
    try {
        const response = await fetch('/referral/track', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            console.log('✅ Referral visit tracked successfully');
        }
    } catch (error) {
        console.error('Error tracking referral visit:', error);
    }
}

// Apply referral discount automatically
async function applyReferralDiscount(utmSource) {
    try {
        // Get referral data from backend
        const response = await fetch(`/referral/get-discount?utm_source=${utmSource}`);
        const data = await response.json();

        if (data.success && data.discount_percentage > 0) {
            // Show discount notification for both plans
            showReferralDiscountBanner('plus', data.discount_percentage, data.bonus_credits, utmSource);
            showReferralDiscountBanner('pro', data.discount_percentage, data.bonus_credits, utmSource);

            // Update pricing display
            updatePricingWithDiscount(data.discount_percentage);
        }
    } catch (error) {
        console.error('Error applying referral discount:', error);
    }
}

// Show referral discount banner
function showReferralDiscountBanner(planType, discountPercentage, bonusCredits, partnerName) {
    const discountDiv = document.getElementById(`referral-discount-${planType}`);
    const detailsDiv = document.getElementById(`referral-details-${planType}`);

    if (discountDiv && detailsDiv) {
        discountDiv.style.display = 'block';
        detailsDiv.innerHTML = `${discountPercentage}% off + ${bonusCredits} bonus credits via ${partnerName} partnership`;
    }
}

// Update pricing display with referral discount
function updatePricingWithDiscount(discountPercentage) {
    // Find all price elements and show discounted prices
    const priceElements = document.querySelectorAll('.text-3xl, .text-2xl');

    priceElements.forEach(element => {
        const priceText = element.textContent;
        const priceMatch = priceText.match(/\$(\d+)/);

        if (priceMatch) {
            const originalPrice = parseInt(priceMatch[1]);
            const discountedPrice = Math.round(originalPrice * (1 - discountPercentage / 100));
            const savings = originalPrice - discountedPrice;

            // Add discount display if not already present
            if (!element.parentNode.querySelector('.referral-discount-display')) {
                const discountHTML = `
                    <div class="referral-discount-display" style="margin-top: 8px;">
                        <span style="text-decoration: line-through; color: #999; font-size: 0.8em;">$${originalPrice}</span>
                        <span style="color: #4ade80; font-weight: bold; margin-left: 8px; font-size: 1.1em;">$${discountedPrice}</span>
                        <div style="color: #4ade80; font-size: 0.7em; margin-top: 2px;">Save $${savings} with referral!</div>
                    </div>
                `;
                element.parentNode.insertAdjacentHTML('afterend', discountHTML);
            }
        }
    });
}

// Removed Apple Pay Integration - Using Paystack only

// Apple Pay functions removed - Using Paystack only for better reliability

// Apple Pay JavaScript removed - Using Paystack's native Apple Pay on payment page

// Subscription Management Functions
async function cancelSubscription(planType) {
    if (!confirm('Are you sure you want to cancel your subscription? You will continue to have access until the end of your current billing period.')) {
        return;
    }

    try {
        const response = await fetch('/payment/cancel-subscription', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showSuccessMessage(result.message || 'Subscription cancelled successfully. You will continue to have access until the end of your current billing period.');

            // Update UI to show cancellation status
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            throw new Error(result.error || 'Failed to cancel subscription');
        }
    } catch (error) {
        console.error('Error cancelling subscription:', error);
        showErrorMessage('Failed to cancel subscription: ' + error.message);
    }
}

// Update pricing page to show subscription management options
async function updateSubscriptionUI() {
    try {
        const response = await fetch('/payment/user-info');
        const data = await response.json();

        if (data.success && data.user_info) {
            const userInfo = data.user_info;

            // Show cancel options for subscribed users
            if (userInfo.plan_name === 'plus') {
                const cancelOption = document.getElementById('plus-cancel-option');
                if (cancelOption) {
                    cancelOption.classList.remove('hidden');
                }
            } else if (userInfo.plan_name === 'pro') {
                const cancelOption = document.getElementById('pro-cancel-option');
                if (cancelOption) {
                    cancelOption.classList.remove('hidden');
                }
            }

            // Update button text for current subscribers
            if (userInfo.plan_name !== 'free') {
                const currentPlanButton = document.querySelector(`[data-plan="${userInfo.plan_name}"] button`);
                if (currentPlanButton) {
                    currentPlanButton.textContent = 'Current Plan';
                    currentPlanButton.disabled = true;
                    currentPlanButton.style.opacity = '0.6';
                    currentPlanButton.style.cursor = 'not-allowed';
                }

                // Show plan switching section for subscribed users
                const planSwitchingSection = document.getElementById('plan-switching-section');
                if (planSwitchingSection) {
                    planSwitchingSection.style.display = 'block';
                }
            }
        }
    } catch (error) {
        console.error('Error updating subscription UI:', error);
    }
}

// Plan switching from pricing page
async function switchPlanFromPricing(planName) {
    if (!confirm(`Are you sure you want to switch to the ${planName} plan?`)) {
        return;
    }

    try {
        const response = await fetch('/payment/switch-plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ plan_name: planName })
        });

        const result = await response.json();

        if (result.success) {
            showSuccessMessage(`Successfully switched to ${planName} plan`);
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            throw new Error(result.error || 'Failed to switch plan');
        }
    } catch (error) {
        console.error('Error switching plan:', error);
        showErrorMessage('Failed to switch plan: ' + error.message);
    }
}

// Call updateSubscriptionUI when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateSubscriptionUI();
});


</script>

<!-- Referral System JavaScript -->
<script src="{{ url_for('static', filename='js/referral.js') }}"></script>
{% endblock %}
