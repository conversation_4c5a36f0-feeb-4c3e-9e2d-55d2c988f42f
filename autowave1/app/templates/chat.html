{% extends "layout.html" %}

{% block title %}ARCHIVED - Agen911 - Chat{% endblock %}

{% block header %}ARCHIVED - Welcome to Chat{% endblock %}

<!--
ARCHIVED PAGE - MOVED TO ARCHIVE
This chat page has been archived because Research Lab now handles all chat functionality.
Use Research Lab instead of this page.
-->

{% block content %}
<div class="chat-page">
    {% if error %}
    <div class="error-message">{{ error }}</div>
    {% endif %}

    <div class="chat-interface">
        <div class="chat-messages" id="chatMessages">
            <!-- Messages will appear here -->
        </div>

        <form id="chatForm" class="chat-input-form">
            <div class="chat-input-container">
                <input type="text" name="message" id="messageInput" placeholder="Type your message here..." required>
                <button type="submit" class="send-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="22" y1="2" x2="11" y2="13"></line>
                        <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    .chat-page {
        max-width: 1000px;
        width: 90%;
        margin: 0 auto;
        height: calc(100vh - 100px); /* Adjust for header and any margins */
        display: flex;
        flex-direction: column;
    }

    .error-message {
        background-color: #2d3748;
        color: #fc8181;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        font-size: 14px;
        border: 1px solid #4a5568;
    }

    .chat-interface {
        border: 1px solid #4a5568;
        border-radius: 8px;
        overflow: hidden;
        background-color: #1a202c;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: #1a202c;
    }

    .message {
        margin-bottom: 16px;
        max-width: 95%;
    }

    .message p {
        margin: 0;
        padding: 0;
    }

    .message.user {
        margin-left: auto;
        color: #4299e1;
        padding: 8px 12px;
        text-align: right;
        max-width: 95%;
        background-color: #2d3748;
        border-radius: 18px 18px 0 18px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        display: inline-block;
        width: auto;
    }

    .message.ai {
        color: #e2e8f0;
        padding: 0;
        max-width: 95%;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    .chat-input-form {
        margin: 0;
    }

    .chat-input-container {
        display: flex;
        padding: 15px;
        background: transparent !important;
        border-top: 1px solid #4a5568;
    }

    #messageInput {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid #4a5568;
        border-radius: 8px;
        font-size: 16px;
        background: transparent !important;
        color: #e2e8f0;
    }

    #messageInput:focus {
        outline: none;
        border-color: #4299e1;
    }

    .send-button {
        background-color: #4299e1;
        color: white;
        border: none;
        padding: 10px;
        border-radius: 50%;
        margin-left: 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        transition: background-color 0.2s ease;
    }

    .send-button:hover {
        background-color: #3182ce;
    }

    .send-button svg {
        width: 20px;
        height: 20px;
    }



    .typing-indicator {
        color: #a0aec0;
        padding: 0;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    .typing-indicator p {
        margin: 0;
        padding: 0;
    }

    .dot {
        display: inline-block;
        animation: wave 1.3s linear infinite;
    }

    .dot:nth-child(2) {
        animation-delay: -1.1s;
    }

    .dot:nth-child(3) {
        animation-delay: -0.9s;
    }

    @keyframes wave {
        0%, 60%, 100% {
            transform: initial;
        }
        30% {
            transform: translateY(-5px);
        }
    }

    .error {
        color: #e74c3c;
    }

    /* Ensure AI messages have transparent backgrounds */
    .message.ai, .message.ai p, .message.ai div {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Message action buttons */
    .message-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #4a5568;
        opacity: 0.7;
        transition: opacity 0.2s ease;
    }

    .message-actions:hover {
        opacity: 1;
    }

    .action-btn {
        background: none;
        border: none;
        color: #a0aec0;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .action-btn:hover {
        color: #e2e8f0;
        background-color: #2d3748;
    }

    .action-btn svg {
        width: 16px;
        height: 16px;
    }

    .action-btn.active {
        color: #4299e1;
    }

    .action-btn.thumbs-up.active {
        color: #48bb78;
    }

    .action-btn.thumbs-down.active {
        color: #f56565;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('chatForm');
        const input = document.getElementById('messageInput');
        const messagesContainer = document.getElementById('chatMessages');

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const message = input.value.trim();
            if (!message) return;

            // Add user message to the chat
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.style.backgroundColor = '#2d3748';
            userMessage.style.borderRadius = '18px 18px 0 18px';
            userMessage.style.boxShadow = '0 1px 2px rgba(0,0,0,0.1)';
            userMessage.style.padding = '8px 12px';
            userMessage.style.display = 'inline-block';
            userMessage.style.width = 'auto';
            userMessage.innerHTML = `<p>${message}</p>`;

            // Create a wrapper div to align the message to the right
            const wrapper = document.createElement('div');
            wrapper.style.textAlign = 'right';
            wrapper.style.marginBottom = '16px';
            wrapper.style.width = '100%';
            wrapper.appendChild(userMessage);

            messagesContainer.appendChild(wrapper);

            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Show typing indicator
            const typingIndicator = document.createElement('div');
            typingIndicator.className = 'message ai typing-indicator';
            typingIndicator.style.background = 'transparent';
            typingIndicator.style.border = 'none';
            typingIndicator.style.boxShadow = 'none';
            typingIndicator.innerHTML = `<p><span class="dot">.</span><span class="dot">.</span><span class="dot">.</span></p>`;
            messagesContainer.appendChild(typingIndicator);

            // Scroll to bottom again
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Clear input
            input.value = '';

            // Make AJAX request
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message }),
            })
            .then(response => response.json())
            .then(data => {
                // Remove typing indicator
                messagesContainer.removeChild(typingIndicator);

                // Add AI response
                const aiMessage = document.createElement('div');
                aiMessage.className = 'message ai';
                aiMessage.style.background = 'transparent';
                aiMessage.style.border = 'none';
                aiMessage.style.boxShadow = 'none';

                if (data.response) {
                    aiMessage.innerHTML = `
                        <p>${data.response}</p>
                        <div class="message-actions">
                            <button class="action-btn thumbs-up" title="Good response" data-message-id="${Date.now()}_up">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7v13m-3-4H4a2 2 0 01-2-2v-4a2 2 0 012-2h2m5 0v10a1 1 0 01-1 1H4a1 1 0 01-1-1v-4a1 1 0 011-1h2"></path>
                                </svg>
                            </button>
                            <button class="action-btn thumbs-down" title="Poor response" data-message-id="${Date.now()}_down">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L15 17V4m-3 4H10a2 2 0 00-2 2v4a2 2 0 002 2h2m1 0v-10a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 01-1 1h-2"></path>
                                </svg>
                            </button>
                            <button class="action-btn copy-btn" title="Copy response" data-message-id="${Date.now()}_copy">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                            </button>
                        </div>
                    `;
                } else if (data.error) {
                    aiMessage.innerHTML = `<p class="error">${data.error}</p>`;
                }

                messagesContainer.appendChild(aiMessage);

                // Add event listeners for the action buttons
                addActionButtonListeners(aiMessage, data.response || data.error);

                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            })
            .catch(error => {
                // Remove typing indicator
                messagesContainer.removeChild(typingIndicator);

                // Add error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'message ai error';
                errorMessage.style.background = 'transparent';
                errorMessage.style.border = 'none';
                errorMessage.style.boxShadow = 'none';
                const errorText = `Sorry, an error occurred: ${error.message}`;
                errorMessage.innerHTML = `
                    <p>${errorText}</p>
                    <div class="message-actions">
                        <button class="action-btn thumbs-up" title="Good response" data-message-id="${Date.now()}_up">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7v13m-3-4H4a2 2 0 01-2-2v-4a2 2 0 012-2h2m5 0v10a1 1 0 01-1 1H4a1 1 0 01-1-1v-4a1 1 0 011-1h2"></path>
                            </svg>
                        </button>
                        <button class="action-btn thumbs-down" title="Poor response" data-message-id="${Date.now()}_down">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L15 17V4m-3 4H10a2 2 0 00-2 2v4a2 2 0 002 2h2m1 0v-10a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 01-1 1h-2"></path>
                            </svg>
                        </button>
                        <button class="action-btn copy-btn" title="Copy response" data-message-id="${Date.now()}_copy">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                        </button>
                    </div>
                `;
                messagesContainer.appendChild(errorMessage);

                // Add event listeners for the action buttons
                addActionButtonListeners(errorMessage, errorText);

                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            });
        });

        // Function to add event listeners to action buttons
        function addActionButtonListeners(messageElement, messageContent) {
            const thumbsUpBtn = messageElement.querySelector('.thumbs-up');
            const thumbsDownBtn = messageElement.querySelector('.thumbs-down');
            const copyBtn = messageElement.querySelector('.copy-btn');

            // Thumbs up functionality
            if (thumbsUpBtn) {
                thumbsUpBtn.addEventListener('click', function() {
                    // Toggle active state
                    const isActive = this.classList.contains('active');

                    // Remove active from thumbs down if it was active
                    thumbsDownBtn.classList.remove('active');

                    if (isActive) {
                        this.classList.remove('active');
                        sendFeedback('neutral', messageContent);
                    } else {
                        this.classList.add('active');
                        sendFeedback('positive', messageContent);
                    }
                });
            }

            // Thumbs down functionality
            if (thumbsDownBtn) {
                thumbsDownBtn.addEventListener('click', function() {
                    // Toggle active state
                    const isActive = this.classList.contains('active');

                    // Remove active from thumbs up if it was active
                    thumbsUpBtn.classList.remove('active');

                    if (isActive) {
                        this.classList.remove('active');
                        sendFeedback('neutral', messageContent);
                    } else {
                        this.classList.add('active');
                        sendFeedback('negative', messageContent);
                    }
                });
            }

            // Copy functionality
            if (copyBtn) {
                copyBtn.addEventListener('click', function() {
                    const textContent = messageContent || messageElement.querySelector('p').textContent;

                    navigator.clipboard.writeText(textContent).then(() => {
                        // Visual feedback
                        const originalTitle = this.getAttribute('title');
                        this.setAttribute('title', 'Copied!');
                        this.classList.add('active');

                        setTimeout(() => {
                            this.setAttribute('title', originalTitle);
                            this.classList.remove('active');
                        }, 1500);

                        showToast('✅ Response copied to clipboard!', 'success');
                    }).catch(err => {
                        console.error('Failed to copy text: ', err);

                        // Fallback for older browsers
                        try {
                            const textArea = document.createElement('textarea');
                            textArea.value = textContent;
                            document.body.appendChild(textArea);
                            textArea.select();
                            document.execCommand('copy');
                            document.body.removeChild(textArea);

                            showToast('✅ Response copied to clipboard!', 'success');
                        } catch (fallbackErr) {
                            console.error('Fallback copy method also failed:', fallbackErr);
                            showToast('❌ Failed to copy response. Please try again.', 'error');
                        }
                    });
                });
            }
        }

        // Function to send feedback to the server
        function sendFeedback(type, messageContent) {
            console.log(`Feedback: ${type} for message:`, messageContent);

            // Send feedback to server for LLM improvement
            fetch('/api/feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    feedback_type: type,
                    message_content: messageContent,
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent
                }),
            })
            .then(response => response.json())
            .then(data => {
                console.log('Feedback sent successfully:', data);

                // Show subtle feedback to user
                if (type === 'positive') {
                    showToast('👍 Thanks for the positive feedback!', 'success');
                } else if (type === 'negative') {
                    showToast('👎 Thanks for the feedback. We\'ll work to improve!', 'info');
                }
            })
            .catch(error => {
                console.error('Error sending feedback:', error);
                // Don't show error to user for feedback failures
            });
        }

        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 text-white font-medium max-w-sm`;

            // Set background color based on type
            if (type === 'success') {
                toast.style.backgroundColor = '#48bb78'; // green-400
            } else if (type === 'error') {
                toast.style.backgroundColor = '#f56565'; // red-400
            } else {
                toast.style.backgroundColor = '#4299e1'; // blue-400
            }

            toast.textContent = message;
            document.body.appendChild(toast);

            // Animate in
            toast.style.transform = 'translateX(100%)';
            toast.style.transition = 'transform 0.3s ease-in-out';
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // Remove after 3 seconds
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    });
</script>
{% endblock %}
