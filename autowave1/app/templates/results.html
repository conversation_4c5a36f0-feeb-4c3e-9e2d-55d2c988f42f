{% extends "layout.html" %}

{% block title %}
    {% if query %}
        Agen911 - Research Results
    {% elif message %}
        Agen911 - Chat Response
    {% else %}
        Agen911 - Results
    {% endif %}
{% endblock %}

{% block header %}
    {% if query %}
        Research Results
    {% elif message %}
        Chat Response
    {% else %}
        Results
    {% endif %}
{% endblock %}

{% block content %}
<div class="results-page">
    {% if query %}
    <div class="query-container">
        <div class="query-info">
            <span class="query-label">Research Query:</span>
            <span class="query-text">{{ query }}</span>
        </div>
        <div class="query-actions">
            <a href="/search" class="action-button">New Research</a>
        </div>
    </div>
    {% endif %}

    {% if message %}
    <div class="query-container">
        <div class="query-info">
            <span class="query-label">Your Message:</span>
            <span class="query-text">{{ message }}</span>
        </div>
        <div class="query-actions">
            <a href="/chat" class="action-button">Continue Chat</a>
        </div>
    </div>
    {% endif %}

    <div class="results-container">
        <div class="results-header">
            <div class="results-title">
                {% if query %}
                    Research Results
                {% elif message %}
                    AI Response
                {% else %}
                    Results
                {% endif %}
            </div>
            <div class="results-actions">
                <button class="action-button" id="copyButton" title="Copy to clipboard">
                    <span class="action-icon">📋</span> Copy
                </button>
                <button class="action-button" id="shareButton" title="Share this result">
                    <span class="action-icon">🔗</span> Share
                </button>
            </div>
        </div>

        <div class="results-content">
            {% if results %}
                {{ results|safe }}
            {% elif response %}
                {{ response|safe }}
            {% else %}
                <p>No results found. Please try a different query.</p>
            {% endif %}
        </div>

        <div class="results-footer">
            <div class="citation">
                Generated by AutoWave using Botrex Technology
            </div>
        </div>
    </div>

    <div class="related-queries">
        <h3>Related Queries</h3>
        <div class="related-query-list">
            {% if query %}
                <a href="/search?query={{ query }} explained simply" class="related-query">{{ query }} explained simply</a>
                <a href="/search?query=history of {{ query }}" class="related-query">History of {{ query }}</a>
                <a href="/search?query={{ query }} applications" class="related-query">{{ query }} applications</a>
            {% elif message %}
                <a href="/chat?message=Tell me more about this topic" class="related-query">Tell me more about this topic</a>
                <a href="/chat?message=Can you explain it more simply?" class="related-query">Can you explain it more simply?</a>
                <a href="/chat?message=What are some practical applications?" class="related-query">What are some practical applications?</a>
            {% endif %}
        </div>
    </div>
</div>

<style>
    .results-page {
        max-width: 800px;
        margin: 0 auto;
    }

    .query-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .query-info {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .query-label {
        font-weight: 600;
        color: #666;
    }

    .query-text {
        font-style: italic;
    }

    .query-actions {
        display: flex;
        gap: 10px;
    }

    .results-container {
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 30px;
        border: 1px solid #eee;
    }

    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #f9f9f9;
        border-bottom: 1px solid #eee;
    }

    .results-title {
        font-weight: 600;
        font-size: 16px;
        color: #333;
    }

    .results-actions {
        display: flex;
        gap: 10px;
    }

    .action-button {
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 5px 10px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: background-color 0.3s ease;
        color: #333;
        text-decoration: none;
    }

    .action-button:hover {
        background-color: #f1f1f1;
    }

    .action-icon {
        font-size: 14px;
    }

    .results-content {
        padding: 25px;
        line-height: 1.8;
        white-space: pre-wrap;
        color: #333;
    }

    .results-footer {
        padding: 15px 25px;
        border-top: 1px solid #eee;
        background-color: #f9f9f9;
    }

    .citation {
        font-size: 14px;
        color: #666;
    }

    .related-queries {
        margin-top: 30px;
    }

    .related-queries h3 {
        font-size: 18px;
        margin-bottom: 15px;
        color: #333;
    }

    .related-query-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .related-query {
        background-color: #f1f1f1;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.3s ease;
        color: #333;
        text-decoration: none;
    }

    .related-query:hover {
        background-color: #e5e5e5;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy button functionality
        const copyButton = document.getElementById('copyButton');
        if (copyButton) {
            copyButton.addEventListener('click', function() {
                const resultsContent = document.querySelector('.results-content').innerText;
                navigator.clipboard.writeText(resultsContent).then(function() {
                    copyButton.innerHTML = '<span class="action-icon">✓</span> Copied';
                    setTimeout(function() {
                        copyButton.innerHTML = '<span class="action-icon">📋</span> Copy';
                    }, 2000);
                });
            });
        }

        // Share button functionality
        const shareButton = document.getElementById('shareButton');
        if (shareButton) {
            shareButton.addEventListener('click', function() {
                // Create a shareable URL
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(function() {
                    shareButton.innerHTML = '<span class="action-icon">✓</span> URL Copied';
                    setTimeout(function() {
                        shareButton.innerHTML = '<span class="action-icon">🔗</span> Share';
                    }, 2000);
                });
            });
        }
    });
</script>
{% endblock %}
