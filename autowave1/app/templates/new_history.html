{% extends "layout.html" %}

{% block title %}AutoWave - Activity History (Archived){% endblock %}

{% block header %}Activity History (Archived){% endblock %}

{% block extra_css %}
<style>
/* History Sidebar Styles */
.history-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: #121212;
    border-left: 1px solid #333;
    z-index: 1000;
    transition: right 0.3s ease-in-out;
    overflow-y: auto;
}

.history-sidebar.open {
    right: 0;
}

.history-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out;
}

.history-overlay.active {
    opacity: 1;
    visibility: visible;
}

.history-header {
    padding: 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: between;
    align-items: center;
}

.history-title {
    color: #e0e0e0;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.history-close {
    background: none;
    border: none;
    color: #aaa;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s;
}

.history-close:hover {
    color: #e0e0e0;
    background: #333;
}

.history-content {
    padding: 20px;
}

.history-search {
    width: 100%;
    padding: 10px 12px;
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 6px;
    color: #e0e0e0;
    font-size: 14px;
    margin-bottom: 20px;
}

.history-search:focus {
    outline: none;
    border-color: #555;
    box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
}

.history-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-chip {
    padding: 6px 12px;
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 16px;
    color: #aaa;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-chip:hover,
.filter-chip.active {
    background: #3d3d3d;
    color: #e0e0e0;
    border-color: #555;
}

.history-list {
    space-y: 12px;
}

.history-item {
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 12px;
}

.history-item:hover {
    background: #2d2d2d;
    border-color: #444;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.history-item-title {
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    flex: 1;
    margin-right: 12px;
}

.history-item-agent {
    background: #333;
    color: #aaa;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.history-item-preview {
    color: #888;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.history-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #666;
}

.history-item-time {
    display: flex;
    align-items: center;
    gap: 4px;
}

.history-item-status {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-success {
    color: #4ade80;
}

.status-error {
    color: #f87171;
}

.history-empty {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.history-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.history-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #333;
    border-top: 3px solid #666;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* History Toggle Button */
.history-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #121212;
    border: 1px solid #333;
    color: #e0e0e0;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    z-index: 998;
    transition: all 0.2s;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.history-toggle:hover {
    background: #2d2d2d;
    border-color: #444;
}

.history-toggle i {
    font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
    .history-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .history-toggle {
        top: 16px;
        right: 16px;
        padding: 10px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="history-page">
    <!-- ARCHIVED NOTICE -->
    <div class="archived-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px; text-align: center;">
        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
            <i class="fas fa-archive" style="color: #856404; margin-right: 10px; font-size: 24px;"></i>
            <h2 style="color: #856404; margin: 0; font-size: 20px; font-weight: 600;">Page Archived</h2>
        </div>
        <p style="color: #856404; margin-bottom: 15px; font-size: 16px;">
            This history page has been replaced by our new <strong>Professional History System</strong>.
        </p>
        <p style="color: #856404; margin-bottom: 20px; font-size: 14px;">
            Look for the 🕒 <strong>History Icon</strong> in the top-right corner of any page to access your activity history.
        </p>
        <a href="/" style="background: #856404; color: white; padding: 10px 20px; border-radius: 6px; text-decoration: none; font-weight: 500;">
            <i class="fas fa-home mr-2"></i>Go to Homepage
        </a>
    </div>

    <!-- Main Content Area -->
    <div class="history-main-content">
        <div class="text-center py-20">
            <div class="mb-8">
                <i class="fas fa-history text-6xl text-gray-300 mb-4"></i>
                <h2 class="text-2xl font-semibold text-gray-700 mb-2">Activity History (Archived)</h2>
                <p class="text-gray-500 max-w-md mx-auto">
                    This page has been archived. Use the professional history system accessible via the history icon on all pages.
                </p>
            </div>

            <button id="open-history-sidebar" class="bg-gray-800 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors" disabled style="opacity: 0.5; cursor: not-allowed;">
                <i class="fas fa-history mr-2"></i>
                View History (Archived)
            </button>
        </div>
    </div>
    
    <!-- History Toggle Button (Fixed Position) -->
    <button id="history-toggle" class="history-toggle" title="View History">
        <i class="fas fa-history"></i>
    </button>
    
    <!-- History Sidebar -->
    <div id="history-sidebar" class="history-sidebar">
        <!-- Header -->
        <div class="history-header">
            <h3 class="history-title">Activity History</h3>
            <button id="close-history-sidebar" class="history-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <!-- Content -->
        <div class="history-content">
            <!-- Search -->
            <input type="text" id="history-search" class="history-search" placeholder="Search your activity...">
            
            <!-- Filters -->
            <div class="history-filters">
                <div class="filter-chip active" data-filter="all">All</div>
                <div class="filter-chip" data-filter="autowave_chat">Chat</div>
                <div class="filter-chip" data-filter="prime_agent">Prime Agent</div>
                <div class="filter-chip" data-filter="agentic_code">Design</div>
                <div class="filter-chip" data-filter="research_lab">Research</div>
                <div class="filter-chip" data-filter="document_generator">Agent Wave</div>
            </div>
            
            <!-- History List -->
            <div id="history-list" class="history-list">
                <!-- Loading state -->
                <div id="history-loading" class="history-loading">
                    <div class="loading-spinner"></div>
                    <p>Loading your activity history...</p>
                </div>
                
                <!-- Empty state -->
                <div id="history-empty" class="history-empty" style="display: none;">
                    <div class="history-empty-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <p>No activity found</p>
                    <p class="text-xs mt-2">Start using AutoWave agents to see your history here</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Overlay -->
    <div id="history-overlay" class="history-overlay"></div>
</div>
{% endblock %}

{% block extra_head %}
<script src="{{ url_for('static', filename='js/enhanced_history.js') }}"></script>
{% endblock %}
