{% extends "base.html" %}

{% block title %}Payment History - AutoWave{% endblock %}

{% block content %}
<div class="min-h-screen" style="background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);">
    <!-- Header -->
    <div class="container mx-auto px-6 py-8">
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold" style="color: #e0e0e0;">Payment History</h1>
                <p class="text-sm mt-2" style="color: #aaa;">View your subscription and payment details</p>
            </div>
            <div class="flex space-x-4">
                <a href="/pricing" class="px-4 py-2 rounded-lg border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors">
                    Back to Pricing
                </a>
                <a href="/billing-info" class="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                    Billing Info
                </a>
            </div>
        </div>

        <!-- Current Subscription Card -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
            <h2 class="text-xl font-semibold mb-4" style="color: #e0e0e0;">Current Subscription</h2>
            <div id="current-subscription-info" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Will be populated by JavaScript -->
                <div class="text-center">
                    <div class="text-sm text-gray-400">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Payment History Table -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <h2 class="text-xl font-semibold" style="color: #e0e0e0;">Payment History</h2>
                <p class="text-sm mt-1" style="color: #aaa;">Your recent transactions and billing history</p>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Invoice</th>
                        </tr>
                    </thead>
                    <tbody id="payment-history-table" class="bg-gray-800 divide-y divide-gray-700">
                        <!-- Will be populated by JavaScript -->
                        <tr>
                            <td colspan="5" class="px-6 py-8 text-center text-gray-400">
                                <div class="flex items-center justify-center">
                                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
                                    Loading payment history...
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Empty State (hidden by default) -->
        <div id="empty-state" class="hidden text-center py-12">
            <div class="text-gray-400 mb-4">
                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-300 mb-2">No payment history</h3>
            <p class="text-gray-400 mb-6">You haven't made any payments yet.</p>
            <a href="/pricing" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                View Plans
            </a>
        </div>
    </div>
</div>

<script>
// Load payment history and subscription info
document.addEventListener('DOMContentLoaded', async function() {
    await loadCurrentSubscription();
    await loadPaymentHistory();
});

async function loadCurrentSubscription() {
    try {
        const response = await fetch('/payment/user-info');
        const data = await response.json();
        
        if (data.success && data.user_info) {
            const userInfo = data.user_info;
            const container = document.getElementById('current-subscription-info');
            
            // Get subscription details
            const subscriptionResponse = await fetch('/payment/subscription-details');
            const subscriptionData = await subscriptionResponse.json();
            
            let subscriptionInfo = '';
            if (subscriptionData.success && subscriptionData.subscription) {
                const sub = subscriptionData.subscription;
                const nextBilling = new Date(sub.current_period_end).toLocaleDateString();
                const status = sub.cancel_at_period_end ? 'Cancelling' : 'Active';
                
                subscriptionInfo = `
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-400">${userInfo.display_name || userInfo.plan_name}</div>
                        <div class="text-sm text-gray-400 mt-1">Current Plan</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-400">${status}</div>
                        <div class="text-sm text-gray-400 mt-1">Status</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-400">${nextBilling}</div>
                        <div class="text-sm text-gray-400 mt-1">${sub.cancel_at_period_end ? 'Expires' : 'Next Billing'}</div>
                    </div>
                `;
            } else {
                subscriptionInfo = `
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-400">Free Plan</div>
                        <div class="text-sm text-gray-400 mt-1">Current Plan</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-400">Active</div>
                        <div class="text-sm text-gray-400 mt-1">Status</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-400">Daily Reset</div>
                        <div class="text-sm text-gray-400 mt-1">Credits</div>
                    </div>
                `;
            }
            
            container.innerHTML = subscriptionInfo;
        }
    } catch (error) {
        console.error('Error loading subscription info:', error);
        document.getElementById('current-subscription-info').innerHTML = `
            <div class="text-center col-span-3">
                <div class="text-red-400">Error loading subscription information</div>
            </div>
        `;
    }
}

async function loadPaymentHistory() {
    try {
        const response = await fetch('/payment/history');
        const data = await response.json();
        
        const tableBody = document.getElementById('payment-history-table');
        
        if (data.success && data.payments && data.payments.length > 0) {
            const paymentsHTML = data.payments.map(payment => {
                const date = new Date(payment.created_at).toLocaleDateString();
                const statusColor = payment.status === 'completed' ? 'text-green-400' : 
                                  payment.status === 'pending' ? 'text-yellow-400' : 'text-red-400';
                
                return `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${date}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${payment.description}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">$${payment.amount}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm ${statusColor} capitalize">${payment.status}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            ${payment.invoice_url ? 
                                `<a href="${payment.invoice_url}" target="_blank" class="text-blue-400 hover:text-blue-300">View</a>` : 
                                '<span class="text-gray-500">N/A</span>'
                            }
                        </td>
                    </tr>
                `;
            }).join('');
            
            tableBody.innerHTML = paymentsHTML;
        } else {
            // Show empty state
            tableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-8 text-center text-gray-400">
                        No payment history found
                    </td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('Error loading payment history:', error);
        document.getElementById('payment-history-table').innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-8 text-center text-red-400">
                    Error loading payment history
                </td>
            </tr>
        `;
    }
}
</script>
{% endblock %}
