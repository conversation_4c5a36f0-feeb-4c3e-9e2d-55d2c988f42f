{% extends "layout.html" %}

{% block title %}Subscription Required - AutoWave{% endblock %}

{% block header %}Subscription Required{% endblock %}

{% block extra_css %}
<style>
    .subscription-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        margin: -20px;
        padding: 20px;
    }
    
    .subscription-card {
        background: white;
        border-radius: 20px;
        padding: 40px;
        max-width: 600px;
        width: 100%;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        animation: slideUp 0.6s ease-out;
    }
    
    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .lock-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 30px;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 36px;
    }
    
    .subscription-title {
        font-size: 32px;
        font-weight: 700;
        color: #000000;
        margin-bottom: 16px;
    }
    
    .subscription-message {
        font-size: 18px;
        color: #4a5568;
        margin-bottom: 30px;
        line-height: 1.6;
    }
    
    .feature-name {
        font-weight: 700;
        color: #000000;
        text-transform: capitalize;
    }
    
    .pricing-link {
        display: inline-block;
        background: white;
        color: #000000;
        padding: 16px 32px;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 18px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 2px solid #1a1a2e;
    }
    
    .pricing-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 26, 46, 0.2);
        text-decoration: none;
        color: #000000;
        background: #f8f9fa;
    }
    
    .free-features {
        background: #f7fafc;
        border-radius: 12px;
        padding: 20px;
        margin: 30px 0;
        border-left: 4px solid #48bb78;
    }
    
    .free-features h4 {
        color: #2d3748;
        margin-bottom: 12px;
        font-weight: 600;
    }
    
    .free-features p {
        color: #4a5568;
        margin: 0;
    }
    
    .plan-benefits {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin: 30px 0;
        text-align: left;
    }
    
    .benefit-item {
        display: flex;
        align-items: center;
        color: #4a5568;
    }
    
    .benefit-item::before {
        content: "✓";
        color: #48bb78;
        font-weight: bold;
        margin-right: 8px;
    }
    
    @media (max-width: 768px) {
        .subscription-card {
            padding: 30px 20px;
            margin: 10px;
        }
        
        .subscription-title {
            font-size: 28px;
        }
        
        .subscription-message {
            font-size: 16px;
        }
        
        .plan-benefits {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="subscription-container">
    <div class="subscription-card">
        <div class="lock-icon">
            🔒
        </div>
        
        <h1 class="subscription-title">Subscribe to Plus or Pro Plan to Get Access to this Page</h1>
        
        <p class="subscription-message">
            You're trying to access <span class="feature-name">{{ page_name.replace('_', ' ').replace('-', ' ') }}</span>, 
            which is a premium feature available to Plus and Pro subscribers.
        </p>
        
        <div class="free-features">
            <h4>🎉 What you can access with your free account:</h4>
            <p><strong>AutoWave Chat</strong> - 50 daily credits for unlimited conversations with our AI assistant</p>
        </div>
        
        <div class="plan-benefits">
            <div class="benefit-item">Research Lab access</div>
            <div class="benefit-item">Agent Wave document generation</div>
            <div class="benefit-item">Agentic Code development</div>
            <div class="benefit-item">Prime Agent AutoWave</div>
            <div class="benefit-item">Context7 Tools suite</div>
            <div class="benefit-item">8,000+ monthly credits</div>
            <div class="benefit-item">Priority support</div>
            <div class="benefit-item">Advanced AI models</div>
        </div>
        
        <a href="/pricing" class="pricing-link">Click</a>
        
        <div style="margin-top: 20px;">
            <p style="color: #718096; font-size: 14px;">
                Already subscribed? Try refreshing the page or 
                <a href="/auth/logout" style="color: #000000; font-weight: 600;">logging out and back in</a>.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    const pricingLink = document.querySelector('.pricing-link');
    
    pricingLink.addEventListener('click', function(e) {
        // Add click animation
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
            this.style.transform = 'translateY(-2px)';
        }, 150);
    });
});
</script>
{% endblock %}
