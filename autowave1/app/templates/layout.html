<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AutoWave - AI-Powered Assistant{% endblock %}</title>

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicons/favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicons/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicons/favicon-96x96.png') }}">
    <link rel="icon" type="image/png" sizes="192x192" href="{{ url_for('static', filename='android-chrome-192x192.png') }}">
    <link rel="icon" type="image/png" sizes="512x512" href="{{ url_for('static', filename='android-chrome-512x512.png') }}">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='apple-touch-icon.png') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ url_for('static', filename='favicons/favicon-152x152.png') }}">
    <link rel="apple-touch-icon" sizes="167x167" href="{{ url_for('static', filename='favicons/favicon-167x167.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='favicons/favicon-180x180.png') }}">

    <!-- Web App Manifest -->
    <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}">

    <!-- SEO and Social Media Meta Tags -->
    <meta name="description" content="{% block description %}AutoWave - Advanced AI-powered assistant platform with multiple AI models including Google Gemini, GPT, Groq, Anthropic Claude, Meta Llama, Qwen, and Deepseek AI. Features research lab, agent wave, design tools, and prime agent capabilities.{% endblock %}">
    <meta name="keywords" content="AI assistant, artificial intelligence, GPT, Gemini, Claude, automation, research, design, coding, productivity">
    <meta name="author" content="Botrex Technology">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://autowave1.pro/">
    <meta property="og:title" content="{% block og_title %}AutoWave - AI-Powered Assistant{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Advanced AI-powered assistant platform with multiple AI models and tools for research, design, coding, and automation.{% endblock %}">
    <meta property="og:image" content="{{ url_for('static', filename='favicons/favicon-512x512.png', _external=True) }}">
    <meta property="og:site_name" content="AutoWave">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://autowave1.pro/">
    <meta property="twitter:title" content="{% block twitter_title %}AutoWave - AI-Powered Assistant{% endblock %}">
    <meta property="twitter:description" content="{% block twitter_description %}Advanced AI-powered assistant platform with multiple AI models and tools.{% endblock %}">
    <meta property="twitter:image" content="{{ url_for('static', filename='favicons/favicon-512x512.png', _external=True) }}">

    <!-- Theme and App Configuration -->
    <meta name="theme-color" content="#4a9eff">
    <meta name="msapplication-TileColor" content="#4a9eff">
    <meta name="msapplication-config" content="{{ url_for('static', filename='browserconfig.xml') }}">

    <!-- Google Site Verification (to be added after Google Search Console setup) -->
    <!-- <meta name="google-site-verification" content="YOUR_VERIFICATION_CODE_HERE"> -->

    <!-- Structured Data for Better Indexing -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "AutoWave",
      "description": "Advanced AI-powered assistant platform with multiple AI models including Google Gemini, GPT, Groq, Anthropic Claude, Meta Llama, Qwen, and Deepseek AI",
      "url": "https://autowave.pro",
      "applicationCategory": "ProductivityApplication",
      "operatingSystem": "Web",
      "browserRequirements": "Requires JavaScript. Requires HTML5.",
      "softwareVersion": "1.0",
      "author": {
        "@type": "Organization",
        "name": "Botrex Technology",
        "url": "https://botrex.pro"
      },
      "offers": [
        {
          "@type": "Offer",
          "name": "Plus Plan",
          "price": "15",
          "priceCurrency": "USD",
          "billingIncrement": "P1M",
          "description": "Monthly subscription with enhanced features"
        },
        {
          "@type": "Offer",
          "name": "Pro Plan",
          "price": "169",
          "priceCurrency": "USD",
          "billingIncrement": "P1M",
          "description": "Professional monthly subscription with full features"
        }
      ],
      "featureList": [
        "AI Chat Assistant",
        "Research Lab",
        "Agent Wave",
        "Design Tools",
        "Prime Agent Tasks",
        "Context7 Tools",
        "Multiple AI Models",
        "Document Processing",
        "Code Generation"
      ],
      "screenshot": "https://autowave.pro/static/images/autowave-logo.png"
    }
    </script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/black_theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/task_summary_enhanced.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/design_task.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/design_task_preview.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/document_display.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Add Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#000000',
              secondary: '#4B5563',
              accent: '#3B82F6',
              light: '#F3F4F6',
              dark: '#1F2937',
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
            animation: {
              'fade-in': 'fadeIn 0.5s ease-in-out',
              'slide-in': 'slideIn 0.5s ease-in-out',
              'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
              'bounce-slow': 'bounce 2s infinite',
              'typing': 'typing 1.5s steps(40, end)',
              'blink': 'blink .75s step-end infinite',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideIn: {
                '0%': { transform: 'translateX(-10px)', opacity: '0' },
                '100%': { transform: 'translateX(0)', opacity: '1' },
              },
              typing: {
                '0%': { width: '0' },
                '100%': { width: '100%' },
              },
              blink: {
                '0%, 100%': { 'border-color': 'transparent' },
                '50%': { 'border-color': 'black' },
              },
            },
          },
        },
      }
    </script>
    <!-- Add Markdown-it for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <!-- Add Prism for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
    <!-- Add JSZip for downloading multiple files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/markdown_processor.js') }}"></script>
    <script src="{{ url_for('static', filename='js/design_task_handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/document_handler.js') }}"></script>
    {% block extra_css %}{% endblock %}
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-white font-sans" data-theme="light">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="w-64 fixed inset-y-0 left-0 z-30 transition-all duration-300 ease-in-out transform flex flex-col" style="display: block; background-color: #121212; border-right: 1px solid #333;">
            <!-- Logo and Brand -->
            <div class="h-16 flex items-center justify-between px-4" style="border-bottom: 1px solid #333;">
                <a href="/" class="flex items-center space-x-2">
                    <img src="/static/images/autowave-logo.png" alt="AutoWave Logo" class="w-8 h-8" onerror="this.style.display='none'">
                    <span class="autowave-logo text-xl tracking-tight font-bold text-white">AutoWave</span>
                </a>
                <button id="collapse-sidebar" class="p-1 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 focus:outline-none">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                    </svg>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="mt-5 px-2 space-y-1 pb-20">
                <a href="/" class="{% if request.path == '/' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span class="sidebar-text">Home</span>
                </a>

                <a href="/deep-research" class="{% if request.path == '/deep-research' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/deep-research' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span class="sidebar-text">Research Lab</span>
                </a>

                <!-- Call Assistant - Archived for now, will add later -->
                <!--
                <a href="/call-assistant" class="{% if request.path == '/call-assistant' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/call-assistant' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <span class="sidebar-text">Call Assistant</span>
                </a>
                -->

                <a href="/document-generator" class="{% if request.path == '/document-generator' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/document-generator' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                    </svg>
                    <span class="sidebar-text">Super Agent</span>
                </a>

                <a href="/agentic-code" class="{% if request.path == '/agentic-code' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/agentic-code' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    <span class="sidebar-text">Design</span>
                </a>

                <!-- Code Wave - Archived -->
                <!--
                <a href="/code-wave" class="{% if request.path == '/code-wave' or request.path == '/code-ide' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/code-wave' or request.path == '/code-ide' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    <span class="sidebar-text">Code Wave</span>
                </a>
                -->


                <a href="/autowave" class="{% if request.path == '/autowave' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/autowave' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                    <span class="sidebar-text">Prime Agent</span>
                </a>

                <a href="/meeting-notes" class="{% if request.path == '/meeting-notes' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/meeting-notes' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <span class="sidebar-text">Meeting Notes</span>
                </a>

                <!-- ARCHIVED: Old history page hidden - replaced by professional history sidebar -->
                <!--
                <a href="/history" class="{% if request.path == '/history' %}bg-gray-800 text-white{% else %}text-gray-300 hover:bg-gray-800 hover:text-white{% endif %} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                    <svg class="mr-4 h-6 w-6 {% if request.path == '/history' %}text-white{% else %}text-gray-400 group-hover:text-white{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="sidebar-text">History</span>
                </a>
                -->
            </nav>






            <!-- User Email Section (directly above footer) -->
            <div id="user-email-section" class="px-4 py-2 border-t border-gray-700 relative" style="display: none; position: fixed; bottom: 72px; left: 0; width: 256px; background-color: #121212;">
                <div class="text-xs text-gray-300 transition-colors duration-200 flex items-center justify-between">
                    <div class="flex items-center space-x-2 cursor-pointer hover:text-gray-100 transition-colors duration-200" id="user-email-display">
                        <i class="fas fa-user-circle text-gray-400 text-sm"></i>
                        <span id="user-email-text" class="truncate max-w-32"></span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400 text-xs transition-transform duration-200" id="email-arrow"></i>
                </div>

                <!-- Account Dropdown Panel (positioned above email section) -->
                <div id="account-dropdown" class="absolute bottom-full left-0 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-40 transform scale-95 opacity-0 transition-all duration-200 ease-out pointer-events-none" style="margin-bottom: 8px; margin-left: 12px;">
                    <!-- Header -->
                    <div class="px-4 py-3 border-b border-gray-600 bg-gradient-to-r from-gray-800 to-gray-900 rounded-t-lg">
                        <h3 class="text-sm font-semibold text-gray-100">Account Settings</h3>
                    </div>

                    <!-- Content -->
                    <div class="p-3 space-y-3 max-h-96 overflow-y-auto">
                        <!-- 1. Upgrade Plan -->
                        <div class="border-b border-gray-100 pb-3">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs font-medium text-gray-700">Current Plan</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded" id="dropdown-plan-badge">Free</span>
                            </div>
                            <button id="dropdown-upgrade-btn" class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-2 px-3 rounded text-sm font-medium hover:from-blue-700 hover:to-indigo-700 transition-all">
                                <i class="fas fa-crown mr-1"></i>
                                Upgrade Plan
                            </button>
                        </div>

                        <!-- 2. Credits -->
                        <div class="border-b border-gray-100 pb-3">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs font-medium text-gray-700">Credits</span>
                                <span class="text-lg font-bold" id="dropdown-credits-count">100</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1.5 mb-1">
                                <div class="h-1.5 rounded-full transition-all duration-300" id="dropdown-credits-bar" style="width: 75%"></div>
                            </div>
                            <p class="text-xs text-gray-600" id="dropdown-credits-text">75 remaining this month</p>
                        </div>

                        <!-- 3. Profile -->
                        <div class="border-b border-gray-100 pb-3">
                            <button id="dropdown-profile-btn" class="w-full flex items-center p-2 rounded hover:bg-gray-800 hover:text-white transition-colors text-left">
                                <i class="fas fa-user-circle text-gray-400 mr-2"></i>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-800">Profile</div>
                                    <div class="text-xs text-gray-600" id="dropdown-email-display"><EMAIL></div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </button>
                        </div>

                        <!-- 4. Support -->
                        <div class="border-b border-gray-100 pb-3">
                            <a href="mailto:<EMAIL>" class="w-full flex items-center p-2 rounded hover:bg-gray-50 transition-colors">
                                <i class="fas fa-life-ring text-gray-400 mr-2"></i>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-800">Support</div>
                                    <div class="text-xs text-gray-600"><EMAIL></div>
                                </div>
                                <i class="fas fa-external-link-alt text-gray-400 text-xs"></i>
                            </a>
                        </div>

                        <!-- 5. Settings -->
                        <div class="border-b border-gray-100 pb-3">
                            <button id="dropdown-settings-btn" class="w-full flex items-center p-2 rounded hover:bg-gray-800 hover:text-white transition-colors text-left">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-800">Settings</div>
                                    <div class="text-xs text-gray-600">Language & Preferences</div>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                            </button>
                        </div>

                        <!-- 6. Sign Out -->
                        <div class="pt-1">
                            <button id="dropdown-signout-btn" class="w-full flex items-center justify-center p-2 rounded border border-red-200 text-red-600 hover:bg-red-50 transition-colors text-sm">
                                <i class="fas fa-sign-out-alt mr-1"></i>
                                Sign Out
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Footer -->
            <div class="px-4 py-4 text-xs text-gray-400 fixed bottom-0 left-0 w-64" style="border-top: 1px solid #e5e7eb;">
                <p>Powered by Botrex Technology</p>
                <p class="mt-1">© 2025 AutoWave. All rights reserved.</p>
            </div>
        </div>

        <!-- Collapsed Sidebar (Icon Only) -->
        <div id="collapsed-sidebar" class="w-16 fixed inset-y-0 left-0 z-30 flex flex-col" style="display: none; background-color: #121212; border-right: 1px solid #333;">
            <!-- Logo -->
            <div class="h-16 flex items-center justify-center" style="border-bottom: 1px solid #333;">
                <a href="/" class="flex items-center justify-center">
                    <img src="/static/images/autowave-logo.png" alt="AutoWave Logo" class="w-8 h-8" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline'">
                    <span class="autowave-logo text-xl tracking-tight font-bold text-white" style="display: none;">AW</span>
                </a>
            </div>

            <!-- Expand Button -->
            <div class="flex justify-center mt-2">
                <button id="expand-sidebar" class="p-1 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 focus:outline-none">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <!-- Icon Navigation -->
            <nav class="mt-5 px-2 space-y-1 pb-20">
                <a href="/" class="{% if request.path == '/' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md">
                    <svg class="h-6 w-6 {% if request.path == '/' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                </a>

                <a href="/deep-research" class="{% if request.path == '/deep-research' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Research Lab">
                    <svg class="h-6 w-6 {% if request.path == '/deep-research' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </a>

                <!-- Call Assistant - Archived for now, will add later -->
                <!--
                <a href="/call-assistant" class="{% if request.path == '/call-assistant' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Call Assistant">
                    <svg class="h-6 w-6 {% if request.path == '/call-assistant' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                </a>
                -->

                <a href="/document-generator" class="{% if request.path == '/document-generator' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Super Agent">
                    <svg class="h-6 w-6 {% if request.path == '/document-generator' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                    </svg>
                </a>

                <a href="/agentic-code" class="{% if request.path == '/agentic-code' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Design">
                    <svg class="h-6 w-6 {% if request.path == '/agentic-code' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                </a>

                <!-- Code Wave - Archived -->
                <!--
                <a href="/code-wave" class="{% if request.path == '/code-wave' or request.path == '/code-ide' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Code Wave">
                    <svg class="h-6 w-6 {% if request.path == '/code-wave' or request.path == '/code-ide' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                </a>
                -->


                <a href="/autowave" class="{% if request.path == '/autowave' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Prime Agent">
                    <svg class="h-6 w-6 {% if request.path == '/autowave' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                </a>

                <a href="/meeting-notes" class="{% if request.path == '/meeting-notes' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="Meeting Notes">
                    <svg class="h-6 w-6 {% if request.path == '/meeting-notes' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </a>

                <a href="/history" class="{% if request.path == '/history' %}bg-gray-100 text-black{% else %}text-gray-600 hover:bg-gray-100 hover:text-black{% endif %} group flex items-center justify-center p-2 rounded-md" title="History">
                    <svg class="h-6 w-6 {% if request.path == '/history' %}text-black{% else %}text-gray-400 group-hover:text-black{% endif %}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </a>
            </nav>

            <!-- Collapsed Sidebar Footer -->
            <div class="mt-auto fixed bottom-0 left-0 w-16 px-2 py-4 text-center text-xs text-gray-400" style="border-top: 1px solid #333;">
                <p class="truncate">Botrex Tech</p>
                <p class="mt-1 truncate">© 2025 AutoWave</p>
            </div>
        </div>

        <!-- Profile Popup Modal -->
        <div id="profile-popup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
            <div class="profile-modal rounded-lg shadow-xl w-full max-w-md mx-4" style="background-color: #121212; border: 1px solid #333;">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6" style="border-bottom: 1px solid #333;">
                    <h2 class="text-xl font-semibold" style="color: #e0e0e0;">Profile Settings</h2>
                    <button id="close-profile-popup" class="transition-colors" style="color: #aaa;" onmouseover="this.style.color='#e0e0e0'" onmouseout="this.style.color='#aaa'">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6">
                    <!-- Profile Avatar -->
                    <div class="text-center mb-6">
                        <div class="profile-avatar w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-3" style="background: linear-gradient(135deg, #2d2d2d, #1e1e1e);">
                            <i class="fas fa-user text-2xl" style="color: #aaa;"></i>
                        </div>
                        <p class="text-sm" style="color: #aaa;">AutoWave Account</p>
                    </div>

                    <!-- Profile Form -->
                    <form id="profile-form" class="space-y-4">
                        <div>
                            <label for="profile-email" class="block text-sm font-medium mb-2" style="color: #e0e0e0;">Email Address</label>
                            <input type="email" id="profile-email" name="email"
                                   class="w-full px-3 py-2 rounded-md focus:outline-none transition-all"
                                   style="background-color: #2d2d2d; border: 1px solid #444; color: #e0e0e0;"
                                   onfocus="this.style.borderColor='#555'; this.style.boxShadow='0 0 0 2px rgba(80, 80, 80, 0.3)'"
                                   onblur="this.style.borderColor='#444'; this.style.boxShadow='none'"
                                   readonly>
                        </div>

                        <div>
                            <label for="profile-name" class="block text-sm font-medium mb-2" style="color: #e0e0e0;">Display Name</label>
                            <input type="text" id="profile-name" name="name"
                                   class="w-full px-3 py-2 rounded-md focus:outline-none transition-all"
                                   style="background-color: #2d2d2d; border: 1px solid #444; color: #e0e0e0;"
                                   onfocus="this.style.borderColor='#555'; this.style.boxShadow='0 0 0 2px rgba(80, 80, 80, 0.3)'"
                                   onblur="this.style.borderColor='#444'; this.style.boxShadow='none'"
                                   placeholder="Enter your display name">
                        </div>

                        <div class="flex items-center justify-between pt-4">
                            <button type="submit" class="px-4 py-2 rounded-md transition-colors focus:outline-none"
                                    style="background-color: #2d2d2d; color: #e0e0e0; border: 1px solid #444;"
                                    onmouseover="this.style.backgroundColor='#3d3d3d'"
                                    onmouseout="this.style.backgroundColor='#2d2d2d'">
                                Save Changes
                            </button>
                            <div class="text-xs" style="color: #888;">
                                <i class="fas fa-shield-alt mr-1"></i>
                                Email verified
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Sign Out Section -->
                <div class="px-6 pb-6">
                    <button id="signout-btn" class="w-full text-sm font-medium transition-colors focus:outline-none"
                            style="color: #ff6b6b;"
                            onmouseover="this.style.color='#ff5252'"
                            onmouseout="this.style.color='#ff6b6b'">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Sign Out
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu button (only visible on small screens) -->
        <div id="mobile-menu-button" class="fixed top-0 left-0 z-40 p-4 md:hidden" style="background: transparent;">
            <svg class="h-6 w-6 text-gray-600 hover:text-gray-800 cursor-pointer" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true" onclick="toggleMobileSidebar()">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
        </div>

        <!-- Mobile overlay (only visible when sidebar is open on mobile) -->
        <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden md:hidden" style="display: none;"></div>

        <!-- Main Content -->
        <div id="main-content" class="flex-1 overflow-auto transition-all duration-300 ease-in-out bg-white flex flex-col" style="margin-left: 16rem;">
            <!-- Centered Page Header -->
            <div class="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-10" id="page-header">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center h-12">
                        <div class="w-12"></div> <!-- Empty div for balance -->
                        <h1 class="text-base font-medium text-gray-700">{% block header %}Welcome to AutoWave{% endblock %}</h1>
                        <div class="flex items-center space-x-3">
                            <!-- User Authentication -->
                            <div id="auth-section" style="display: none;">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <main class="w-full mx-auto px-4 sm:px-6 lg:px-8 py-4 bg-white rounded-lg shadow-sm flex-1 flex flex-col">
                <!-- Page Content -->
                <div class="bg-white shadow-sm rounded-lg overflow-hidden flex-1 flex flex-col w-full">
                    {% block content %}{% endblock %}
                </div>
            </main>
    </div>
</div>

<!-- Sidebar toggle script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.getElementById('sidebar');
        const collapsedSidebar = document.getElementById('collapsed-sidebar');
        const mainContent = document.getElementById('main-content');
        const collapseSidebarBtn = document.getElementById('collapse-sidebar');
        const expandSidebarBtn = document.getElementById('expand-sidebar');
        const mobileMenuBtn = document.getElementById('mobile-menu-button');

        console.log('Sidebar elements:', {
            sidebar: sidebar,
            collapsedSidebar: collapsedSidebar,
            mainContent: mainContent,
            collapseSidebarBtn: collapseSidebarBtn,
            expandSidebarBtn: expandSidebarBtn,
            mobileMenuBtn: mobileMenuBtn
        });

        // Function to collapse sidebar
        function collapseSidebar() {
            console.log('Collapsing sidebar');

            // Block pricing sidebar during sidebar operations
            window.sidebarOperationInProgress = true;

            if (!sidebar || !collapsedSidebar || !mainContent) {
                console.error('Missing required elements for sidebar collapse');
                return;
            }

            sidebar.style.display = 'none';
            collapsedSidebar.style.display = 'block';
            mainContent.classList.remove('ml-64');
            mainContent.classList.add('ml-16');

            // Store preference in localStorage
            localStorage.setItem('sidebarCollapsed', 'true');

            // Clear the flag after animation
            setTimeout(function() {
                window.sidebarOperationInProgress = false;
            }, 500);
        }

        // Function to expand sidebar
        function expandSidebar() {
            console.log('Expanding sidebar');

            // Block pricing sidebar during sidebar operations
            window.sidebarOperationInProgress = true;

            if (!sidebar || !collapsedSidebar || !mainContent) {
                console.error('Missing required elements for sidebar expand');
                return;
            }

            sidebar.style.display = 'block';
            collapsedSidebar.style.display = 'none';
            mainContent.classList.remove('ml-16');
            mainContent.classList.add('ml-64');

            // Store preference in localStorage
            localStorage.setItem('sidebarCollapsed', 'false');

            // Clear the flag after animation
            setTimeout(function() {
                window.sidebarOperationInProgress = false;
            }, 500);
        }

        // Mobile menu toggle
        function toggleMobileSidebar() {
            console.log('Toggling mobile sidebar');
            if (!sidebar || !mainContent) {
                console.error('Missing required elements for mobile sidebar toggle');
                return;
            }

            const mobileOverlay = document.getElementById('mobile-overlay');

            if (window.innerWidth < 768) { // Only for mobile screens
                if (sidebar.style.display === 'none' || sidebar.style.display === '') {
                    // Show sidebar and overlay
                    sidebar.style.display = 'block';
                    sidebar.style.transform = 'translateX(0)';
                    sidebar.style.zIndex = '30';
                    mainContent.style.marginLeft = '0';
                    collapsedSidebar.style.display = 'none';

                    // Show overlay
                    if (mobileOverlay) {
                        mobileOverlay.style.display = 'block';
                        mobileOverlay.addEventListener('click', toggleMobileSidebar);
                    }
                } else {
                    // Hide sidebar and overlay
                    sidebar.style.display = 'none';
                    mainContent.style.marginLeft = '0';

                    // Hide overlay
                    if (mobileOverlay) {
                        mobileOverlay.style.display = 'none';
                        mobileOverlay.removeEventListener('click', toggleMobileSidebar);
                    }
                }
            }
        }

        // Make toggleMobileSidebar available globally for the onclick handler
        window.toggleMobileSidebar = toggleMobileSidebar;

        // Add event listeners
        if (collapseSidebarBtn) {
            collapseSidebarBtn.addEventListener('click', function(e) {
                e.preventDefault();
                collapseSidebar();
            });
        }

        if (expandSidebarBtn) {
            expandSidebarBtn.addEventListener('click', function(e) {
                e.preventDefault();
                expandSidebar();
            });
        }

        // We're now using the onclick attribute directly on the SVG element
        // No need for the event listener here anymore

        // Handle responsive behavior
        function handleResize() {
            console.log('Window resized, width:', window.innerWidth);

            const mobileOverlay = document.getElementById('mobile-overlay');

            if (window.innerWidth < 768) {
                // On mobile, start with sidebar hidden
                console.log('Mobile view detected');
                sidebar.style.display = 'none';
                collapsedSidebar.style.display = 'none';
                mainContent.style.marginLeft = '0';

                // Hide overlay
                if (mobileOverlay) {
                    mobileOverlay.style.display = 'none';
                }
            } else {
                // On desktop, restore saved preference
                console.log('Desktop view detected');
                mainContent.style.marginLeft = '';

                // Hide overlay
                if (mobileOverlay) {
                    mobileOverlay.style.display = 'none';
                }

                if (localStorage.getItem('sidebarCollapsed') === 'true') {
                    collapseSidebar();
                } else {
                    expandSidebar();
                }
            }
        }

        // Initial setup based on screen size
        handleResize();

        // Listen for window resize events
        window.addEventListener('resize', handleResize);

        // Force initial state after a short delay to ensure DOM is fully loaded
        setTimeout(function() {
            if (window.innerWidth >= 768) {
                if (localStorage.getItem('sidebarCollapsed') === 'true') {
                    collapseSidebar();
                } else {
                    expandSidebar();
                }
            }
        }, 100);
    });

    // Authentication Status Check
    async function checkAuthStatus() {
        try {
            const response = await fetch('/auth/status');
            const data = await response.json();

            const authSection = document.getElementById('auth-section');
            const userEmailSection = document.getElementById('user-email-section');
            const userEmailText = document.getElementById('user-email-text');

            if (!authSection) return;

            if (data.authenticated) {
                // User is logged in - show in header
                authSection.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <i class="fas fa-user-circle text-gray-400"></i>
                            <span class="hidden sm:inline">${data.user.email}</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <button onclick="openProfilePopup()" class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100" title="Profile">
                                <i class="fas fa-cog text-sm"></i>
                            </button>
                            <button onclick="logout()" class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100" title="Sign Out">
                                <i class="fas fa-sign-out-alt text-sm"></i>
                            </button>
                        </div>
                    </div>
                `;

                // Show user email in sidebar
                if (userEmailSection && userEmailText) {
                    userEmailSection.style.display = 'block';
                    userEmailText.textContent = data.user.email;

                    // Add click handler to open account dropdown
                    const userEmailDisplay = document.getElementById('user-email-display');
                    if (userEmailDisplay && !userEmailDisplay.hasAttribute('data-dropdown-handler')) {
                        userEmailDisplay.setAttribute('data-dropdown-handler', 'true');
                        userEmailDisplay.addEventListener('click', function(event) {
                            event.preventDefault();
                            event.stopPropagation();
                            openAccountDropdown(event);
                        });
                    }

                    // Update dropdown email display
                    const dropdownEmailDisplay = document.getElementById('dropdown-email-display');
                    if (dropdownEmailDisplay) {
                        dropdownEmailDisplay.textContent = data.user.email;
                    }

                    // Load real user credit information
                    loadUserCredits();
                }
            } else {
                // User is not logged in
                // Only redirect to login if not on homepage (where we show the auth overlay)
                const currentPath = window.location.pathname;
                if (currentPath !== '/' && currentPath !== '') {
                    window.location.href = '/auth/login';
                }

                // Hide user email in sidebar
                if (userEmailSection) {
                    userEmailSection.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
            // Only redirect to login on error if not on homepage
            const currentPath = window.location.pathname;
            if (currentPath !== '/' && currentPath !== '') {
                window.location.href = '/auth/login';
            }

            if (userEmailSection) {
                userEmailSection.style.display = 'none';
            }
        }
    }

    // Logout function
    async function logout() {
        if (confirm('Are you sure you want to sign out?')) {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    window.location.href = '/auth/login';
                } else {
                    alert('Error signing out. Please try again.');
                }
            } catch (error) {
                console.error('Logout error:', error);
                alert('Error signing out. Please try again.');
            }
        }
    }

    // Account Dropdown Functions (stays within sidebar)
    function openAccountDropdown(event) {
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        }

        console.log('Opening account dropdown from email click');
        const dropdown = document.getElementById('account-dropdown');
        const emailArrow = document.getElementById('email-arrow');

        if (dropdown) {
            dropdown.classList.remove('scale-95', 'opacity-0', 'pointer-events-none');
            dropdown.classList.add('scale-100', 'opacity-100', 'pointer-events-auto');

            if (emailArrow) {
                emailArrow.style.transform = 'rotate(90deg)';
            }
        }
    }

    function closeAccountDropdown() {
        const dropdown = document.getElementById('account-dropdown');
        const emailArrow = document.getElementById('email-arrow');

        if (dropdown) {
            dropdown.classList.remove('scale-100', 'opacity-100', 'pointer-events-auto');
            dropdown.classList.add('scale-95', 'opacity-0', 'pointer-events-none');

            if (emailArrow) {
                emailArrow.style.transform = 'rotate(0deg)';
            }
        }
    }

    // Load user credits from API
    async function loadUserCredits() {
        try {
            console.log('Loading user credits for sidebar...');
            const response = await fetch('/payment/user-info');
            const data = await response.json();

            if (data.success && data.user_info && data.user_info.credits) {
                const credits = data.user_info.credits;
                console.log('Loaded credits:', credits);
                updateDropdownCredits(credits.remaining, credits.total, credits.type);
            } else {
                console.log('Failed to load credits, using fallback');
                updateDropdownCredits(50, 50, 'daily');
            }
        } catch (error) {
            console.error('Error loading user credits:', error);
            updateDropdownCredits(50, 50, 'daily');
        }
    }

    // Dropdown Credit indicator functionality with color changes
    function updateDropdownCredits(credits, maxCredits, creditType = 'monthly') {
        const creditsDisplay = document.getElementById('dropdown-credits-count');
        const progressBar = document.getElementById('dropdown-credits-bar');
        const creditsText = document.getElementById('dropdown-credits-text');

        // Check if this is an admin user with unlimited credits
        const isUnlimited = credits === -1 || maxCredits === -1 || creditType === 'unlimited';

        if (creditsDisplay) {
            if (isUnlimited) {
                creditsDisplay.textContent = 'Unlimited';
                creditsDisplay.className = 'text-lg font-bold text-purple-600';
            } else {
                creditsDisplay.textContent = credits;
                creditsDisplay.className = ''; // Reset class for regular users
            }
        }

        // Update text based on credit type
        if (creditsText) {
            if (isUnlimited) {
                creditsText.textContent = 'Admin - Unlimited Credits';
            } else if (creditType === 'daily') {
                creditsText.textContent = `${credits} remaining today`;
            } else {
                creditsText.textContent = `${credits} remaining this month`;
            }
        }

        if (progressBar) {
            if (isUnlimited) {
                // Admin users get full purple progress bar
                progressBar.style.width = '100%';
                progressBar.className = 'h-1.5 rounded-full transition-all duration-300 bg-gradient-to-r from-purple-500 to-blue-500';
                // Don't override creditsDisplay class - it's already set above
            } else {
                const percentage = (credits / maxCredits) * 100;
                progressBar.style.width = percentage + '%';

                // Change color to dark red when credits are low (10 or less)
                if (credits <= 10) {
                    progressBar.className = 'h-1.5 rounded-full transition-all duration-300 bg-gradient-to-r from-red-600 to-red-800';
                    if (creditsDisplay) creditsDisplay.className = 'text-lg font-bold text-red-600';
                } else if (credits <= 25) {
                    progressBar.className = 'h-1.5 rounded-full transition-all duration-300 bg-gradient-to-r from-yellow-500 to-orange-500';
                    if (creditsDisplay) creditsDisplay.className = 'text-lg font-bold text-yellow-600';
                } else {
                    progressBar.className = 'h-1.5 rounded-full transition-all duration-300 bg-gradient-to-r from-green-500 to-emerald-500';
                    if (creditsDisplay) creditsDisplay.className = 'text-lg font-bold text-green-600';
                }
            }
        }
    }

    // Global credit refresh function - can be called from any page
    window.refreshCredits = function() {
        console.log('Refreshing credits globally...');

        // Refresh sidebar credits
        if (typeof loadUserCredits === 'function') {
            loadUserCredits();
        }

        // Refresh pricing page credits if on pricing page
        if (typeof loadUserPlanInfo === 'function') {
            loadUserPlanInfo();
        }

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('creditsUpdated'));
    };

    // Auto-refresh credits every 30 seconds (disabled when Universal Credit System is active)
    setInterval(function() {
        if (window.refreshCredits && !window.legacyAutoRefreshDisabled) {
            window.refreshCredits();
        }
    }, 30000);

    // Global function to consume credits and update display
    window.consumeCredits = async function(taskType, amount = null) {
        try {
            console.log(`Consuming credits for task: ${taskType}, amount: ${amount}`);

            // Use Universal Credit System if available
            if (window.creditSystem && window.creditSystem.isInitialized) {
                console.log('Using Universal Credit System for credit consumption');
                return await window.creditSystem.consumeCredits(taskType, amount);
            }

            // Fallback to direct API call
            console.log('Using fallback credit consumption method');
            const response = await fetch('/payment/consume-credits', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_type: taskType,
                    amount: amount
                })
            });

            const result = await response.json();

            if (result.success) {
                console.log(`Successfully consumed ${result.credits_consumed} credits`);

                // Update all credit displays immediately
                if (window.refreshCredits) {
                    window.refreshCredits();
                }

                // Show success notification
                if (window.showNotification) {
                    window.showNotification(`Used ${result.credits_consumed} credits for ${taskType}`, 'success');
                }

                return {
                    success: true,
                    consumed: result.credits_consumed,
                    remaining: result.remaining_credits
                };
            } else {
                console.error('Failed to consume credits:', result.error);

                // Show error notification
                if (window.showNotification) {
                    window.showNotification(result.error, 'error');
                }

                return {
                    success: false,
                    error: result.error
                };
            }

        } catch (error) {
            console.error('Error consuming credits:', error);
            return {
                success: false,
                error: 'Network error'
            };
        }
    };

    // Simple notification system
    window.showNotification = function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

        // Set color based on type
        if (type === 'success') {
            notification.style.background = 'linear-gradient(90deg, #10b981, #059669)';
        } else if (type === 'error') {
            notification.style.background = 'linear-gradient(90deg, #ef4444, #dc2626)';
        } else {
            notification.style.background = 'linear-gradient(90deg, #3b82f6, #2563eb)';
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(full)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    };

    // Profile Popup Functions
    function openProfilePopup() {
        const popup = document.getElementById('profile-popup');
        const profileEmail = document.getElementById('profile-email');
        const profileName = document.getElementById('profile-name');

        if (popup) {
            popup.style.display = 'flex';

            // Populate form with current user data
            fetch('/auth/status')
                .then(response => response.json())
                .then(data => {
                    if (data.authenticated) {
                        if (profileEmail) profileEmail.value = data.user.email;
                        if (profileName) profileName.value = data.user.name || '';
                    }
                })
                .catch(error => console.error('Error loading profile data:', error));
        }
    }

    function closeProfilePopup() {
        const popup = document.getElementById('profile-popup');
        if (popup) {
            popup.style.display = 'none';
        }
    }

    // Profile form submission
    async function updateProfile(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const profileData = {
            name: formData.get('name')
        };

        try {
            const response = await fetch('/auth/profile/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(profileData)
            });

            const result = await response.json();

            if (result.success) {
                alert('Profile updated successfully!');
                closeProfilePopup();
                checkAuthStatus(); // Refresh auth status
            } else {
                alert(result.error || 'Failed to update profile');
            }
        } catch (error) {
            console.error('Profile update error:', error);
            alert('Error updating profile. Please try again.');
        }
    }

    // Sign out from popup
    async function signOutFromPopup() {
        if (confirm('Are you sure you want to sign out?')) {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    closeProfilePopup();
                    window.location.href = '/auth/login';
                } else {
                    alert('Error signing out. Please try again.');
                }
            } catch (error) {
                console.error('Logout error:', error);
                alert('Error signing out. Please try again.');
            }
        }
    }

    // Check auth status when page loads
    document.addEventListener('DOMContentLoaded', function() {
        checkAuthStatus();

        // Initialize Universal Credit System integration
        initializeUniversalCreditSidebarIntegration();

        // Account Dropdown Event Listeners
        const dropdownUpgradeBtn = document.getElementById('dropdown-upgrade-btn');
        if (dropdownUpgradeBtn) {
            dropdownUpgradeBtn.addEventListener('click', function() {
                window.location.href = '/pricing';
            });
        }

        // Profile button - open existing profile popup
        const dropdownProfileBtn = document.getElementById('dropdown-profile-btn');
        if (dropdownProfileBtn) {
            dropdownProfileBtn.addEventListener('click', function() {
                closeAccountDropdown();
                setTimeout(openProfilePopup, 300);
            });
        }

        // Settings button - show language settings
        const dropdownSettingsBtn = document.getElementById('dropdown-settings-btn');
        if (dropdownSettingsBtn) {
            dropdownSettingsBtn.addEventListener('click', function() {
                alert('Settings: Language preferences coming soon!');
            });
        }

        // Dropdown Sign Out button
        const dropdownSignoutBtn = document.getElementById('dropdown-signout-btn');
        if (dropdownSignoutBtn) {
            dropdownSignoutBtn.addEventListener('click', function() {
                closeAccountDropdown();
                signOutFromPopup();
            });
        }



        // Add event listeners for profile popup
        const closeBtn = document.getElementById('close-profile-popup');
        const profileForm = document.getElementById('profile-form');
        const popup = document.getElementById('profile-popup');

        if (closeBtn) {
            closeBtn.addEventListener('click', closeProfilePopup);
        }

        if (profileForm) {
            profileForm.addEventListener('submit', updateProfile);
        }

        // Close popup when clicking outside
        if (popup) {
            popup.addEventListener('click', function(event) {
                if (event.target === popup) {
                    closeProfilePopup();
                }
            });
        }

        // Close account dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('account-dropdown');
            const userEmailDisplay = document.getElementById('user-email-display');

            if (dropdown && !dropdown.contains(e.target) &&
                userEmailDisplay && !userEmailDisplay.contains(e.target)) {
                if (dropdown.classList.contains('opacity-100')) {
                    closeAccountDropdown();
                }
            }
        });

    });

    // Integration with Universal Credit System for sidebar
    function initializeUniversalCreditSidebarIntegration() {
        console.log('Initializing Universal Credit System integration for sidebar...');

        // Wait for Universal Credit System to be ready
        const checkCreditSystem = setInterval(() => {
            if (window.creditSystem && window.creditSystem.isInitialized) {
                console.log('Universal Credit System is ready, updating sidebar...');

                // Update sidebar with current credits
                const creditStatus = window.creditSystem.getCreditStatus();
                updateDropdownCredits(creditStatus.remaining, creditStatus.total, creditStatus.type);

                // Disable legacy auto-refresh to prevent conflicts
                window.legacyAutoRefreshDisabled = true;
                console.log('Legacy auto-refresh disabled to prevent conflicts with Universal Credit System');

                clearInterval(checkCreditSystem);
            }
        }, 100);

        // Clear interval after 10 seconds if credit system doesn't load
        setTimeout(() => {
            clearInterval(checkCreditSystem);
        }, 10000);
    }

</script>

<!-- Professional History System -->
<div id="history-toggle" class="history-toggle" title="Activity History">
    <i class="fas fa-history"></i>
</div>

<div id="history-sidebar" class="history-sidebar">
    <!-- Header -->
    <div class="history-header">
        <h3 class="history-title">Activity History</h3>
        <div class="history-header-buttons">
            <button id="refresh-history" class="history-refresh" title="Refresh History">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button id="close-history-sidebar" class="history-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="history-controls">
        <input type="text" id="history-search" class="history-search" placeholder="Search activities...">

        <div class="history-filters">
            <div class="filter-chip active" data-filter="all">All</div>
            <div class="filter-chip" data-filter="autowave_chat">Chat</div>
            <div class="filter-chip" data-filter="prime_agent">Prime Agent</div>
            <div class="filter-chip" data-filter="agentic_code">Agent Alpha</div>
            <div class="filter-chip" data-filter="research_lab">Research</div>
            <div class="filter-chip" data-filter="agent_wave">Super Agent</div>
        </div>
    </div>

    <!-- History List -->
    <div id="history-list" class="history-list">
        <!-- Loading state -->
        <div id="history-loading" class="history-loading">
            <div class="loading-spinner"></div>
            <p>Loading your activity history...</p>
        </div>

        <!-- Empty state -->
        <div id="history-empty" class="history-empty" style="display: none;">
            <div class="history-empty-icon">
                <i class="fas fa-clock"></i>
            </div>
            <p>No activity found</p>
            <p class="text-xs mt-2">Start using AutoWave agents to see your history here</p>
        </div>
    </div>
</div>

<!-- Overlay -->
<div id="history-overlay" class="history-overlay"></div>

<style>
/* Professional History System Styles */
.history-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #1a1a1a;
    border: 1px solid #333;
    color: #e0e0e0;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    z-index: 998;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: block;
}

.history-toggle:hover {
    background: #2d2d2d;
    border-color: #444;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.history-sidebar {
    position: fixed;
    top: 0;
    right: -420px;
    width: 420px;
    height: 100vh;
    background: #121212;
    border-left: 1px solid #333;
    z-index: 1000;
    transition: right 0.3s ease-in-out;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.history-sidebar.open {
    right: 0;
}

.history-header {
    padding: 20px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #1a1a1a;
}

.history-title {
    color: #e0e0e0;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.history-close {
    background: none;
    border: none;
    color: #aaa;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.history-close:hover {
    color: #e0e0e0;
    background: #333;
}

.history-header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.history-refresh {
    background: none;
    border: none;
    color: #aaa;
    font-size: 16px;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-refresh:hover {
    color: #e0e0e0;
    background: #333;
}

.history-refresh.spinning {
    animation: spin 1s linear infinite;
}

.history-controls {
    padding: 20px;
    border-bottom: 1px solid #333;
    background: #1a1a1a;
}

.history-search {
    width: 100%;
    padding: 12px;
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 6px;
    color: #e0e0e0;
    font-size: 14px;
    margin-bottom: 16px;
    transition: all 0.2s;
}

.history-search:focus {
    outline: none;
    border-color: #555;
    box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
}

.history-filters {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-chip {
    padding: 6px 12px;
    background: #2d2d2d;
    border: 1px solid #444;
    border-radius: 16px;
    color: #aaa;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.filter-chip:hover,
.filter-chip.active {
    background: #3d3d3d;
    color: #e0e0e0;
    border-color: #555;
}

.history-list {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.history-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #333;
    border-top: 3px solid #666;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.history-empty {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.history-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.history-item {
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 12px;
    position: relative;
}

.history-item:hover {
    background: #2d2d2d;
    border-color: #444;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.agent-badge {
    background: #333;
    color: #e0e0e0;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.history-timestamp {
    color: #888;
    font-size: 12px;
}

.history-preview {
    color: #ccc;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.continue-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    color: #4CAF50;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.2s;
}

.history-item:hover .continue-indicator {
    opacity: 1;
}

.history-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out;
}

.history-overlay.active {
    opacity: 1;
    visibility: visible;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .history-sidebar {
        width: 100%;
        right: -100%;
    }

    .history-toggle {
        top: 16px;
        right: 16px;
        padding: 10px;
    }
}

/* Hide history toggle on history page */
body.history-page .history-toggle {
    display: none;
}
</style>

<!-- Professional History System JavaScript -->
<script src="{{ url_for('static', filename='js/professional_history.js') }}"></script>

<!-- Session Restoration System JavaScript -->
<script src="{{ url_for('static', filename='js/session_restoration.js') }}"></script>

<!-- Universal Credit System JavaScript -->
<script src="{{ url_for('static', filename='js/universal_credit_system.js') }}?v=admin-fix-2024"></script>

<!-- Professional History System is now active -->

<!-- JavaScript functionality is now in static/js/main.js -->
{% block extra_js %}{% endblock %}
</body>
</html>
