{% extends "layout.html" %}

{% block title %}Deep Research - AutoWave{% endblock %}

{% block header %}Deep Academic Research{% endblock %}

{% block extra_css %}
<style>
    /* Dark theme styles */
    body {
        background-color: #121212;
        color: #e0e0e0;
    }

    #main-content {
        background-color: #121212;
    }

    main {
        background-color: #121212;
    }

    .bg-white {
        background-color: #121212 !important;
    }

    .text-gray-700 {
        color: #e0e0e0 !important;
    }

    .border-gray-200 {
        border-color: #333 !important;
    }

    .shadow-sm {
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.5) !important;
    }

    /* Research container styles */
    .research-container {
        background-color: #1e1e1e;
        border-radius: 8px;
        border: 1px solid #333;
        padding: 20px;
        margin-bottom: 20px;
    }

    /* Modern Expandable Input Styles for Research */
    .modern-research-input-container {
        background-color: #2d2d2d;
        border: 1px solid #444;
        border-radius: 12px;
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .modern-research-input-container:focus-within {
        border-color: #555;
        box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
    }

    .modern-research-expandable-input {
        background: transparent;
        border: none;
        color: #e0e0e0;
        padding: 16px 16px 50px 16px;
        width: 100%;
        font-size: 16px;
        line-height: 1.5;
        resize: none;
        outline: none;
        min-height: 24px;
        max-height: 200px;
        overflow-y: auto;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transition: all 0.2s ease;
    }

    .modern-research-expandable-input::placeholder {
        color: #888;
    }

    .modern-research-expandable-input::-webkit-scrollbar {
        width: 4px;
    }

    .modern-research-expandable-input::-webkit-scrollbar-track {
        background: transparent;
    }

    .modern-research-expandable-input::-webkit-scrollbar-thumb {
        background: #555;
        border-radius: 2px;
    }

    .modern-research-expandable-input::-webkit-scrollbar-thumb:hover {
        background: #666;
    }

    .research-input-bottom-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: transparent;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 40px;
        pointer-events: none;
        z-index: 10;
    }

    .research-input-bottom-bar > * {
        pointer-events: auto;
    }

    .research-input-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .research-input-actions-left {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .research-input-actions-right {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .research-input-action-btn {
        background: transparent;
        border: none;
        color: #888;
        cursor: pointer;
        padding: 6px;
        border-radius: 6px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .research-input-action-btn:hover {
        color: #e0e0e0;
        background: rgba(255, 255, 255, 0.1);
    }

    /* Flip the import icon to match microphone direction */
    .research-input-action-btn.flipped-icon {
        transform: scaleY(-1);
    }

    .research-mic-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        padding: 6px;
        border-radius: 50%;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
    }

    .research-mic-btn:hover {
        color: #e0e0e0;
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Button styles */
    .research-button {
        background-color: #2d2d2d;
        color: #e0e0e0;
        border: 1px solid #444;
        border-radius: 8px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .research-button:hover {
        background-color: #3d3d3d;
    }

    .research-button:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(80, 80, 80, 0.3);
    }

    /* Results styles */
    .research-results {
        background-color: #1e1e1e;
        border-radius: 8px;
        border: 1px solid #333;
        padding: 20px;
        margin-top: 20px;
    }

    .research-step {
        background-color: #2d2d2d;
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
    }

    .step-icon {
        margin-right: 12px;
        color: #888;
        font-size: 18px;
        display: flex;
        align-items: center;
    }

    .step-text {
        color: #ccc;
    }

    /* Loading animation */
    .loading-dots {
        display: inline-flex;
    }

    .loading-dots span {
        width: 6px;
        height: 6px;
        margin: 0 2px;
        background-color: #888;
        border-radius: 50%;
        animation: pulse 1.5s infinite;
    }

    .loading-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .loading-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 0.4;
        }
        50% {
            opacity: 1;
        }
    }

    /* Final research report */
    .research-report {
        background-color: #1e1e1e;
        border-radius: 8px;
        border: 1px solid #333;
        padding: 20px;
        margin-top: 20px;
        line-height: 1.6;
        position: relative;
    }

    .research-report h1,
    .research-report h2,
    .research-report h3 {
        color: #e0e0e0;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
    }

    .research-report p {
        margin-bottom: 1em;
        color: #ccc;
    }

    .research-report ul,
    .research-report ol {
        margin-bottom: 1em;
        padding-left: 1.5em;
        color: #ccc;
    }

    .research-report blockquote {
        border-left: 3px solid #444;
        padding-left: 1em;
        margin-left: 0;
        margin-right: 0;
        color: #aaa;
    }

    /* Feedback and download icons */
    .research-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #333;
    }

    .action-button {
        background-color: #2d2d2d;
        color: #aaa;
        border: 1px solid #444;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .action-button:hover {
        background-color: #3d3d3d;
        color: #e0e0e0;
    }

    .action-button.liked {
        color: #4CAF50;
        border-color: #4CAF50;
    }

    .action-button.disliked {
        color: #F44336;
        border-color: #F44336;
    }

    .action-button {
        font-size: 18px;
        line-height: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Fixed input container at bottom */
    .fixed-input-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #121212;
        padding: 16px;
        border-top: 1px solid #333;
        z-index: 100;
        margin-left: 16rem; /* Match sidebar width */
        transition: margin-left 0.3s ease;
    }

    @media (max-width: 768px) {
        .fixed-input-container {
            margin-left: 0;
        }
    }

    /* Adjust main content to prevent overlap with fixed input */
    .content-wrapper {
        padding-bottom: 80px;
    }

    /* Collapsed sidebar adjustment */
    body.collapsed-sidebar .fixed-input-container {
        margin-left: 4rem;
    }

    /* File upload styles */
    .file-preview {
        background-color: #1e1e1e;
        border: 1px solid #333;
        border-radius: 0.5rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
    }

    .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        background-color: #2d2d2d;
        border-radius: 0.375rem;
        margin-bottom: 0.5rem;
        border: 1px solid #333;
    }

    .file-item:last-child {
        margin-bottom: 0;
    }

    .file-info {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .file-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.75rem;
        color: #60a5fa;
    }

    .file-details {
        flex: 1;
    }

    .file-name {
        font-weight: 500;
        color: #e0e0e0;
        font-size: 0.875rem;
    }

    .file-size {
        color: #9ca3af;
        font-size: 0.75rem;
    }

    .file-remove {
        background: none;
        border: none;
        color: #ef4444;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s;
    }

    .file-remove:hover {
        background-color: #333;
    }

    .file-image-preview {
        width: 3rem;
        height: 3rem;
        object-fit: cover;
        border-radius: 0.375rem;
        margin-right: 0.75rem;
        border: 1px solid #333;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        /* Container adjustments */
        .research-container {
            margin: 0 16px 16px 16px;
            padding: 16px;
        }

        .content-wrapper {
            padding: 16px;
        }

        /* Input container */
        .modern-research-input-container {
            margin: 0;
        }

        .modern-research-expandable-input {
            font-size: 16px !important;
            padding: 12px 12px 45px 12px;
            min-height: 80px;
        }

        /* Research actions */
        .research-input-actions {
            flex-wrap: wrap;
            gap: 4px;
            padding: 8px 12px;
        }

        .research-input-action-btn {
            padding: 6px 8px;
            font-size: 12px;
        }

        /* Results container */
        .research-results {
            margin: 16px;
            padding: 16px;
        }

        /* File upload area */
        .file-upload-area {
            padding: 16px;
            margin: 8px 0;
        }

        .file-upload-text {
            font-size: 14px;
        }

        /* File preview */
        .file-preview-item {
            padding: 8px;
            margin: 4px 0;
        }

        .file-image-preview {
            width: 2rem;
            height: 2rem;
            margin-right: 0.5rem;
        }

        /* Research title */
        h1, h2 {
            font-size: 20px !important;
            line-height: 1.3;
            margin-bottom: 12px;
        }

        /* Button adjustments */
        button {
            padding: 8px 16px;
            font-size: 14px;
        }

        /* Loading states */
        .loading-spinner {
            width: 20px;
            height: 20px;
        }
    }

    @media (max-width: 480px) {
        /* Extra small screens */
        .research-container {
            margin: 0 8px 12px 8px;
            padding: 12px;
        }

        .content-wrapper {
            padding: 8px;
        }

        .modern-research-expandable-input {
            font-size: 14px !important;
            padding: 10px 10px 40px 10px;
        }

        h1, h2 {
            font-size: 18px !important;
        }

        .research-input-action-btn {
            padding: 4px 6px;
            font-size: 11px;
        }

        .file-image-preview {
            width: 1.5rem;
            height: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <div class="research-container">
        <h2 class="text-xl font-semibold mb-4">Academic Research Assistant</h2>
        <p class="mb-4">Enter a research topic to generate a comprehensive, PhD-level analysis with citations and academic insights.</p>
    </div>

    <div id="researchResults" class="research-results hidden">
        <div id="researchSteps" class="mb-4">
            <!-- Research steps will be added here dynamically -->
        </div>
    </div>

    <div id="finalReport" class="research-report hidden">
        <!-- Final research report will be added here -->
        <div class="research-actions">
            <button id="likeButton" class="action-button" title="Like this research">
                👍
            </button>
            <button id="dislikeButton" class="action-button" title="Dislike this research">
                👎
            </button>
            <button id="downloadButton" class="action-button" title="Download as PDF">
                📥
            </button>
        </div>
    </div>
</div>

<div class="fixed-input-container">
    <div class="flex">
        <div class="relative flex-1 mr-2">
            <div class="modern-research-input-container">
                <textarea
                    id="researchQuery"
                    class="modern-research-expandable-input"
                    placeholder="Enter a research topic (e.g., 'Quantum computing applications in cryptography')"
                    rows="1"
                ></textarea>

                <!-- Bottom Bar with Actions Only -->
                <div class="research-input-bottom-bar">
                    <!-- Left side actions -->
                    <div class="research-input-actions-left">
                        <!-- Empty left side for now -->
                    </div>

                    <!-- Right side actions -->
                    <div class="research-input-actions-right">
                        <!-- File Upload Button -->
                        <button type="button" id="researchFileUploadBtn" class="research-input-action-btn flipped-icon" title="Upload file or image">
                            📎
                        </button>
                        <!-- Speech to Text Button -->
                        <button type="button" id="researchMicBtn" class="research-mic-btn" title="Speech to text">
                            🎤
                        </button>
                    </div>
                </div>
            </div>

            <!-- Hidden File Input -->
            <input type="file" id="researchFileInput" class="hidden" accept="image/*,.txt,.py,.js,.html,.css,.json,.md,.pdf,.doc,.docx" multiple>

            <!-- File Preview -->
            <div id="researchFilePreview" class="file-preview mt-2" style="display: none;">
                <!-- File previews will be added here dynamically -->
            </div>
        </div>
        <button id="searchButton" class="research-button">Research</button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add html2pdf.js for PDF generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script src="/static/js/universal_file_upload.js"></script>
<!-- Note: History functionality is provided by professional_history.js in layout.html -->
<script>
    // Modern Expandable Input Functionality for Research
    function initializeResearchExpandableInput(inputId) {
        const textarea = document.getElementById(inputId);
        if (!textarea) return;

        // Auto-expand functionality
        function autoExpand() {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        }

        // Event listeners
        textarea.addEventListener('input', autoExpand);
        textarea.addEventListener('paste', () => setTimeout(autoExpand, 0));

        // Initial setup
        autoExpand();

        // Handle Enter key (submit on Enter, new line on Shift+Enter)
        textarea.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                // Trigger research
                const searchButton = document.getElementById('searchButton');
                if (searchButton && textarea.value.trim()) {
                    searchButton.click();
                }
            }
        });
    }

    // Suggestion function removed - clean input design

    document.addEventListener('DOMContentLoaded', function() {
        const researchQuery = document.getElementById('researchQuery');
        const searchButton = document.getElementById('searchButton');

        // Initialize expandable input
        initializeResearchExpandableInput('researchQuery');
        const researchResults = document.getElementById('researchResults');
        const researchSteps = document.getElementById('researchSteps');
        const finalReport = document.getElementById('finalReport');
        const likeButton = document.getElementById('likeButton');
        const dislikeButton = document.getElementById('dislikeButton');
        const downloadButton = document.getElementById('downloadButton');

        let currentResearchQuery = '';
        let currentResearchResults = '';

        // Check if sidebar is collapsed and add class to body
        function updateSidebarState() {
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                document.body.classList.add('collapsed-sidebar');
            } else {
                document.body.classList.remove('collapsed-sidebar');
            }
        }

        // Initial check
        updateSidebarState();

        // Listen for sidebar state changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'sidebarCollapsed') {
                updateSidebarState();
            }
        });

        // Research steps simulation
        const researchStepTexts = [
            "Analyzing research topic and generating key questions...",
            "Searching academic databases for relevant literature...",
            "Identifying key theories and frameworks in the field...",
            "Analyzing methodological approaches in recent studies...",
            "Examining empirical evidence and research findings...",
            "Identifying gaps in current research literature...",
            "Synthesizing information from multiple academic sources...",
            "Evaluating the strength of evidence across studies...",
            "Formulating academic insights and conclusions...",
            "Compiling comprehensive research report with citations..."
        ];

        let currentStep = 0;
        let stepInterval;
        let researchInProgress = false;

        function addResearchStep(text) {
            const stepElement = document.createElement('div');
            stepElement.className = 'research-step';
            stepElement.innerHTML = `
                <div class="step-icon">
                    ⏳
                </div>
                <div class="step-text">${text}</div>
            `;
            researchSteps.appendChild(stepElement);

            // Scroll to the bottom of the steps
            researchSteps.scrollTop = researchSteps.scrollHeight;

            return stepElement;
        }

        function startResearchSimulation() {
            if (researchInProgress) return;

            researchInProgress = true;
            currentStep = 0;
            researchSteps.innerHTML = '';
            researchResults.classList.remove('hidden');
            finalReport.classList.add('hidden');

            // Add first step immediately
            const firstStep = addResearchStep(researchStepTexts[currentStep]);
            currentStep++;

            // Add subsequent steps with delay
            stepInterval = setInterval(() => {
                if (currentStep < researchStepTexts.length) {
                    addResearchStep(researchStepTexts[currentStep]);
                    currentStep++;
                } else {
                    clearInterval(stepInterval);
                }
            }, 2000);
        }

        function stopResearchSimulation() {
            clearInterval(stepInterval);
            researchInProgress = false;

            // Complete all steps
            while (currentStep < researchStepTexts.length) {
                addResearchStep(researchStepTexts[currentStep]);
                currentStep++;
            }

            // Mark all steps as completed
            const steps = researchSteps.querySelectorAll('.research-step');
            steps.forEach(step => {
                const iconDiv = step.querySelector('.step-icon');
                iconDiv.innerHTML = '✅';
                iconDiv.style.color = '#4CAF50';
            });
        }

        // Handle search button click
        searchButton.addEventListener('click', async function() {
            const query = researchQuery.value.trim();
            if (!query) return;

            // Check credits before starting research
            if (window.creditSystem) {
                const canProceed = await window.creditSystem.enforceCredits('research_basic');
                if (!canProceed) {
                    console.log('Insufficient credits for Research Lab');
                    return;
                }
            }

            // Get uploaded files if available
            let currentUploadedFiles = [];
            if (window.universalFileUpload) {
                currentUploadedFiles = window.universalFileUpload.getFiles('research') || [];
            }

            // Prepare files for API
            const filesForAPI = currentUploadedFiles.map(file => ({
                name: file.name,
                type: file.type,
                content: file.content,
                contentType: file.contentType
            }));

            // Start research simulation
            startResearchSimulation();

            // Make API call to backend
            fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query: query,
                    files: filesForAPI
                })
            })
            .then(response => response.json())
            .then(data => {
                // Stop simulation
                stopResearchSimulation();

                // Display final report
                finalReport.innerHTML = data.results || 'No results found.';
                finalReport.classList.remove('hidden');

                // Consume credits after successful research
                if (window.creditSystem) {
                    window.creditSystem.consumeCredits('research_basic').then(result => {
                        if (result.success) {
                            console.log('Credits consumed successfully for Research Lab:', result.consumed);
                        } else {
                            console.warn('Failed to consume credits for Research Lab:', result.error);
                        }
                    }).catch(error => {
                        console.error('Error consuming credits for Research Lab:', error);
                    });
                }

                // Track activity in enhanced history
                if (window.trackActivity) {
                    try {
                        // Get uploaded files if available
                        let currentUploadedFiles = [];
                        if (window.universalFileUpload) {
                            currentUploadedFiles = window.universalFileUpload.getFiles('research') || [];
                        }

                        window.trackActivity('research_lab', 'research', {
                            query: query,
                            files: currentUploadedFiles
                        }, {
                            results: data.results || 'No results found.',
                            research_type: 'academic_research'
                        });
                    } catch (trackError) {
                        console.warn('Error tracking activity:', trackError);
                        // Don't throw error, just log it
                    }
                }

                // Clear uploaded files after successful research
                if (window.universalFileUpload) {
                    window.universalFileUpload.clearFiles('research');
                }

                // Convert markdown to HTML
                if (window.markdownit) {
                    const md = window.markdownit({
                        html: true,
                        linkify: true,
                        typographer: true,
                        highlight: function (str, lang) {
                            if (lang && Prism.languages[lang]) {
                                try {
                                    return Prism.highlight(str, Prism.languages[lang], lang);
                                } catch (__) {}
                            }
                            return ''; // use external default escaping
                        }
                    });

                    finalReport.innerHTML = md.render(data.results || 'No results found.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                stopResearchSimulation();
                finalReport.innerHTML = `<p>Error: ${error.message || 'An error occurred during research.'}</p>`;
                finalReport.classList.remove('hidden');

                // Track failed activity
                if (window.trackActivity) {
                    try {
                        // Get uploaded files if available
                        let currentUploadedFiles = [];
                        if (window.universalFileUpload) {
                            currentUploadedFiles = window.universalFileUpload.getFiles('research') || [];
                        }

                        window.trackActivity('research_lab', 'research', {
                            query: query,
                            files: currentUploadedFiles
                        }, null, null, false, error.message || 'Research request failed');
                    } catch (trackError) {
                        console.warn('Error tracking failed activity:', trackError);
                        // Don't throw error, just log it
                    }
                }
            });
        });

        // Handle Enter key in input field
        researchQuery.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchButton.click();
            }
        });

        // Handle like button click
        likeButton.addEventListener('click', function() {
            if (likeButton.classList.contains('liked')) {
                // Already liked, remove like
                likeButton.classList.remove('liked');
            } else {
                // Add like and remove dislike if present
                likeButton.classList.add('liked');
                dislikeButton.classList.remove('disliked');

                // Send feedback to server
                sendFeedback('like', currentResearchQuery, currentResearchResults);

                // Show feedback message
                showFeedbackMessage('Thank you for your feedback! We\'ll use it to improve future research.');
            }
        });

        // Handle dislike button click
        dislikeButton.addEventListener('click', function() {
            if (dislikeButton.classList.contains('disliked')) {
                // Already disliked, remove dislike
                dislikeButton.classList.remove('disliked');
            } else {
                // Add dislike and remove like if present
                dislikeButton.classList.add('disliked');
                likeButton.classList.remove('liked');

                // Send feedback to server
                sendFeedback('dislike', currentResearchQuery, currentResearchResults);

                // Show feedback message
                showFeedbackMessage('Thank you for your feedback! We\'ll use it to improve future research.');
            }
        });

        // Handle download button click
        downloadButton.addEventListener('click', function() {
            // Create a clone of the report without the action buttons
            const reportContent = finalReport.cloneNode(true);
            const actionsDiv = reportContent.querySelector('.research-actions');
            if (actionsDiv) {
                reportContent.removeChild(actionsDiv);
            }

            // Generate filename from research query
            const filename = currentResearchQuery.toLowerCase()
                .replace(/[^\w\s]/gi, '')  // Remove special characters
                .replace(/\s+/g, '_')      // Replace spaces with underscores
                .substring(0, 50) + '_research.pdf';

            // Configure PDF options
            const options = {
                margin: 10,
                filename: filename,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };

            // Generate and download PDF
            html2pdf().from(reportContent).set(options).save();

            // Show download message
            showFeedbackMessage('Research report downloaded as PDF.');
        });

        // Function to send feedback to server
        function sendFeedback(type, query, results) {
            fetch('/api/research-feedback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    feedback_type: type,
                    query: query,
                    results: results
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Feedback sent successfully:', data);
            })
            .catch(error => {
                console.error('Error sending feedback:', error);
            });
        }

        // Function to show feedback message
        function showFeedbackMessage(message) {
            // Create message element
            const messageElement = document.createElement('div');
            messageElement.className = 'feedback-message';
            messageElement.style.position = 'fixed';
            messageElement.style.bottom = '100px';
            messageElement.style.left = '50%';
            messageElement.style.transform = 'translateX(-50%)';
            messageElement.style.backgroundColor = '#2d2d2d';
            messageElement.style.color = '#e0e0e0';
            messageElement.style.padding = '10px 20px';
            messageElement.style.borderRadius = '4px';
            messageElement.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';
            messageElement.style.zIndex = '1000';
            messageElement.textContent = message;

            // Add to document
            document.body.appendChild(messageElement);

            // Remove after 3 seconds
            setTimeout(() => {
                document.body.removeChild(messageElement);
            }, 3000);
        }

        // Update the API call to store the current query and results
        const originalFetch = window.fetch;
        searchButton.addEventListener('click', function() {
            const query = researchQuery.value.trim();
            if (!query) return;

            // Store current query
            currentResearchQuery = query;

            // Reset feedback buttons
            likeButton.classList.remove('liked');
            dislikeButton.classList.remove('disliked');

            // Start research simulation
            startResearchSimulation();

            // Make API call to backend
            fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                // Store current results
                currentResearchResults = data.results || '';

                // Stop simulation
                stopResearchSimulation();

                // Display final report
                finalReport.innerHTML = data.results || 'No results found.';

                // Add back the action buttons
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'research-actions';
                actionsDiv.innerHTML = `
                    <button id="likeButton" class="action-button" title="Like this research">
                        👍
                    </button>
                    <button id="dislikeButton" class="action-button" title="Dislike this research">
                        👎
                    </button>
                    <button id="downloadButton" class="action-button" title="Download as PDF">
                        📥
                    </button>
                `;
                finalReport.appendChild(actionsDiv);

                // Re-attach event listeners to the new buttons
                document.getElementById('likeButton').addEventListener('click', likeButton.onclick);
                document.getElementById('dislikeButton').addEventListener('click', dislikeButton.onclick);
                document.getElementById('downloadButton').addEventListener('click', downloadButton.onclick);

                finalReport.classList.remove('hidden');

                // Convert markdown to HTML
                if (window.markdownit) {
                    const md = window.markdownit({
                        html: true,
                        linkify: true,
                        typographer: true,
                        highlight: function (str, lang) {
                            if (lang && Prism.languages[lang]) {
                                try {
                                    return Prism.highlight(str, Prism.languages[lang], lang);
                                } catch (__) {}
                            }
                            return ''; // use external default escaping
                        }
                    });

                    // Replace content but preserve action buttons
                    const actionsDiv = finalReport.querySelector('.research-actions');
                    finalReport.innerHTML = md.render(data.results || 'No results found.');
                    finalReport.appendChild(actionsDiv);

                    // Re-attach event listeners again
                    document.getElementById('likeButton').addEventListener('click', likeButton.onclick);
                    document.getElementById('dislikeButton').addEventListener('click', dislikeButton.onclick);
                    document.getElementById('downloadButton').addEventListener('click', downloadButton.onclick);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                stopResearchSimulation();
                finalReport.innerHTML = `<p>Error: ${error.message || 'An error occurred during research.'}</p>`;
                finalReport.classList.remove('hidden');
            });
        });

        // Speech-to-Text functionality for Research input
        let researchRecognition = null;
        let isResearchRecording = false;
        const researchMicBtn = document.getElementById('researchMicBtn');
        const researchTextInput = document.getElementById('researchQuery');

        // Check if browser supports speech recognition
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            researchRecognition = new SpeechRecognition();

            // Configure speech recognition
            researchRecognition.continuous = true;
            researchRecognition.interimResults = true;
            researchRecognition.lang = 'en-US';

            let finalTranscript = '';

            researchRecognition.onresult = function(event) {
                let interimTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                    } else {
                        interimTranscript += transcript;
                    }
                }

                // Update the textarea with the transcribed text
                researchTextInput.value = finalTranscript + interimTranscript;

                // Auto-resize the textarea
                researchTextInput.style.height = 'auto';
                researchTextInput.style.height = Math.min(researchTextInput.scrollHeight, 200) + 'px';
            };

            researchRecognition.onstart = function() {
                isResearchRecording = true;
                researchMicBtn.style.color = '#ef4444'; // Red color when recording
                researchMicBtn.style.animation = 'pulse 1.5s infinite';
                console.log('Speech recognition started for research input');
            };

            researchRecognition.onend = function() {
                isResearchRecording = false;
                researchMicBtn.style.color = '';
                researchMicBtn.style.animation = '';
                console.log('Speech recognition ended for research input');
            };

            researchRecognition.onerror = function(event) {
                console.error('Speech recognition error for research input:', event.error);
                isResearchRecording = false;
                researchMicBtn.style.color = '';
                researchMicBtn.style.animation = '';
            };

            // Add click event listener to microphone button
            if (researchMicBtn) {
                researchMicBtn.addEventListener('click', function() {
                    if (isResearchRecording) {
                        researchRecognition.stop();
                    } else {
                        finalTranscript = researchTextInput.value || '';
                        researchRecognition.start();
                    }
                });
            }
        } else {
            // Hide microphone button if speech recognition is not supported
            if (researchMicBtn) {
                researchMicBtn.style.display = 'none';
            }
            console.log('Speech recognition not supported in this browser');
        }
    });
</script>
{% endblock %}
