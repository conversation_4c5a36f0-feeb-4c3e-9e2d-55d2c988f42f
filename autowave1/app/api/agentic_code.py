"""
Agentic Code API - Smart Code Assistant with Conversational AI
Provides Augment-like capabilities for iterative code generation and modification
"""

from flask import Blueprint, request, jsonify
import json
import time
import re
import requests
from typing import Dict, List, Any, Optional
import os
from dotenv import load_dotenv
from app.services.file_processor import file_processor
from app.decorators.paywall import require_credits, trial_limit, require_subscription
from app.services.activity_logger import log_agentic_code_activity

# Load environment variables
load_dotenv()

# Create blueprint
agentic_code_bp = Blueprint('agentic_code', __name__)

# Pexels API Configuration
PEXELS_API_KEY = "oZD5LZ47AcYcMuWC4oAHRzLdUDq7SKaI4qFwCXOXM7JLsmtUejkEa9TI"
PEXELS_BASE_URL = "https://api.pexels.com/v1"

def search_pexels_images(query: str, per_page: int = 5) -> List[Dict]:
    """
    Search for images on Pexels API

    Args:
        query: Search query for images
        per_page: Number of images to return (max 80)

    Returns:
        List of image data dictionaries
    """
    try:
        headers = {
            'Authorization': PEXELS_API_KEY
        }

        params = {
            'query': query,
            'per_page': min(per_page, 10),  # Limit to 10 for performance
            'orientation': 'all'
        }

        response = requests.get(
            f"{PEXELS_BASE_URL}/search",
            headers=headers,
            params=params,
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            images = []

            for photo in data.get('photos', []):
                images.append({
                    'id': photo['id'],
                    'url': photo['src']['medium'],  # Medium size for web use
                    'large_url': photo['src']['large'],
                    'original_url': photo['src']['original'],
                    'alt': photo.get('alt', query),
                    'photographer': photo['photographer'],
                    'photographer_url': photo['photographer_url'],
                    'width': photo['width'],
                    'height': photo['height']
                })

            return images
        else:
            print(f"Pexels API error: {response.status_code} - {response.text}")
            return []

    except Exception as e:
        print(f"Error searching Pexels images: {str(e)}")
        return []

def get_image_for_context(message: str) -> Optional[str]:
    """
    Analyze the message and get a relevant image URL if needed

    Args:
        message: User's message/request

    Returns:
        Image URL if relevant, None otherwise
    """
    # Keywords that suggest image might be useful
    image_keywords = [
        'background', 'hero', 'banner', 'image', 'photo', 'picture',
        'landing page', 'website', 'portfolio', 'gallery', 'visual',
        'design', 'ui', 'interface', 'mockup', 'template'
    ]

    message_lower = message.lower()

    # Check if message suggests need for images
    needs_image = any(keyword in message_lower for keyword in image_keywords)

    if needs_image:
        # Extract potential search terms
        search_terms = []

        # Look for specific image requests
        if 'background' in message_lower:
            if 'nature' in message_lower or 'landscape' in message_lower:
                search_terms.append('nature landscape')
            elif 'city' in message_lower or 'urban' in message_lower:
                search_terms.append('city skyline')
            elif 'abstract' in message_lower:
                search_terms.append('abstract background')
            else:
                search_terms.append('background texture')

        if 'hero' in message_lower or 'banner' in message_lower:
            search_terms.append('business professional')

        if 'portfolio' in message_lower:
            search_terms.append('creative workspace')

        if 'landing page' in message_lower or 'website' in message_lower:
            search_terms.append('modern office')

        # Default search term if no specific terms found
        if not search_terms:
            search_terms.append('modern design')

        # Search for the first relevant term
        for term in search_terms:
            images = search_pexels_images(term, per_page=1)
            if images:
                return images[0]['url']

    return None

# Import LLM clients
try:
    import google.generativeai as genai

    # Configure Gemini
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
    if GEMINI_API_KEY:
        genai.configure(api_key=GEMINI_API_KEY)

    # Configure Groq (with error handling)
    GROQ_API_KEY = os.getenv('GROQ_API_KEY')
    groq_client = None
    if GROQ_API_KEY:
        try:
            from groq import Groq
            groq_client = Groq(api_key=GROQ_API_KEY)
        except Exception as e:
            print(f"Warning: Could not initialize Groq client: {e}")
            groq_client = None

except ImportError as e:
    print(f"Warning: Could not import LLM libraries: {e}")
    genai = None
    groq_client = None

# Session storage (in production, use Redis or database)
sessions = {}

class AgenticCodeAssistant:
    """
    AI Code Assistant with Augment-like capabilities
    """

    def __init__(self):
        self.model_name = "gemini-1.5-flash"
        self.groq_model = "llama-3.1-70b-versatile"

    def analyze_request(self, message: str, current_code: str = "", files: list = None) -> Dict[str, Any]:
        """
        Analyze user request and determine the approach (like Augment's planning phase)
        """
        try:
            # Process uploaded files if any
            processed_message = message
            if files:
                processed_message = self._process_uploaded_files(message, files)

            # Use Gemini for analysis
            if genai and GEMINI_API_KEY:
                return self._analyze_with_gemini(processed_message, current_code)
            elif groq_client:
                return self._analyze_with_groq(processed_message, current_code)
            else:
                return self._fallback_analysis(processed_message, current_code)
        except Exception as e:
            print(f"Error in analysis: {e}")
            return self._fallback_analysis(message, current_code)

    def _process_uploaded_files(self, message: str, files: list) -> str:
        """Process uploaded files and enhance the message with file content"""
        if not files:
            return message

        enhanced_message = message + "\n\n--- UPLOADED FILES ---\n"

        for file_data in files:
            file_name = file_data.get('name', 'unknown')
            file_type = file_data.get('type', '')
            content = file_data.get('content', '')
            content_type = file_data.get('contentType', '')

            enhanced_message += f"\n📁 File: {file_name}\n"

            if content_type == 'image':
                # For images, describe what we can see
                enhanced_message += f"Type: Image ({file_type})\n"
                enhanced_message += "Content: [Image file uploaded - please analyze the image and use it as reference for the code generation]\n"
                if content:
                    enhanced_message += f"Image data: {content[:100]}...\n"
            elif content_type == 'text' and content:
                # For text files, include the content
                enhanced_message += f"Type: Text file ({file_type})\n"
                enhanced_message += f"Content:\n```\n{content}\n```\n"
            else:
                enhanced_message += f"Type: {file_type}\n"
                enhanced_message += "Content: [File uploaded for reference]\n"

        enhanced_message += "\n--- END OF FILES ---\n\n"
        enhanced_message += "Please analyze the uploaded files and use them as reference or input for generating the requested code. "
        enhanced_message += "If images are provided, describe what you see and incorporate relevant elements into the design. "
        enhanced_message += "If code files are provided, use them as a starting point or reference for the new code."

        return enhanced_message

    def _analyze_with_gemini(self, message: str, current_code: str) -> Dict[str, Any]:
        """Analyze request using Gemini with enhanced language support"""
        model = genai.GenerativeModel(self.model_name)

        if not current_code.strip():
            # Detect language from user request
            detected_language = self._detect_language_from_request(message)

            # Initial code generation using enhanced approach
            system_prompt = self._get_enhanced_system_prompt(detected_language)

            if detected_language == "python":
                full_prompt = f"{system_prompt}\n\nCreate the following Python application or script: {message}"
            else:
                full_prompt = f"{system_prompt}\n\nCreate the following web component or application: {message}"

            response = model.generate_content(full_prompt)
            code = response.text.strip()

            # Extract code from response if needed
            if f"```{detected_language}" in code:
                code = code.split(f"```{detected_language}")[1].split("```")[0].strip()
            elif "```html" in code:
                code = code.split("```html")[1].split("```")[0].strip()
            elif "```python" in code:
                code = code.split("```python")[1].split("```")[0].strip()
            elif "```" in code:
                code = code.split("```")[1].split("```")[0].strip()

            if detected_language == "python":
                return {
                    "plan": f"Generate professional Python application for: {message}",
                    "steps": [
                        "Analyzing your request and determining project type",
                        "Designing application structure and logic",
                        "Implementing with Python best practices",
                        "Adding error handling and user interaction features"
                    ],
                    "code": code,
                    "explanation": f"I've created a professional Python application based on your request: '{message}'. The code follows Python best practices with proper error handling and functionality.",
                    "language": "python"
                }
            else:
                return {
                    "plan": f"Generate professional webpage for: {message}",
                    "steps": [
                        "Analyzing your request and determining content type",
                        "Designing layout with modern UI patterns",
                        "Implementing with HTML, Tailwind CSS, and JavaScript",
                        "Adding interactive features and optimizations"
                    ],
                    "code": code,
                    "explanation": f"I've created a professional webpage based on your request: '{message}'. The code uses modern HTML, Tailwind CSS, and JavaScript with responsive design and rich visual elements.",
                    "language": "html"
                }
        else:
            # Code modification
            prompt = f"""
You are an expert AI coding assistant similar to Augment. Analyze this user request and provide a structured response.

Current Code:
```
{current_code}
```

User Request: {message}

Provide a JSON response with:
1. "plan" - Brief description of what you'll do
2. "steps" - Array of 3-4 implementation steps
3. "code" - The complete updated code
4. "explanation" - Friendly explanation of changes made
5. "language" - Detected programming language

Focus on:
- Clear, step-by-step approach
- Professional code quality
- Helpful explanations
- Iterative improvements

Return only valid JSON.
"""

            response = model.generate_content(prompt)
            return self._parse_llm_response(response.text)

    def _detect_language_from_request(self, message: str) -> str:
        """Detect programming language from user request"""
        message_lower = message.lower()

        # Python keywords and indicators - prioritize Flask and web frameworks
        python_indicators = [
            'flask', 'django', 'fastapi', 'python web', 'python app', 'web application',
            'api', 'backend', 'server', 'python', 'py', 'script', 'data analysis',
            'machine learning', 'ml', 'ai', 'pandas', 'numpy', 'matplotlib',
            'automation', 'scraping', 'tkinter', 'gui', 'desktop app',
            'algorithm', 'data processing', 'csv', 'json', 'api client',
            'command line', 'cli', 'terminal', 'file processing', 'contact form',
            'form submission', 'json file', 'save submissions'
        ]

        # Web development indicators (default)
        web_indicators = [
            'website', 'webpage', 'web app', 'landing page', 'portfolio',
            'dashboard', 'blog', 'ecommerce', 'shop', 'store', 'html',
            'css', 'javascript', 'js', 'frontend', 'ui', 'ux', 'responsive',
            'bootstrap', 'tailwind', 'react', 'vue', 'angular'
        ]

        # Count matches for each language
        python_score = sum(1 for indicator in python_indicators if indicator in message_lower)
        web_score = sum(1 for indicator in web_indicators if indicator in message_lower)

        # If Python indicators are stronger, return Python
        if python_score > web_score and python_score > 0:
            return "python"

        # Default to HTML/web development
        return "html"

    def _analyze_with_groq(self, message: str, current_code: str) -> Dict[str, Any]:
        """Analyze request using Groq with enhanced language support"""
        if not current_code.strip():
            # Detect language from user request
            detected_language = self._detect_language_from_request(message)

            # Initial code generation using enhanced approach
            system_prompt = self._get_enhanced_system_prompt(detected_language)

            if detected_language == "python":
                full_prompt = f"{system_prompt}\n\nCreate the following Python application or script: {message}"
            else:
                full_prompt = f"{system_prompt}\n\nCreate the following web component or application: {message}"

            response = groq_client.chat.completions.create(
                messages=[{"role": "user", "content": full_prompt}],
                model=self.groq_model,
                temperature=0.2,
                max_tokens=4000
            )

            code = response.choices[0].message.content.strip()

            # Extract code from response if needed
            if f"```{detected_language}" in code:
                code = code.split(f"```{detected_language}")[1].split("```")[0].strip()
            elif "```html" in code:
                code = code.split("```html")[1].split("```")[0].strip()
            elif "```python" in code:
                code = code.split("```python")[1].split("```")[0].strip()
            elif "```" in code:
                code = code.split("```")[1].split("```")[0].strip()

            if detected_language == "python":
                return {
                    "plan": f"Generate professional Python application for: {message}",
                    "steps": [
                        "Analyzing your request and determining project type",
                        "Designing application structure and logic",
                        "Implementing with Python best practices",
                        "Adding error handling and user interaction features"
                    ],
                    "code": code,
                    "explanation": f"I've created a professional Python application based on your request: '{message}'. The code follows Python best practices with proper error handling and functionality.",
                    "language": "python"
                }
            else:
                return {
                    "plan": f"Generate professional webpage for: {message}",
                    "steps": [
                        "Analyzing your request and determining content type",
                        "Designing layout with modern UI patterns",
                        "Implementing with HTML, Tailwind CSS, and JavaScript",
                        "Adding interactive features and optimizations"
                    ],
                    "code": code,
                    "explanation": f"I've created a professional webpage based on your request: '{message}'. The code uses modern HTML, Tailwind CSS, and JavaScript with responsive design and rich visual elements.",
                    "language": "html"
                }
        else:
            # Code modification
            prompt = f"""
You are an expert AI coding assistant similar to Augment. Analyze this user request and provide a structured response.

Current Code:
```
{current_code}
```

User Request: {message}

Provide a JSON response with:
1. "plan" - Brief description of what you'll do
2. "steps" - Array of 3-4 implementation steps
3. "code" - The complete updated code
4. "explanation" - Friendly explanation of changes made
5. "language" - Detected programming language

Focus on:
- Clear, step-by-step approach
- Professional code quality
- Helpful explanations
- Iterative improvements

Return only valid JSON.
"""

            response = groq_client.chat.completions.create(
                messages=[{"role": "user", "content": prompt}],
                model=self.groq_model,
                temperature=0.7,
                max_tokens=4000
            )

            return self._parse_llm_response(response.choices[0].message.content)

    def _parse_llm_response(self, response_text: str) -> Dict[str, Any]:
        """Parse LLM response and extract JSON"""
        try:
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # If no JSON found, create structured response
                return self._create_structured_response(response_text)
        except json.JSONDecodeError:
            return self._create_structured_response(response_text)

    def _create_structured_response(self, text: str) -> Dict[str, Any]:
        """Create structured response from unstructured text"""
        return {
            "plan": "Process user request and generate appropriate code",
            "steps": [
                "Analyzing requirements",
                "Generating code structure",
                "Implementing functionality",
                "Optimizing and finalizing"
            ],
            "code": self._generate_fallback_code(text),
            "explanation": f"I've processed your request: {text[:100]}...",
            "language": "html"
        }

    def _fallback_analysis(self, message: str, current_code: str) -> Dict[str, Any]:
        """Fallback analysis when no LLM is available"""
        if not current_code.strip():
            # Detect language from user request
            detected_language = self._detect_language_from_request(message)

            # Initial code generation using enhanced style
            code = self._generate_initial_code_enhanced_style(message, detected_language)

            if detected_language == "python":
                explanation = f"I've created a Python application based on your request: '{message}'"
                return {
                    "plan": f"Generate professional Python application for: {message}",
                    "steps": [
                        "Analyzing your request and determining project type",
                        "Designing application structure and logic",
                        "Implementing with Python best practices",
                        "Adding error handling and user interaction features"
                    ],
                    "code": code,
                    "explanation": explanation,
                    "language": "python"
                }
            else:
                explanation = f"I've created a webpage based on your request: '{message}'"
                return {
                    "plan": f"Generate professional webpage for: {message}",
                    "steps": [
                        "Analyzing your request and determining content type",
                        "Designing layout with modern UI patterns",
                        "Implementing with HTML, Tailwind CSS, and JavaScript",
                        "Adding interactive features and optimizations"
                    ],
                    "code": code,
                    "explanation": explanation,
                    "language": "html"
                }
        else:
            # Code modification
            code = self._modify_existing_code(message, current_code)
            explanation = f"I've modified the existing code based on your request: '{message}'"

            return {
                "plan": f"Modify code for: {message}",
                "steps": [
                    "Analyzing your request",
                    "Planning the implementation",
                    "Writing the code",
                    "Finalizing and optimizing"
                ],
                "code": code,
                "explanation": explanation,
                "language": self._detect_language(code)
            }

    def _generate_initial_code_codewave_style(self, message: str) -> str:
        """Generate initial code using Code Wave's LLM approach"""
        try:
            # Import the Gemini API from Code Wave
            from app.api.gemini import GeminiAPI
            gemini_api = GeminiAPI()

            # Use Code Wave's comprehensive system prompt
            system_prompt = self._get_codewave_system_prompt()

            # Generate code using the same approach as Code Wave
            full_prompt = f"{system_prompt}\n\nCreate the following web component or application: {message}"
            response = gemini_api.generate_text(
                prompt=full_prompt,
                temperature=0.2
            )

            if response:
                # Extract code from response
                code = response.strip()

                # If the response contains markdown code blocks, extract the code
                if "```html" in code:
                    code = code.split("```html")[1].split("```")[0].strip()
                elif "```" in code:
                    code = code.split("```")[1].split("```")[0].strip()

                return code
            else:
                # Fallback to template if LLM fails
                return self._generate_initial_code_template(message)

        except Exception as e:
            print(f"Error generating code with LLM: {e}")
            # Fallback to template if LLM fails
            return self._generate_initial_code_template(message)

    def _generate_initial_code_enhanced_style(self, message: str, language: str) -> str:
        """Generate initial code using enhanced approach with language support"""
        try:
            # Try to use LLM first
            from app.api.gemini import GeminiAPI
            gemini_api = GeminiAPI()

            # Use enhanced system prompt
            system_prompt = self._get_enhanced_system_prompt(language)

            # Generate code using the enhanced approach
            if language == "python":
                full_prompt = f"{system_prompt}\n\nCreate the following Python application or script: {message}"
            else:
                full_prompt = f"{system_prompt}\n\nCreate the following web component or application: {message}"

            response = gemini_api.generate_text(
                prompt=full_prompt,
                temperature=0.2
            )

            if response:
                # Extract code from response
                code = response.strip()

                # If the response contains markdown code blocks, extract the code
                if f"```{language}" in code:
                    code = code.split(f"```{language}")[1].split("```")[0].strip()
                elif "```html" in code:
                    code = code.split("```html")[1].split("```")[0].strip()
                elif "```python" in code:
                    code = code.split("```python")[1].split("```")[0].strip()
                elif "```" in code:
                    code = code.split("```")[1].split("```")[0].strip()

                return code
            else:
                # Fallback to template if LLM fails
                return self._generate_initial_code_template(message)

        except Exception as e:
            print(f"Error generating code with enhanced LLM: {e}")
            # Fallback to template if LLM fails
            return self._generate_initial_code_template(message)

    def _get_enhanced_system_prompt(self, language: str = "html") -> str:
        """Get the enhanced system prompt for multiple languages"""
        if language.lower() == "python":
            return self._get_python_system_prompt()
        else:
            return self._get_web_system_prompt()

    def _get_python_system_prompt(self) -> str:
        """Get the comprehensive system prompt for Python development"""
        return """
        You are an expert Python developer specializing in clean, functional, and well-documented Python code.
        Your task is to generate professional Python applications, scripts, and tools based on the user's request.

        Guidelines:
        1. Use modern Python best practices and PEP 8 standards
        2. Write clean, readable, and well-commented code
        3. Include proper error handling and input validation
        4. Use type hints where appropriate
        5. Create modular, reusable functions and classes
        6. Include docstrings for all functions and classes
        7. Use appropriate Python libraries and frameworks
        8. Ensure code is complete, error-free, and ready to run
        9. Include example usage and test cases when appropriate
        10. Follow security best practices

        PYTHON PROJECT TYPES:
        1. SCRIPTS: Data processing, automation, utilities, file operations
        2. WEB APPS: Flask/Django applications, APIs, web scrapers
        3. DATA ANALYSIS: Pandas, NumPy, Matplotlib, data visualization
        4. MACHINE LEARNING: Scikit-learn, TensorFlow, PyTorch models
        5. GUI APPLICATIONS: Tkinter, PyQt, desktop applications
        6. GAMES: Pygame, simple games and interactive applications
        7. AUTOMATION: Selenium, web automation, task scheduling
        8. APIS: FastAPI, Flask REST APIs, microservices

        STRUCTURE GUIDELINES:
        1. Always include proper imports at the top
        2. Define constants and configuration variables
        3. Create helper functions for reusable logic
        4. Use main() function for script entry point
        5. Include if __name__ == "__main__": guard
        6. Add comprehensive error handling
        7. Include logging where appropriate
        8. Use virtual environments and requirements.txt for dependencies

        FUNCTIONALITY REQUIREMENTS:
        1. Make applications fully functional and interactive
        2. Include proper user input handling and validation
        3. Implement complete business logic
        4. Add file I/O operations when needed
        5. Include data persistence (files, databases) when appropriate
        6. Add configuration options and command-line arguments
        7. Implement proper testing and debugging features

        FLASK APPLICATION REQUIREMENTS (CRITICAL):
        1. For Flask web applications, ALWAYS use port 5002 to avoid conflicts
        2. Use app.run(debug=True, port=5002, host="127.0.0.1") for Flask apps
        3. Never use port 5000 or 5001 as they may be in use
        4. Include proper error handling for port conflicts
        5. Set FLASK_DEBUG=True instead of deprecated FLASK_ENV

        Your response should ONLY include the complete Python code without any explanations or markdown formatting.
        Just provide the raw Python file that is ready to run.
        """

    def _get_web_system_prompt(self) -> str:
        """Get the comprehensive system prompt for web development"""
        return """
        You are an expert web developer specializing in HTML, CSS (with Tailwind CSS), and JavaScript.
        Your task is to generate clean, well-structured, and FULLY FUNCTIONAL code based on the user's request.

        Guidelines:
        1. Use modern best practices and standards
        2. Prioritize responsive design using Tailwind CSS classes directly in HTML elements (not @apply directives)
        3. Write clean, well-commented code
        4. Include all necessary HTML, CSS, and JavaScript in a single file
        5. Make sure the code is complete, error-free, and ready to run
        6. Use semantic HTML elements
        7. Ensure the code is accessible
        8. Optimize for performance
        9. Include the Tailwind CSS CDN in the head section: <script src="https://cdn.tailwindcss.com"></script>
        10. Use regular CSS for custom styles, not @apply directives
        11. Double-check your JavaScript code for syntax errors and missing brackets

        CRITICAL FUNCTIONALITY REQUIREMENTS - MUST BE FULLY WORKING:
        1. ALL INTERACTIVE ELEMENTS MUST BE FULLY FUNCTIONAL - NO EXCEPTIONS
        2. Buttons must have proper click event handlers that actually do something
        3. Forms must have complete validation and submission handling with feedback
        4. Navigation links must work correctly and scroll/navigate properly
        5. Modals and popups must open/close properly with proper event handling
        6. Tabs must switch content correctly with proper state management
        7. Search functionality must actually search and filter data in real-time
        8. Calculators must perform real calculations with proper math operations
        9. Games must be playable with complete game logic and state management
        10. APIs must be called and data displayed correctly with error handling
        11. Local storage must be used for data persistence when needed
        12. Error handling must be implemented for all user interactions
        13. CRUD operations must be fully implemented (Create, Read, Update, Delete)
        14. Data must persist between page reloads using localStorage
        15. All user inputs must be validated with proper error messages
        16. Loading states must be shown during operations
        17. Success/error notifications must be displayed to users
        18. All features mentioned in the user request must actually work

        JAVASCRIPT IMPLEMENTATION REQUIREMENTS:
        1. Use modern ES6+ JavaScript syntax
        2. Implement proper event listeners for all interactive elements
        3. Add input validation and error handling
        4. Use async/await for API calls
        5. Implement proper state management
        6. Add loading states and user feedback
        7. Include proper error messages and success notifications
        8. Use localStorage or sessionStorage for data persistence
        9. Implement proper form handling and validation
        10. Add keyboard navigation support
        11. Include proper accessibility features
        12. Test all functionality before finalizing code

        CONTENT TYPE DETECTION (VERY IMPORTANT):
        1. ANALYZE THE USER'S PROMPT CAREFULLY to determine what type of content they want:
           - WEBSITE: Landing pages, portfolios, company sites, blogs, e-commerce
           - APPLICATION: Weather apps, calculators, tools, dashboards, games
           - DIAGRAM: Flowcharts, mind maps, org charts, process diagrams, network diagrams
           - PRESENTATION: Slides, pitch decks, visual presentations, infographics
           - VISUALIZATION: Data charts, graphs, interactive visualizations
        2. For each content type, use the appropriate libraries and techniques as described below

        DESIGN GUIDELINES (VERY IMPORTANT):
        1. NAVIGATION AND STRUCTURE:
           - For WEBSITES: ALWAYS separate navigation bar from header section (nav should be its own distinct element)
           - Navigation should be <nav> element, header should be <header> element with hero content
           - Use proper semantic structure: <nav>, <header>, <main>, <section>, <footer>
           - For APPLICATIONS: Use appropriate UI patterns like sidebars, tab bars, or minimal navigation
           - For DIAGRAMS & PRESENTATIONS: Include minimal navigation with clear controls for interaction

        2. SECTION SPACING AND LAYOUT:
           - Create proper section spacing with adequate margins/padding (use py-16 or py-20 for main sections)
           - Ensure sections are well-separated visually with proper spacing (min py-8 between elements)
           - Use proper container classes (max-w-7xl mx-auto px-4) for content width
           - Add mb-8 or mb-12 for element spacing within sections

        3. FOOTER REQUIREMENTS:
           - ALWAYS include a consistent, appropriately-sized footer (max height 200px)
           - Use py-8 or py-12 for footer padding (never more than py-16)
           - Footer should be compact but informative (links, contact, copyright)
           - Footer should have dark background with light text for contrast

        4. TAB FUNCTIONALITY:
           - For multi-tab websites: create functional tabs that show/hide different content sections
           - Use JavaScript to implement proper tab switching functionality
           - Each tab should reveal different content areas, not just styling changes
           - Add active states and smooth transitions between tabs

        5. COLOR PALETTE AND BACKGROUNDS:
           - ALWAYS use a dark theme by default for applications (dark backgrounds with light text)
           - Use a cohesive color palette throughout the design (4-6 complementary colors)
           - Every section should have a rich background color, gradient, or pattern
           - Avoid plain white backgrounds - use subtle gradients or dark themes instead
           - Create visually appealing designs with rich colors and gradients that work well together
           - For gradients, use at least 3 colors from the same palette to create depth

        6. VISUAL ELEMENTS (CRITICAL FOR RICH APPEARANCE):
           - Add depth with layered elements, shadows, and 3D effects
           - Use glass morphism effects (frosted glass) for cards and containers
           - Incorporate subtle animations for hover states and transitions
           - Add micro-interactions (e.g., button hover effects, loading animations)
           - Use rounded corners (at least 0.5rem) for containers and buttons
           - Include icons from Font Awesome or Material Icons for visual enhancement
           - Add subtle patterns or textures to backgrounds when appropriate
           - Use border highlights with glowing effects for active elements
           - Implement card-based designs with elevation shadows
           - Make sure text has sufficient contrast with background colors
           - Use proper spacing between elements (min 1rem) for better readability

        7. APPLICATION STRUCTURE GUIDELINES (CRITICAL FOR RICH APPLICATIONS):
           - Create a proper layout with header, main content area, and footer
           - Use a sidebar or navigation panel for applications with multiple sections
           - Implement proper state management using JavaScript objects or Alpine.js
           - Add loading states and transitions between application states
           - Include proper error handling and user feedback mechanisms
           - Use card-based layouts for content organization
           - Implement responsive designs that work on mobile and desktop

        8. ENHANCED CONTENT RICHNESS REQUIREMENTS (CRITICAL FOR PROFESSIONAL RESULTS):
           - WEBSITES: Generate 5-8 comprehensive sections minimum (Hero, About, Services/Features, Portfolio/Gallery, Testimonials, Contact, FAQ, Footer)
           - APPLICATIONS: Include multiple functional screens/views with rich feature sets
           - Add interactive elements in every section (buttons, forms, sliders, galleries, etc.)
           - Include realistic placeholder content that demonstrates the full functionality
           - Create multiple content variations within each section (e.g., 3-6 service cards, 4-8 portfolio items)
           - Add comprehensive navigation between all sections and features
           - Include search functionality, filtering, and sorting where appropriate
           - Implement user profiles, dashboards, or admin panels for applications

        9. ANIMATION AND INTERACTION REQUIREMENTS (CRITICAL FOR MODERN FEEL):
           - Add CSS animations for page load (fade-in, slide-up effects)
           - Implement scroll-triggered animations using Intersection Observer API
           - Add hover animations for all interactive elements (scale, glow, color transitions)
           - Include loading animations and progress indicators
           - Add smooth scrolling between sections
           - Implement parallax effects for hero sections
           - Add typing animations for text reveals
           - Include modal animations (slide-in, fade-in effects)
           - Add carousel/slider animations with smooth transitions
           - Implement form validation animations (shake, highlight effects)
           - Add success/error notification animations
           - Include particle effects or background animations where appropriate

        10. FOOTER POSITIONING FIX (CRITICAL):
            - ALWAYS position footer at the bottom of the page content, not in the middle
            - Use proper CSS to ensure footer stays at bottom: position relative, not fixed
            - Ensure main content has sufficient min-height to push footer down
            - Add proper margin-top to footer to separate from main content
            - Footer should only appear after ALL main content sections are complete
           - Add subtle animations for user interactions (button clicks, form submissions)
           - Include proper form validation with visual feedback
           - Use modals/dialogs for confirmations and additional information

        8. SPECIALIZED CONTENT GUIDELINES:
           - For DIAGRAMS: Use libraries like Mermaid.js, D3.js, or Chart.js to create interactive diagrams
           - For PRESENTATIONS: Create slide-like sections with navigation controls and transitions
           - For VISUALIZATIONS: Use appropriate chart types and interactive elements

        COLOR PALETTE SUGGESTIONS (USE THESE FOR RICH DESIGNS):
        - Dark Mode UI: #121212 (background), #1e1e1e (surface), #bb86fc (primary), #03dac6 (secondary), #cf6679 (error)
        - Cyberpunk: #000000 (background), #ff2a6d (primary), #05d9e8 (secondary), #d1f7ff (text), #7700a6 (accent)
        - Gradient Dark: #0f0c29 (start), #302b63 (middle), #24243e (end), #f8f8f8 (text), #ff7b00 (accent)
        - Neon Glow: #10002b (background), #240046 (surface), #3c096c (container), #5a189a (primary), #7b2cbf (secondary), #9d4edd (accent), #c77dff (highlight)
        - Glass Morphism: #111827 (background), rgba(255,255,255,0.1) (glass), #3b82f6 (primary), #10b981 (success), #f43f5e (error)
        - Modern Dark: #0f172a (background), #1e293b (surface), #334155 (container), #38bdf8 (primary), #fb7185 (secondary), #34d399 (success)
        - Luxury Dark: #1a1a1a (background), #2d2d2d (surface), #bc9a6c (gold), #e0e0e0 (silver), #a67c52 (bronze), #f5f5f5 (text)
        - Vibrant Dark: #13111c (background), #221e2f (surface), #f637ec (primary), #ffd60a (secondary), #00e1d9 (tertiary), #fbfbfb (text)
        - Gradient Mesh: #0f2027 (start), #203a43 (middle), #2c5364 (end), #4cc9f0 (primary), #f72585 (secondary), #ffffff (text)
        - Futuristic: #000000 (background), #0a0a0a (surface), #7928ca (primary), #ff0080 (secondary), #0070f3 (tertiary), #00dfd8 (quaternary)

        SPECIALIZED LIBRARIES (ALWAYS INCLUDE APPROPRIATE ONES):
        - For DIAGRAMS:
          <script src="https://cdn.jsdelivr.net/npm/mermaid@10.0.0/dist/mermaid.min.js"></script>
          <script>mermaid.initialize({startOnLoad:true});</script>

        - For CHARTS & VISUALIZATIONS:
          <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
          or
          <script src="https://d3js.org/d3.v7.min.js"></script>

        - For PRESENTATIONS:
          <script src="https://cdn.jsdelivr.net/npm/reveal.js/dist/reveal.js"></script>
          <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js/dist/reveal.css">
          <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/reveal.js/dist/theme/black.css">

        - For RICH UI COMPONENTS (ALWAYS INCLUDE FOR APPLICATIONS):
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
          <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

        - For ANIMATIONS (ALWAYS INCLUDE FOR APPLICATIONS):
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
          <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>

        - For GLASS MORPHISM EFFECTS:
          <style>
            .glass {
              background: rgba(255, 255, 255, 0.1);
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.18);
              box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            }
            .dark-glass {
              background: rgba(15, 23, 42, 0.6);
              backdrop-filter: blur(10px);
              -webkit-backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.08);
              box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            }
          </style>

        Your response should ONLY include the complete code without any explanations or markdown formatting.
        Just provide the raw HTML file with embedded CSS and JavaScript.
        """

    def _generate_initial_code_template(self, message: str) -> str:
        """Generate initial code based on user request using templates as fallback"""
        message_lower = message.lower()

        # Prioritize Flask and Python web applications
        if any(word in message_lower for word in ['flask', 'django', 'fastapi', 'python web', 'python app', 'web application', 'api', 'backend', 'contact form']):
            return self._generate_flask_template(message)
        elif any(word in message_lower for word in ['python', 'script', 'py', 'data', 'analysis']):
            return self._generate_python_template(message)
        else:
            # Default to HTML for all web-related requests
            return self._generate_html_template(message)

    def _generate_html_template(self, message: str) -> str:
        """Generate professional HTML template with Tailwind CSS - Code Wave Style"""
        message_lower = message.lower()

        # Determine the type of page based on keywords
        if any(word in message_lower for word in ['startup', 'business', 'company']):
            return self._generate_startup_page(message)
        elif any(word in message_lower for word in ['portfolio', 'personal', 'resume']):
            return self._generate_portfolio_page(message)
        elif any(word in message_lower for word in ['blog', 'article', 'news']):
            return self._generate_blog_page(message)
        elif any(word in message_lower for word in ['dashboard', 'admin', 'panel']):
            return self._generate_dashboard_page(message)
        elif any(word in message_lower for word in ['landing', 'product', 'service']):
            return self._generate_landing_page(message)
        elif any(word in message_lower for word in ['ecommerce', 'shop', 'store', 'cart']):
            return self._generate_ecommerce_page(message)
        else:
            return self._generate_modern_page(message)

    def _generate_startup_page(self, message: str) -> str:
        """Generate a professional startup landing page - Code Wave Style"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechStart - Innovative Solutions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">TechStart</h1>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-gray-900">Home</a>
                    <a href="#features" class="text-gray-700 hover:text-gray-900">Features</a>
                    <a href="#about" class="text-gray-700 hover:text-gray-900">About</a>
                    <a href="#contact" class="text-gray-700 hover:text-gray-900">Contact</a>
                    <button class="bg-black text-white px-6 py-2 rounded-lg hover:bg-gray-800">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-20 pb-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                    Innovation Meets
                    <span class="text-blue-600">Excellence</span>
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                    We're building the future with cutting-edge technology solutions that transform businesses and empower growth.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-black text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-800 transition-colors">
                        Start Your Journey
                    </button>
                    <button class="border-2 border-black text-black px-8 py-4 rounded-lg text-lg font-semibold hover:bg-black hover:text-white transition-colors">
                        Learn More
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Why Choose TechStart?</h2>
                <p class="text-xl text-gray-600">Discover the features that set us apart</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-blue-500 rounded-lg mb-4"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Lightning Fast</h3>
                    <p class="text-gray-600">Experience blazing-fast performance with our optimized solutions.</p>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-green-500 rounded-lg mb-4"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Reliable</h3>
                    <p class="text-gray-600">Built with enterprise-grade reliability and 99.9% uptime guarantee.</p>
                </div>
                <div class="bg-white p-8 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-purple-500 rounded-lg mb-4"></div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">Scalable</h3>
                    <p class="text-gray-600">Grow without limits with our infinitely scalable infrastructure.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-black text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-4xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl text-gray-300 mb-8">Join thousands of companies already using TechStart</p>
            <button class="bg-white text-black px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors">
                Start Free Trial
            </button>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-400">&copy; 2024 TechStart. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>"""

    def _generate_portfolio_page(self, message: str) -> str:
        """Generate a professional portfolio page - Code Wave Style"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>John Doe - Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm fixed w-full z-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">John Doe</h1>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#about" class="text-gray-700 hover:text-gray-900">About</a>
                    <a href="#projects" class="text-gray-700 hover:text-gray-900">Projects</a>
                    <a href="#skills" class="text-gray-700 hover:text-gray-900">Skills</a>
                    <a href="#contact" class="text-gray-700 hover:text-gray-900">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-20 pb-16 bg-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-8"></div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">John Doe</h1>
                <p class="text-xl text-gray-600 mb-8">Full Stack Developer & UI/UX Designer</p>
                <div class="flex justify-center space-x-4">
                    <button class="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800">
                        Download CV
                    </button>
                    <button class="border border-black text-black px-6 py-3 rounded-lg hover:bg-black hover:text-white">
                        Contact Me
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-16 bg-gray-50">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Featured Projects</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-48 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">E-commerce Platform</h3>
                        <p class="text-gray-600 mb-4">Modern e-commerce solution built with React and Node.js</p>
                        <div class="flex space-x-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded">React</span>
                            <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded">Node.js</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-48 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Task Management App</h3>
                        <p class="text-gray-600 mb-4">Collaborative task management with real-time updates</p>
                        <div class="flex space-x-2">
                            <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded">Vue.js</span>
                            <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded">Firebase</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-48 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Weather Dashboard</h3>
                        <p class="text-gray-600 mb-4">Beautiful weather app with location-based forecasts</p>
                        <div class="flex space-x-2">
                            <span class="px-3 py-1 bg-red-100 text-red-800 text-sm rounded">Angular</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded">API</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 bg-black text-white">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-8">Let's Work Together</h2>
            <p class="text-xl text-gray-300 mb-8">Have a project in mind? Let's discuss how we can bring it to life.</p>
            <div class="flex justify-center space-x-6">
                <a href="mailto:<EMAIL>" class="bg-white text-black px-6 py-3 rounded-lg hover:bg-gray-100">
                    Email Me
                </a>
                <a href="#" class="border border-white text-white px-6 py-3 rounded-lg hover:bg-white hover:text-black">
                    LinkedIn
                </a>
            </div>
        </div>
    </section>
</body>
</html>"""

    def _generate_modern_page(self, message: str) -> str:
        """Generate a rich, feature-complete modern webpage with animations"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Digital Experience</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'fade-in': 'fadeIn 1s ease-out'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 30px rgba(59, 130, 246, 0.8)' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(50px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
        .gradient-text {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .parallax {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease-out;
        }
        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white overflow-x-hidden">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass transition-all duration-300" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="text-2xl font-bold gradient-text animate-glow">
                    <i class="fas fa-rocket mr-2"></i>ModernTech
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="hover:text-blue-400 transition-colors duration-300 scroll-link">Home</a>
                    <a href="#about" class="hover:text-blue-400 transition-colors duration-300 scroll-link">About</a>
                    <a href="#services" class="hover:text-blue-400 transition-colors duration-300 scroll-link">Services</a>
                    <a href="#portfolio" class="hover:text-blue-400 transition-colors duration-300 scroll-link">Portfolio</a>
                    <a href="#testimonials" class="hover:text-blue-400 transition-colors duration-300 scroll-link">Reviews</a>
                    <a href="#contact" class="hover:text-blue-400 transition-colors duration-300 scroll-link">Contact</a>
                </div>
                <button class="md:hidden text-white" id="mobile-menu-btn">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
        <div class="absolute inset-0">
            <div class="absolute top-20 left-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"></div>
            <div class="absolute top-40 right-20 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-20 left-1/2 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style="animation-delay: 4s;"></div>
        </div>
        <div class="relative z-10 text-center max-w-5xl mx-auto px-4">
            <h1 class="text-6xl md:text-8xl font-bold mb-6 animate-slide-up">
                <span class="gradient-text">Digital</span><br>
                <span class="text-white">Innovation</span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in" style="animation-delay: 0.5s;">
                Transforming ideas into extraordinary digital experiences with cutting-edge technology
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in" style="animation-delay: 1s;">
                <button class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                    <i class="fas fa-rocket mr-2"></i>Get Started
                </button>
                <button class="border-2 border-white/30 hover:border-white/60 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 glass hover:bg-white/10">
                    <i class="fas fa-play mr-2"></i>Watch Demo
                </button>
            </div>
        </div>
        <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
            <i class="fas fa-chevron-down text-2xl text-white/60"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gradient-to-r from-slate-800 to-slate-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="scroll-reveal">
                    <h2 class="text-4xl md:text-5xl font-bold mb-6">
                        <span class="gradient-text">About</span> Our Vision
                    </h2>
                    <p class="text-lg text-gray-300 mb-6">
                        We are passionate innovators dedicated to creating digital solutions that push the boundaries of what's possible. Our team combines creativity with technical expertise to deliver exceptional results.
                    </p>
                    <div class="grid grid-cols-2 gap-6 mb-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-400 mb-2">500+</div>
                            <div class="text-gray-400">Projects Completed</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-400 mb-2">50+</div>
                            <div class="text-gray-400">Happy Clients</div>
                        </div>
                    </div>
                    <button class="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        Learn More <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
                <div class="scroll-reveal">
                    <div class="relative">
                        <div class="glass p-8 rounded-2xl">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-4 rounded-lg text-center">
                                    <i class="fas fa-code text-2xl mb-2"></i>
                                    <div class="text-sm">Development</div>
                                </div>
                                <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-4 rounded-lg text-center">
                                    <i class="fas fa-palette text-2xl mb-2"></i>
                                    <div class="text-sm">Design</div>
                                </div>
                                <div class="bg-gradient-to-br from-pink-500 to-pink-600 p-4 rounded-lg text-center">
                                    <i class="fas fa-mobile-alt text-2xl mb-2"></i>
                                    <div class="text-sm">Mobile</div>
                                </div>
                                <div class="bg-gradient-to-br from-indigo-500 to-indigo-600 p-4 rounded-lg text-center">
                                    <i class="fas fa-cloud text-2xl mb-2"></i>
                                    <div class="text-sm">Cloud</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-gradient-to-r from-slate-900 to-purple-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Our <span class="gradient-text">Services</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Comprehensive digital solutions tailored to your business needs
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass p-8 rounded-2xl hover:scale-105 transition-all duration-300 scroll-reveal group">
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 w-16 h-16 rounded-lg flex items-center justify-center mb-6 group-hover:animate-pulse">
                        <i class="fas fa-laptop-code text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Web Development</h3>
                    <p class="text-gray-300 mb-6">Custom websites and web applications built with modern technologies and best practices.</p>
                    <ul class="text-sm text-gray-400 space-y-2">
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Responsive Design</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Performance Optimization</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>SEO Ready</li>
                    </ul>
                </div>
                <div class="glass p-8 rounded-2xl hover:scale-105 transition-all duration-300 scroll-reveal group">
                    <div class="bg-gradient-to-br from-purple-500 to-purple-600 w-16 h-16 rounded-lg flex items-center justify-center mb-6 group-hover:animate-pulse">
                        <i class="fas fa-mobile-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Mobile Apps</h3>
                    <p class="text-gray-300 mb-6">Native and cross-platform mobile applications for iOS and Android devices.</p>
                    <ul class="text-sm text-gray-400 space-y-2">
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Cross-Platform</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Native Performance</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>App Store Ready</li>
                    </ul>
                </div>
                <div class="glass p-8 rounded-2xl hover:scale-105 transition-all duration-300 scroll-reveal group">
                    <div class="bg-gradient-to-br from-pink-500 to-pink-600 w-16 h-16 rounded-lg flex items-center justify-center mb-6 group-hover:animate-pulse">
                        <i class="fas fa-cloud text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Cloud Solutions</h3>
                    <p class="text-gray-300 mb-6">Scalable cloud infrastructure and deployment solutions for your applications.</p>
                    <ul class="text-sm text-gray-400 space-y-2">
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Auto Scaling</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>High Availability</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Security First</li>
                    </ul>
                </div>
                <div class="glass p-8 rounded-2xl hover:scale-105 transition-all duration-300 scroll-reveal group">
                    <div class="bg-gradient-to-br from-indigo-500 to-indigo-600 w-16 h-16 rounded-lg flex items-center justify-center mb-6 group-hover:animate-pulse">
                        <i class="fas fa-palette text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">UI/UX Design</h3>
                    <p class="text-gray-300 mb-6">Beautiful and intuitive user interfaces that provide exceptional user experiences.</p>
                    <ul class="text-sm text-gray-400 space-y-2">
                        <li><i class="fas fa-check text-green-400 mr-2"></i>User Research</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Prototyping</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Design Systems</li>
                    </ul>
                </div>
                <div class="glass p-8 rounded-2xl hover:scale-105 transition-all duration-300 scroll-reveal group">
                    <div class="bg-gradient-to-br from-green-500 to-green-600 w-16 h-16 rounded-lg flex items-center justify-center mb-6 group-hover:animate-pulse">
                        <i class="fas fa-chart-line text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Analytics</h3>
                    <p class="text-gray-300 mb-6">Data-driven insights and analytics to help you make informed business decisions.</p>
                    <ul class="text-sm text-gray-400 space-y-2">
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Real-time Data</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Custom Reports</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Predictive Analysis</li>
                    </ul>
                </div>
                <div class="glass p-8 rounded-2xl hover:scale-105 transition-all duration-300 scroll-reveal group">
                    <div class="bg-gradient-to-br from-yellow-500 to-orange-500 w-16 h-16 rounded-lg flex items-center justify-center mb-6 group-hover:animate-pulse">
                        <i class="fas fa-shield-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">Security</h3>
                    <p class="text-gray-300 mb-6">Comprehensive security solutions to protect your digital assets and user data.</p>
                    <ul class="text-sm text-gray-400 space-y-2">
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Penetration Testing</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>Compliance</li>
                        <li><i class="fas fa-check text-green-400 mr-2"></i>24/7 Monitoring</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-20 bg-gradient-to-r from-purple-900 to-slate-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Our <span class="gradient-text">Portfolio</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Showcasing our latest projects and creative solutions
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="group cursor-pointer scroll-reveal">
                    <div class="relative overflow-hidden rounded-2xl glass">
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 h-48 flex items-center justify-center">
                            <i class="fas fa-shopping-cart text-4xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">E-Commerce Platform</h3>
                            <p class="text-gray-300 text-sm mb-4">Modern online store with advanced features</p>
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded">React</span>
                                <span class="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded">Node.js</span>
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="bg-white text-black px-4 py-2 rounded-lg font-semibold">View Project</button>
                        </div>
                    </div>
                </div>
                <div class="group cursor-pointer scroll-reveal">
                    <div class="relative overflow-hidden rounded-2xl glass">
                        <div class="bg-gradient-to-br from-green-500 to-teal-600 h-48 flex items-center justify-center">
                            <i class="fas fa-mobile-alt text-4xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Fitness Mobile App</h3>
                            <p class="text-gray-300 text-sm mb-4">Cross-platform fitness tracking application</p>
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded">Flutter</span>
                                <span class="px-2 py-1 bg-teal-500/20 text-teal-300 text-xs rounded">Firebase</span>
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="bg-white text-black px-4 py-2 rounded-lg font-semibold">View Project</button>
                        </div>
                    </div>
                </div>
                <div class="group cursor-pointer scroll-reveal">
                    <div class="relative overflow-hidden rounded-2xl glass">
                        <div class="bg-gradient-to-br from-pink-500 to-red-600 h-48 flex items-center justify-center">
                            <i class="fas fa-chart-bar text-4xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Analytics Dashboard</h3>
                            <p class="text-gray-300 text-sm mb-4">Real-time data visualization platform</p>
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-pink-500/20 text-pink-300 text-xs rounded">Vue.js</span>
                                <span class="px-2 py-1 bg-red-500/20 text-red-300 text-xs rounded">D3.js</span>
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="bg-white text-black px-4 py-2 rounded-lg font-semibold">View Project</button>
                        </div>
                    </div>
                </div>
                <div class="group cursor-pointer scroll-reveal">
                    <div class="relative overflow-hidden rounded-2xl glass">
                        <div class="bg-gradient-to-br from-indigo-500 to-blue-600 h-48 flex items-center justify-center">
                            <i class="fas fa-brain text-4xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">AI Assistant</h3>
                            <p class="text-gray-300 text-sm mb-4">Intelligent chatbot with natural language processing</p>
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-indigo-500/20 text-indigo-300 text-xs rounded">Python</span>
                                <span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded">TensorFlow</span>
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="bg-white text-black px-4 py-2 rounded-lg font-semibold">View Project</button>
                        </div>
                    </div>
                </div>
                <div class="group cursor-pointer scroll-reveal">
                    <div class="relative overflow-hidden rounded-2xl glass">
                        <div class="bg-gradient-to-br from-yellow-500 to-orange-600 h-48 flex items-center justify-center">
                            <i class="fas fa-gamepad text-4xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Gaming Platform</h3>
                            <p class="text-gray-300 text-sm mb-4">Multiplayer online gaming experience</p>
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded">Unity</span>
                                <span class="px-2 py-1 bg-orange-500/20 text-orange-300 text-xs rounded">WebGL</span>
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="bg-white text-black px-4 py-2 rounded-lg font-semibold">View Project</button>
                        </div>
                    </div>
                </div>
                <div class="group cursor-pointer scroll-reveal">
                    <div class="relative overflow-hidden rounded-2xl glass">
                        <div class="bg-gradient-to-br from-purple-500 to-pink-600 h-48 flex items-center justify-center">
                            <i class="fas fa-video text-4xl text-white"></i>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Video Streaming</h3>
                            <p class="text-gray-300 text-sm mb-4">High-quality video streaming platform</p>
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded">Next.js</span>
                                <span class="px-2 py-1 bg-pink-500/20 text-pink-300 text-xs rounded">WebRTC</span>
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="bg-white text-black px-4 py-2 rounded-lg font-semibold">View Project</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-gradient-to-r from-slate-900 to-purple-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Client <span class="gradient-text">Testimonials</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    What our clients say about working with us
                </p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass p-8 rounded-2xl scroll-reveal">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">JS</span>
                        </div>
                        <div>
                            <div class="font-semibold">John Smith</div>
                            <div class="text-sm text-gray-400">CEO, TechCorp</div>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                    <p class="text-gray-300 italic">"Exceptional work! They delivered our project on time and exceeded all expectations. The attention to detail was remarkable."</p>
                </div>
                <div class="glass p-8 rounded-2xl scroll-reveal">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-red-600 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">MJ</span>
                        </div>
                        <div>
                            <div class="font-semibold">Maria Johnson</div>
                            <div class="text-sm text-gray-400">Founder, StartupXYZ</div>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                    <p class="text-gray-300 italic">"Professional, creative, and reliable. Our mobile app has been a huge success thanks to their expertise."</p>
                </div>
                <div class="glass p-8 rounded-2xl scroll-reveal">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">DL</span>
                        </div>
                        <div>
                            <div class="font-semibold">David Lee</div>
                            <div class="text-sm text-gray-400">CTO, InnovateLab</div>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                    <p class="text-gray-300 italic">"Outstanding technical skills and great communication. They transformed our vision into reality perfectly."</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gradient-to-r from-purple-900 to-slate-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Get In <span class="gradient-text">Touch</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Ready to start your next project? Let's discuss how we can help you achieve your goals.
                </p>
            </div>
            <div class="grid lg:grid-cols-2 gap-12">
                <div class="scroll-reveal">
                    <div class="glass p-8 rounded-2xl">
                        <h3 class="text-2xl font-bold mb-6">Contact Information</h3>
                        <div class="space-y-6">
                            <div class="flex items-center">
                                <div class="bg-gradient-to-br from-blue-500 to-purple-600 w-12 h-12 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div>
                                    <div class="font-semibold">Email</div>
                                    <div class="text-gray-300"><EMAIL></div>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="bg-gradient-to-br from-green-500 to-teal-600 w-12 h-12 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-phone text-white"></i>
                                </div>
                                <div>
                                    <div class="font-semibold">Phone</div>
                                    <div class="text-gray-300">+****************</div>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="bg-gradient-to-br from-pink-500 to-red-600 w-12 h-12 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <div class="font-semibold">Location</div>
                                    <div class="text-gray-300">San Francisco, CA</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-8">
                            <h4 class="text-lg font-semibold mb-4">Follow Us</h4>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-gradient-to-br from-blue-500 to-blue-600 w-10 h-10 rounded-lg flex items-center justify-center hover:scale-110 transition-transform duration-300">
                                    <i class="fab fa-twitter text-white"></i>
                                </a>
                                <a href="#" class="bg-gradient-to-br from-blue-600 to-blue-700 w-10 h-10 rounded-lg flex items-center justify-center hover:scale-110 transition-transform duration-300">
                                    <i class="fab fa-linkedin text-white"></i>
                                </a>
                                <a href="#" class="bg-gradient-to-br from-purple-500 to-purple-600 w-10 h-10 rounded-lg flex items-center justify-center hover:scale-110 transition-transform duration-300">
                                    <i class="fab fa-github text-white"></i>
                                </a>
                                <a href="#" class="bg-gradient-to-br from-pink-500 to-pink-600 w-10 h-10 rounded-lg flex items-center justify-center hover:scale-110 transition-transform duration-300">
                                    <i class="fab fa-dribbble text-white"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="scroll-reveal">
                    <form class="glass p-8 rounded-2xl" id="contactForm">
                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium mb-2">First Name</label>
                                <input type="text" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:border-blue-400 focus:outline-none transition-colors duration-300" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Last Name</label>
                                <input type="text" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:border-blue-400 focus:outline-none transition-colors duration-300" required>
                            </div>
                        </div>
                        <div class="mb-6">
                            <label class="block text-sm font-medium mb-2">Email</label>
                            <input type="email" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:border-blue-400 focus:outline-none transition-colors duration-300" required>
                        </div>
                        <div class="mb-6">
                            <label class="block text-sm font-medium mb-2">Subject</label>
                            <input type="text" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:border-blue-400 focus:outline-none transition-colors duration-300" required>
                        </div>
                        <div class="mb-6">
                            <label class="block text-sm font-medium mb-2">Message</label>
                            <textarea rows="5" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg focus:border-blue-400 focus:outline-none transition-colors duration-300 resize-none" required></textarea>
                        </div>
                        <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-6 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-paper-plane mr-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-slate-900 py-12 mt-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <div>
                    <div class="text-2xl font-bold gradient-text mb-4">
                        <i class="fas fa-rocket mr-2"></i>ModernTech
                    </div>
                    <p class="text-gray-400 mb-4">Creating digital experiences that inspire and innovate.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors duration-300">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Services</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Web Development</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Mobile Apps</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Cloud Solutions</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">UI/UX Design</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Company</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors duration-300">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Careers</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Blog</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Newsletter</h4>
                    <p class="text-gray-400 mb-4">Stay updated with our latest news and offers.</p>
                    <div class="flex">
                        <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-l-lg focus:outline-none focus:border-blue-400">
                        <button class="bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-2 rounded-r-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-8 text-center text-gray-400">
                <p>&copy; 2024 ModernTech. All rights reserved. | Privacy Policy | Terms of Service</p>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.scroll-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(15, 23, 42, 0.9)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.1)';
            }
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.createElement('div');
        mobileMenu.className = 'md:hidden absolute top-16 left-0 w-full glass p-4 space-y-4';
        mobileMenu.innerHTML = `
            <a href="#home" class="block hover:text-blue-400 transition-colors duration-300 scroll-link">Home</a>
            <a href="#about" class="block hover:text-blue-400 transition-colors duration-300 scroll-link">About</a>
            <a href="#services" class="block hover:text-blue-400 transition-colors duration-300 scroll-link">Services</a>
            <a href="#portfolio" class="block hover:text-blue-400 transition-colors duration-300 scroll-link">Portfolio</a>
            <a href="#testimonials" class="block hover:text-blue-400 transition-colors duration-300 scroll-link">Reviews</a>
            <a href="#contact" class="block hover:text-blue-400 transition-colors duration-300 scroll-link">Contact</a>
        `;
        mobileMenu.style.display = 'none';
        document.querySelector('nav').appendChild(mobileMenu);

        mobileMenuBtn.addEventListener('click', function() {
            if (mobileMenu.style.display === 'none') {
                mobileMenu.style.display = 'block';
                this.innerHTML = '<i class="fas fa-times text-xl"></i>';
            } else {
                mobileMenu.style.display = 'none';
                this.innerHTML = '<i class="fas fa-bars text-xl"></i>';
            }
        });

        // Contact form handling
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Message Sent!';
                submitBtn.style.background = 'linear-gradient(to right, #10b981, #059669)';

                // Reset form
                this.reset();

                // Reset button after 3 seconds
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = '';
                    submitBtn.disabled = false;
                }, 3000);
            }, 2000);
        });

        // Add typing animation to hero text
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Initialize animations when page loads
        window.addEventListener('load', function() {
            // Add entrance animations
            document.body.style.opacity = '1';

            // Parallax effect for floating elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallaxElements = document.querySelectorAll('.animate-float');
                parallaxElements.forEach((element, index) => {
                    const speed = 0.5 + (index * 0.1);
                    element.style.transform = `translateY(${scrolled * speed}px)`;
                });
            });
        });
    </script>
</body>
</html>"""

    def _generate_blog_page(self, message: str) -> str:
        """Generate a blog page - Code Wave Style"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tech Blog</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">Tech Blog</h1>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-gray-700 hover:text-gray-900">Home</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900">Articles</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900">About</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900">Contact</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Featured Article -->
            <article class="mb-16">
                <div class="h-64 bg-gray-200 rounded-lg mb-8"></div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">The Future of Web Development</h1>
                <div class="flex items-center text-gray-600 mb-6">
                    <span>By John Doe</span>
                    <span class="mx-2">•</span>
                    <span>March 15, 2024</span>
                    <span class="mx-2">•</span>
                    <span>5 min read</span>
                </div>
                <div class="prose prose-lg max-w-none">
                    <p class="text-gray-700 leading-relaxed mb-6">
                        Web development is evolving at an unprecedented pace. From the rise of AI-powered tools to the emergence of new frameworks, developers today have more opportunities than ever to create innovative digital experiences.
                    </p>
                    <p class="text-gray-700 leading-relaxed mb-6">
                        In this article, we'll explore the latest trends shaping the industry and what they mean for developers, businesses, and users alike.
                    </p>
                </div>
            </article>

            <!-- Recent Articles -->
            <section>
                <h2 class="text-2xl font-bold text-gray-900 mb-8">Recent Articles</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <article class="bg-gray-50 rounded-lg p-6">
                        <div class="h-32 bg-gray-200 rounded mb-4"></div>
                        <h3 class="text-xl font-semibold mb-2">Building Scalable APIs</h3>
                        <p class="text-gray-600 mb-4">Learn how to design and implement APIs that can handle millions of requests.</p>
                        <div class="text-sm text-gray-500">March 10, 2024</div>
                    </article>
                    <article class="bg-gray-50 rounded-lg p-6">
                        <div class="h-32 bg-gray-200 rounded mb-4"></div>
                        <h3 class="text-xl font-semibold mb-2">Modern CSS Techniques</h3>
                        <p class="text-gray-600 mb-4">Discover the latest CSS features that will transform your styling workflow.</p>
                        <div class="text-sm text-gray-500">March 5, 2024</div>
                    </article>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p>&copy; 2024 Tech Blog. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>"""

    def _generate_dashboard_page(self, message: str) -> str:
        """Generate a dashboard page - Code Wave Style"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <!-- Sidebar -->
    <div class="flex h-screen">
        <div class="w-64 bg-gray-900 text-white">
            <div class="p-6">
                <h1 class="text-xl font-bold">Dashboard</h1>
            </div>
            <nav class="mt-6">
                <a href="#" class="block px-6 py-3 bg-gray-800">Overview</a>
                <a href="#" class="block px-6 py-3 hover:bg-gray-800">Users</a>
                <a href="#" class="block px-6 py-3 hover:bg-gray-800">Analytics</a>
                <a href="#" class="block px-6 py-3 hover:bg-gray-800">Settings</a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white shadow-sm p-6">
                <h1 class="text-2xl font-bold text-gray-900">Overview</h1>
            </header>

            <!-- Content -->
            <main class="p-6">
                <!-- Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-sm font-medium text-gray-500">Total Users</h3>
                        <p class="text-3xl font-bold text-gray-900">12,345</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-sm font-medium text-gray-500">Revenue</h3>
                        <p class="text-3xl font-bold text-gray-900">$54,321</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-sm font-medium text-gray-500">Orders</h3>
                        <p class="text-3xl font-bold text-gray-900">1,234</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-sm font-medium text-gray-500">Growth</h3>
                        <p class="text-3xl font-bold text-green-600">+12%</p>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-lg font-semibold mb-4">Sales Chart</h3>
                        <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
                            <span class="text-gray-500">Chart Placeholder</span>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm">
                        <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-500 rounded-full"></div>
                                <div>
                                    <p class="font-medium">New user registered</p>
                                    <p class="text-sm text-gray-500">2 minutes ago</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-500 rounded-full"></div>
                                <div>
                                    <p class="font-medium">Order completed</p>
                                    <p class="text-sm text-gray-500">5 minutes ago</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full"></div>
                                <div>
                                    <p class="font-medium">Payment pending</p>
                                    <p class="text-sm text-gray-500">10 minutes ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>"""

    def _generate_landing_page(self, message: str) -> str:
        """Generate a landing page - Code Wave Style"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Landing</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-5xl font-bold text-gray-900 mb-6">
                    Transform Your Business
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                    Our innovative solution helps companies streamline operations, increase productivity, and drive growth.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-black text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-800">
                        Start Free Trial
                    </button>
                    <button class="border-2 border-black text-black px-8 py-4 rounded-lg text-lg font-semibold hover:bg-black hover:text-white">
                        Watch Demo
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Powerful Features</h2>
                <p class="text-xl text-gray-600">Everything you need to succeed</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-lg mx-auto mb-6"></div>
                    <h3 class="text-xl font-semibold mb-4">Easy Integration</h3>
                    <p class="text-gray-600">Seamlessly integrate with your existing tools and workflows.</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-500 rounded-lg mx-auto mb-6"></div>
                    <h3 class="text-xl font-semibold mb-4">Real-time Analytics</h3>
                    <p class="text-gray-600">Get insights into your business performance with live data.</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-500 rounded-lg mx-auto mb-6"></div>
                    <h3 class="text-xl font-semibold mb-4">24/7 Support</h3>
                    <p class="text-gray-600">Our team is here to help you succeed around the clock.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-black text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-4xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl text-gray-300 mb-8">Join thousands of satisfied customers</p>
            <button class="bg-white text-black px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100">
                Start Your Free Trial
            </button>
        </div>
    </section>
</body>
</html>"""

    def _generate_ecommerce_page(self, message: str) -> str:
        """Generate an ecommerce page - Code Wave Style"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Store</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">Store</h1>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-gray-700 hover:text-gray-900">Products</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900">Categories</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900">About</a>
                    <button class="bg-black text-white px-4 py-2 rounded-lg">Cart (0)</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gray-50 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Shop the Latest Collection</h1>
            <p class="text-xl text-gray-600 mb-8">Discover amazing products at unbeatable prices</p>
            <button class="bg-black text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-800">
                Shop Now
            </button>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-12 text-center">Featured Products</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-64 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-2">Product Name</h3>
                        <p class="text-gray-600 mb-4">Brief product description</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold">$99</span>
                            <button class="bg-black text-white px-4 py-2 rounded hover:bg-gray-800">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-64 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-2">Product Name</h3>
                        <p class="text-gray-600 mb-4">Brief product description</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold">$149</span>
                            <button class="bg-black text-white px-4 py-2 rounded hover:bg-gray-800">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-64 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-2">Product Name</h3>
                        <p class="text-gray-600 mb-4">Brief product description</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold">$79</span>
                            <button class="bg-black text-white px-4 py-2 rounded hover:bg-gray-800">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-64 bg-gray-200"></div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-2">Product Name</h3>
                        <p class="text-gray-600 mb-4">Brief product description</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold">$199</span>
                            <button class="bg-black text-white px-4 py-2 rounded hover:bg-gray-800">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p>&copy; 2024 Online Store. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>"""

    def _generate_flask_template(self, message: str) -> str:
        """Generate Flask application template with proper port configuration"""
        return """#!/usr/bin/env python3
\"\"\"
Flask Web Application
Generated based on user request
\"\"\"

from flask import Flask, render_template_string, request, jsonify
import json
import os
from datetime import datetime

app = Flask(__name__)

# HTML template
TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flask Contact Form</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <h1 class="text-4xl font-bold text-white text-center mb-8">
                <i class="fas fa-envelope mr-3"></i>Contact Form
            </h1>

            {% if message %}
            <div class="bg-green-500/20 border border-green-500 rounded-lg p-4 mb-6 text-green-300 text-center">
                <i class="fas fa-check-circle mr-2"></i>{{ message }}
            </div>
            {% endif %}

            <div class="bg-white/10 backdrop-blur-lg rounded-lg p-8 border border-white/20">
                <form method="POST" action="/submit" class="space-y-6">
                    <div>
                        <label for="name" class="block text-white font-semibold mb-2">Name</label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400">
                    </div>

                    <div>
                        <label for="email" class="block text-white font-semibold mb-2">Email</label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400">
                    </div>

                    <div>
                        <label for="subject" class="block text-white font-semibold mb-2">Subject</label>
                        <input type="text" id="subject" name="subject" required
                               class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400">
                    </div>

                    <div>
                        <label for="message" class="block text-white font-semibold mb-2">Message</label>
                        <textarea id="message" name="message" rows="5" required
                                  class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"></textarea>
                    </div>

                    <button type="submit"
                            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                        <i class="fas fa-paper-plane mr-2"></i>Send Message
                    </button>
                </form>
            </div>

            <div class="mt-8 text-center">
                <a href="/submissions" class="text-blue-300 hover:text-blue-200 underline">
                    <i class="fas fa-list mr-2"></i>View All Submissions
                </a>
            </div>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def home():
    return render_template_string(TEMPLATE)

@app.route('/submit', methods=['POST'])
def submit_form():
    try:
        form_data = {
            'name': request.form.get('name'),
            'email': request.form.get('email'),
            'subject': request.form.get('subject'),
            'message': request.form.get('message'),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # Save to JSON file
        submissions_file = 'contact_submissions.json'
        submissions = []

        if os.path.exists(submissions_file):
            with open(submissions_file, 'r') as f:
                submissions = json.load(f)

        submissions.append(form_data)

        with open(submissions_file, 'w') as f:
            json.dump(submissions, f, indent=2)

        return render_template_string(TEMPLATE, message="Thank you! Your message has been submitted successfully.")

    except Exception as e:
        return render_template_string(TEMPLATE, message=f"Error: {str(e)}")

@app.route('/submissions')
def view_submissions():
    try:
        submissions_file = 'contact_submissions.json'
        submissions = []

        if os.path.exists(submissions_file):
            with open(submissions_file, 'r') as f:
                submissions = json.load(f)

        return jsonify({'submissions': submissions, 'count': len(submissions)})

    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    import socket

    # Function to find an available port
    def find_available_port(start_port=5002):
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return None

    # Find an available port starting from 5002
    port = find_available_port(5002)

    if port:
        print("Starting Flask Contact Form Application...")
        print(f"Access the application at: http://127.0.0.1:{port}")
        print(f"View submissions at: http://127.0.0.1:{port}/submissions")

        try:
            # Use the available port to avoid conflicts
            app.run(debug=True, port=port, host="127.0.0.1", use_reloader=False)
        except Exception as e:
            print(f"Error starting server on port {port}: {e}")
            print("The application may already be running or the port is in use.")
    else:
        print("Error: Could not find an available port between 5002-5101")
        print("Please check if other applications are using these ports.")
"""

    def _generate_python_template(self, message: str) -> str:
        """Generate Python script template"""
        return """#!/usr/bin/env python3
\"\"\"
Python Script
Created with Agentic Code Assistant
\"\"\"

def main():
    print("Hello from Python!")

    # Example functionality
    numbers = [1, 2, 3, 4, 5]
    squared = [x**2 for x in numbers]

    print(f"Original numbers: {numbers}")
    print(f"Squared numbers: {squared}")

    # Calculate sum
    total = sum(squared)
    print(f"Sum of squares: {total}")

if __name__ == "__main__":
    main()"""

    def _generate_css_template(self, message: str) -> str:
        """Generate CSS template"""
        return """/* CSS Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.animated-element {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 50%;
    animation: pulse 2s infinite;
    margin: 50px auto;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.button {
    display: inline-block;
    padding: 12px 24px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.button:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}"""

    def _modify_existing_code(self, message: str, current_code: str) -> str:
        """Modify existing code based on user request"""
        # Simple modifications based on keywords
        message_lower = message.lower()

        if "color" in message_lower and "blue" in message_lower:
            current_code = re.sub(r'#[0-9a-fA-F]{6}', '#007bff', current_code)
        elif "color" in message_lower and "red" in message_lower:
            current_code = re.sub(r'#[0-9a-fA-F]{6}', '#dc3545', current_code)
        elif "bigger" in message_lower or "larger" in message_lower:
            current_code = re.sub(r'font-size:\s*(\d+)', lambda m: f'font-size: {int(m.group(1)) + 4}', current_code)
        elif "smaller" in message_lower:
            current_code = re.sub(r'font-size:\s*(\d+)', lambda m: f'font-size: {max(10, int(m.group(1)) - 4)}', current_code)

        return current_code

    def _detect_language(self, code: str) -> str:
        """Detect programming language from code"""
        if '<!DOCTYPE html>' in code or '<html' in code:
            return 'html'
        elif 'import React' in code or 'jsx' in code.lower():
            return 'javascript'
        elif 'def ' in code and 'python' in code.lower():
            return 'python'
        elif '{' in code and '}' in code and ('background' in code or 'color' in code):
            return 'css'
        else:
            return 'html'

    def _generate_fallback_code(self, text: str) -> str:
        """Generate fallback code when LLM fails"""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Code</title>
</head>
<body>
    <h1>Code Generated</h1>
    <p>Request: {text[:100]}...</p>
</body>
</html>"""

# Initialize assistant
assistant = AgenticCodeAssistant()

from app.decorators.trial_limit import trial_required

@agentic_code_bp.route('/process', methods=['POST'])
@trial_required('agentic_code')  # Enforce 3-prompt trial restriction
def process_agentic_request():
    """
    Process agentic code request with step-by-step updates
    """
    start_time = time.time()
    try:
        data = request.get_json()
        message = data.get('message', '')
        current_code = data.get('current_code', '')
        session_id = data.get('session_id', 'default')
        files = data.get('files', [])  # Get uploaded files

        if not message:
            return jsonify({'error': 'Message is required'}), 400

        # Get user_id from session for activity logging
        from flask import session
        user_id = session.get('user_id')

        # Initialize credit service for token-based consumption
        from app.services.credit_service import CreditService
        credit_service = CreditService()

        # Determine task type for minimum charge calculation
        if len(message) > 500 or any(keyword in message.lower() for keyword in ['complex', 'comprehensive', 'detailed', 'advanced', 'full application', 'complete system']):
            task_type = 'agentic_code_complex'  # Higher cost for complex code generation
        else:
            task_type = 'agentic_code_basic'    # Lower cost for basic code generation

        # Pre-consume minimum credits (will be adjusted after execution)
        pre_credit_result = credit_service.consume_credits(
            user_id=user_id,
            task_type=task_type,
            input_text=message,
            output_text="",  # Will update after execution
            use_token_based=True
        )

        if not pre_credit_result['success']:
            return jsonify({
                'success': False,
                'error': pre_credit_result.get('error', 'Insufficient credits'),
                'credits_needed': pre_credit_result.get('credits_needed'),
                'credits_available': pre_credit_result.get('credits_available'),
                'plan': 'Please upgrade your plan or wait for daily credit reset'
            }), 402  # Payment Required

        # Process uploaded files if present (from universal file upload)
        enhanced_message = message

        if "--- File:" in message or "--- Image:" in message:
            try:
                # Extract the original user message (before file content)
                parts = message.split('\n\n--- File:')
                if len(parts) > 1:
                    original_message = parts[0]
                    file_content = '\n\n--- File:' + '\n\n--- File:'.join(parts[1:])
                else:
                    parts = message.split('\n\n--- Image:')
                    if len(parts) > 1:
                        original_message = parts[0]
                        file_content = '\n\n--- Image:' + '\n\n--- Image:'.join(parts[1:])
                    else:
                        original_message = message
                        file_content = ""

                if file_content:
                    # Use the file processor to enhance the message
                    enhanced_message = file_processor.enhance_prompt_with_files(original_message, file_content)
                    print(f"Enhanced Agentic Code message with file analysis: {len(enhanced_message)} characters")
            except Exception as e:
                print(f"Error processing files in Agentic Code: {str(e)}")
                # Continue with original message if file processing fails
                enhanced_message = message

        # Store session
        if session_id not in sessions:
            sessions[session_id] = {
                'history': [],
                'created_at': time.time()
            }

        # Add to session history
        sessions[session_id]['history'].append({
            'message': enhanced_message,
            'current_code': current_code,
            'files': files,  # Store files in session
            'timestamp': time.time()
        })

        # Check if we need to get images from Pexels
        suggested_image_url = get_image_for_context(enhanced_message)
        if suggested_image_url:
            print(f"🖼️ Pexels image suggested for context: {suggested_image_url}")
            # Add image context to the message
            enhanced_message += f"\n\n[SYSTEM: A relevant image is available at {suggested_image_url} - you can use this in your code if appropriate for the design.]"

        # Process the request with enhanced message and files
        result = assistant.analyze_request(enhanced_message, current_code, files)

        # Add Pexels image to result if available
        if suggested_image_url:
            result['suggested_image'] = suggested_image_url

        # Add result to session
        sessions[session_id]['history'][-1]['result'] = result

        # Calculate processing time and log activity
        processing_time_ms = int((time.time() - start_time) * 1000)

        # Log activity if user_id is available
        if user_id:
            try:
                log_agentic_code_activity(
                    user_id=user_id,
                    message=message,
                    result=result,
                    current_code=current_code,
                    processing_time_ms=processing_time_ms,
                    session_id=session_id
                )
            except Exception as e:
                print(f"Failed to log Agentic Code activity: {e}")

        # Add credit consumption info to response
        if pre_credit_result.get('success'):
            result['credits_consumed'] = pre_credit_result.get('credits_consumed', 0)
            result['remaining_credits'] = pre_credit_result.get('remaining_credits', 0)
            result['plan'] = pre_credit_result.get('plan', 'free')

        return jsonify(result)

    except Exception as e:
        print(f"Error processing agentic request: {e}")
        return jsonify({
            'error': 'Failed to process request',
            'plan': 'Encountered an error',
            'steps': ['Error occurred'],
            'code': current_code,
            'explanation': 'Sorry, I encountered an error processing your request.',
            'language': 'html'
        }), 500

@agentic_code_bp.route('/session/<session_id>', methods=['GET'])
def get_session_history(session_id):
    """Get session history"""
    if session_id in sessions:
        return jsonify(sessions[session_id])
    else:
        return jsonify({'error': 'Session not found'}), 404

@agentic_code_bp.route('/search-images', methods=['POST'])
def search_images():
    """
    Search for images using Pexels API
    """
    try:
        data = request.get_json()
        query = data.get('query', '')
        per_page = data.get('per_page', 5)

        if not query:
            return jsonify({'error': 'Query is required'}), 400

        # Search for images
        images = search_pexels_images(query, per_page)

        return jsonify({
            'success': True,
            'query': query,
            'images': images,
            'count': len(images)
        })

    except Exception as e:
        print(f"Error in search_images: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@agentic_code_bp.route('/execute', methods=['POST'])
@require_subscription('plus')  # Code execution requires Plus plan or higher
def execute_code():
    """Execute generated code using the simple code executor"""
    try:
        data = request.get_json()
        files = data.get('files', [])

        if not files:
            return jsonify({
                'success': False,
                'error': 'No files provided for execution'
            }), 400

        # Import the simple executor
        from app.services.simple_code_executor import simple_executor

        # Execute the project
        result = simple_executor.execute_project(files)

        return jsonify(result)

    except Exception as e:
        print(f"Error executing code: {e}")
        return jsonify({
            'success': False,
            'error': f'Failed to execute code: {str(e)}'
        }), 500

@agentic_code_bp.route('/status/<project_id>', methods=['GET'])
def get_execution_status(project_id):
    """Get execution status for a project"""
    try:
        from app.services.simple_code_executor import simple_executor

        result = simple_executor.get_execution_status(project_id)

        return jsonify(result)

    except Exception as e:
        print(f"Error getting execution status: {e}")
        return jsonify({
            'success': False,
            'error': f'Failed to get execution status: {str(e)}'
        }), 500

@agentic_code_bp.route('/stop/<project_id>', methods=['POST'])
def stop_execution(project_id):
    """Stop code execution"""
    try:
        from app.services.simple_code_executor import simple_executor

        result = simple_executor.stop_execution(project_id)

        return jsonify(result)

    except Exception as e:
        print(f"Error stopping execution: {e}")
        return jsonify({
            'success': False,
            'error': f'Failed to stop execution: {str(e)}'
        }), 500

@agentic_code_bp.route('/sessions', methods=['GET'])
def list_sessions():
    """List all sessions"""
    return jsonify({
        'sessions': list(sessions.keys()),
        'count': len(sessions)
    })
