"""
AI Sheets API - Interactive spreadsheet with AI generation capabilities
Enhanced with MCP server context and conversational AI like Agentic Code
"""

from flask import Blueprint, request, jsonify, session
import json
import csv
import io
import pandas as pd
from datetime import datetime
import logging
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
ai_sheets_bp = Blueprint('ai_sheets', __name__)

# Session key for storing conversation history
AI_SHEETS_SESSION_KEY = 'ai_sheets_conversation'
AI_SHEETS_DATA_KEY = 'ai_sheets_current_data'

@ai_sheets_bp.route('/generate-sheet', methods=['POST'])
def generate_sheet():
    """Generate AI-powered spreadsheet content with conversational context"""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '')
        sheet_type = data.get('sheet_type', 'general')
        rows = data.get('rows', 10)
        columns = data.get('columns', 5)
        is_follow_up = data.get('is_follow_up', False)

        if not prompt:
            return jsonify({'error': 'Prompt is required'}), 400

        # Get conversation history from session
        conversation_history = session.get(AI_SHEETS_SESSION_KEY, [])
        current_sheet_data = session.get(AI_SHEETS_DATA_KEY, None)

        # Add current prompt to conversation history
        conversation_history.append({
            'role': 'user',
            'content': prompt,
            'timestamp': datetime.now().isoformat()
        })

        # Try to use MCP server context first, then fallback to Gemini
        try:
            # Try MCP server for enhanced context
            from app.utils.enhanced_mcp_client import EnhancedMCPClient
            mcp_client = EnhancedMCPClient()

            # Build context-aware prompt
            context_prompt = build_context_prompt(prompt, conversation_history, current_sheet_data, is_follow_up)

            # Use MCP chat tool for better context understanding
            mcp_response = mcp_client.execute_tool('chat', {
                'message': context_prompt,
                'system_prompt': get_ai_sheets_system_prompt(rows, columns, is_follow_up)
            })

            if mcp_response and mcp_response.get('success'):
                ai_response = mcp_response.get('response', '')
                reasoning = mcp_response.get('reasoning', 'AI generated spreadsheet using MCP context')
            else:
                raise Exception("MCP server not available")

        except Exception as mcp_error:
            logger.warning(f"MCP server not available, falling back to Gemini: {mcp_error}")

            # Fallback to Gemini API
            try:
                from app.api.gemini import GeminiAPI
                gemini_api = GeminiAPI()

                context_prompt = build_context_prompt(prompt, conversation_history, current_sheet_data, is_follow_up)
                system_prompt = get_ai_sheets_system_prompt(rows, columns, is_follow_up)

                logger.info(f"🔄 Generating AI Sheets content for prompt: '{prompt[:100]}...'")
                logger.info(f"📝 System prompt length: {len(system_prompt)}")
                logger.info(f"📝 Context prompt length: {len(context_prompt)}")

                ai_response = gemini_api.generate_content(context_prompt, system_prompt=system_prompt)
                reasoning = 'AI generated spreadsheet using Gemini API'

                logger.info(f"✅ AI response received, length: {len(ai_response) if ai_response else 0}")
                logger.info(f"📄 AI response preview: {ai_response[:300] if ai_response else 'None'}...")

                if not ai_response or len(ai_response.strip()) < 10:
                    raise Exception("AI response is empty or too short")

            except Exception as gemini_error:
                logger.error(f"❌ Both MCP and Gemini failed: {gemini_error}")
                logger.error(traceback.format_exc())

                # Create fallback response with better logging
                logger.warning(f"🔄 Creating fallback response for prompt: '{prompt[:50]}...'")
                fallback_data = create_fallback_response(prompt, rows, columns, conversation_history)

                # Return the fallback response properly
                return jsonify({
                    'success': True,
                    'data': fallback_data,
                    'reasoning': f'Fallback response due to AI error: {str(gemini_error)[:100]}',
                    'conversation_id': len(conversation_history),
                    'timestamp': datetime.now().isoformat(),
                    'is_fallback': True
                })

        # Parse AI response and extract spreadsheet data
        try:
            logger.info("Attempting to parse AI response...")
            sheet_data = parse_ai_response(ai_response, prompt, rows, columns)
            logger.info(f"Successfully parsed sheet data: {sheet_data.get('title', 'Untitled')}")

            # Store updated data in session
            session[AI_SHEETS_DATA_KEY] = sheet_data
            conversation_history.append({
                'role': 'assistant',
                'content': f"Generated spreadsheet: {sheet_data.get('title', 'Untitled')}",
                'timestamp': datetime.now().isoformat(),
                'sheet_data': sheet_data
            })
            session[AI_SHEETS_SESSION_KEY] = conversation_history

            return jsonify({
                'success': True,
                'data': sheet_data,
                'reasoning': reasoning,
                'conversation_id': len(conversation_history),
                'timestamp': datetime.now().isoformat()
            })

        except Exception as parse_error:
            logger.error(f"❌ Failed to parse AI response: {parse_error}")
            logger.error(f"📄 Raw AI response was: {ai_response}")
            logger.error(traceback.format_exc())

            # Create fallback response with proper format
            logger.warning(f"🔄 Creating fallback response due to parse error for prompt: '{prompt[:50]}...'")
            fallback_data = create_fallback_response(prompt, rows, columns, conversation_history)

            # Store fallback data in session
            session[AI_SHEETS_DATA_KEY] = fallback_data
            conversation_history.append({
                'role': 'assistant',
                'content': f"Generated fallback spreadsheet: {fallback_data.get('title', 'Untitled')}",
                'timestamp': datetime.now().isoformat(),
                'sheet_data': fallback_data
            })
            session[AI_SHEETS_SESSION_KEY] = conversation_history

            return jsonify({
                'success': True,
                'data': fallback_data,
                'reasoning': f'Fallback response due to parse error: {str(parse_error)[:100]}',
                'conversation_id': len(conversation_history),
                'timestamp': datetime.now().isoformat(),
                'is_fallback': True
            })

    except Exception as e:
        logger.error(f"Error in generate_sheet: {e}")
        logger.error(traceback.format_exc())
        return jsonify({'error': 'Internal server error'}), 500

def build_context_prompt(prompt, conversation_history, current_sheet_data, is_follow_up):
    """Build a context-aware prompt for AI generation"""
    context_parts = []

    if is_follow_up and current_sheet_data:
        context_parts.append(f"Current spreadsheet context:")
        context_parts.append(f"Title: {current_sheet_data.get('title', 'Untitled')}")
        context_parts.append(f"Description: {current_sheet_data.get('description', 'No description')}")
        context_parts.append(f"Headers: {', '.join(current_sheet_data.get('headers', []))}")
        context_parts.append(f"Number of rows: {len(current_sheet_data.get('rows', []))}")
        context_parts.append("")

    if len(conversation_history) > 1:
        context_parts.append("Previous conversation:")
        for msg in conversation_history[-3:]:  # Last 3 messages for context
            if msg['role'] == 'user':
                context_parts.append(f"User: {msg['content']}")
            elif msg['role'] == 'assistant':
                context_parts.append(f"Assistant: {msg['content']}")
        context_parts.append("")

    context_parts.append(f"Current request: {prompt}")

    return "\n".join(context_parts)

def get_ai_sheets_system_prompt(rows, columns, is_follow_up):
    """Get the system prompt for AI Sheets generation"""
    base_prompt = f"""
    You are an AI Sheets assistant that creates and modifies interactive spreadsheets.
    You can generate new spreadsheets or modify existing ones based on user requests.

    {'MODIFY the existing spreadsheet' if is_follow_up else 'CREATE a new spreadsheet'} with exactly {rows} rows and {columns} columns.

    CRITICAL: You must respond with ONLY a valid JSON object. Do not include any markdown formatting, explanations, or text outside the JSON.

    Return exactly this JSON structure:
    {{
        "headers": ["Column1", "Column2", "Column3", "Column4", "Column5"],
        "rows": [
            ["Row1Col1", "Row1Col2", "Row1Col3", "Row1Col4", "Row1Col5"],
            ["Row2Col1", "Row2Col2", "Row2Col3", "Row2Col4", "Row2Col5"]
        ],
        "title": "Descriptive Spreadsheet Title",
        "description": "Brief description of what this spreadsheet contains",
        "reasoning": "Explanation of how you created this spreadsheet"
    }}

    Requirements:
    - Generate exactly {columns} headers
    - Generate exactly {rows} data rows
    - Each row must have exactly {columns} cells
    - Make data realistic and relevant to the user's request
    - Use appropriate data types (numbers, dates, text)
    - For dates, use YYYY-MM-DD format
    - Headers should be descriptive and specific to the request
    - Data should be meaningful and varied
    - No markdown formatting (no ```json or ``` tags)
    - Response must be valid JSON that can be parsed directly
    """

    return base_prompt

def parse_ai_response(ai_response, prompt, rows, columns):
    """Parse AI response and extract spreadsheet data"""
    try:
        # Extract JSON from response if it's wrapped in markdown
        json_str = ai_response.strip()

        if '```json' in json_str:
            json_start = json_str.find('```json') + 7
            json_end = json_str.find('```', json_start)
            json_str = json_str[json_start:json_end].strip()
        elif '```' in json_str:
            json_start = json_str.find('```') + 3
            json_end = json_str.find('```', json_start)
            json_str = json_str[json_start:json_end].strip()

        sheet_data = json.loads(json_str)

        # Validate required fields
        required_fields = ['headers', 'rows', 'title']
        if not all(field in sheet_data for field in required_fields):
            raise ValueError(f"Missing required fields: {required_fields}")

        # Ensure data consistency
        if not sheet_data['headers']:
            raise ValueError("Headers cannot be empty")

        if not sheet_data['rows']:
            raise ValueError("Rows cannot be empty")

        # Validate row structure
        header_count = len(sheet_data['headers'])
        for i, row in enumerate(sheet_data['rows']):
            if len(row) != header_count:
                # Pad or trim row to match header count
                if len(row) < header_count:
                    row.extend([''] * (header_count - len(row)))
                else:
                    sheet_data['rows'][i] = row[:header_count]

        # Add default description if missing
        if 'description' not in sheet_data:
            sheet_data['description'] = f'AI-generated spreadsheet for: {prompt}'

        return sheet_data

    except (json.JSONDecodeError, ValueError, KeyError) as e:
        logger.error(f"Failed to parse AI response: {e}")
        raise e

def create_fallback_response(prompt, rows, columns, conversation_history):
    """Create a contextual fallback response when AI generation fails"""

    logger.warning(f"Creating fallback response for prompt: {prompt}")

    # Generate contextual headers and data based on prompt keywords
    prompt_lower = prompt.lower()

    if 'budget' in prompt_lower or 'expense' in prompt_lower or 'financial' in prompt_lower or 'finance' in prompt_lower or 'money' in prompt_lower:
        headers = ['Category', 'Amount', 'Date', 'Description', 'Status', 'Column 6']
        sample_rows = [
            ['Housing', '1200.00', '2024-01-01', 'Monthly rent payment', 'Paid', 'Extra'],
            ['Food', '350.00', '2024-01-02', 'Groceries and dining', 'Paid', 'Extra'],
            ['Transportation', '150.00', '2024-01-03', 'Gas and maintenance', 'Paid', 'Extra'],
            ['Entertainment', '100.00', '2024-01-04', 'Movies and activities', 'Pending', 'Extra'],
            ['Utilities', '200.00', '2024-01-05', 'Electric and water', 'Due', 'Extra']
        ]
        title = f'Budget Tracker - {prompt[:30]}'
    elif 'employee' in prompt_lower or 'schedule' in prompt_lower or 'staff' in prompt_lower:
        headers = ['Employee Name', 'Shift', 'Day', 'Hours', 'Role']
        sample_rows = [
            ['John Smith', 'Morning', 'Monday', '8', 'Server'],
            ['Jane Doe', 'Evening', 'Monday', '6', 'Cook'],
            ['Mike Johnson', 'Afternoon', 'Tuesday', '8', 'Manager'],
            ['Sarah Wilson', 'Morning', 'Wednesday', '8', 'Server'],
            ['Tom Brown', 'Evening', 'Thursday', '6', 'Cook']
        ]
        title = 'Employee Schedule'
    elif 'inventory' in prompt_lower or 'stock' in prompt_lower or 'product' in prompt_lower:
        headers = ['Product Name', 'SKU', 'Stock Level', 'Reorder Point', 'Supplier']
        sample_rows = [
            ['Widget A', 'WID001', '150', '50', 'Supplier Inc'],
            ['Widget B', 'WID002', '75', '25', 'Parts Co'],
            ['Gadget X', 'GAD001', '200', '100', 'Tech Supply'],
            ['Tool Y', 'TOL001', '30', '20', 'Tool Corp'],
            ['Component Z', 'COM001', '500', '200', 'Component Ltd']
        ]
        title = 'Inventory Management'
    else:
        # Generic fallback - create more meaningful content based on prompt
        import hashlib
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()[:8]

        headers = [f'Item', 'Value', 'Category', 'Date', 'Status', 'Notes'][:columns]
        sample_rows = []

        # Generate unique sample data based on prompt
        for i in range(min(rows, 15)):
            row = [
                f'{prompt[:20]} Item {i+1}',
                f'{100 + i * 10}',
                f'Category {(i % 3) + 1}',
                f'2024-{(i % 12) + 1:02d}-{(i % 28) + 1:02d}',
                ['Active', 'Pending', 'Complete'][i % 3],
                f'Note for {prompt_hash}-{i+1}'
            ][:columns]
            sample_rows.append(row)

        title = f'Generated Sheet: {prompt[:40]}'

    # Ensure we have the right number of columns
    if len(headers) > columns:
        headers = headers[:columns]
        sample_rows = [row[:columns] for row in sample_rows]
    elif len(headers) < columns:
        headers.extend([f'Column {i+1}' for i in range(len(headers), columns)])
        for row in sample_rows:
            row.extend([''] * (columns - len(row)))

    # Ensure we have the right number of rows
    if len(sample_rows) > rows:
        sample_rows = sample_rows[:rows]
    elif len(sample_rows) < rows:
        for i in range(len(sample_rows), rows):
            sample_rows.append([f'Row {i+1} Col {j+1}' for j in range(columns)])

    fallback_data = {
        'title': title,
        'description': f'AI-generated spreadsheet for: {prompt}',
        'headers': headers,
        'rows': sample_rows,
        'reasoning': 'Generated contextual sample data based on your request'
    }

    logger.info(f"✅ Created fallback data: {title} with {len(headers)} columns and {len(sample_rows)} rows")

    # Return just the data structure, not a jsonify response
    return fallback_data

@ai_sheets_bp.route('/export-csv', methods=['POST'])
def export_csv():
    """Export spreadsheet data as CSV"""
    try:
        data = request.get_json()
        sheet_data = data.get('data', {})
        
        if not sheet_data or 'headers' not in sheet_data or 'rows' not in sheet_data:
            return jsonify({'error': 'Invalid sheet data'}), 400
        
        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write headers
        writer.writerow(sheet_data['headers'])
        
        # Write rows
        for row in sheet_data['rows']:
            writer.writerow(row)
        
        csv_content = output.getvalue()
        output.close()
        
        return jsonify({
            'success': True,
            'csv_content': csv_content,
            'filename': f"ai_sheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        })
        
    except Exception as e:
        logger.error(f"Error in export_csv: {e}")
        return jsonify({'error': 'Export failed'}), 500

@ai_sheets_bp.route('/export-excel', methods=['POST'])
def export_excel():
    """Export spreadsheet data as Excel (returns base64 encoded data)"""
    try:
        data = request.get_json()
        sheet_data = data.get('data', {})

        if not sheet_data or 'headers' not in sheet_data or 'rows' not in sheet_data:
            return jsonify({'error': 'Invalid sheet data'}), 400

        try:
            # Create DataFrame
            df = pd.DataFrame(sheet_data['rows'], columns=sheet_data['headers'])

            # Create Excel file in memory
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='AI Sheet', index=False)

                # Get the workbook and worksheet to add formatting
                workbook = writer.book
                worksheet = writer.sheets['AI Sheet']

                # Add some basic formatting
                from openpyxl.styles import Font, PatternFill, Alignment

                # Header formatting
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

                for col_num, header in enumerate(sheet_data['headers'], 1):
                    cell = worksheet.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal="center")

                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            excel_data = output.getvalue()
            output.close()

            # Convert to base64 for JSON response
            import base64
            excel_b64 = base64.b64encode(excel_data).decode('utf-8')

            return jsonify({
                'success': True,
                'excel_data': excel_b64,
                'filename': f"ai_sheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            })

        except ImportError:
            # Fallback if pandas/openpyxl not available
            logger.warning("Pandas/openpyxl not available, using CSV fallback for Excel export")
            return export_csv()

    except Exception as e:
        logger.error(f"Error in export_excel: {e}")
        return jsonify({'error': 'Excel export failed'}), 500

@ai_sheets_bp.route('/export-json', methods=['POST'])
def export_json():
    """Export spreadsheet data as JSON"""
    try:
        data = request.get_json()
        sheet_data = data.get('data', {})

        if not sheet_data or 'headers' not in sheet_data or 'rows' not in sheet_data:
            return jsonify({'error': 'Invalid sheet data'}), 400

        # Convert to JSON format
        json_data = {
            'title': sheet_data.get('title', 'AI Generated Sheet'),
            'description': sheet_data.get('description', ''),
            'created': datetime.now().isoformat(),
            'headers': sheet_data['headers'],
            'data': []
        }

        # Convert rows to objects
        for row in sheet_data['rows']:
            row_obj = {}
            for i, header in enumerate(sheet_data['headers']):
                row_obj[header] = row[i] if i < len(row) else ''
            json_data['data'].append(row_obj)

        json_content = json.dumps(json_data, indent=2)

        return jsonify({
            'success': True,
            'json_content': json_content,
            'filename': f"ai_sheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        })

    except Exception as e:
        logger.error(f"Error in export_json: {e}")
        return jsonify({'error': 'JSON export failed'}), 500

@ai_sheets_bp.route('/update-cell', methods=['POST'])
def update_cell():
    """Update a specific cell in the spreadsheet"""
    try:
        data = request.get_json()
        row = data.get('row')
        col = data.get('col')
        value = data.get('value', '')
        
        if row is None or col is None:
            return jsonify({'error': 'Row and column indices required'}), 400
        
        # Store the update in session or database
        # For now, just return success
        return jsonify({
            'success': True,
            'message': f'Cell ({row}, {col}) updated to: {value}'
        })
        
    except Exception as e:
        logger.error(f"Error in update_cell: {e}")
        return jsonify({'error': 'Cell update failed'}), 500

@ai_sheets_bp.route('/follow-up', methods=['POST'])
def follow_up():
    """Handle follow-up prompts for existing spreadsheet"""
    try:
        data = request.get_json()
        prompt = data.get('prompt', '')

        if not prompt:
            return jsonify({'error': 'Prompt is required'}), 400

        # Mark as follow-up and use existing sheet dimensions
        current_sheet_data = session.get(AI_SHEETS_DATA_KEY, None)
        if current_sheet_data:
            rows = len(current_sheet_data.get('rows', [])) + 5  # Allow for expansion
            columns = len(current_sheet_data.get('headers', [])) + 2  # Allow for new columns
        else:
            rows = 10
            columns = 5

        # Use the generate_sheet function with follow_up flag
        follow_up_data = {
            'prompt': prompt,
            'is_follow_up': True,
            'rows': rows,
            'columns': columns
        }

        # Temporarily store the follow-up data in request
        request.json = follow_up_data

        # Call generate_sheet function
        return generate_sheet()

    except Exception as e:
        logger.error(f"Error in follow_up: {e}")
        return jsonify({'error': 'Follow-up failed'}), 500

@ai_sheets_bp.route('/get-conversation', methods=['GET'])
def get_conversation():
    """Get the current conversation history"""
    try:
        conversation_history = session.get(AI_SHEETS_SESSION_KEY, [])
        current_sheet_data = session.get(AI_SHEETS_DATA_KEY, None)

        return jsonify({
            'success': True,
            'conversation': conversation_history,
            'current_sheet': current_sheet_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in get_conversation: {e}")
        return jsonify({'error': 'Failed to get conversation'}), 500

@ai_sheets_bp.route('/new-session', methods=['POST'])
def new_session():
    """Start a new AI Sheets session"""
    try:
        # Clear any session data related to AI Sheets
        session_keys_to_clear = [AI_SHEETS_SESSION_KEY, AI_SHEETS_DATA_KEY]
        for key in session_keys_to_clear:
            session.pop(key, None)

        return jsonify({
            'success': True,
            'message': 'New session started',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error in new_session: {e}")
        return jsonify({'error': 'Failed to start new session'}), 500
