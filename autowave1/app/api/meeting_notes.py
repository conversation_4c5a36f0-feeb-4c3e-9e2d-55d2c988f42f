"""
Meeting Notes API Blueprint
Handles voice recording transcription and meeting notes functionality
"""

from flask import Blueprint, request, jsonify
import logging
from app.decorators.free_user_access import require_subscription_for_page

# Set up logging
logger = logging.getLogger(__name__)

# Create blueprint
meeting_notes_bp = Blueprint('meeting_notes', __name__)

@meeting_notes_bp.route('/transcribe', methods=['POST'])
@require_subscription_for_page('Meeting Notes')
def transcribe_audio():
    """Transcribe audio using OpenAI Whisper"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        audio_data = data.get('audio_data')
        audio_format = data.get('format', 'webm')
        
        if not audio_data:
            return jsonify({'success': False, 'error': 'Audio data is required'}), 400
        
        # Transcribe the audio using AI
        from app.services.meeting_notes_service import MeetingNotesService
        meeting_notes_service = MeetingNotesService()
        
        result = meeting_notes_service.transcribe_audio(
            audio_data=audio_data,
            audio_format=audio_format
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in transcribe_audio: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@meeting_notes_bp.route('/save-notes', methods=['POST'])
@require_subscription_for_page('Meeting Notes')
def save_notes():
    """Save meeting notes to user's account"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400
        
        notes_content = data.get('notes', '').strip()
        meeting_title = data.get('title', 'Untitled Meeting')
        
        if not notes_content:
            return jsonify({'success': False, 'error': 'Notes content is required'}), 400
        
        # Save the notes using the service
        from app.services.meeting_notes_service import MeetingNotesService
        meeting_notes_service = MeetingNotesService()
        
        result = meeting_notes_service.save_notes(
            title=meeting_title,
            content=notes_content
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in save_notes: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@meeting_notes_bp.route('/list-notes', methods=['GET'])
@require_subscription_for_page('Meeting Notes')
def list_notes():
    """List user's saved meeting notes"""
    try:
        # Get user's saved notes
        from app.services.meeting_notes_service import MeetingNotesService
        meeting_notes_service = MeetingNotesService()

        result = meeting_notes_service.list_user_notes()

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in list_notes: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@meeting_notes_bp.route('/enhance', methods=['POST'])
@require_subscription_for_page('Meeting Notes')
def enhance_transcription():
    """Enhance raw transcription with AI"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        raw_transcription = data.get('transcription', '').strip()

        if not raw_transcription:
            return jsonify({'success': False, 'error': 'Transcription content is required'}), 400

        # Enhance the transcription using AI
        from app.services.meeting_notes_service import MeetingNotesService
        meeting_notes_service = MeetingNotesService()

        result = meeting_notes_service.enhance_transcription(raw_transcription)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in enhance_transcription: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500

@meeting_notes_bp.route('/summarize', methods=['POST'])
@require_subscription_for_page('Meeting Notes')
def summarize_notes():
    """Generate summary of meeting notes"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        notes_content = data.get('notes', '').strip()

        if not notes_content:
            return jsonify({'success': False, 'error': 'Notes content is required'}), 400

        # Generate summary using AI
        from app.services.meeting_notes_service import MeetingNotesService
        meeting_notes_service = MeetingNotesService()

        result = meeting_notes_service.generate_meeting_summary(notes_content)

        return jsonify(result)

    except Exception as e:
        logger.error(f"Error in summarize_notes: {str(e)}")
        return jsonify({'success': False, 'error': 'Internal server error'}), 500
