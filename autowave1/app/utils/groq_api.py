"""
Groq API client for Agen911.

This module provides a client for the Groq API, which can be used as a fallback
when the Gemini API is unavailable or rate-limited.
"""

import os
import json
import time
import requests
from typing import Dict, Any, Optional, List

class GroqAPI:
    """Client for the Groq API."""

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Groq API client.

        Args:
            api_key (Optional[str]): The Groq API key. If not provided, it will be read from the GROQ_API_KEY environment variable.
        """
        self.api_key = api_key or os.environ.get("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
        self.model = "llama3-70b-8192"  # Default model with 8192 token context window

        # Print initialization message for debugging
        print(f"Groq API initialized with model: {self.model}")
        print(f"API key available: {bool(self.api_key)}")

    @property
    def api_key(self) -> Optional[str]:
        """Get the API key."""
        return self._api_key

    @api_key.setter
    def api_key(self, value: Optional[str]):
        """Set the API key."""
        self._api_key = value

    def generate_text(self, prompt: str, max_tokens: int = 8192, temperature: float = 0.7,
                       system_prompt: Optional[str] = None, timeout: int = 180) -> str:
        """
        Generate text using the Groq API.

        Args:
            prompt (str): The prompt to generate text from.
            max_tokens (int): The maximum number of tokens to generate. Default is 8192 for long-form content.
            temperature (float): The temperature for text generation. Default is 0.7.
            system_prompt (Optional[str]): Optional system prompt to guide generation.
            timeout (int): Request timeout in seconds. Default is 180 for longer generations.

        Returns:
            str: The generated text.
        """
        if not self.api_key:
            raise ValueError("Groq API key is not set. Please set it using the GROQ_API_KEY environment variable.")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Prepare messages with optional system prompt
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # Log the request for debugging
        print(f"Sending request to Groq API with max_tokens={max_tokens}, temperature={temperature}")

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=timeout  # Increased timeout for longer generations
            )
            response.raise_for_status()
            result = response.json()

            # Extract the generated text from the response
            if "choices" in result and len(result["choices"]) > 0:
                generated_text = result["choices"][0]["message"]["content"].strip()
                print(f"Successfully generated text with Groq API ({len(generated_text)} characters)")
                return generated_text
            else:
                print("Error: No text was generated by Groq API")
                return "Error: No text was generated."
        except requests.exceptions.RequestException as e:
            print(f"Error generating text with Groq API: {str(e)}")
            return f"Error: {str(e)}"

    def generate_structured_output(self, prompt: str, system_prompt: Optional[str] = None,
                                  max_tokens: int = 1024, temperature: float = 0.2) -> Dict[str, Any]:
        """
        Generate structured output using the Groq API.

        Args:
            prompt (str): The prompt to generate text from.
            system_prompt (Optional[str]): The system prompt to use. Default is None.
            max_tokens (int): The maximum number of tokens to generate. Default is 1024.
            temperature (float): The temperature for text generation. Default is 0.2.

        Returns:
            Dict[str, Any]: The generated structured output.
        """
        if not self.api_key:
            raise ValueError("Groq API key is not set. Please set it using the GROQ_API_KEY environment variable.")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()
            result = response.json()

            # Extract the generated text from the response
            if "choices" in result and len(result["choices"]) > 0:
                text = result["choices"][0]["message"]["content"].strip()

                # Try to parse the text as JSON
                try:
                    # Find JSON-like content in the text
                    json_start = text.find('{')
                    json_end = text.rfind('}') + 1

                    if json_start >= 0 and json_end > json_start:
                        json_text = text[json_start:json_end]
                        return json.loads(json_text)
                    else:
                        return {"text": text}
                except json.JSONDecodeError:
                    return {"text": text}
            else:
                return {"error": "No text was generated."}
        except requests.exceptions.RequestException as e:
            print(f"Error generating structured output with Groq API: {str(e)}")
            return {"error": str(e)}

    def analyze_image(self, image_url: str, prompt: str) -> str:
        """
        Analyze an image using the Groq API.

        Args:
            image_url (str): The URL of the image to analyze.
            prompt (str): The prompt to guide the image analysis.

        Returns:
            str: The analysis result.
        """
        # Note: As of my knowledge, Groq doesn't support image analysis directly.
        # This is a placeholder for future implementation if Groq adds this capability.
        return "Image analysis is not currently supported by the Groq API."

    def set_model(self, model: str) -> None:
        """
        Set the model to use for text generation.

        Args:
            model (str): The model name.
        """
        self.model = model


# Create a global instance for easy import
groq_api = GroqAPI()
