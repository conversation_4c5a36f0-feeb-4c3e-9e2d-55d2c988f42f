"""
Free User Access Control Decorator
Restricts free users to only AutoWave Chat page, blocks access to premium features
"""

import logging
from functools import wraps
from flask import request, session, render_template, redirect, url_for, make_response
from app.services.admin_service import admin_service
from app.services.subscription_service import SubscriptionService

logger = logging.getLogger(__name__)

def require_subscription_for_page(page_name: str):
    """
    Decorator that blocks free users from accessing premium pages.
    Only allows access to AutoWave Chat for free users.
    
    Args:
        page_name: Name of the page being accessed
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get user info from session
                user_id = session.get('user_id')
                user_email = session.get('user_email')
                
                # If not authenticated, redirect to login
                if not user_id or not user_email:
                    return redirect(url_for('auth.login_page'))
                
                # ADMIN BYPASS: Check if user is admin (unlimited access)
                logger.info(f"🔍 Checking admin status for {user_email} on page {page_name}")
                logger.info(f"🔍 Admin service check result: {admin_service.is_admin(user_email) if user_email else 'No email'}")
                if user_email and admin_service.is_admin(user_email):
                    logger.info(f"🔑 ADMIN BYPASS: {user_email} has unlimited access to {page_name}")
                    return f(*args, **kwargs)
                else:
                    logger.info(f"🚫 Admin check failed for {user_email} - not recognized as admin")
                
                # Check user's subscription status
                subscription_service = SubscriptionService()
                
                try:
                    # Get user access level (includes subscription expiry check)
                    access_info = subscription_service.get_user_access_level(user_id)

                    # If user has an active paid subscription, allow access
                    if access_info['plan'] in ['plus', 'pro', 'enterprise'] and not access_info.get('subscription_expired', False):
                        logger.info(f"✅ Paid user {user_id} ({access_info['plan']}) accessing {page_name}")
                        return f(*args, **kwargs)

                    # If subscription is expired, redirect to pricing
                    if access_info.get('subscription_expired', False):
                        logger.warning(f"🚫 Subscription expired for user {user_id} accessing {page_name}")
                        return render_template('subscription_required.html',
                                             page_name=page_name,
                                             message="Your subscription has expired. Please renew to continue using premium features.",
                                             current_plan=access_info['plan'],
                                             subscription_expired=True)

                except Exception as e:
                    logger.warning(f"Error checking subscription for {user_id}: {e}")
                    # Continue with free user logic if subscription check fails
                
                # Free user - block access to premium pages
                logger.info(f"🚫 Free user {user_id} blocked from accessing {page_name}")
                
                # Render subscription required page with cache-busting headers
                response = make_response(render_template('subscription_required.html', 
                                                       page_name=page_name,
                                                       user_email=user_email))
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
                return response
                
            except Exception as e:
                logger.error(f"Error in free user access control for {page_name}: {e}")
                # On error, show subscription required page for safety with cache-busting headers
                response = make_response(render_template('subscription_required.html', 
                                                       page_name=page_name,
                                                       user_email=session.get('user_email', 'Unknown')))
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
                return response
        
        return decorated_function
    return decorator

def is_free_user(user_id: str) -> bool:
    """
    Check if a user is on the free plan
    
    Args:
        user_id: User ID to check
        
    Returns:
        True if user is on free plan, False if paid plan
    """
    try:
        subscription_service = SubscriptionService()
        subscription = subscription_service.get_user_subscription(user_id)
        
        # If no subscription or free plan, return True
        if not subscription or subscription.get('plan') == 'free':
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error checking if user {user_id} is free: {e}")
        # On error, assume free user for safety
        return True
