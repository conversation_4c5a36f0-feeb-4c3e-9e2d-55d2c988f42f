"""
Trial Limit Decorator for AutoWave
Enforces one-time trial limits per page for free users
"""

import logging
from functools import wraps
from flask import request, jsonify, session
from app.services.trial_service import trial_service
from app.services.subscription_service import subscription_service
from app.services.admin_service import admin_service

logger = logging.getLogger(__name__)

def trial_required(page_name: str):
    """
    Decorator to enforce trial limits for specific pages
    
    Args:
        page_name: Name of the page (research_lab, agent_wave, etc.)
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get user ID from session
                user_id = session.get('user_id')
                user_email = session.get('user_email')

                if not user_id:
                    logger.warning(f"No user_id in session for trial check on {page_name}")
                    return jsonify({
                        'success': False,
                        'error': 'Authentication required',
                        'trial_exhausted': False
                    }), 401

                # ADMIN BYPASS: Check if user is admin (unlimited access)
                if user_email and admin_service.is_admin(user_email):
                    logger.info(f"🔑 ADMIN BYPASS: {user_email} has unlimited access to {page_name}")
                    return f(*args, **kwargs)

                # Check if user has a paid subscription
                try:
                    subscription_info = subscription_service.get_user_subscription(user_id)
                    if subscription_info and subscription_info.get('plan') != 'free':
                        logger.info(f"✅ Paid user {user_id} bypassing trial limit for {page_name}")
                        return f(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"Error checking subscription for {user_id}: {e}")
                    # Continue with trial check if subscription check fails
                
                # Check trial limit for free users
                can_use, used_count, limit = trial_service.check_trial_limit(user_id, page_name)
                
                if not can_use:
                    logger.warning(f"🚫 Trial exhausted for {user_id} on {page_name}: {used_count}/{limit}")
                    return jsonify({
                        'success': False,
                        'error': 'Free trial exhausted, subscribe to have more access.',
                        'trial_exhausted': True,
                        'trial_info': {
                            'page': page_name,
                            'used': used_count,
                            'limit': limit,
                            'remaining': 0
                        }
                    }), 403
                
                # Consume trial usage
                success, new_used_count, limit = trial_service.consume_trial(user_id, page_name)
                
                if not success:
                    logger.error(f"Failed to consume trial for {user_id} on {page_name}")
                    return jsonify({
                        'success': False,
                        'error': 'Free trial exhausted, subscribe to have more access.',
                        'trial_exhausted': True,
                        'trial_info': {
                            'page': page_name,
                            'used': used_count,
                            'limit': limit,
                            'remaining': 0
                        }
                    }), 403
                
                logger.info(f"✅ Trial consumed for {user_id} on {page_name}: {new_used_count}/{limit}")
                
                # Add trial info to request context for the endpoint to use
                request.trial_info = {
                    'page': page_name,
                    'used': new_used_count,
                    'limit': limit,
                    'remaining': max(0, limit - new_used_count)
                }
                
                # Execute the original function
                return f(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"Error in trial_required decorator for {page_name}: {e}")
                # Allow the request to proceed if there's an error in trial checking
                return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def get_trial_status_for_user(user_id: str) -> dict:
    """
    Get comprehensive trial status for a user
    
    Args:
        user_id: User identifier
        
    Returns:
        Dict with trial status information
    """
    try:
        # Check if user has a paid subscription
        try:
            subscription_info = subscription_service.get_user_subscription(user_id)
            if subscription_info and subscription_info.get('plan') != 'free':
                return {
                    'is_paid_user': True,
                    'plan': subscription_info.get('plan'),
                    'trial_status': 'unlimited'
                }
        except Exception as e:
            logger.warning(f"Error checking subscription for {user_id}: {e}")
        
        # Get trial status for free user
        trial_status = trial_service.get_trial_status(user_id)
        
        return {
            'is_paid_user': False,
            'plan': 'free',
            'trial_status': trial_status
        }
        
    except Exception as e:
        logger.error(f"Error getting trial status for {user_id}: {e}")
        return {
            'is_paid_user': False,
            'plan': 'free',
            'trial_status': {},
            'error': str(e)
        }
