"""
Professional PowerPoint-Style Infographics Generator
Creates rich, animated presentations with graphs, charts, pictures, and cartoons like Genspark
"""

import json
import base64
import io
import logging
import os
import tempfile
import uuid
import requests
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import colorsys
import random
import re

# Core dependencies
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.patches import FancyBboxPatch, Circle, Rectangle, Polygon
    import numpy as np
    import pandas as pd
    from matplotlib.animation import FuncAnimation
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

# Advanced visualization
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

# Image processing
try:
    from PIL import Image as PILImage, ImageDraw, ImageFont, Image<PERSON>ilter, ImageEnhance
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# HTML/CSS generation
try:
    from jinja2 import Template
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

logger = logging.getLogger(__name__)

class PowerPointInfographics:
    """Professional PowerPoint-style infographics generator with rich animations and visuals"""
    
    def __init__(self, gemini_api=None):
        self.gemini_api = gemini_api
        self.logger = logger
        
        # Professional color schemes inspired by PowerPoint and Genspark
        self.color_schemes = {
            'corporate_blue': ['#1f4e79', '#2e75b6', '#5b9bd5', '#8db4e2', '#bdd7ee'],
            'modern_green': ['#2d5016', '#4f7942', '#70ad47', '#a9d18e', '#c5e0b4'],
            'elegant_purple': ['#4a148c', '#7b1fa2', '#9c27b0', '#ba68c8', '#e1bee7'],
            'vibrant_orange': ['#d84315', '#f57c00', '#ff9800', '#ffb74d', '#ffe0b2'],
            'professional_gray': ['#263238', '#455a64', '#607d8b', '#90a4ae', '#cfd8dc'],
            'creative_teal': ['#004d40', '#00695c', '#00897b', '#4db6ac', '#b2dfdb'],
            'dynamic_red': ['#b71c1c', '#d32f2f', '#f44336', '#ef5350', '#ffcdd2'],
            'tech_navy': ['#0d47a1', '#1565c0', '#1976d2', '#42a5f5', '#bbdefb']
        }
        
        # Professional templates
        self.templates = {
            'executive_summary': {
                'layout': 'hero_stats_content',
                'sections': ['hero', 'key_metrics', 'insights', 'recommendations'],
                'style': 'corporate'
            },
            'business_presentation': {
                'layout': 'title_content_visual',
                'sections': ['title', 'overview', 'data_visualization', 'conclusion'],
                'style': 'professional'
            },
            'data_story': {
                'layout': 'narrative_flow',
                'sections': ['introduction', 'data_exploration', 'key_findings', 'implications'],
                'style': 'analytical'
            },
            'product_showcase': {
                'layout': 'feature_grid',
                'sections': ['hero', 'features', 'benefits', 'call_to_action'],
                'style': 'modern'
            },
            'research_report': {
                'layout': 'academic_flow',
                'sections': ['abstract', 'methodology', 'results', 'discussion'],
                'style': 'academic'
            },
            'marketing_deck': {
                'layout': 'visual_impact',
                'sections': ['hook', 'problem', 'solution', 'proof'],
                'style': 'creative'
            }
        }
        
        # Animation types
        self.animations = {
            'fade_in': 'opacity: 0; animation: fadeIn 1s ease-in forwards;',
            'slide_up': 'transform: translateY(50px); opacity: 0; animation: slideUp 1s ease-out forwards;',
            'scale_in': 'transform: scale(0.8); opacity: 0; animation: scaleIn 1s ease-out forwards;',
            'bounce_in': 'transform: scale(0.3); opacity: 0; animation: bounceIn 1s ease-out forwards;',
            'rotate_in': 'transform: rotate(-180deg); opacity: 0; animation: rotateIn 1s ease-out forwards;',
            'flip_in': 'transform: rotateY(-90deg); opacity: 0; animation: flipIn 1s ease-out forwards;'
        }

    def create_professional_presentation(self, content: str, presentation_type: str = "business_presentation",
                                       title: str = "Professional Presentation",
                                       color_scheme: str = "corporate_blue",
                                       include_animations: bool = True,
                                       include_charts: bool = True,
                                       include_icons: bool = True) -> Dict[str, Any]:
        """
        Create a professional PowerPoint-style presentation with rich visuals and animations
        
        Args:
            content: Text content to transform into presentation
            presentation_type: Type of presentation template
            title: Presentation title
            color_scheme: Color scheme to use
            include_animations: Whether to include CSS animations
            include_charts: Whether to generate interactive charts
            include_icons: Whether to include professional icons
            
        Returns:
            Dict containing HTML presentation, charts, and metadata
        """
        try:
            self.logger.info(f"Creating professional presentation: {title}")
            
            # Analyze content with AI to extract structure
            structured_content = self._analyze_content_with_ai(content, presentation_type)
            
            # Generate interactive charts if requested
            charts = []
            if include_charts and structured_content.get('data_points'):
                charts = self._generate_interactive_charts(structured_content['data_points'], color_scheme)
            
            # Generate professional icons
            icons = []
            if include_icons:
                icons = self._generate_professional_icons(structured_content.get('sections', []))
            
            # Create the presentation HTML
            presentation_html = self._generate_presentation_html(
                structured_content, 
                presentation_type, 
                title, 
                color_scheme,
                include_animations,
                charts,
                icons
            )
            
            # Generate slide thumbnails
            thumbnails = self._generate_slide_thumbnails(structured_content, color_scheme)
            
            return {
                "success": True,
                "title": title,
                "type": presentation_type,
                "html": presentation_html,
                "charts": charts,
                "icons": icons,
                "thumbnails": thumbnails,
                "slides_count": len(structured_content.get('sections', [])),
                "color_scheme": color_scheme,
                "has_animations": include_animations,
                "summary": structured_content.get('summary', ''),
                "sections": structured_content.get('sections', [])
            }
            
        except Exception as e:
            self.logger.error(f"Error creating presentation: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to create presentation: {str(e)}"
            }

    def _analyze_content_with_ai(self, content: str, presentation_type: str) -> Dict[str, Any]:
        """Use AI to analyze content and extract structured information"""
        try:
            prompt = f"""
            Analyze the following content and structure it for a professional {presentation_type} presentation:

            Content: {content}

            Please provide a JSON response with:
            1. title: A compelling presentation title
            2. summary: Executive summary (2-3 sentences)
            3. sections: Array of sections with:
               - title: Section title
               - content: Main content points
               - visual_type: Suggested visual (chart, icon, image, etc.)
               - key_points: Array of bullet points
               - data_points: Any numerical data for charts
            4. key_metrics: Important numbers or KPIs
            5. visual_elements: Suggested visual elements and their types
            6. tone: Professional tone (formal, casual, technical, creative)
            7. target_audience: Intended audience

            Make it professional, engaging, and suitable for a PowerPoint-style presentation with rich visuals.
            Focus on creating compelling slides that tell a story with data and visuals.
            """

            if self.gemini_api:
                response = self.gemini_api.generate_text(prompt)
                
                # Try to parse JSON response
                try:
                    import re
                    json_match = re.search(r'\{.*\}', response, re.DOTALL)
                    if json_match:
                        return json.loads(json_match.group())
                except:
                    pass
            
            # Fallback structure
            return self._create_fallback_structure(content, presentation_type)
            
        except Exception as e:
            self.logger.warning(f"AI analysis failed, using fallback: {str(e)}")
            return self._create_fallback_structure(content, presentation_type)

    def _create_fallback_structure(self, content: str, presentation_type: str) -> Dict[str, Any]:
        """Create a fallback structure when AI analysis fails"""
        sections = []
        
        # Split content into logical sections
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for i, paragraph in enumerate(paragraphs[:6]):  # Limit to 6 slides
            section = {
                'title': f"Section {i+1}",
                'content': paragraph,
                'visual_type': 'icon' if i % 2 == 0 else 'chart',
                'key_points': [point.strip() for point in paragraph.split('.') if point.strip()][:4],
                'data_points': []
            }
            sections.append(section)
        
        return {
            'title': 'Professional Presentation',
            'summary': content[:200] + '...' if len(content) > 200 else content,
            'sections': sections,
            'key_metrics': [],
            'visual_elements': ['charts', 'icons', 'animations'],
            'tone': 'professional',
            'target_audience': 'business professionals'
        }

    def _generate_interactive_charts(self, data_points: List[Dict], color_scheme: str) -> List[Dict]:
        """Generate interactive charts using Plotly"""
        charts = []
        colors = self.color_schemes.get(color_scheme, self.color_schemes['corporate_blue'])

        try:
            if not PLOTLY_AVAILABLE:
                return []

            for i, data_point in enumerate(data_points[:4]):  # Limit to 4 charts
                chart_type = data_point.get('type', 'bar')
                chart_data = data_point.get('data', {})

                if chart_type == 'bar':
                    fig = go.Figure(data=[
                        go.Bar(
                            x=chart_data.get('labels', ['A', 'B', 'C', 'D']),
                            y=chart_data.get('values', [20, 35, 30, 25]),
                            marker_color=colors[i % len(colors)],
                            text=chart_data.get('values', [20, 35, 30, 25]),
                            textposition='auto',
                        )
                    ])
                elif chart_type == 'pie':
                    fig = go.Figure(data=[
                        go.Pie(
                            labels=chart_data.get('labels', ['A', 'B', 'C', 'D']),
                            values=chart_data.get('values', [20, 35, 30, 25]),
                            marker_colors=colors[:len(chart_data.get('labels', ['A', 'B', 'C', 'D']))],
                            hole=0.3,
                            textinfo='label+percent',
                            textposition='auto'
                        )
                    ])
                elif chart_type == 'line':
                    fig = go.Figure(data=[
                        go.Scatter(
                            x=chart_data.get('x', list(range(len(chart_data.get('y', [1, 3, 2, 4, 3]))))),
                            y=chart_data.get('y', [1, 3, 2, 4, 3]),
                            mode='lines+markers',
                            line=dict(color=colors[0], width=3),
                            marker=dict(size=8, color=colors[1])
                        )
                    ])
                else:
                    continue

                # Style the chart
                fig.update_layout(
                    title=dict(
                        text=data_point.get('title', f'Chart {i+1}'),
                        font=dict(size=18, color=colors[0]),
                        x=0.5
                    ),
                    plot_bgcolor='rgba(0,0,0,0)',
                    paper_bgcolor='rgba(0,0,0,0)',
                    font=dict(family="Arial, sans-serif", size=12, color=colors[0]),
                    showlegend=chart_type == 'pie',
                    margin=dict(l=40, r=40, t=60, b=40),
                    height=400
                )

                # Convert to HTML
                chart_html = pio.to_html(fig, include_plotlyjs='cdn', div_id=f'chart_{i}')

                charts.append({
                    'id': f'chart_{i}',
                    'type': chart_type,
                    'title': data_point.get('title', f'Chart {i+1}'),
                    'html': chart_html,
                    'data': chart_data
                })

        except Exception as e:
            self.logger.warning(f"Error generating charts: {str(e)}")

        return charts

    def _generate_professional_icons(self, sections: List[Dict]) -> List[Dict]:
        """Generate professional icons for sections"""
        icons = []

        # Icon mapping based on content keywords
        icon_mapping = {
            'business': '💼', 'strategy': '🎯', 'growth': '📈', 'analysis': '📊',
            'team': '👥', 'leadership': '👑', 'innovation': '💡', 'technology': '⚙️',
            'finance': '💰', 'marketing': '📢', 'sales': '🤝', 'customer': '👤',
            'product': '📦', 'service': '🔧', 'quality': '⭐', 'performance': '🏆',
            'data': '📊', 'research': '🔬', 'development': '🚀', 'future': '🔮'
        }

        for i, section in enumerate(sections):
            content_lower = section.get('content', '').lower()
            title_lower = section.get('title', '').lower()

            # Find matching icon
            icon = '📋'  # Default icon
            for keyword, emoji in icon_mapping.items():
                if keyword in content_lower or keyword in title_lower:
                    icon = emoji
                    break

            icons.append({
                'id': f'icon_{i}',
                'emoji': icon,
                'section': section.get('title', f'Section {i+1}'),
                'color': self.color_schemes['corporate_blue'][i % len(self.color_schemes['corporate_blue'])]
            })

        return icons

    def _generate_slide_thumbnails(self, structured_content: Dict, color_scheme: str) -> List[str]:
        """Generate thumbnail images for slides"""
        thumbnails = []
        colors = self.color_schemes.get(color_scheme, self.color_schemes['corporate_blue'])

        try:
            if not MATPLOTLIB_AVAILABLE:
                return []

            for i, section in enumerate(structured_content.get('sections', [])):
                fig, ax = plt.subplots(figsize=(4, 3))
                fig.patch.set_facecolor('white')

                # Create simple thumbnail representation
                ax.add_patch(Rectangle((0.1, 0.7), 0.8, 0.2, facecolor=colors[0], alpha=0.8))
                ax.text(0.5, 0.8, section.get('title', f'Slide {i+1}')[:20],
                       ha='center', va='center', fontsize=10, color='white', weight='bold')

                # Add content representation
                ax.add_patch(Rectangle((0.1, 0.1), 0.8, 0.5, facecolor=colors[1], alpha=0.3))
                ax.text(0.5, 0.35, '■ Content Preview\n■ Key Points\n■ Visual Elements',
                       ha='center', va='center', fontsize=8, color=colors[0])

                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')

                # Convert to base64
                buffer = io.BytesIO()
                plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                buffer.seek(0)
                thumbnail_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                buffer.close()
                plt.close()

                thumbnails.append(thumbnail_base64)

        except Exception as e:
            self.logger.warning(f"Error generating thumbnails: {str(e)}")

        return thumbnails

    def _generate_presentation_html(self, structured_content: Dict, presentation_type: str,
                                  title: str, color_scheme: str, include_animations: bool,
                                  charts: List[Dict], icons: List[Dict]) -> str:
        """Generate the main presentation HTML with PowerPoint-style design"""

        colors = self.color_schemes.get(color_scheme, self.color_schemes['corporate_blue'])
        template = self.templates.get(presentation_type, self.templates['business_presentation'])

        # CSS animations
        animations_css = self._generate_animations_css() if include_animations else ""

        # Generate slides HTML
        slides_html = ""
        sections = structured_content.get('sections', [])

        for i, section in enumerate(sections):
            slide_html = self._generate_slide_html(section, i, colors, include_animations, charts, icons)
            slides_html += slide_html

        # Main HTML template
        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        {self._generate_base_css(colors)}
        {animations_css}
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Navigation -->
        <div class="presentation-nav">
            <div class="nav-title">{title}</div>
            <div class="nav-controls">
                <button onclick="previousSlide()" class="nav-btn">‹</button>
                <span class="slide-counter">1 / {len(sections)}</span>
                <button onclick="nextSlide()" class="nav-btn">›</button>
            </div>
        </div>

        <!-- Slides Container -->
        <div class="slides-container" id="slidesContainer">
            {slides_html}
        </div>

        <!-- Slide Thumbnails -->
        <div class="thumbnails-container">
            {self._generate_thumbnails_html(sections, colors)}
        </div>
    </div>

    <script>
        {self._generate_presentation_js(len(sections))}
    </script>
</body>
</html>
        """

        return html_template

    def _generate_slide_html(self, section: Dict, index: int, colors: List[str],
                           include_animations: bool, charts: List[Dict], icons: List[Dict]) -> str:
        """Generate HTML for a single slide"""

        animation_class = f"slide-animation-{index % 6}" if include_animations else ""

        # Get matching chart and icon
        chart_html = ""
        if index < len(charts):
            chart_html = charts[index]['html']

        icon_emoji = "📋"
        if index < len(icons):
            icon_emoji = icons[index]['emoji']

        slide_html = f"""
        <div class="slide {animation_class}" data-slide="{index}">
            <div class="slide-content">
                <!-- Slide Header -->
                <div class="slide-header">
                    <div class="slide-icon">{icon_emoji}</div>
                    <h2 class="slide-title">{section.get('title', f'Slide {index + 1}')}</h2>
                </div>

                <!-- Main Content Area -->
                <div class="slide-body">
                    <div class="content-left">
                        <div class="content-text">
                            {self._format_content_text(section.get('content', ''))}
                        </div>

                        {self._generate_key_points_html(section.get('key_points', []), colors)}
                    </div>

                    <div class="content-right">
                        {chart_html if chart_html else self._generate_placeholder_visual(colors, index)}
                    </div>
                </div>

                <!-- Slide Footer -->
                <div class="slide-footer">
                    <div class="slide-number">Slide {index + 1}</div>
                    <div class="slide-progress">
                        <div class="progress-bar" style="width: {((index + 1) / max(len(section.get('key_points', [])), 1)) * 100}%; background: {colors[0]};"></div>
                    </div>
                </div>
            </div>
        </div>
        """

        return slide_html

    def _format_content_text(self, content: str) -> str:
        """Format content text with proper HTML styling"""
        if not content:
            return ""

        # Split into paragraphs and format
        paragraphs = [p.strip() for p in content.split('\n') if p.strip()]
        formatted_paragraphs = []

        for paragraph in paragraphs[:3]:  # Limit to 3 paragraphs per slide
            # Bold important words
            paragraph = re.sub(r'\b(important|key|critical|essential|significant)\b',
                             r'<strong>\1</strong>', paragraph, flags=re.IGNORECASE)
            formatted_paragraphs.append(f'<p class="content-paragraph">{paragraph}</p>')

        return '\n'.join(formatted_paragraphs)

    def _generate_key_points_html(self, key_points: List[str], colors: List[str]) -> str:
        """Generate HTML for key points with professional styling"""
        if not key_points:
            return ""

        points_html = ""
        for i, point in enumerate(key_points[:4]):  # Limit to 4 points
            color = colors[i % len(colors)]
            points_html += f"""
            <div class="key-point" style="border-left: 4px solid {color};">
                <div class="point-marker" style="background: {color};"></div>
                <span class="point-text">{point}</span>
            </div>
            """

        return f'<div class="key-points-container">{points_html}</div>'

    def _generate_placeholder_visual(self, colors: List[str], index: int) -> str:
        """Generate a placeholder visual when no chart is available"""
        visual_types = ['chart', 'diagram', 'infographic', 'illustration']
        visual_type = visual_types[index % len(visual_types)]

        return f"""
        <div class="placeholder-visual" style="background: linear-gradient(135deg, {colors[0]}20, {colors[1]}20);">
            <div class="visual-icon" style="color: {colors[0]};">
                {'📊' if visual_type == 'chart' else '🎨' if visual_type == 'diagram' else '📈' if visual_type == 'infographic' else '🖼️'}
            </div>
            <div class="visual-label" style="color: {colors[0]};">{visual_type.title()}</div>
        </div>
        """

    def _generate_base_css(self, colors: List[str]) -> str:
        """Generate base CSS for the presentation"""
        return f"""
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }}

        .presentation-container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }}

        .presentation-nav {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 15px 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }}

        .nav-title {{
            font-size: 24px;
            font-weight: 700;
            color: {colors[0]};
            background: linear-gradient(135deg, {colors[0]}, {colors[1]});
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }}

        .nav-controls {{
            display: flex;
            align-items: center;
            gap: 15px;
        }}

        .nav-btn {{
            background: {colors[0]};
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }}

        .nav-btn:hover {{
            background: {colors[1]};
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}

        .slide-counter {{
            font-weight: 600;
            color: {colors[0]};
            font-size: 16px;
        }}

        .slides-container {{
            position: relative;
            height: 600px;
            overflow: hidden;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }}

        .slide {{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 20px;
            overflow: hidden;
        }}

        .slide.active {{
            opacity: 1;
            transform: translateX(0);
        }}

        .slide-content {{
            padding: 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }}

        .slide-header {{
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid {colors[0]};
        }}

        .slide-icon {{
            font-size: 32px;
            margin-right: 15px;
            background: {colors[0]};
            padding: 10px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }}

        .slide-title {{
            font-size: 28px;
            font-weight: 700;
            color: {colors[0]};
            margin: 0;
        }}

        .slide-body {{
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
        }}

        .content-left {{
            display: flex;
            flex-direction: column;
            gap: 20px;
        }}

        .content-text {{
            line-height: 1.6;
            color: #333;
        }}

        .content-paragraph {{
            margin-bottom: 15px;
            font-size: 16px;
            text-align: justify;
        }}

        .key-points-container {{
            display: flex;
            flex-direction: column;
            gap: 12px;
        }}

        .key-point {{
            display: flex;
            align-items: center;
            padding: 12px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            position: relative;
            padding-left: 25px;
        }}

        .point-marker {{
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }}

        .point-text {{
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }}

        .content-right {{
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
        }}

        .placeholder-visual {{
            width: 100%;
            height: 300px;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 2px dashed {colors[1]};
        }}

        .visual-icon {{
            font-size: 48px;
            margin-bottom: 10px;
        }}

        .visual-label {{
            font-size: 18px;
            font-weight: 600;
        }}

        .slide-footer {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }}

        .slide-number {{
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }}

        .slide-progress {{
            width: 200px;
            height: 4px;
            background: #eee;
            border-radius: 2px;
            overflow: hidden;
        }}

        .progress-bar {{
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 2px;
        }}

        .thumbnails-container {{
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow-x: auto;
        }}

        .thumbnail {{
            width: 80px;
            height: 60px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            flex-shrink: 0;
        }}

        .thumbnail:hover {{
            transform: scale(1.05);
            border-color: {colors[0]};
        }}

        .thumbnail.active {{
            border-color: {colors[0]};
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}

        @media (max-width: 768px) {{
            .slide-body {{
                grid-template-columns: 1fr;
                gap: 20px;
            }}

            .slide-content {{
                padding: 20px;
            }}

            .slide-title {{
                font-size: 24px;
            }}

            .nav-title {{
                font-size: 20px;
            }}
        }}
        """

    def _generate_animations_css(self) -> str:
        """Generate CSS animations for PowerPoint-style effects"""
        return """
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes rotateIn {
            from {
                opacity: 0;
                transform: rotate(-180deg);
            }
            to {
                opacity: 1;
                transform: rotate(0deg);
            }
        }

        @keyframes flipIn {
            from {
                opacity: 0;
                transform: rotateY(-90deg);
            }
            to {
                opacity: 1;
                transform: rotateY(0deg);
            }
        }

        .slide-animation-0 .slide-header { animation: fadeIn 1s ease-in 0.2s both; }
        .slide-animation-0 .content-left { animation: slideUp 1s ease-out 0.4s both; }
        .slide-animation-0 .content-right { animation: scaleIn 1s ease-out 0.6s both; }

        .slide-animation-1 .slide-header { animation: slideUp 1s ease-out 0.2s both; }
        .slide-animation-1 .content-left { animation: bounceIn 1s ease-out 0.4s both; }
        .slide-animation-1 .content-right { animation: fadeIn 1s ease-in 0.6s both; }

        .slide-animation-2 .slide-header { animation: scaleIn 1s ease-out 0.2s both; }
        .slide-animation-2 .content-left { animation: rotateIn 1s ease-out 0.4s both; }
        .slide-animation-2 .content-right { animation: slideUp 1s ease-out 0.6s both; }

        .slide-animation-3 .slide-header { animation: bounceIn 1s ease-out 0.2s both; }
        .slide-animation-3 .content-left { animation: flipIn 1s ease-out 0.4s both; }
        .slide-animation-3 .content-right { animation: scaleIn 1s ease-out 0.6s both; }

        .slide-animation-4 .slide-header { animation: rotateIn 1s ease-out 0.2s both; }
        .slide-animation-4 .content-left { animation: fadeIn 1s ease-in 0.4s both; }
        .slide-animation-4 .content-right { animation: bounceIn 1s ease-out 0.6s both; }

        .slide-animation-5 .slide-header { animation: flipIn 1s ease-out 0.2s both; }
        .slide-animation-5 .content-left { animation: scaleIn 1s ease-out 0.4s both; }
        .slide-animation-5 .content-right { animation: rotateIn 1s ease-out 0.6s both; }

        .key-point {
            animation: slideUp 0.6s ease-out both;
        }

        .key-point:nth-child(1) { animation-delay: 0.8s; }
        .key-point:nth-child(2) { animation-delay: 1.0s; }
        .key-point:nth-child(3) { animation-delay: 1.2s; }
        .key-point:nth-child(4) { animation-delay: 1.4s; }
        """

    def _generate_thumbnails_html(self, sections: List[Dict], colors: List[str]) -> str:
        """Generate HTML for slide thumbnails"""
        thumbnails_html = ""

        for i, section in enumerate(sections):
            thumbnails_html += f"""
            <div class="thumbnail" onclick="goToSlide({i})" data-slide="{i}">
                <div style="
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, {colors[0]}20, {colors[1]}20);
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    color: {colors[0]};
                    font-weight: 600;
                ">
                    {i + 1}
                </div>
            </div>
            """

        return thumbnails_html

    def _generate_presentation_js(self, total_slides: int) -> str:
        """Generate JavaScript for presentation functionality"""
        return f"""
        let currentSlide = 0;
        const totalSlides = {total_slides};

        function showSlide(index) {{
            const slides = document.querySelectorAll('.slide');
            const thumbnails = document.querySelectorAll('.thumbnail');
            const counter = document.querySelector('.slide-counter');

            // Hide all slides
            slides.forEach(slide => {{
                slide.classList.remove('active');
            }});

            // Remove active class from thumbnails
            thumbnails.forEach(thumb => {{
                thumb.classList.remove('active');
            }});

            // Show current slide
            if (slides[index]) {{
                slides[index].classList.add('active');
            }}

            // Highlight current thumbnail
            if (thumbnails[index]) {{
                thumbnails[index].classList.add('active');
            }}

            // Update counter
            if (counter) {{
                counter.textContent = `${{index + 1}} / ${{totalSlides}}`;
            }}
        }}

        function nextSlide() {{
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }}

        function previousSlide() {{
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        }}

        function goToSlide(index) {{
            currentSlide = index;
            showSlide(currentSlide);
        }}

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {{
            if (e.key === 'ArrowRight' || e.key === ' ') {{
                nextSlide();
            }} else if (e.key === 'ArrowLeft') {{
                previousSlide();
            }}
        }});

        // Auto-start presentation
        document.addEventListener('DOMContentLoaded', function() {{
            showSlide(0);

            // Auto-advance slides (optional)
            // setInterval(nextSlide, 10000); // 10 seconds per slide
        }});

        // Touch/swipe support for mobile
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function(e) {{
            startX = e.touches[0].clientX;
        }});

        document.addEventListener('touchend', function(e) {{
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        }});

        function handleSwipe() {{
            const threshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > threshold) {{
                if (diff > 0) {{
                    nextSlide(); // Swipe left - next slide
                }} else {{
                    previousSlide(); // Swipe right - previous slide
                }}
            }}
        }}
        """
