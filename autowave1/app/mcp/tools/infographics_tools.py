"""
Infographics Tools for MCP Server.
These tools handle professional infographic generation with AI-powered customization.
"""

import logging
import json
import base64
import io
import os
import tempfile
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import colorsys
import random

# Core dependencies
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.patches import FancyBboxPatch, Circle, Rectangle
    import numpy as np
    import pandas as pd
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

# Advanced visualization
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

# PDF generation
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.colors import HexColor
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

logger = logging.getLogger(__name__)

class InfographicsTools:
    """
    Professional infographics generation tools with AI-powered customization.
    """

    def __init__(self):
        """Initialize the infographics tools."""
        self.logger = logging.getLogger(__name__)
        
        # Load API keys from environment
        from dotenv import load_dotenv
        load_dotenv()

        # Initialize LLM API clients
        try:
            from app.api.gemini import GeminiAPI
            self.gemini_api = GeminiAPI()
            self.has_llm_api = True
        except Exception as e:
            self.logger.warning(f"Failed to initialize Gemini API: {str(e)}")
            self.has_llm_api = False

        # Professional color schemes
        self.color_schemes = {
            'business': ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83'],
            'modern': ['#264653', '#2A9D8F', '#E9C46A', '#F4A261', '#E76F51'],
            'classic': ['#1B365D', '#5D737E', '#B4A5A5', '#D6C0B3', '#AB6C82'],
            'vibrant': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
            'professional': ['#34495E', '#3498DB', '#E74C3C', '#F39C12', '#9B59B6'],
            'minimal': ['#2C3E50', '#95A5A6', '#BDC3C7', '#ECF0F1', '#E67E22']
        }

        # Template configurations
        self.templates = {
            'business_profile': {
                'sections': ['header', 'stats', 'features', 'timeline'],
                'layout': 'vertical',
                'color_scheme': 'business'
            },
            'data_visualization': {
                'sections': ['title', 'charts', 'insights', 'summary'],
                'layout': 'grid',
                'color_scheme': 'professional'
            },
            'process_flow': {
                'sections': ['title', 'steps', 'details', 'conclusion'],
                'layout': 'horizontal',
                'color_scheme': 'modern'
            },
            'comparison': {
                'sections': ['title', 'comparison_table', 'highlights', 'recommendation'],
                'layout': 'split',
                'color_scheme': 'classic'
            },
            'educational': {
                'sections': ['title', 'concepts', 'examples', 'summary'],
                'layout': 'vertical',
                'color_scheme': 'vibrant'
            }
        }

    def create_infographic(self, content: str, infographic_type: str = "business_profile",
                          title: str = "Professional Infographic",
                          color_scheme: str = "business",
                          data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a professional infographic based on content and specifications.

        Args:
            content: Text content to visualize
            infographic_type: Type of infographic template
            title: Title for the infographic
            color_scheme: Color scheme to use
            data: Optional structured data for charts

        Returns:
            Dictionary containing infographic data and metadata
        """
        self.logger.info(f"Creating infographic: {title} (type: {infographic_type})")

        if not MATPLOTLIB_AVAILABLE:
            return {
                "error": "Matplotlib not available. Please install matplotlib for infographics generation.",
                "title": title
            }

        try:
            # Generate AI-enhanced content structure
            structured_content = self._analyze_content_with_ai(content, infographic_type)
            
            # Select template and colors
            template = self.templates.get(infographic_type, self.templates['business_profile'])
            colors = self.color_schemes.get(color_scheme, self.color_schemes['business'])
            
            # Create the infographic
            infographic_data = self._generate_infographic(
                structured_content, template, colors, title, data
            )
            
            return {
                "title": title,
                "type": infographic_type,
                "image_base64": infographic_data['image'],
                "pdf_base64": infographic_data.get('pdf', ''),
                "summary": structured_content.get('summary', ''),
                "sections": structured_content.get('sections', []),
                "color_scheme": color_scheme
            }
            
        except Exception as e:
            self.logger.error(f"Error creating infographic: {str(e)}")
            return {
                "error": str(e),
                "title": title
            }

    def _analyze_content_with_ai(self, content: str, infographic_type: str) -> Dict[str, Any]:
        """
        Use AI to analyze content and structure it for infographic generation.
        """
        if not self.has_llm_api:
            return self._fallback_content_analysis(content, infographic_type)

        try:
            prompt = f"""
            Analyze the following content and structure it for a {infographic_type} infographic:

            Content: {content}

            Please provide a JSON response with:
            1. title: A compelling title
            2. summary: Brief summary (1-2 sentences)
            3. sections: Array of sections with title, content, and visual_type
            4. key_stats: Important numbers or statistics
            5. visual_elements: Suggested visual elements (icons, charts, etc.)

            Make it professional, engaging, and suitable for visual presentation.
            """

            response = self.gemini_api.generate_text(prompt)
            
            # Try to parse JSON response
            try:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
            except:
                pass
                
            # Fallback to structured parsing
            return self._parse_ai_response(response, infographic_type)
            
        except Exception as e:
            self.logger.warning(f"AI analysis failed: {e}")
            return self._fallback_content_analysis(content, infographic_type)

    def _fallback_content_analysis(self, content: str, infographic_type: str) -> Dict[str, Any]:
        """
        Fallback content analysis without AI.
        """
        # Extract key information
        lines = content.split('\n')
        title = lines[0] if lines else "Infographic"
        
        # Basic structure based on type
        if infographic_type == 'business_profile':
            sections = [
                {"title": "Overview", "content": content[:200], "visual_type": "text"},
                {"title": "Key Features", "content": "Professional services", "visual_type": "list"},
                {"title": "Statistics", "content": "Growth metrics", "visual_type": "chart"}
            ]
        elif infographic_type == 'data_visualization':
            sections = [
                {"title": "Data Insights", "content": content[:300], "visual_type": "chart"},
                {"title": "Key Findings", "content": "Important discoveries", "visual_type": "highlight"}
            ]
        else:
            sections = [
                {"title": "Main Content", "content": content[:400], "visual_type": "text"}
            ]

        return {
            "title": title,
            "summary": content[:100] + "..." if len(content) > 100 else content,
            "sections": sections,
            "key_stats": ["100%", "24/7", "2025"],
            "visual_elements": ["chart", "icon", "text"]
        }

    def _parse_ai_response(self, response: str, infographic_type: str) -> Dict[str, Any]:
        """
        Parse AI response into structured format.
        """
        # Extract title
        title_match = response.split('\n')[0] if response else "Infographic"

        # Basic parsing - in production, this would be more sophisticated
        return {
            "title": title_match[:50],
            "summary": response[:150] + "..." if len(response) > 150 else response,
            "sections": [
                {"title": "Main Content", "content": response[:300], "visual_type": "text"}
            ],
            "key_stats": ["100%", "Growth", "Success"],
            "visual_elements": ["chart", "text", "icon"]
        }

    def _generate_infographic(self, content_data: Dict[str, Any], template: Dict[str, Any],
                            colors: List[str], title: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate the actual infographic using matplotlib.
        """
        # Create figure with professional styling
        fig, ax = plt.subplots(figsize=(12, 16))
        fig.patch.set_facecolor('white')
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 16)
        ax.axis('off')

        # Color palette
        primary_color = colors[0]
        secondary_color = colors[1] if len(colors) > 1 else colors[0]
        accent_color = colors[2] if len(colors) > 2 else colors[0]

        # Header section
        self._add_header(ax, title, primary_color, secondary_color)

        # Content sections
        y_position = 14
        for i, section in enumerate(content_data.get('sections', [])):
            y_position = self._add_section(ax, section, colors[i % len(colors)], y_position)
            y_position -= 0.5  # Spacing between sections

        # Add key statistics if available
        if content_data.get('key_stats'):
            self._add_stats_section(ax, content_data['key_stats'], colors, y_position - 1)

        # Footer
        self._add_footer(ax, accent_color)

        # Save to base64
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        buffer.close()
        plt.close()

        # Generate PDF version if reportlab is available
        pdf_base64 = ""
        if REPORTLAB_AVAILABLE:
            pdf_base64 = self._generate_pdf_version(content_data, colors, title)

        return {
            "image": image_base64,
            "pdf": pdf_base64
        }

    def _add_header(self, ax, title: str, primary_color: str, secondary_color: str):
        """
        Add professional header to the infographic.
        """
        # Background rectangle
        header_bg = FancyBboxPatch((0.5, 14.5), 9, 1.2,
                                  boxstyle="round,pad=0.1",
                                  facecolor=primary_color,
                                  edgecolor='none',
                                  alpha=0.9)
        ax.add_patch(header_bg)

        # Title text
        ax.text(5, 15.1, title, fontsize=24, fontweight='bold',
               ha='center', va='center', color='white',
               fontfamily='sans-serif')

        # Decorative elements
        circle1 = Circle((1, 15.1), 0.15, facecolor=secondary_color, alpha=0.7)
        circle2 = Circle((9, 15.1), 0.15, facecolor=secondary_color, alpha=0.7)
        ax.add_patch(circle1)
        ax.add_patch(circle2)

    def _add_section(self, ax, section: Dict[str, Any], color: str, y_pos: float) -> float:
        """
        Add a content section to the infographic.
        """
        # Section header
        section_bg = FancyBboxPatch((0.5, y_pos - 0.4), 9, 0.6,
                                   boxstyle="round,pad=0.05",
                                   facecolor=color,
                                   alpha=0.2)
        ax.add_patch(section_bg)

        # Section title
        ax.text(1, y_pos - 0.1, section.get('title', 'Section'),
               fontsize=16, fontweight='bold', color=color,
               fontfamily='sans-serif')

        # Section content
        content = section.get('content', '')[:200]  # Limit content length
        wrapped_content = self._wrap_text(content, 80)

        ax.text(1, y_pos - 0.8, wrapped_content, fontsize=11,
               color='#333333', fontfamily='sans-serif',
               verticalalignment='top')

        # Enhanced visual elements based on content type
        visual_type = section.get('visual_type', 'auto')

        # Auto-detect visual type based on content
        if visual_type == 'auto':
            content_lower = content.lower()
            if any(word in content_lower for word in ['data', 'statistics', 'metrics', 'numbers']):
                visual_type = 'bar'
            elif any(word in content_lower for word in ['growth', 'trend', 'progress', 'timeline']):
                visual_type = 'line'
            elif any(word in content_lower for word in ['distribution', 'share', 'percentage', 'portion']):
                visual_type = 'pie'
            elif any(word in content_lower for word in ['completion', 'progress', 'achievement']):
                visual_type = 'donut'
            else:
                visual_type = 'list'

        # Add appropriate visual element
        if visual_type in ['bar', 'line', 'pie', 'donut']:
            self._add_mini_chart(ax, 7, y_pos - 0.5, color, visual_type)
        elif visual_type == 'list':
            self._add_bullet_points(ax, 7, y_pos - 0.5, color)
        elif visual_type == 'icon':
            self._add_professional_icon(ax, 7.5, y_pos - 0.5, color)

        # Add decorative elements for professional look
        self._add_section_decorations(ax, y_pos, color)

        return y_pos - 2  # Return next y position

    def _add_stats_section(self, ax, stats: List[str], colors: List[str], y_pos: float):
        """
        Add statistics section with visual elements.
        """
        # Stats background
        stats_bg = FancyBboxPatch((0.5, y_pos - 1), 9, 1.5,
                                 boxstyle="round,pad=0.1",
                                 facecolor='#F8F9FA',
                                 edgecolor=colors[0],
                                 linewidth=2)
        ax.add_patch(stats_bg)

        # Add individual stats
        x_positions = [2, 5, 8]
        for i, stat in enumerate(stats[:3]):  # Limit to 3 stats
            if i < len(x_positions):
                color = colors[i % len(colors)]

                # Stat circle
                stat_circle = Circle((x_positions[i], y_pos - 0.3), 0.3,
                                   facecolor=color, alpha=0.8)
                ax.add_patch(stat_circle)

                # Stat text
                ax.text(x_positions[i], y_pos - 0.3, stat,
                       fontsize=12, fontweight='bold', ha='center', va='center',
                       color='white', fontfamily='sans-serif')

    def _add_footer(self, ax, color: str):
        """
        Add professional footer.
        """
        # Footer line
        ax.plot([1, 9], [0.5, 0.5], color=color, linewidth=2, alpha=0.7)

        # Footer text
        ax.text(5, 0.2, f"Generated by AutoWave • {datetime.now().strftime('%Y')}",
               fontsize=10, ha='center', color='#666666',
               fontfamily='sans-serif', style='italic')

    def _add_mini_chart(self, ax, x: float, y: float, color: str, chart_type: str = "bar"):
        """
        Add professional mini chart visualizations with multiple types.
        """
        if chart_type == "bar":
            self._add_bar_chart(ax, x, y, color)
        elif chart_type == "pie":
            self._add_pie_chart(ax, x, y, color)
        elif chart_type == "line":
            self._add_line_chart(ax, x, y, color)
        elif chart_type == "donut":
            self._add_donut_chart(ax, x, y, color)
        else:
            self._add_bar_chart(ax, x, y, color)  # Default to bar chart

    def _add_bar_chart(self, ax, x: float, y: float, color: str):
        """Add professional bar chart."""
        bars = [0.3, 0.5, 0.4, 0.6, 0.7, 0.45, 0.8]
        bar_width = 0.12
        colors = [color, '#48bb78', '#ed8936', '#9f7aea', '#38b2ac', '#f56565', '#4299e1']

        for i, height in enumerate(bars):
            bar_color = colors[i % len(colors)]
            bar = Rectangle((x + i * 0.15, y - 0.5), bar_width, height,
                          facecolor=bar_color, alpha=0.8, edgecolor='white', linewidth=0.5)
            ax.add_patch(bar)

            # Add value labels on top of bars
            ax.text(x + i * 0.15 + bar_width/2, y - 0.5 + height + 0.02,
                   f'{int(height*100)}%', fontsize=6, ha='center', va='bottom',
                   color='#333333', fontweight='bold')

    def _add_pie_chart(self, ax, x: float, y: float, color: str):
        """Add professional pie chart."""
        sizes = [30, 25, 20, 15, 10]
        colors = [color, '#48bb78', '#ed8936', '#9f7aea', '#38b2ac']

        # Create pie chart
        wedges, texts = ax.pie(sizes, colors=colors, center=(x + 0.4, y - 0.2),
                              radius=0.3, startangle=90,
                              wedgeprops=dict(width=0.15, edgecolor='white', linewidth=1))

    def _add_line_chart(self, ax, x: float, y: float, color: str):
        """Add professional line chart."""
        x_data = np.linspace(x, x + 1, 8)
        y_data = np.array([0.2, 0.4, 0.3, 0.6, 0.5, 0.7, 0.8, 0.9]) + y - 0.5

        # Plot line with gradient effect
        ax.plot(x_data, y_data, color=color, linewidth=3, alpha=0.8)
        ax.fill_between(x_data, y - 0.5, y_data, color=color, alpha=0.3)

        # Add data points
        ax.scatter(x_data, y_data, color=color, s=30, zorder=5,
                  edgecolors='white', linewidth=1)

    def _add_donut_chart(self, ax, x: float, y: float, color: str):
        """Add professional donut chart."""
        sizes = [40, 30, 20, 10]
        colors = [color, '#48bb78', '#ed8936', '#9f7aea']

        # Create donut chart
        wedges, texts = ax.pie(sizes, colors=colors, center=(x + 0.4, y - 0.2),
                              radius=0.3, startangle=90,
                              wedgeprops=dict(width=0.2, edgecolor='white', linewidth=2))

        # Add center text
        ax.text(x + 0.4, y - 0.2, '85%', fontsize=12, ha='center', va='center',
               fontweight='bold', color='#333333')

    def _add_bullet_points(self, ax, x: float, y: float, color: str):
        """
        Add professional bullet points visualization.
        """
        points = ["Key Feature", "Benefits", "Results"]
        colors = [color, '#48bb78', '#ed8936']

        for i, point in enumerate(points):
            # Professional bullet with gradient effect
            bullet_color = colors[i % len(colors)]
            bullet = Circle((x, y - i * 0.25), 0.06, facecolor=bullet_color,
                          edgecolor='white', linewidth=1, alpha=0.9)
            ax.add_patch(bullet)

            # Add inner highlight
            highlight = Circle((x - 0.02, y - i * 0.25 + 0.02), 0.02,
                             facecolor='white', alpha=0.6)
            ax.add_patch(highlight)

            # Bullet text with better styling
            ax.text(x + 0.25, y - i * 0.25, point, fontsize=10,
                   va='center', color='#333333', fontfamily='sans-serif',
                   fontweight='500')

    def _add_professional_icon(self, ax, x: float, y: float, color: str):
        """
        Add professional icon representation.
        """
        # Create a stylized icon using geometric shapes
        # Main circle
        main_circle = Circle((x, y), 0.15, facecolor=color, alpha=0.8,
                           edgecolor='white', linewidth=2)
        ax.add_patch(main_circle)

        # Inner elements
        inner_circle = Circle((x, y), 0.08, facecolor='white', alpha=0.9)
        ax.add_patch(inner_circle)

        # Add checkmark or arrow
        ax.plot([x - 0.04, x - 0.01, x + 0.04], [y - 0.01, y - 0.04, y + 0.02],
               color=color, linewidth=3, linecap='round')

    def _add_section_decorations(self, ax, y_pos: float, color: str):
        """
        Add decorative elements to make sections more professional.
        """
        # Side accent line
        accent_line = Rectangle((0.2, y_pos - 1.5), 0.05, 1.2,
                              facecolor=color, alpha=0.6)
        ax.add_patch(accent_line)

        # Corner decoration
        corner_circle = Circle((9.3, y_pos - 0.1), 0.08,
                             facecolor=color, alpha=0.4)
        ax.add_patch(corner_circle)

    def _wrap_text(self, text: str, width: int) -> str:
        """
        Wrap text to specified width.
        """
        words = text.split()
        lines = []
        current_line = []
        current_length = 0

        for word in words:
            if current_length + len(word) + 1 <= width:
                current_line.append(word)
                current_length += len(word) + 1
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
                current_length = len(word)

        if current_line:
            lines.append(' '.join(current_line))

        return '\n'.join(lines[:5])  # Limit to 5 lines

    def _generate_pdf_version(self, content_data: Dict[str, Any], colors: List[str], title: str) -> str:
        """
        Generate PDF version of the infographic.
        """
        try:
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                textColor=HexColor(colors[0]),
                alignment=1  # Center
            )

            # Add title
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 0.5*inch))

            # Add sections
            for section in content_data.get('sections', []):
                section_title = Paragraph(f"<b>{section.get('title', '')}</b>", styles['Heading2'])
                section_content = Paragraph(section.get('content', ''), styles['Normal'])

                story.append(section_title)
                story.append(section_content)
                story.append(Spacer(1, 0.3*inch))

            doc.build(story)
            buffer.seek(0)
            pdf_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            buffer.close()

            return pdf_base64
        except Exception as e:
            self.logger.warning(f"PDF generation failed: {e}")
            return ""
