"""
AutoWave Invitation Routes
Handles invitation link generation and management
Special promotion: Pay 1 month Plus, get 2 months free (total 3 months)
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, session, redirect, url_for, render_template
from app.services.invitation_service import invitation_service

logger = logging.getLogger(__name__)

# Create blueprint
invitation_bp = Blueprint('invitation', __name__, url_prefix='/invitation')

@invitation_bp.route('/generator', methods=['GET'])
def invitation_generator():
    """
    Invitation link generator page
    Similar to referral generator but for invitation promotions
    """
    try:
        # Check if user is authenticated (basic check)
        user_id = session.get('user_id')
        if not user_id:
            return redirect(url_for('auth.login'))
        
        return render_template('invitation_generator.html')
        
    except Exception as e:
        logger.error(f"Error loading invitation generator: {e}")
        return render_template('invitation_generator.html', error=str(e))

@invitation_bp.route('/generate', methods=['POST'])
def generate_invitation():
    """
    Generate a new invitation link
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        data = request.get_json()
        description = data.get('description', '')
        
        # Generate invitation link
        invitation = invitation_service.generate_invitation_link(
            created_by=user_id,
            description=description
        )
        
        # Create the full invitation URL
        base_url = request.host_url.rstrip('/')
        invitation_url = f"{base_url}/auth/register?invitation={invitation.code}"
        
        return jsonify({
            'success': True,
            'invitation': {
                'id': invitation.id,
                'code': invitation.code,
                'url': invitation_url,
                'description': invitation.description,
                'created_at': invitation.created_at.isoformat(),
                'promotion_type': invitation.promotion_type,
                'credits_total': invitation.credits_total,
                'is_active': invitation.is_active
            }
        })
        
    except Exception as e:
        logger.error(f"Error generating invitation: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@invitation_bp.route('/list', methods=['GET'])
def list_invitations():
    """
    List all invitation links for the current user
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({
                'success': False,
                'error': 'Authentication required'
            }), 401
        
        # Get all invitations created by this user
        invitations = invitation_service.get_all_invitations(created_by=user_id)
        
        invitation_list = []
        for invitation in invitations:
            base_url = request.host_url.rstrip('/')
            invitation_url = f"{base_url}/auth/register?invitation={invitation.code}"
            
            invitation_list.append({
                'id': invitation.id,
                'code': invitation.code,
                'url': invitation_url,
                'description': invitation.description,
                'created_at': invitation.created_at.isoformat(),
                'expires_at': invitation.expires_at.isoformat() if invitation.expires_at else None,
                'used_by': invitation.used_by,
                'used_at': invitation.used_at.isoformat() if invitation.used_at else None,
                'is_active': invitation.is_active,
                'promotion_type': invitation.promotion_type,
                'credits_total': invitation.credits_total,
                'status': 'Used' if invitation.used_by else 'Active'
            })
        
        return jsonify({
            'success': True,
            'invitations': invitation_list
        })
        
    except Exception as e:
        logger.error(f"Error listing invitations: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@invitation_bp.route('/validate/<code>', methods=['GET'])
def validate_invitation(code):
    """
    Validate an invitation code
    """
    try:
        invitation = invitation_service.get_invitation_by_code(code)
        
        if not invitation:
            return jsonify({
                'success': False,
                'error': 'Invalid invitation code',
                'valid': False
            })
        
        if not invitation.is_active:
            return jsonify({
                'success': False,
                'error': 'Invitation code is inactive',
                'valid': False
            })
        
        if invitation.used_by:
            return jsonify({
                'success': False,
                'error': 'Invitation code has already been used',
                'valid': False
            })
        
        return jsonify({
            'success': True,
            'valid': True,
            'invitation': {
                'code': invitation.code,
                'promotion_type': invitation.promotion_type,
                'credits_total': invitation.credits_total,
                'description': invitation.description,
                'promotion_details': {
                    'title': 'Special Invitation Offer!',
                    'subtitle': 'Pay for 1 month Plus, get 2 months FREE',
                    'total_months': 3,
                    'total_credits': invitation.credits_total,
                    'plan': 'Plus',
                    'savings': 'Save 67% on your first 3 months!'
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error validating invitation: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'valid': False
        }), 500

@invitation_bp.route('/use', methods=['POST'])
def use_invitation():
    """
    Use an invitation code during signup
    """
    try:
        data = request.get_json()
        code = data.get('code')
        user_id = data.get('user_id')
        
        if not code or not user_id:
            return jsonify({
                'success': False,
                'error': 'Missing invitation code or user ID'
            }), 400
        
        # Use the invitation link
        success = invitation_service.use_invitation_link(code, user_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Invitation code applied successfully',
                'promotion': {
                    'type': 'plus_3_months',
                    'credits_total': 16000,
                    'expiry_days': 7,
                    'description': 'Pay for 1 month Plus, get 2 months FREE!'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to apply invitation code'
            }), 400
        
    except Exception as e:
        logger.error(f"Error using invitation: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@invitation_bp.route('/status/<user_id>', methods=['GET'])
def get_invitation_status(user_id):
    """
    Get invitation status for a user
    """
    try:
        invitation_user = invitation_service.get_invitation_user(user_id)
        
        if not invitation_user:
            return jsonify({
                'success': True,
                'has_invitation': False
            })
        
        # Check if expired
        is_expired = False
        if invitation_user.expiry_date and datetime.now() > invitation_user.expiry_date:
            is_expired = True
        
        return jsonify({
            'success': True,
            'has_invitation': True,
            'invitation': {
                'code': invitation_user.invitation_code,
                'signup_date': invitation_user.signup_date.isoformat(),
                'subscription_date': invitation_user.subscription_date.isoformat() if invitation_user.subscription_date else None,
                'credits_used': invitation_user.credits_used,
                'credits_remaining': invitation_user.credits_remaining,
                'is_expired': is_expired,
                'expiry_date': invitation_user.expiry_date.isoformat() if invitation_user.expiry_date else None,
                'days_remaining': (invitation_user.expiry_date - datetime.now()).days if invitation_user.expiry_date and not is_expired else 0,
                'has_subscribed': invitation_user.subscription_date is not None
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting invitation status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@invitation_bp.route('/activate', methods=['POST'])
def activate_invitation():
    """
    Activate invitation subscription when user pays
    """
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({
                'success': False,
                'error': 'Missing user ID'
            }), 400
        
        success = invitation_service.activate_invitation_subscription(user_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Invitation subscription activated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to activate invitation subscription'
            }), 400
        
    except Exception as e:
        logger.error(f"Error activating invitation: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
