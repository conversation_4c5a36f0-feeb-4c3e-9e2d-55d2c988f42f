"""
AutoWave Payment Routes
Handles subscription creation, management, and webhook processing
"""

import logging
import os
import time
from datetime import datetime
from flask import Blueprint, request, jsonify, session
from ..services.subscription_service import SubscriptionService
from ..services.payment_gateway import PaymentGatewayFactory
from ..services.currency_service import currency_service
from ..services.invoice_email_service import invoice_email_service
from ..services.coupon_service import coupon_service
from ..decorators.paywall import require_subscription, get_user_plan_info
from ..security.auth_manager import require_auth
from .referral_routes import get_referral_discount, get_referral_bonus_credits
import json

logger = logging.getLogger(__name__)

payment_bp = Blueprint('payment', __name__, url_prefix='/payment')

@payment_bp.route('/plans', methods=['GET'])
def get_subscription_plans():
    """Get all available subscription plans with currency conversion"""
    try:
        subscription_service = SubscriptionService()
        plans = subscription_service.get_subscription_plans()

        # Get user location for payment provider detection
        user_location = request.args.get('location', 'US')
        provider = PaymentGatewayFactory.detect_best_provider(user_location)

        plans_data = []
        for plan in plans:
            # Convert pricing for the detected provider
            monthly_conversion = currency_service.convert_plan_price(
                plan.monthly_price_usd, provider, 'monthly'
            )
            annual_conversion = currency_service.convert_plan_price(
                plan.annual_price_usd, provider, 'annual'
            )

            # Calculate Naira conversion for dual display
            monthly_naira = plan.monthly_price_usd * 1650
            annual_naira = plan.annual_price_usd * 1650

            plan_data = {
                'id': plan.id,
                'name': plan.plan_name,
                'display_name': plan.display_name,
                'monthly_price': plan.monthly_price_usd,
                'annual_price': plan.annual_price_usd,
                'monthly_credits': plan.monthly_credits,
                'features': plan.features,
                'is_active': plan.is_active,
                'provider': provider,
                'pricing': {
                    'monthly': {
                        'usd': plan.monthly_price_usd,
                        'naira': monthly_naira,
                        'naira_formatted': f"₦{monthly_naira:,.0f}",
                        'local': monthly_conversion['converted_amount'],
                        'currency': monthly_conversion['to_currency'],
                        'formatted': monthly_conversion['display_price'],
                        'exchange_rate': monthly_conversion['exchange_rate'],
                        'dual_display': f"${plan.monthly_price_usd} (₦{monthly_naira:,.0f})"
                    },
                    'annual': {
                        'usd': plan.annual_price_usd,
                        'naira': annual_naira,
                        'naira_formatted': f"₦{annual_naira:,.0f}",
                        'local': annual_conversion['converted_amount'],
                        'currency': annual_conversion['to_currency'],
                        'formatted': annual_conversion['display_price'],
                        'exchange_rate': annual_conversion['exchange_rate'],
                        'dual_display': f"${plan.annual_price_usd} (₦{annual_naira:,.0f})"
                    }
                }
            }
            plans_data.append(plan_data)

        return jsonify({
            'success': True,
            'plans': plans_data,
            'provider': provider,
            'currency_info': currency_service.get_provider_currency_info(provider),
            'dual_currency': {
                'primary': 'USD',
                'secondary': 'NGN',
                'exchange_rate': 1650,
                'display_format': 'USD with Naira below'
            }
        })

    except Exception as e:
        logger.error(f"Error fetching subscription plans: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch subscription plans'
        }), 500

@payment_bp.route('/user-info', methods=['GET'])
def get_user_payment_info():
    """Get user's current subscription and credit information"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            # Return default free plan info for non-authenticated users
            return jsonify({
                'success': True,
                'user_info': {
                    'plan_name': 'free',
                    'display_name': 'Free Plan',
                    'credits': {
                        'remaining': 50,
                        'total': 50,
                        'type': 'daily'
                    },
                    'authenticated': False
                }
            })

        # Check if user is admin first
        from ..services.admin_service import admin_service
        user_email = session.get('user_email', '')
        is_admin_user = admin_service.is_admin(user_email)

        # Use CreditService for accurate credit information
        from ..services.credit_service import credit_service
        credit_info = credit_service.get_user_credits(user_id)

        # Get subscription info
        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)

        # If no subscription found, create free subscription
        if not user_subscription:
            try:
                subscription_created = subscription_service.create_free_subscription(user_id)
                if subscription_created:
                    logger.info(f"Created free subscription for existing user: {user_id}")
                    user_subscription = subscription_service.get_user_subscription(user_id)
                else:
                    logger.warning(f"Failed to create free subscription for user: {user_id}")
            except Exception as e:
                logger.error(f"Error creating free subscription: {str(e)}")

        # Build response with accurate credit information
        if is_admin_user:
            # Admin user with unlimited credits
            user_info = {
                'plan_name': 'admin',
                'display_name': 'Admin Plan',
                'credits': {
                    'remaining': -1,  # Unlimited
                    'total': -1,      # Unlimited
                    'type': 'unlimited',
                    'percentage': 100,
                    'reset_date': None
                },
                'authenticated': True,
                'subscription_status': 'active',
                'is_admin': True
            }
        else:
            # Regular user
            plan_name = credit_info.get('plan', 'free')
            display_names = {
                'free': 'Free Plan',
                'plus': 'Plus Plan',
                'pro': 'Pro Plan',
                'enterprise': 'Enterprise Plan'
            }

            user_info = {
                'plan_name': plan_name,
                'display_name': display_names.get(plan_name, 'Free Plan'),
                'credits': {
                    'remaining': credit_info.get('remaining', 50),
                    'total': credit_info.get('total', 50),
                    'type': credit_info.get('type', 'daily'),
                    'percentage': credit_info.get('percentage', 100),
                    'reset_date': credit_info.get('reset_date')
                },
                'authenticated': True,
                'subscription_status': getattr(user_subscription, 'status', 'active') if user_subscription else 'active',
                'is_admin': False
            }

        return jsonify({
            'success': True,
            'user_info': user_info
        })

    except Exception as e:
        logger.error(f"Error fetching user payment info: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch user information'
        }), 500

@payment_bp.route('/consume-credits', methods=['POST'])
def consume_credits():
    """Consume credits for a user action and return updated credit info"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'success': False, 'error': 'User not authenticated'}), 401

        task_type = data.get('task_type', 'general')
        amount = data.get('amount')  # Optional, will use default if not provided

        # Consume credits
        from ..services.credit_service import credit_service
        result = credit_service.consume_credits(user_id, task_type, amount)

        if result['success']:
            # Get updated user info for frontend
            updated_info = get_user_plan_info(user_id)

            return jsonify({
                'success': True,
                'credits_consumed': result['credits_consumed'],
                'remaining_credits': result['remaining_credits'],
                'user_info': updated_info,
                'task_type': task_type
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error'],
                'credits_needed': result.get('credits_needed'),
                'credits_available': result.get('credits_available')
            }), 400

    except Exception as e:
        logger.error(f"Error consuming credits: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to consume credits'
        }), 500

@payment_bp.route('/apple-pay/validate-merchant', methods=['POST'])
def validate_apple_pay_merchant():
    """
    Professional Apple Pay merchant validation for Paystack integration
    """
    try:
        data = request.get_json()
        validation_url = data.get('validationURL')
        display_name = data.get('displayName', 'AutoWave')

        if not validation_url:
            return jsonify({
                'success': False,
                'error': 'Validation URL is required'
            }), 400

        logger.info(f"Apple Pay merchant validation requested for: {validation_url}")

        # For Paystack integration, we need to handle Apple Pay validation properly
        # Since we don't have Apple's merchant certificates, we'll use Paystack's approach

        # Check if Paystack supports Apple Pay validation
        paystack_secret = os.getenv('PAYSTACK_SECRET_KEY')
        if not paystack_secret:
            return jsonify({
                'success': False,
                'error': 'Payment gateway not configured'
            }), 500

        # For now, we'll create a mock merchant session for testing
        # In production with proper Apple Developer account, this would be real
        mock_merchant_session = {
            'epochTimestamp': int(time.time() * 1000),
            'expiresAt': int(time.time() * 1000) + 300000,  # 5 minutes
            'merchantSessionIdentifier': f'paystack_session_{int(time.time())}',
            'nonce': f'paystack_nonce_{int(time.time())}',
            'merchantIdentifier': 'merchant.com.autowave.paystack',
            'domainName': 'autowave.pro',
            'displayName': display_name,
            'signature': f'paystack_signature_{int(time.time())}'
        }

        return jsonify({
            'success': True,
            'merchantSession': mock_merchant_session,
            'message': 'Merchant validation successful'
        })

    except Exception as e:
        logger.error(f"Apple Pay merchant validation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Merchant validation failed'
        }), 500

@payment_bp.route('/apple-pay/process', methods=['POST'])
def process_apple_pay_payment():
    """
    Professional Apple Pay payment processing with Paystack integration
    """
    try:
        data = request.get_json()
        payment_data = data.get('payment')
        plan_details = data.get('planDetails')

        # Validate required data
        if not payment_data or not plan_details:
            return jsonify({
                'success': False,
                'error': 'Payment data and plan details are required'
            }), 400

        # Get user information
        user_id = session.get('user_id')
        user_email = session.get('user_email')

        if not user_id or not user_email:
            return jsonify({
                'success': False,
                'error': 'User authentication required'
            }), 401

        logger.info(f"Processing Apple Pay payment for user {user_email}, plan {plan_details.get('name')}")

        # Extract payment information
        payment_token = payment_data.get('token', {})
        billing_contact = payment_data.get('billingContact', {})

        # Create a unique transaction reference
        transaction_ref = f"apple_pay_{user_id}_{int(time.time())}"

        # For professional implementation, we would:
        # 1. Validate the Apple Pay token with Apple's servers
        # 2. Process the payment through Paystack's Apple Pay endpoint
        # 3. Handle the subscription creation

        # Since Paystack may not have direct Apple Pay support,
        # we'll create a subscription using their standard flow
        # but mark it as Apple Pay for tracking

        from ..services.subscription_service import SubscriptionService
        subscription_service = SubscriptionService()

        # Create payment info for subscription
        payment_info = {
            'gateway': 'paystack_apple_pay',
            'gateway_subscription_id': transaction_ref,
            'gateway_customer_id': f'apple_customer_{user_id}',
            'billing_cycle': plan_details.get('billingCycle', 'monthly'),
            'payment_method': 'apple_pay',
            'transaction_reference': transaction_ref,
            'amount': plan_details.get('amount'),
            'currency': 'USD'
        }

        # Process subscription renewal/creation
        renewal_result = subscription_service.handle_subscription_renewal(
            user_id,
            plan_details.get('id'),
            payment_info
        )

        if renewal_result['success']:
            # Log successful Apple Pay transaction
            logger.info(f"Apple Pay payment successful: {transaction_ref}")

            return jsonify({
                'success': True,
                'message': 'Apple Pay payment processed successfully',
                'transaction_reference': transaction_ref,
                'subscription_id': payment_info['gateway_subscription_id'],
                'total_credits': renewal_result.get('total_credits'),
                'rollover_credits': renewal_result.get('rollover_credits')
            })
        else:
            return jsonify({
                'success': False,
                'error': renewal_result.get('error', 'Failed to create subscription')
            }), 400

    except Exception as e:
        logger.error(f"Apple Pay processing error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Apple Pay processing failed'
        }), 500

@payment_bp.route('/create-subscription', methods=['POST'])
@require_auth()
def create_subscription():
    """Create a new subscription"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401
        
        data = request.get_json()
        plan_id = data.get('plan_id')
        billing_cycle = data.get('billing_cycle', 'monthly')  # 'monthly' or 'annual'
        payment_provider = data.get('payment_provider', 'auto')
        user_location = data.get('user_location')
        coupon_code = data.get('coupon_code', '').strip()
        
        if not plan_id:
            return jsonify({'error': 'Plan ID is required'}), 400
        
        # Get user email for customer creation
        subscription_service = SubscriptionService()

        # Get user info from session or Supabase
        user_email = session.get('user_email')
        user_name = session.get('user_name', '')

        # Try to get user info from Supabase user_profiles table if available
        if subscription_service.use_supabase and not user_email:
            try:
                user_response = subscription_service.supabase.table('user_profiles').select('email, full_name').eq('id', user_id).single().execute()
                if user_response.data:
                    user_email = user_response.data['email']
                    user_name = user_response.data.get('full_name', '')
            except Exception as e:
                logger.warning(f"Could not fetch user profile from database: {e}")
                # Fall back to session data or default values
                pass

        # If still no email, use a default or return error
        if not user_email:
            # For testing purposes, use a default email
            user_email = f"user_{user_id}@autowave.pro"
            user_name = "AutoWave User"
            logger.warning(f"Using default email for user {user_id}: {user_email}")
        
        # Create payment gateway
        gateway = PaymentGatewayFactory.create_gateway(payment_provider, user_location)
        
        # Create customer in payment gateway
        customer_result = gateway.create_customer(user_email, user_name)
        
        if not customer_result['success']:
            return jsonify({
                'success': False,
                'error': customer_result.get('error', 'Failed to create customer')
            }), 400
        
        customer_id = customer_result['customer_id']
        
        # Get plan details for gateway-specific plan ID
        if not subscription_service.use_supabase:
            # In fallback mode, get plan from local data
            plans = subscription_service.get_subscription_plans()
            plan = next((p for p in plans if p.id == plan_id), None)

            if not plan:
                return jsonify({'error': 'Invalid plan ID'}), 400

            # Convert to dict format
            plan = {
                'id': plan.id,
                'plan_name': plan.plan_name,
                'display_name': plan.display_name,
                'monthly_price_usd': plan.monthly_price_usd,
                'annual_price_usd': plan.annual_price_usd,
                'monthly_credits': plan.monthly_credits,
                'features': plan.features,
                'is_active': plan.is_active
            }
        else:
            plan_response = subscription_service.supabase.table('subscription_plans').select('*').eq('id', plan_id).single().execute()

            if not plan_response.data:
                return jsonify({'error': 'Invalid plan ID'}), 400

            plan = plan_response.data
        
        # For Paystack, initialize payment instead of creating subscription directly
        if hasattr(gateway, 'initialize_payment'):
            # Calculate amount based on plan and billing cycle
            if billing_cycle == 'monthly':
                amount_usd = plan['monthly_price_usd']
            else:
                amount_usd = plan['annual_price_usd']

            # Apply coupon discount if provided
            coupon_info = None
            bonus_credits = 0
            original_amount = amount_usd
            if coupon_code:
                coupon_result = coupon_service.validate_coupon(coupon_code, plan['plan_name'], amount_usd)
                if coupon_result['valid']:
                    amount_usd = coupon_result['final_amount']
                    coupon_info = coupon_result.get('coupon')
                    bonus_credits = coupon_result.get('bonus_credits', 0)
                    logger.info(f"Applied coupon {coupon_code}: ${coupon_result['discount_amount']:.2f} discount")
                else:
                    return jsonify({
                        'success': False,
                        'error': f"Coupon error: {coupon_result['error']}"
                    }), 400

            # Apply referral discount if available (only if no coupon was applied)
            referral_discount = 0
            referral_bonus_credits = 0
            if not coupon_code:  # Don't stack discounts
                referral_discount = get_referral_discount()
                referral_bonus_credits = get_referral_bonus_credits()

                if referral_discount > 0:
                    discount_amount = amount_usd * (referral_discount / 100)
                    amount_usd = amount_usd - discount_amount
                    bonus_credits += referral_bonus_credits
                    logger.info(f"Applied referral discount: {referral_discount}% (${discount_amount:.2f}) + {referral_bonus_credits} bonus credits")

            # Convert to local currency if needed
            if payment_provider == 'paystack' or PaymentGatewayFactory.detect_best_provider(user_location) == 'paystack':
                # Convert USD to NGN
                exchange_rate = float(os.getenv('USD_TO_NGN_RATE', '1650'))
                amount_ngn = int(amount_usd * exchange_rate)

                # Initialize payment
                payment_result = gateway.initialize_payment(
                    customer_email=user_email,
                    amount=amount_ngn,
                    plan_name=plan['plan_name'],
                    billing_cycle=billing_cycle
                )

                if not payment_result['success']:
                    return jsonify({
                        'success': False,
                        'error': payment_result.get('error', 'Failed to initialize payment')
                    }), 400

                # Return payment URL for redirect (most reliable approach)
                return jsonify({
                    'success': True,
                    'authorization_url': payment_result['authorization_url'],
                    'access_code': payment_result.get('access_code'),
                    'reference': payment_result.get('reference'),
                    'message': 'Payment initialized. Redirecting to payment page...'
                })
            else:
                # For Stripe, use the existing subscription creation
                gateway_plan_id = f"{plan['plan_name']}_{billing_cycle}"
                subscription_result = gateway.create_subscription(customer_id, gateway_plan_id, billing_cycle)

                if not subscription_result['success']:
                    return jsonify({
                        'success': False,
                        'error': subscription_result.get('error', 'Failed to create subscription')
                    }), 400
        else:
            # Fallback to original subscription creation
            gateway_plan_id = f"{plan['plan_name']}_{billing_cycle}"
            subscription_result = gateway.create_subscription(customer_id, gateway_plan_id, billing_cycle)

            if not subscription_result['success']:
                return jsonify({
                    'success': False,
                    'error': subscription_result.get('error', 'Failed to create subscription')
                }), 400
        
        # Handle subscription renewal with credit rollover
        payment_data = {
            'gateway': payment_provider if payment_provider != 'auto' else PaymentGatewayFactory.detect_best_provider(user_location),
            'gateway_subscription_id': subscription_result['subscription_id'],
            'gateway_customer_id': customer_id,
            'billing_cycle': billing_cycle
        }

        renewal_result = subscription_service.handle_subscription_renewal(user_id, plan_id, payment_data)

        if not renewal_result['success']:
            logger.error(f"Failed to handle subscription renewal: {renewal_result.get('error')}")
            # Fallback to old method if renewal fails
            from datetime import timedelta, timezone

            now = datetime.now(timezone.utc)
            period_end = now + timedelta(days=30 if billing_cycle == 'monthly' else 365)

            subscription_data = {
                'user_id': user_id,
                'plan_id': plan_id,
                'status': 'active',
                'payment_gateway': payment_data['gateway'],
                'gateway_subscription_id': subscription_result['subscription_id'],
                'gateway_customer_id': customer_id,
                'current_period_start': now.isoformat(),
                'current_period_end': period_end.isoformat(),
                'cancel_at_period_end': False
            }

            # Store subscription based on available storage
            if subscription_service.use_supabase:
                result = subscription_service.supabase.table('user_subscriptions').insert(subscription_data).execute()
                subscription_id = result.data[0]['id'] if result.data else subscription_result['subscription_id']
            else:
                # In fallback mode, just log the subscription creation
                logger.info(f"Subscription created for user {user_id}: {subscription_data}")
                subscription_id = subscription_result['subscription_id']
        else:
            subscription_id = subscription_result['subscription_id']
            logger.info(f"Subscription renewed with credit rollover: {renewal_result['total_credits']} total credits ({renewal_result['rollover_credits']} rolled over)")

        # Track referral conversion if applicable
        try:
            referral_data_dict = session.get('referral_data')
            if referral_data_dict:
                from app.services.referral_service import ReferralData, ReferralService

                # Convert dict back to ReferralData object
                referral_data = ReferralData(
                    influencer_id=referral_data_dict.get('influencer_id'),
                    utm_source=referral_data_dict.get('utm_source'),
                    utm_medium=referral_data_dict.get('utm_medium'),
                    utm_campaign=referral_data_dict.get('utm_campaign'),
                    utm_content=referral_data_dict.get('utm_content'),
                    utm_term=referral_data_dict.get('utm_term'),
                    referral_code=referral_data_dict.get('referral_code'),
                    discount_percentage=referral_data_dict.get('discount_percentage', 0.0),
                    bonus_credits=referral_data_dict.get('bonus_credits', 0)
                )

                # Track the conversion
                referral_service = ReferralService()
                referral_service.track_referral_conversion(
                    referral_data, user_id, subscription_id, original_amount
                )
                logger.info(f"Referral conversion tracked for user {user_id}")
        except Exception as e:
            logger.error(f"Error tracking referral conversion: {e}")

        return jsonify({
            'success': True,
            'subscription_id': subscription_result['subscription_id'],
            'client_secret': subscription_result.get('client_secret'),  # For Stripe
            'message': 'Subscription created successfully'
        })
        
    except Exception as e:
        logger.error(f"Error creating subscription: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to create subscription'
        }), 500

@payment_bp.route('/cancel-subscription', methods=['POST'])
@require_auth()
@require_subscription('plus')
def cancel_subscription():
    """Cancel user's current subscription"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401
        
        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)
        
        if not user_subscription:
            return jsonify({'error': 'No active subscription found'}), 404
        
        # Create payment gateway
        gateway = PaymentGatewayFactory.create_gateway(user_subscription.payment_gateway)
        
        # Cancel subscription in payment gateway
        cancel_result = gateway.cancel_subscription(user_subscription.gateway_subscription_id)
        
        if not cancel_result['success']:
            return jsonify({
                'success': False,
                'error': cancel_result.get('error', 'Failed to cancel subscription')
            }), 400
        
        # Update subscription in database
        subscription_service.supabase.table('user_subscriptions').update({
            'cancel_at_period_end': True,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('id', user_subscription.id).execute()
        
        return jsonify({
            'success': True,
            'message': 'Subscription will be cancelled at the end of the current billing period'
        })

    except Exception as e:
        logger.error(f"Error cancelling subscription: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to cancel subscription'
        }), 500

@payment_bp.route('/subscription-details', methods=['GET'])
@require_auth()
def get_subscription_details():
    """Get detailed subscription information"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)

        if not user_subscription:
            return jsonify({
                'success': True,
                'subscription': None
            })

        # Get plan details
        plan_response = subscription_service.supabase.table('subscription_plans').select('*').eq('id', user_subscription.plan_id).single().execute()
        plan_name = plan_response.data['plan_name'] if plan_response.data else 'Unknown'

        return jsonify({
            'success': True,
            'subscription': {
                'id': user_subscription.id,
                'plan_name': plan_name,
                'status': user_subscription.status,
                'payment_gateway': user_subscription.payment_gateway,
                'current_period_start': user_subscription.current_period_start.isoformat(),
                'current_period_end': user_subscription.current_period_end.isoformat(),
                'cancel_at_period_end': user_subscription.cancel_at_period_end
            }
        })

    except Exception as e:
        logger.error(f"Error getting subscription details: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get subscription details'
        }), 500

@payment_bp.route('/history', methods=['GET'])
@require_auth()
def get_payment_history():
    """Get user's payment history"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        subscription_service = SubscriptionService()

        # Get payment history from Supabase (using payment_transactions table)
        payments_response = subscription_service.supabase.table('payment_transactions').select('*').eq('user_id', user_id).order('created_at', desc=True).execute()

        payments = []
        if payments_response.data:
            for payment in payments_response.data:
                payments.append({
                    'id': payment['id'],
                    'amount': payment['amount'],
                    'currency': payment.get('currency', 'USD'),
                    'status': payment['status'],
                    'description': payment.get('description', 'Subscription payment'),
                    'created_at': payment['created_at'],
                    'invoice_url': payment.get('invoice_url')
                })

        return jsonify({
            'success': True,
            'payments': payments
        })

    except Exception as e:
        logger.error(f"Error getting payment history: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get payment history'
        }), 500

@payment_bp.route('/payment-method', methods=['GET'])
@require_auth()
def get_payment_method():
    """Get user's current payment method"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)

        if not user_subscription or not user_subscription.gateway_customer_id:
            return jsonify({
                'success': True,
                'payment_method': None
            })

        # Get payment method from gateway
        gateway = PaymentGatewayFactory.create_gateway(user_subscription.payment_gateway)
        payment_method = gateway.get_customer_payment_method(user_subscription.gateway_customer_id)

        return jsonify({
            'success': True,
            'payment_method': payment_method
        })

    except Exception as e:
        logger.error(f"Error getting payment method: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get payment method'
        }), 500

@payment_bp.route('/billing-address', methods=['GET'])
@require_auth()
def get_billing_address():
    """Get user's billing address"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        subscription_service = SubscriptionService()

        # Get billing address from Supabase (with fallback for missing table)
        address = None
        try:
            address_response = subscription_service.supabase.table('billing_addresses').select('*').eq('user_id', user_id).single().execute()

            if address_response.data:
                address = {
                    'name': address_response.data.get('name'),
                    'email': address_response.data.get('email'),
                    'address': address_response.data.get('address'),
                    'city': address_response.data.get('city'),
                    'country': address_response.data.get('country')
                }
        except Exception as e:
            logger.warning(f"Billing addresses table not available: {str(e)}")
            # Return empty address for now
            address = None

        return jsonify({
            'success': True,
            'address': address
        })

    except Exception as e:
        logger.error(f"Error getting billing address: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get billing address'
        }), 500

@payment_bp.route('/update-billing-address', methods=['POST'])
@require_auth()
def update_billing_address():
    """Update user's billing address"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        data = request.get_json()

        subscription_service = SubscriptionService()

        # Update or insert billing address
        address_data = {
            'user_id': user_id,
            'name': data.get('name'),
            'email': data.get('email'),
            'address': data.get('address'),
            'city': data.get('city'),
            'country': data.get('country'),
            'updated_at': datetime.utcnow().isoformat()
        }

        # Try to update existing address first (with fallback for missing table)
        try:
            existing_response = subscription_service.supabase.table('billing_addresses').select('id').eq('user_id', user_id).execute()

            if existing_response.data:
                # Update existing
                subscription_service.supabase.table('billing_addresses').update(address_data).eq('user_id', user_id).execute()
            else:
                # Insert new
                address_data['created_at'] = datetime.utcnow().isoformat()
                subscription_service.supabase.table('billing_addresses').insert(address_data).execute()
        except Exception as e:
            logger.warning(f"Billing addresses table not available: {str(e)}")
            # For now, just return success without storing
            pass

        return jsonify({
            'success': True,
            'message': 'Billing address updated successfully'
        })

    except Exception as e:
        logger.error(f"Error updating billing address: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to update billing address'
        }), 500

@payment_bp.route('/update-payment-method-url', methods=['POST'])
@require_auth()
def get_update_payment_method_url():
    """Get URL for updating payment method"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)

        if not user_subscription:
            return jsonify({
                'success': False,
                'error': 'No active subscription found'
            }), 400

        # Get update URL from payment gateway
        gateway = PaymentGatewayFactory.create_gateway(user_subscription.payment_gateway)
        update_url = gateway.get_payment_method_update_url(user_subscription.gateway_customer_id)

        return jsonify({
            'success': True,
            'update_url': update_url
        })

    except Exception as e:
        logger.error(f"Error getting payment method update URL: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get payment method update URL'
        }), 500

@payment_bp.route('/switch-plan', methods=['POST'])
@require_auth()
def switch_plan():
    """Switch user's subscription plan"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        data = request.get_json()
        new_plan_name = data.get('plan_name')

        if not new_plan_name:
            return jsonify({
                'success': False,
                'error': 'Plan name is required'
            }), 400

        subscription_service = SubscriptionService()

        # Handle downgrade to free plan
        if new_plan_name == 'free':
            result = subscription_service.cancel_subscription(user_id)
            if result:
                return jsonify({
                    'success': True,
                    'message': 'Subscription cancelled. You will be moved to the free plan at the end of your billing period.'
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to cancel subscription'
                }), 500

        # Handle upgrade/change to paid plan
        user_subscription = subscription_service.get_user_subscription(user_id)

        if not user_subscription:
            return jsonify({
                'success': False,
                'error': 'No active subscription found. Please subscribe to a plan first.'
            }), 400

        # Get new plan details
        plan_response = subscription_service.supabase.table('subscription_plans').select('*').eq('plan_name', new_plan_name).single().execute()

        if not plan_response.data:
            return jsonify({
                'success': False,
                'error': 'Invalid plan name'
            }), 400

        new_plan = plan_response.data

        # Update subscription with new plan
        gateway = PaymentGatewayFactory.create_gateway(user_subscription.payment_gateway)
        success = gateway.update_subscription_plan(
            user_subscription.gateway_subscription_id,
            new_plan['gateway_plan_id']
        )

        if success:
            # Update local subscription record
            subscription_service.supabase.table('user_subscriptions').update({
                'plan_id': new_plan['id'],
                'updated_at': datetime.utcnow().isoformat()
            }).eq('user_id', user_id).execute()

            return jsonify({
                'success': True,
                'message': f'Successfully switched to {new_plan_name} plan'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update subscription plan'
            }), 500

    except Exception as e:
        logger.error(f"Error switching plan: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to switch plan'
        }), 500

# Page Routes
@payment_bp.route('/payment-history')
@require_auth()
def payment_history_page():
    """Payment history page - only accessible to subscribed users"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return redirect('/login')

        # Check if user has an active subscription
        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)

        # Allow access for subscribed users or admin
        user_email = session.get('user_email', '')
        is_admin = user_email == '<EMAIL>'

        if not user_subscription and not is_admin:
            flash('Payment history is only available for subscribed users.', 'warning')
            return redirect('/pricing')

        return render_template('payment-history.html')

    except Exception as e:
        logger.error(f"Error loading payment history page: {str(e)}")
        flash('Error loading payment history page', 'error')
        return redirect('/pricing')

@payment_bp.route('/billing-info')
@require_auth()
def billing_info_page():
    """Billing information page - only accessible to subscribed users"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return redirect('/login')

        # Check if user has an active subscription
        subscription_service = SubscriptionService()
        user_subscription = subscription_service.get_user_subscription(user_id)

        # Allow access for subscribed users or admin
        user_email = session.get('user_email', '')
        is_admin = user_email == '<EMAIL>'

        if not user_subscription and not is_admin:
            flash('Billing information is only available for subscribed users.', 'warning')
            return redirect('/pricing')

        return render_template('billing-info.html')

    except Exception as e:
        logger.error(f"Error loading billing info page: {str(e)}")
        flash('Error loading billing info page', 'error')
        return redirect('/pricing')

@payment_bp.route('/webhook/<provider>', methods=['POST'])
def handle_webhook(provider):
    """Handle payment gateway webhooks"""
    try:
        payload = request.get_data(as_text=True)
        signature = request.headers.get('X-Paystack-Signature') if provider == 'paystack' else request.headers.get('Stripe-Signature')
        
        if not signature:
            return jsonify({'error': 'Missing signature'}), 400
        
        # Create gateway and verify webhook
        gateway = PaymentGatewayFactory.create_gateway(provider)
        
        if not gateway.verify_webhook(payload, signature):
            return jsonify({'error': 'Invalid signature'}), 400
        
        # Parse event data
        event_data = json.loads(payload)
        
        # Handle webhook
        result = gateway.handle_webhook(event_data)
        
        if result['success']:
            # Update database based on webhook event
            _process_webhook_event(result, provider)
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"Error handling {provider} webhook: {str(e)}")
        return jsonify({'error': 'Webhook processing failed'}), 500

def _process_webhook_event(webhook_result, provider):
    """Process webhook events and update database"""
    try:
        action = webhook_result.get('action')
        data = webhook_result.get('data', {})

        subscription_service = SubscriptionService()

        if action == 'subscription_created':
            # Handle subscription activation
            logger.info(f"Processing subscription creation via {provider}")
        elif action == 'subscription_cancelled':
            # Handle subscription cancellation
            logger.info(f"Processing subscription cancellation via {provider}")
        elif action == 'payment_failed':
            # Handle payment failure
            logger.info(f"Processing payment failure via {provider}")
        elif action == 'payment_succeeded':
            # Handle successful payment
            logger.info(f"Processing payment success via {provider}")

    except Exception as e:
        logger.error(f"Error processing webhook event: {str(e)}")

@payment_bp.route('/upgrade-plan', methods=['POST'])
@require_auth()
def upgrade_plan():
    """Upgrade user's current plan"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401
        
        data = request.get_json()
        new_plan_id = data.get('plan_id')
        
        if not new_plan_id:
            return jsonify({'error': 'New plan ID is required'}), 400
        
        subscription_service = SubscriptionService()
        current_subscription = subscription_service.get_user_subscription(user_id)
        
        if not current_subscription:
            return jsonify({'error': 'No current subscription found'}), 404
        
        # Update subscription plan
        subscription_service.supabase.table('user_subscriptions').update({
            'plan_id': new_plan_id,
            'updated_at': datetime.utcnow().isoformat()
        }).eq('id', current_subscription.id).execute()
        
        return jsonify({
            'success': True,
            'message': 'Plan upgraded successfully'
        })
        
    except Exception as e:
        logger.error(f"Error upgrading plan: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to upgrade plan'
        }), 500

@payment_bp.route('/callback', methods=['GET'])
def payment_callback():
    """Handle payment callback from Paystack"""
    try:
        # Get payment reference from query parameters
        reference = request.args.get('reference')

        if not reference:
            return jsonify({'error': 'Payment reference not provided'}), 400

        # Verify payment with Paystack
        gateway = PaymentGatewayFactory.create_gateway('paystack')

        if hasattr(gateway, 'verify_payment'):
            verification_result = gateway.verify_payment(reference)

            if verification_result.get('success'):
                # Payment successful - create subscription
                payment_data = verification_result.get('data', {})

                # Extract metadata
                metadata = payment_data.get('metadata', {})
                plan_name = metadata.get('plan_name')
                billing_cycle = metadata.get('billing_cycle')

                if plan_name and billing_cycle:
                    # Create subscription record
                    # This would typically be done in a webhook, but for simplicity we'll do it here
                    logger.info(f"Payment verified for plan {plan_name}, cycle {billing_cycle}")

                    # Send invoice email
                    try:
                        customer_email = payment_data.get('customer', {}).get('email')
                        if customer_email:
                            invoice_data = {
                                'customer_email': customer_email,
                                'amount': payment_data.get('amount', 0) / 100,  # Convert from kobo
                                'currency': 'NGN',
                                'plan_name': plan_name,
                                'billing_cycle': billing_cycle,
                                'reference': reference,
                                'payment_date': datetime.now().strftime('%B %d, %Y')
                            }

                            email_result = invoice_email_service.send_invoice_email(invoice_data)
                            if email_result['success']:
                                logger.info(f"Invoice email sent to {customer_email}")
                    except Exception as e:
                        logger.error(f"Invoice email error: {str(e)}")

                    return f"""
                    <html>
                    <head><title>Payment Successful</title></head>
                    <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                        <h1 style="color: green;">Payment Successful!</h1>
                        <p>Your subscription to {plan_name.title()} Plan has been activated.</p>
                        <p>You will be redirected to your dashboard in 3 seconds...</p>
                        <script>
                            setTimeout(function() {{
                                window.location.href = '/';
                            }}, 3000);
                        </script>
                    </body>
                    </html>
                    """
                else:
                    return jsonify({'error': 'Invalid payment metadata'}), 400
            else:
                return f"""
                <html>
                <head><title>Payment Failed</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1 style="color: red;">Payment Failed</h1>
                    <p>Your payment could not be processed. Please try again.</p>
                    <a href="/pricing" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Again</a>
                </body>
                </html>
                """
        else:
            # Test mode - simulate successful payment
            return f"""
            <html>
            <head><title>Payment Successful (Test Mode)</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1 style="color: green;">Payment Successful! (Test Mode)</h1>
                <p>Your test payment has been processed successfully.</p>
                <p>In production, this would activate your subscription.</p>
                <p>You will be redirected to your dashboard in 3 seconds...</p>
                <script>
                    setTimeout(function() {{
                        window.location.href = '/';
                    }}, 3000);
                </script>
            </body>
            </html>
            """

    except Exception as e:
        logger.error(f"Payment callback error: {str(e)}")
        return f"""
        <html>
        <head><title>Payment Error</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: red;">Payment Error</h1>
            <p>An error occurred while processing your payment.</p>
            <a href="/pricing" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Again</a>
        </body>
        </html>
        """

@payment_bp.route('/billing-history', methods=['GET'])
@require_auth()
def get_billing_history():
    """Get user's billing history"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'User not authenticated'}), 401

        subscription_service = SubscriptionService()

        # Get payment transactions
        transactions_response = subscription_service.supabase.table('payment_transactions').select('*').eq('user_id', user_id).order('created_at', desc=True).execute()

        transactions = []
        for transaction in transactions_response.data:
            transactions.append({
                'id': transaction['id'],
                'amount': float(transaction['amount']),
                'currency': transaction['currency'],
                'status': transaction['status'],
                'payment_method': transaction.get('payment_method'),
                'created_at': transaction['created_at']
            })

        return jsonify({
            'success': True,
            'transactions': transactions
        })

    except Exception as e:
        logger.error(f"Error fetching billing history: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch billing history'
        }), 500

# Second Apple Pay process route removed - Using Paystack only

@payment_bp.route('/webhook/paystack', methods=['POST'])
def paystack_webhook():
    """Handle Paystack webhook events"""
    try:
        # Get the raw payload
        payload = request.get_data()
        signature = request.headers.get('X-Paystack-Signature')

        # Verify webhook signature
        webhook_secret = os.getenv('PAYSTACK_WEBHOOK_SECRET')
        if webhook_secret and webhook_secret != 'your_paystack_webhook_secret_here':
            import hmac
            import hashlib

            expected_signature = hmac.new(
                webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha512
            ).hexdigest()

            if signature != expected_signature:
                logger.warning("Invalid webhook signature")
                return jsonify({'error': 'Invalid signature'}), 400

        # Parse the event
        event = request.get_json()
        event_type = event.get('event')

        logger.info(f"Received Paystack webhook: {event_type}")

        if event_type == 'charge.success':
            # Handle successful payment
            data = event.get('data', {})
            reference = data.get('reference')
            amount = data.get('amount')  # Amount in kobo
            customer_email = data.get('customer', {}).get('email')
            metadata = data.get('metadata', {})

            logger.info(f"Payment successful: {reference}, Amount: ₦{amount/100}, Email: {customer_email}")

            # Extract plan information from metadata
            plan_name = metadata.get('plan_name', 'Unknown')
            billing_cycle = metadata.get('billing_cycle', 'monthly')

            # Prepare invoice data
            invoice_data = {
                'customer_email': customer_email,
                'amount': amount / 100,  # Convert from kobo to naira
                'currency': 'NGN',
                'plan_name': plan_name,
                'billing_cycle': billing_cycle,
                'reference': reference,
                'payment_date': datetime.now().strftime('%B %d, %Y')
            }

            # Send invoice email
            try:
                email_result = invoice_email_service.send_invoice_email(invoice_data)
                if email_result['success']:
                    logger.info(f"Invoice email sent to {customer_email}")
                else:
                    logger.error(f"Failed to send invoice email: {email_result.get('error')}")
            except Exception as e:
                logger.error(f"Invoice email error: {str(e)}")

            # Here you would also:
            # 1. Find the user by email
            # 2. Activate their subscription
            # 3. Update database records

            return jsonify({'status': 'success'}), 200

        elif event_type == 'subscription.create':
            # Handle subscription creation
            data = event.get('data', {})
            subscription_code = data.get('subscription_code')
            customer_email = data.get('customer', {}).get('email')

            logger.info(f"Subscription created: {subscription_code}, Email: {customer_email}")

            return jsonify({'status': 'success'}), 200

        else:
            logger.info(f"Unhandled webhook event: {event_type}")
            return jsonify({'status': 'ignored'}), 200

    except Exception as e:
        logger.error(f"Webhook error: {str(e)}")
        return jsonify({'error': 'Webhook processing failed'}), 500
