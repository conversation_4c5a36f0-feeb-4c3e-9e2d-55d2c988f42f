/**
 * Document Display Styles
 * Styles for displaying generated documents in the task summary
 */

/* Document container */
.document-container {
    border: 1px solid #333;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
    background-color: #1e1e1e;
}

/* Document header */
.document-header {
    background-color: #2d2d2d;
    padding: 1rem;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #e0e0e0;
    margin: 0;
}

.document-type-badge {
    background-color: #333;
    color: #ccc;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

/* Document content */
.document-content {
    padding: 1.5rem;
    font-family: 'Georgia', serif;
    line-height: 1.6;
    color: #e0e0e0;
}

.document-content h1 {
    font-size: 1.875rem;
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #e0e0e0;
    font-weight: 700;
}

.document-content h2 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #d0d0d0;
    font-weight: 600;
}

.document-content h3 {
    font-size: 1.25rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #c0c0c0;
    font-weight: 600;
}

.document-content p {
    margin-bottom: 1rem;
}

.document-content ul,
.document-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.document-content li {
    margin-bottom: 0.5rem;
}

.document-content blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin-left: 0;
    color: #4b5563;
    font-style: italic;
}

/* Document preview */
.document-preview {
    padding: 1rem;
    background-color: #2d2d2d;
    border-top: 1px solid #333;
    text-align: center;
}

.document-preview img {
    max-width: 100%;
    max-height: 300px;
    border: 1px solid #444;
    border-radius: 0.25rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

/* Document actions */
.document-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #2d2d2d;
    border-top: 1px solid #333;
}

.document-action-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #333;
    color: #e0e0e0;
    border: 1px solid #444;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.document-action-button:hover {
    background-color: #444;
    color: #ffffff;
}

.document-action-button svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

/* Document analysis */
.document-analysis {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background-color: #2d2d2d;
    border: 1px solid #444;
    border-radius: 0.5rem;
}

.document-analysis h3 {
    font-size: 1.25rem;
    margin-top: 0;
    margin-bottom: 1rem;
    color: #e0e0e0;
    font-weight: 600;
}

.document-metrics {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.document-metric {
    background-color: #333;
    padding: 0.75rem 1rem;
    border: 1px solid #444;
    border-radius: 0.375rem;
    min-width: 150px;
}

.document-metric-label {
    font-size: 0.75rem;
    color: #aaa;
    margin-bottom: 0.25rem;
}

.document-metric-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #e0e0e0;
}

.document-suggestions {
    background-color: #333;
    padding: 1rem;
    border: 1px solid #444;
    border-radius: 0.375rem;
}

.document-suggestion {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
    color: #ccc;
}

.document-suggestion:before {
    content: "•";
    position: absolute;
    left: 0.5rem;
    color: #aaa;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .document-metrics {
        flex-direction: column;
    }

    .document-metric {
        width: 100%;
    }
}
