/* Black Theme for Agen911 Super Agent */

/* Global styles */
body {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Background for all pages */
.bg-gray-50 {
    background-color: #000000 !important;
}

/* Main content background */
#main-content,
main {
    background-color: #000000 !important;
}

/* Fix main content area */
.max-w-7xl.mx-auto.px-4.sm\:px-6.lg\:px-8.py-8 {
    background-color: #000000 !important;
}

/* Header background */
.bg-white.border-b.border-gray-200.shadow-sm.sticky.top-0.z-10 {
    background-color: #000000 !important;
    border-color: #333333 !important;
}

/* Header text color */
.text-base.font-medium.text-gray-700 {
    color: #ffffff !important;
}

/* Footer background */
footer {
    background-color: #000000 !important;
    color: #ffffff !important;
}

/* Footer text color */
footer p {
    color: #ffffff !important;
}

/* Content container background */
.bg-white.shadow-sm.rounded-lg.border.border-gray-200.overflow-hidden,
.bg-white {
    background-color: #111111 !important;
    border-color: #333333 !important;
}

/* Fix any remaining white backgrounds */
div[class*="bg-white"],
section[class*="bg-white"],
article[class*="bg-white"],
aside[class*="bg-white"],
header[class*="bg-white"],
footer[class*="bg-white"],
nav[class*="bg-white"],
main[class*="bg-white"] {
    background-color: #111111 !important;
}

/* Fix any elements with inline background-color style */
[style*="background-color: white"],
[style*="background-color:#fff"],
[style*="background-color: #fff"],
[style*="background-color:#ffffff"],
[style*="background-color: #ffffff"] {
    background-color: #111111 !important;
}

/* Text colors */
.text-gray-700, .text-gray-600, .text-gray-900 {
    color: #ffffff !important;
}

.text-gray-500 {
    color: #aaaaaa !important;
}

/* Border colors */
.border-gray-200 {
    border-color: #333333 !important;
}

/* Card backgrounds */
.bg-white {
    background-color: #111111 !important;
}

/* Card header backgrounds */
.bg-gray-50 {
    background-color: #222222 !important;
}

/* Sidebar background */
#sidebar, #collapsed-sidebar {
    background-color: #111111 !important;
    border-color: #333333 !important;
}

/* Sidebar text */
#sidebar a, #collapsed-sidebar a {
    color: #ffffff !important;
}

/* Sidebar active item */
#sidebar .bg-gray-100, #collapsed-sidebar .bg-gray-100 {
    background-color: #222222 !important;
}

/* Sidebar hover */
#sidebar a:hover, #collapsed-sidebar a:hover {
    background-color: #222222 !important;
}

/* Sidebar icons */
#sidebar svg, #collapsed-sidebar svg {
    color: #ffffff !important;
}

/* Sidebar border */
#sidebar .border-b, #collapsed-sidebar .border-b {
    border-color: #333333 !important;
}

/* Button colors */
.bg-black, .hover\:bg-gray-800:hover {
    background-color: #222222 !important;
}

/* Button hover */
.hover\:bg-gray-800:hover {
    background-color: #333333 !important;
}

/* Mobile menu button */
#mobile-menu-button {
    background-color: #111111 !important;
}

#mobile-menu-button button {
    color: #ffffff !important;
}

/* Search box */
.search-box, textarea, input[type="text"], select {
    background-color: #222222 !important;
    color: #ffffff !important;
    border-color: #333333 !important;
}

.search-box::placeholder, textarea::placeholder, input::placeholder {
    color: #aaaaaa !important;
}

/* Super Agent specific styles */
.tab-button {
    color: #aaaaaa !important;
}

.tab-button.active, .tab-button[data-tab].border-black {
    color: #ffffff !important;
    border-color: #ffffff !important;
}

.tab-button:hover {
    color: #ffffff !important;
    border-color: #444444 !important;
}

/* Task progress and summary containers */
#taskProgress, #thinkingProcess, #taskResults {
    background-color: #111111 !important;
    border-color: #333333 !important;
}

/* Thinking process container */
.thinking-container {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
}

.thinking-process {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
}

/* Summary container */
.summary-container {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
}

.summary-container-header {
    background-color: #222222 !important;
    border-color: #333333 !important;
}

.summary-container-content {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
    color: #e0e0e0 !important;
}

/* Modal styles */
#summaryModal .bg-white {
    background-color: #111111 !important;
}

#summaryModal .bg-gray-50 {
    background-color: #222222 !important;
}

/* Processing indicator */
#processingIndicator .animate-spin {
    border-color: #333333 !important;
    border-top-color: transparent !important;
    background-color: #444444 !important;
}

/* Code editor and preview */
#codeEditor, pre {
    background-color: #1a1a1a !important;
    color: #e0e0e0 !important;
    border-color: #333333 !important;
}

/* File tabs */
.file-tab {
    background-color: #222222 !important;
    color: #aaaaaa !important;
    border-color: #333333 !important;
}

.file-tab.active {
    background-color: #333333 !important;
    color: #ffffff !important;
}

/* Fix prose content in task summaries */
.prose p, .prose li, .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #e0e0e0 !important;
}

.prose a {
    color: #3b82f6 !important;
}

.prose code {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
}

.prose pre {
    background-color: #1a1a1a !important;
}

/* Fix checkboxes and radio buttons */
input[type="checkbox"], input[type="radio"] {
    background-color: #222222 !important;
    border-color: #444444 !important;
}

/* Fix focus rings */
.focus\:ring-black:focus {
    --tw-ring-color: rgba(255, 255, 255, 0.3) !important;
}

/* Fix hover states for buttons */
.hover\:bg-gray-100:hover {
    background-color: #333333 !important;
}

.hover\:text-black:hover {
    color: #ffffff !important;
}

/* Fix advanced options section */
.space-y-4.mb-6.bg-gray-50.p-4.rounded-lg.border.border-gray-200 {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
}

/* Fix intro section */
.mb-6.bg-gradient-to-r.from-gray-50.to-gray-100.p-4.rounded-lg.border-l-4.border-black.shadow-sm {
    background: #1a1a1a !important;
    border-left-color: #444444 !important;
}

/* Fix disabled buttons */
button[disabled], .cursor-not-allowed {
    background-color: #2a2a2a !important;
    color: #666666 !important;
    border-color: #444444 !important;
}

/* Chat page styles */
.chat-page {
    background-color: #111111 !important;
    color: #e0e0e0 !important;
}

.chat-description {
    color: #aaaaaa !important;
}

.chat-interface {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
}

.chat-messages {
    background-color: #1a1a1a !important;
}

.message.user {
    background-color: #333333 !important;
    color: #ffffff !important;
}

.message.ai {
    background-color: #222222 !important;
    color: #e0e0e0 !important;
    border-color: #333333 !important;
}

.welcome-message {
    border-left-color: #444444 !important;
}

.chat-input-container {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
}

.send-button {
    background-color: #333333 !important;
    color: #ffffff !important;
    border-color: #444444 !important;
}

.send-button:hover {
    background-color: #444444 !important;
}

.chat-tips {
    background-color: #222222 !important;
    color: #ffffff !important;
    border-color: #333333 !important;
}

.chat-tips h3 {
    color: #ffffff !important;
}

.chat-tips li {
    color: #aaaaaa !important;
}

/* Search page styles */
.search-page {
    background-color: #111111 !important;
    color: #e0e0e0 !important;
}

.search-description {
    color: #aaaaaa !important;
}

.search-container {
    background-color: #111111 !important;
}

.search-form {
    background-color: #111111 !important;
}

.search-box {
    background-color: #222222 !important;
    color: #ffffff !important;
    border-color: #333333 !important;
}

.search-button {
    background-color: #333333 !important;
    color: #ffffff !important;
    border-color: #444444 !important;
}

.search-button:hover {
    background-color: #444444 !important;
}

.search-tips {
    background-color: #222222 !important;
    color: #ffffff !important;
    border-color: #333333 !important;
}

.search-tips h3 {
    color: #ffffff !important;
}

.search-tips li {
    color: #aaaaaa !important;
}

.error-message {
    background-color: #331111 !important;
    color: #ff6666 !important;
    border-color: #662222 !important;
}

/* Home page styles */
.hero-section {
    background-color: #111111 !important;
    color: #ffffff !important;
}

.hero-section h2 {
    color: #ffffff !important;
}

.subtitle {
    color: #aaaaaa !important;
}

.feature-card {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.feature-card:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2) !important;
}

.card-header h3 {
    color: #ffffff !important;
}

.card-description {
    color: #aaaaaa !important;
}

.activity-item {
    color: #e0e0e0 !important;
}

.activity-item i {
    color: #aaaaaa !important;
}

.activity-time {
    color: #888888 !important;
}

.feature-link {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #444444 !important;
}

.feature-link:hover {
    background-color: #444444 !important;
    color: #ffffff !important;
}

/* TARGETED FIXES FOR SPECIFIC TOOL OUTPUTS ONLY */
/* These styles ONLY target the output areas for Email Campaign Manager, SEO Content Optimizer, and Learning Path Generator */

/* Fix Learning Path Generator output - target the actual container class */
.learning-path-container .module-card,
.learning-path-container .resource-item,
.learning-path-container .stat-card,
.learning-path-container .progress-card,
.learning-path-container .badge-card {
    background: #111111 !important;
    color: #ffffff !important;
    border-left-color: #6f42c1 !important;
}

.learning-path-container .concept-tag {
    background: #333333 !important;
    color: #ffffff !important;
}

.learning-path-container h1,
.learning-path-container h2,
.learning-path-container h3,
.learning-path-container h4 {
    color: #ffffff !important;
}

.learning-path-container .module-details {
    color: #aaaaaa !important;
}

.learning-path-container .progress-text,
.learning-path-container .badge-requirement {
    color: #aaaaaa !important;
}

/* Fix Email Campaign Manager output - target the actual container class */
.email-campaign-container .subject-card,
.email-campaign-container .metric-card,
.email-campaign-container .test-card,
.email-campaign-container .send-time-card,
.email-campaign-container .overview-item {
    background: #111111 !important;
    color: #ffffff !important;
    border-left-color: #007bff !important;
}

.email-campaign-container .email-preview {
    background: #222222 !important;
    color: #ffffff !important;
    border-color: #444444 !important;
}

/* Fix all content inside email preview */
.email-campaign-container .email-preview *,
.email-campaign-container .email-preview div,
.email-campaign-container .email-preview p,
.email-campaign-container .email-preview h1,
.email-campaign-container .email-preview h2,
.email-campaign-container .email-preview h3,
.email-campaign-container .email-preview h4,
.email-campaign-container .email-preview span,
.email-campaign-container .email-preview section,
.email-campaign-container .email-preview article {
    background: transparent !important;
    color: #ffffff !important;
}

/* Fix any inline styles in email content */
.email-campaign-container .email-preview [style*="background"],
.email-campaign-container .email-preview [style*="color"] {
    background: transparent !important;
    color: #ffffff !important;
}

.email-campaign-container h1,
.email-campaign-container h2,
.email-campaign-container h3,
.email-campaign-container h4 {
    color: #ffffff !important;
}

.email-campaign-container .subject-text {
    color: #ffffff !important;
}

.email-campaign-container .subject-stats,
.email-campaign-container .content-stats {
    color: #aaaaaa !important;
}

.email-campaign-container .metric-value {
    color: #007bff !important;
}

/* Fix any remaining white elements in email campaign */
.email-campaign-container .overview-grid > div,
.email-campaign-container .subject-grid > div,
.email-campaign-container .metrics-grid > div,
.email-campaign-container .testing-grid > div {
    background: #111111 !important;
    color: #ffffff !important;
    padding: 15px !important;
    border-radius: 8px !important;
    border-left: 4px solid #007bff !important;
}

/* Fix SEO Content Optimizer output - target the actual container class */
.seo-optimization-container .analysis-card,
.seo-optimization-container .meta-card,
.seo-optimization-container .technical-card,
.seo-optimization-container .recommendation-card,
.seo-optimization-container .overview-item {
    background: #111111 !important;
    color: #ffffff !important;
    border-left-color: #28a745 !important;
}

.seo-optimization-container .content-preview {
    background: #222222 !important;
    color: #ffffff !important;
    border-color: #444444 !important;
}

.seo-optimization-container .meta-content {
    background: #333333 !important;
    color: #ffffff !important;
}

.seo-optimization-container h1,
.seo-optimization-container h2,
.seo-optimization-container h3,
.seo-optimization-container h4 {
    color: #ffffff !important;
}

.seo-optimization-container .metric-value {
    color: #28a745 !important;
}

.seo-optimization-container .metric-change,
.seo-optimization-container .metric-level {
    color: #aaaaaa !important;
}

/* Fix any remaining white elements in SEO optimization */
.seo-optimization-container .overview-grid > div,
.seo-optimization-container .analysis-grid > div,
.seo-optimization-container .meta-grid > div,
.seo-optimization-container .technical-grid > div {
    background: #111111 !important;
    color: #ffffff !important;
    padding: 15px !important;
    border-radius: 8px !important;
    border-left: 4px solid #28a745 !important;
}

/* Dark scrollbar styles for all pages */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: #444444;
    border-radius: 6px;
    border: 2px solid #1a1a1a;
}

::-webkit-scrollbar-thumb:hover {
    background: #555555;
}

::-webkit-scrollbar-corner {
    background: #1a1a1a;
}

/* Firefox */
html {
    scrollbar-width: thin;
    scrollbar-color: #444444 #1a1a1a;
}

/* Ensure dark scrollbars on all containers */
body, html, div, section, article, aside, main, nav, header, footer {
    scrollbar-width: thin;
    scrollbar-color: #444444 #1a1a1a;
}

/* Specific dark scrollbar for text areas and content containers */
textarea, .chat-messages, .search-results, .task-results, .thinking-process,
.summary-container, .code-editor, pre, .research-results, .autowave-content {
    scrollbar-width: thin;
    scrollbar-color: #444444 #1a1a1a;
}

textarea::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar,
.search-results::-webkit-scrollbar,
.task-results::-webkit-scrollbar,
.thinking-process::-webkit-scrollbar,
.summary-container::-webkit-scrollbar,
.code-editor::-webkit-scrollbar,
pre::-webkit-scrollbar,
.research-results::-webkit-scrollbar,
.autowave-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

textarea::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track,
.search-results::-webkit-scrollbar-track,
.task-results::-webkit-scrollbar-track,
.thinking-process::-webkit-scrollbar-track,
.summary-container::-webkit-scrollbar-track,
.code-editor::-webkit-scrollbar-track,
pre::-webkit-scrollbar-track,
.research-results::-webkit-scrollbar-track,
.autowave-content::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

textarea::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb,
.search-results::-webkit-scrollbar-thumb,
.task-results::-webkit-scrollbar-thumb,
.thinking-process::-webkit-scrollbar-thumb,
.summary-container::-webkit-scrollbar-thumb,
.code-editor::-webkit-scrollbar-thumb,
pre::-webkit-scrollbar-thumb,
.research-results::-webkit-scrollbar-thumb,
.autowave-content::-webkit-scrollbar-thumb {
    background: #444444;
    border-radius: 4px;
    border: 1px solid #1a1a1a;
}

textarea::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover,
.search-results::-webkit-scrollbar-thumb:hover,
.task-results::-webkit-scrollbar-thumb:hover,
.thinking-process::-webkit-scrollbar-thumb:hover,
.summary-container::-webkit-scrollbar-thumb:hover,
.code-editor::-webkit-scrollbar-thumb:hover,
pre::-webkit-scrollbar-thumb:hover,
.research-results::-webkit-scrollbar-thumb:hover,
.autowave-content::-webkit-scrollbar-thumb:hover {
    background: #555555;
}
