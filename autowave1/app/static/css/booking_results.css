/* Booking Results Styling */

/* Main container */
.booking-results {
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 100%;
    margin: 0 auto;
    overflow: visible;
    position: relative;
    letter-spacing: 0.01em;
}

/* Price comparison section */
.price-comparison {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #3b82f6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.price-comparison h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: #1e3a8a;
}

.price-range {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 0.5rem;
}

.providers-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.provider-tag {
    background-color: #e0e7ff;
    color: #4338ca;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Best value and lowest price options */
.best-value-option, .lowest-price-option {
    background-color: #ffffff;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.best-value-option {
    border-left: 4px solid #10b981;
}

.lowest-price-option {
    border-left: 4px solid #f59e0b;
}

.option-label {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 0.75rem;
}

.best-value-label {
    background-color: #d1fae5;
    color: #047857;
}

.lowest-price-label {
    background-color: #fef3c7;
    color: #b45309;
}

/* Option details */
.option-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 600;
    color: #4b5563;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-size: 1.125rem;
    color: #111827;
}

.price-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
}

/* All options section */
.all-options {
    margin-top: 2rem;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.option-card {
    background-color: #ffffff;
    border-radius: 0.5rem;
    padding: 1.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.option-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.option-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.option-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
}

.option-card-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
}

.option-card-details {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #4b5563;
}

.option-card-detail {
    margin-bottom: 0.5rem;
    display: flex;
}

.option-card-detail-icon {
    margin-right: 0.5rem;
    color: #6b7280;
}

/* Booking links section */
.booking-links {
    margin-top: 2rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.25rem;
    border: 1px solid #e5e7eb;
}

.booking-links h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: #111827;
}

.booking-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
}

.booking-link {
    display: block;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    text-align: center;
    color: #4b5563;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
}

.booking-link:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    color: #111827;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .options-grid {
        grid-template-columns: 1fr;
    }
    
    .booking-links-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .option-details {
        grid-template-columns: 1fr;
    }
}
