/* Custom CSS for Prime Agent */

/* Typing cursor animation */
.typing-cursor {
    display: inline-block;
    width: 2px;
    height: 1em;
    background-color: currentColor;
    margin-left: 2px;
    vertical-align: middle;
    animation: blink 1s step-end infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

/* Thinking step styling */
.thinking-step {
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

/* Processing indicator animation */
.processing-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #333;
    animation: pulse 1.5s ease-in-out infinite;
}

.processing-indicator.completed {
    animation: none;
    background-color: #10B981;
}

@keyframes pulse {
    0% { transform: scale(0.8); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(0.8); opacity: 0.7; }
}

/* Task summary container styling */
.summary-container {
    margin-bottom: 1.5rem;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.summary-container-header {
    padding: 0.75rem 1rem;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.summary-container-content {
    padding: 1rem;
    background-color: white;
}

/* Direct image styling (no containers) */
img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem auto;
    display: block;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

img:hover {
    transform: scale(1.02);
}

.image-caption {
    margin-top: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
    text-align: center;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
}
