/* Genspark-inspired Black and White Theme */
:root {
    /* Light mode (default) */
    --primary-color: #000000;
    --secondary-color: #333333;
    --background-color: #ffffff;
    --text-color: #000000;
    --light-gray: #f5f5f5;
    --medium-gray: #e0e0e0;
    --dark-gray: #666666;
    --accent-color: #007bff;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;

    /* Card and UI elements */
    --card-bg: #ffffff;
    --card-border: #e0e0e0;
    --card-shadow: rgba(0, 0, 0, 0.05);
    --sidebar-bg: #ffffff;
    --sidebar-border: #e0e0e0;
    --header-bg: #ffffff;
    --header-border: #e0e0e0;
    --input-bg: #ffffff;
    --input-border: #e0e0e0;
    --button-hover: #f5f5f5;
}

/* Dark mode variables */
[data-theme="dark"] {
    --primary-color: #ffffff;
    --secondary-color: #cccccc;
    --background-color: #121212;
    --text-color: #ffffff;
    --light-gray: #1f1f1f;
    --medium-gray: #2a2a2a;
    --dark-gray: #999999;
    --accent-color: #3b9cff;

    /* Card and UI elements */
    --card-bg: #1f1f1f;
    --card-border: #2a2a2a;
    --card-shadow: rgba(0, 0, 0, 0.2);
    --sidebar-bg: #1a1a1a;
    --sidebar-border: #2a2a2a;
    --header-bg: #1a1a1a;
    --header-border: #2a2a2a;
    --input-bg: #2a2a2a;
    --input-border: #3a3a3a;
    --button-hover: #2a2a2a;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    font-size: 16px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--accent-color);
}

/* Layout */
.container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: var(--light-gray);
    padding: 20px;
    position: fixed;
    height: 100vh;
    border-right: 1px solid var(--medium-gray);
}

.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    max-width: 800px;
    margin-right: auto;
}

/* Header */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
    border-bottom: 1px solid var(--medium-gray);
    margin-bottom: 20px;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

/* Navigation */
.nav {
    margin-top: 30px;
}

.nav-item {
    display: block;
    padding: 10px 0;
    margin-bottom: 5px;
    font-weight: 500;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.nav-item:hover {
    background-color: var(--medium-gray);
    padding-left: 10px;
}

.nav-item.active {
    font-weight: bold;
    color: var(--accent-color);
}

/* Search Box */
.search-container {
    margin: 20px 0;
}

.search-box {
    width: 100%;
    padding: 15px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background-color: var(--background-color);
}

.search-box:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.search-button {
    background-color: #1f1f1f;
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 10px;
    transition: background-color 0.3s ease;
}

.search-button:hover {
    background-color: #2a2a2a;
}

/* AutoWave button styling */
.autowave-button {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    margin-top: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.autowave-button:hover {
    background: linear-gradient(90deg, #2563eb, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Chat Interface */
.chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: var(--light-gray);
}

.message {
    margin-bottom: 20px;
    max-width: 80%;
}

.message.user {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: white;
    border-radius: 18px 18px 0 18px;
    padding: 12px 18px;
    margin-left: auto;
}

.message.ai {
    align-self: flex-start;
    background-color: var(--medium-gray);
    border-radius: 18px 18px 18px 0;
    padding: 12px 18px;
}

.chat-input {
    display: flex;
    padding: 15px;
    background-color: var(--background-color);
    border-top: 1px solid var(--medium-gray);
}

.chat-input input {
    flex: 1;
    padding: 12px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    margin-right: 10px;
}

.chat-input button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
}

/* Results */
.results-container {
    background-color: var(--light-gray);
    border-radius: 8px;
    padding: 25px;
    margin-top: 20px;
    white-space: pre-wrap;
    line-height: 1.8;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.results-title {
    font-size: 18px;
    font-weight: 600;
}

.results-actions {
    display: flex;
    gap: 10px;
}

.action-button {
    background-color: var(--background-color);
    border: 1px solid var(--medium-gray);
    border-radius: 5px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.action-button:hover {
    background-color: var(--medium-gray);
}

/* Citations */
.citation {
    font-size: 14px;
    color: var(--dark-gray);
    margin-top: 5px;
    display: flex;
    align-items: center;
}

.citation:before {
    content: "Source:";
    font-weight: 500;
    margin-right: 5px;
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.loading-dots {
    display: flex;
    gap: 5px;
}

.loading-dots span {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--primary-color);
    animation: pulse 1.5s infinite ease-in-out;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        border-right: none;
        border-bottom: 1px solid var(--medium-gray);
    }

    .main-content {
        margin-left: 0;
        padding: 15px;
    }

    .chat-container {
        height: calc(100vh - 300px);
    }
}

/* Screenshot styles */
.screenshot-container {
    margin-top: 20px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    padding: 15px;
    background-color: var(--light-gray);
}

.task-screenshot {
    max-width: 100%;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-top: 10px;
}

/* Progress indicator styles */
.task-progress {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    background-color: var(--light-gray);
}

/* Planning phase styles */
.planning-phase {
    margin-bottom: 20px;
    border-left: 4px solid #000;
}

.planning-steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
    max-height: 500px;
    overflow-y: auto;
    padding-right: 10px;
}

.planning-step {
    display: flex;
    gap: 15px;
    padding: 15px;
    background-color: var(--light-gray);
    border-radius: 8px;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-10px);
    animation: fadeInStep 0.5s forwards;
}

@keyframes fadeInStep {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.planning-step.typing::after {
    content: '|';
    display: inline-block;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

.planning-step:hover {
    background-color: #f0f0f0;
    transform: translateX(5px);
}

.planning-step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-color: #000;
    color: #fff;
    border-radius: 50%;
    font-weight: bold;
    flex-shrink: 0;
}

.planning-step-content {
    flex: 1;
    line-height: 1.6;
}

/* Make sure Markdown formatting works properly */
.planning-step-content p {
    margin: 0 0 10px 0;
}

.planning-step-content strong {
    font-weight: 700;
    color: #000;
}

.planning-step-content em {
    font-style: italic;
}

.planning-step-content h1,
.planning-step-content h2,
.planning-step-content h3,
.planning-step-content h4 {
    margin: 10px 0;
    font-weight: 700;
    color: #000;
}

.planning-step-content ul,
.planning-step-content ol {
    margin-left: 20px;
    margin-bottom: 10px;
    padding-left: 10px;
}

.planning-step-content ul li,
.planning-step-content ol li {
    margin-bottom: 5px;
}

.planning-step-content ul li::marker {
    color: #000;
}

.planning-step-content ol li::marker {
    color: #000;
    font-weight: 600;
}

/* Thinking indicator styles */
.thinking-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
}

.thinking-dots {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.thinking-dots span {
    width: 10px;
    height: 10px;
    background-color: #000;
    border-radius: 50%;
    display: inline-block;
    animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.thinking-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes thinking {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Session history styles */
.session-info {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--light-gray);
    border-radius: 8px;
    border: 1px solid var(--medium-gray);
}

.session-info p {
    margin: 5px 0;
    font-size: 14px;
    color: var(--text-secondary);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.history-item {
    padding: 15px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    background-color: white;
    transition: box-shadow 0.3s ease;
}

.history-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.history-action {
    font-weight: 600;
    color: var(--primary-color);
}

.history-timestamp {
    font-size: 12px;
    color: var(--text-secondary);
}

.history-task, .history-url, .history-prompt {
    margin: 5px 0;
    font-size: 14px;
    line-height: 1.4;
}

.history-replay-btn {
    margin-top: 10px;
    padding: 5px 10px;
    background-color: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.history-replay-btn:hover {
    background-color: var(--medium-gray);
    color: var(--text-color);
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    height: 8px;
    background-color: var(--medium-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    width: 0%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20px;
}

.step-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--medium-gray);
    margin-bottom: 5px;
}

.step-dot.active {
    background-color: var(--primary-color);
}

.step-dot.completed {
    background-color: var(--success-color);
}

.step-label {
    font-size: 10px;
    text-align: center;
    color: var(--text-color);
}

.current-step {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    background-color: white;
}

.step-icon {
    margin-right: 15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.step-details h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

.step-details p {
    margin: 0;
    font-size: 14px;
    color: var(--text-secondary);
}

/* Markdown HTML Block */
.markdown-html-block {
    display: block;
    width: 100%;
}

/* Ensure proper rendering of Markdown */
.html-content h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.html-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 1.25rem;
    margin-bottom: 0.75rem;
}

.html-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.html-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.html-content ul, .html-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.html-content li {
    margin-bottom: 0.5rem;
}

.html-content strong, .html-content b {
    font-weight: 700;
}

.html-content em, .html-content i {
    font-style: italic;
}

.html-content a {
    color: #007bff;
    text-decoration: underline;
}

.html-content a:hover {
    text-decoration: none;
}

.html-content code {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Dark mode specific styles */
[data-theme="dark"] .bg-white {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .bg-gray-50,
[data-theme="dark"] .bg-gray-100 {
    background-color: var(--background-color) !important;
}

[data-theme="dark"] .border-gray-200 {
    border-color: var(--card-border) !important;
}

[data-theme="dark"] .text-gray-700,
[data-theme="dark"] .text-gray-600,
[data-theme="dark"] .text-black {
    color: var(--text-color) !important;
}

[data-theme="dark"] .text-gray-400 {
    color: var(--dark-gray) !important;
}

[data-theme="dark"] .hover\:bg-gray-100:hover {
    background-color: var(--button-hover) !important;
}

[data-theme="dark"] .hover\:text-black:hover {
    color: var(--text-color) !important;
}

[data-theme="dark"] #theme-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .html-content code {
    background-color: #2a2a2a;
    color: #e0e0e0;
}