/**
 * Trial Manager for AutoWave
 * Handles trial limit notifications and subscription prompts
 */

class TrialManager {
    constructor() {
        this.trialStatus = null;
        this.init();
    }

    async init() {
        try {
            await this.loadTrialStatus();
        } catch (error) {
            console.error('Error initializing trial manager:', error);
        }
    }

    async loadTrialStatus() {
        try {
            const response = await fetch('/api/trial-status');
            const data = await response.json();
            
            if (data.success) {
                this.trialStatus = data.trial_status;
                console.log('Trial status loaded:', this.trialStatus);
            } else {
                console.warn('Failed to load trial status:', data.error);
            }
        } catch (error) {
            console.error('Error loading trial status:', error);
        }
    }

    showTrialExhaustedMessage(pageName, trialInfo) {
        const pageDisplayNames = {
            'research_lab': 'Research Lab',
            'agent_wave': 'Agent Wave (Document Generator)',
            'agentic_code': 'Agentic Code Editor',
            'prime_agent_task': 'Prime Agent Task',
            'context7_tools': 'Context7 Tools'
        };

        const displayName = pageDisplayNames[pageName] || pageName;
        const used = trialInfo?.used || 0;
        const limit = trialInfo?.limit || 0;

        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        overlay.innerHTML = `
            <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
                <div class="mb-4">
                    <svg class="w-16 h-16 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Trial Exhausted</h2>
                <p class="text-gray-600 mb-2">You've used all ${limit} free prompts for <strong>${displayName}</strong>.</p>
                <p class="text-gray-600 mb-6">Subscribe to get unlimited access to all AutoWave features!</p>
                
                <div class="space-y-3">
                    <button onclick="window.location.href='/pricing'" class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
                        🚀 Subscribe Now
                    </button>
                    <button onclick="this.closest('.fixed').remove()" class="w-full bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors">
                        Close
                    </button>
                </div>
                
                <div class="mt-4 text-sm text-gray-500">
                    <p>✅ AutoWave Chat: 50 credits daily (always free)</p>
                    <p>🔒 Other features: One-time trial limits</p>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);

        // Auto-remove after 30 seconds
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 30000);
    }

    showTrialStatus(pageName) {
        if (!this.trialStatus || this.trialStatus.is_paid_user) {
            return; // No need to show for paid users
        }

        const pageStatus = this.trialStatus.trial_status[pageName];
        if (!pageStatus) {
            return;
        }

        const { used, limit, remaining, exhausted } = pageStatus;

        if (exhausted) {
            this.showTrialExhaustedMessage(pageName, { used, limit });
            return;
        }

        // Show remaining trials notification
        if (remaining <= 1) {
            this.showRemainingTrialsNotification(pageName, remaining);
        }
    }

    showRemainingTrialsNotification(pageName, remaining) {
        const pageDisplayNames = {
            'research_lab': 'Research Lab',
            'agent_wave': 'Agent Wave',
            'agentic_code': 'Agentic Code',
            'prime_agent_task': 'Prime Agent Task',
            'context7_tools': 'Context7 Tools'
        };

        const displayName = pageDisplayNames[pageName] || pageName;

        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded-lg shadow-lg z-40 max-w-sm';
        toast.innerHTML = `
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">
                        ${remaining} trial${remaining === 1 ? '' : 's'} remaining for ${displayName}
                    </p>
                    <p class="text-xs mt-1">
                        <a href="/pricing" class="underline hover:text-yellow-800">Subscribe for unlimited access</a>
                    </p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="this.closest('.fixed').remove()" class="text-yellow-400 hover:text-yellow-600">
                        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(toast);

        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 8000);
    }

    // Handle API responses with trial information
    handleApiResponse(response, pageName) {
        if (response.trial_exhausted) {
            this.showTrialExhaustedMessage(pageName, response.trial_info);
            return false; // Prevent further processing
        }
        return true; // Allow processing to continue
    }

    // Get trial info for a specific page
    getTrialInfo(pageName) {
        if (!this.trialStatus || this.trialStatus.is_paid_user) {
            return null;
        }
        return this.trialStatus.trial_status[pageName] || null;
    }

    // Check if user is on a paid plan
    isPaidUser() {
        return this.trialStatus?.is_paid_user || false;
    }
}

// Global instance
window.trialManager = new TrialManager();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrialManager;
}
