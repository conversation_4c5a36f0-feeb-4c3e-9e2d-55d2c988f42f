/**
 * AI Sheets - Interactive Spreadsheet with AI Generation
 * Handles spreadsheet generation, editing, and export functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Sheets initialized');

    // DOM elements
    const form = document.getElementById('aiSheetsForm');
    const promptInput = document.getElementById('sheetPrompt');
    const generateBtn = document.getElementById('generateBtn');
    const newSessionBtn = document.getElementById('newSessionBtn');
    const exportCsvBtn = document.getElementById('exportCsvBtn');
    const exportExcelBtn = document.getElementById('exportExcelBtn');
    const exportJsonBtn = document.getElementById('exportJsonBtn');
    
    // Content elements
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const spreadsheetContent = document.getElementById('spreadsheetContent');
    const spreadsheetTable = document.getElementById('spreadsheetTable');
    const sheetTitle = document.getElementById('sheetTitle');
    const sheetDescription = document.getElementById('sheetDescription');
    const sheetInfo = document.getElementById('sheetInfo');
    const followUpPrompts = document.getElementById('followUpPrompts');
    const reasoningContent = document.getElementById('reasoningContent');

    // Current sheet data and conversation
    let currentSheetData = null;
    let conversationHistory = [];
    let isFollowUpMode = false;

    // Initialize
    init();

    function init() {
        // Load existing conversation if any
        loadConversation();

        // Check if there's an initial task
        const initialTask = promptInput.value.trim();
        if (initialTask) {
            generateSpreadsheet(initialTask);
        }

        // Event listeners
        form.addEventListener('submit', handleFormSubmit);
        newSessionBtn.addEventListener('click', startNewSession);
        exportCsvBtn.addEventListener('click', exportToCsv);
        exportExcelBtn.addEventListener('click', exportToExcel);
        exportJsonBtn.addEventListener('click', exportToJson);
    }

    function loadConversation() {
        // Load existing conversation from server
        fetch('/api/ai-sheets/get-conversation')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                conversationHistory = data.conversation || [];
                if (data.current_sheet) {
                    currentSheetData = data.current_sheet;
                    displaySpreadsheet(data.current_sheet);
                    isFollowUpMode = true;
                    updateFollowUpPrompts();
                }
                updateReasoningFromHistory();
            }
        })
        .catch(error => {
            console.error('Error loading conversation:', error);
        });
    }

    function handleFormSubmit(e) {
        e.preventDefault();
        const prompt = promptInput.value.trim();
        if (prompt) {
            // Determine if this is a follow-up based on existing data
            const isFollowUp = isFollowUpMode && currentSheetData !== null;
            generateSpreadsheet(prompt, isFollowUp);
        }
    }

    function generateSpreadsheet(prompt, isFollowUp = false) {
        console.log('Generating spreadsheet for:', prompt, 'Follow-up:', isFollowUp);

        // Show loading state
        showLoadingState();

        // Update reasoning section
        updateReasoningSection(`${isFollowUp ? 'Processing follow-up' : 'Analyzing request'}: "${prompt}"`);

        // Choose endpoint based on whether it's a follow-up
        const endpoint = isFollowUp ? '/api/ai-sheets/follow-up' : '/api/ai-sheets/generate-sheet';
        const requestBody = isFollowUp ?
            { prompt: prompt } :
            { prompt: prompt, sheet_type: 'general', rows: 15, columns: 6, is_follow_up: false };

        // Make API call
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentSheetData = data.data;
                isFollowUpMode = true;

                displaySpreadsheet(data.data);
                updateFollowUpPrompts();
                updateSheetInfo(data.data);

                // Update reasoning with AI's explanation
                if (data.reasoning) {
                    updateReasoningSection(data.reasoning);
                }
                updateReasoningSection(`✅ ${isFollowUp ? 'Updated' : 'Generated'} spreadsheet: "${data.data.title}"`);

                // Clear the input for next follow-up
                if (isFollowUp) {
                    promptInput.value = '';
                    promptInput.placeholder = 'Ask me to modify the spreadsheet, add data, or make changes...';
                }

            } else {
                showError('Failed to generate spreadsheet: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error generating spreadsheet:', error);
            showError('Network error occurred while generating spreadsheet');
        });
    }

    function showLoadingState() {
        emptyState.style.display = 'none';
        spreadsheetContent.style.display = 'none';
        loadingState.style.display = 'flex';
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    }

    function displaySpreadsheet(data) {
        // Hide loading, show content
        loadingState.style.display = 'none';
        emptyState.style.display = 'none';
        spreadsheetContent.style.display = 'block';
        
        // Reset button
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate';
        
        // Update title and description
        sheetTitle.textContent = data.title || 'AI Generated Spreadsheet';
        sheetDescription.textContent = data.description || 'Generated by AI';
        
        // Create table
        createTable(data.headers, data.rows);
        
        // Switch to spreadsheet tab
        const spreadsheetTab = document.getElementById('spreadsheet-tab');
        if (spreadsheetTab) {
            spreadsheetTab.click();
        }
    }

    function createTable(headers, rows) {
        // Clear existing table
        spreadsheetTable.innerHTML = '';

        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        // Add row number header
        const rowNumHeader = document.createElement('th');
        rowNumHeader.textContent = '#';
        rowNumHeader.className = 'row-number-header';
        rowNumHeader.style.width = '40px';
        rowNumHeader.style.backgroundColor = '#f8f9fa';
        rowNumHeader.style.textAlign = 'center';
        headerRow.appendChild(rowNumHeader);

        headers.forEach((header, index) => {
            const th = document.createElement('th');
            th.textContent = header;
            th.className = 'column-header';
            th.dataset.col = index;
            th.style.minWidth = '120px';
            th.style.backgroundColor = '#f8f9fa';
            th.style.fontWeight = '600';
            th.style.textAlign = 'center';
            th.style.cursor = 'pointer';

            // Add column header click handler for sorting
            th.addEventListener('click', () => sortColumn(index));

            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        spreadsheetTable.appendChild(thead);

        // Create body
        const tbody = document.createElement('tbody');

        rows.forEach((row, rowIndex) => {
            const tr = document.createElement('tr');
            tr.className = 'spreadsheet-row';

            // Add row number cell
            const rowNumCell = document.createElement('td');
            rowNumCell.textContent = rowIndex + 1;
            rowNumCell.className = 'row-number-cell';
            rowNumCell.style.backgroundColor = '#f8f9fa';
            rowNumCell.style.textAlign = 'center';
            rowNumCell.style.fontWeight = '500';
            rowNumCell.style.color = '#6c757d';
            rowNumCell.style.userSelect = 'none';
            tr.appendChild(rowNumCell);

            row.forEach((cell, colIndex) => {
                const td = document.createElement('td');
                td.textContent = cell;
                td.className = 'editable-cell';
                td.contentEditable = true;
                td.dataset.row = rowIndex;
                td.dataset.col = colIndex;
                td.style.backgroundColor = 'white';
                td.style.border = '1px solid #dee2e6';
                td.style.padding = '8px 12px';
                td.style.minWidth = '120px';
                td.style.cursor = 'text';

                // Add cell edit handlers
                td.addEventListener('blur', handleCellEdit);
                td.addEventListener('focus', handleCellFocus);
                td.addEventListener('keydown', handleCellKeydown);

                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });

        spreadsheetTable.appendChild(tbody);

        // Add table styling
        spreadsheetTable.style.backgroundColor = 'white';
        spreadsheetTable.style.border = '1px solid #dee2e6';
        spreadsheetTable.style.borderCollapse = 'collapse';
        spreadsheetTable.style.width = '100%';
    }

    function handleCellEdit(e) {
        const cell = e.target;
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        const value = cell.textContent;

        // Update current sheet data
        if (currentSheetData && currentSheetData.rows[row]) {
            currentSheetData.rows[row][col] = value;
        }

        // Remove focus styling
        cell.style.outline = 'none';
        cell.style.backgroundColor = 'white';

        // Optional: Send update to server
        updateCell(row, col, value);
    }

    function handleCellFocus(e) {
        const cell = e.target;
        // Add focus styling
        cell.style.outline = '2px solid #007bff';
        cell.style.backgroundColor = '#f8f9fa';

        // Select all text when focusing
        const range = document.createRange();
        range.selectNodeContents(cell);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
    }

    function handleCellKeydown(e) {
        const cell = e.target;
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);

        // Handle navigation keys
        if (e.key === 'Enter') {
            e.preventDefault();
            // Move to next row
            const nextRow = row + 1;
            const nextCell = document.querySelector(`[data-row="${nextRow}"][data-col="${col}"]`);
            if (nextCell) {
                nextCell.focus();
            }
        } else if (e.key === 'Tab') {
            e.preventDefault();
            // Move to next column
            const nextCol = col + 1;
            const nextCell = document.querySelector(`[data-row="${row}"][data-col="${nextCol}"]`);
            if (nextCell) {
                nextCell.focus();
            } else {
                // Move to first column of next row
                const nextRow = row + 1;
                const firstCellNextRow = document.querySelector(`[data-row="${nextRow}"][data-col="0"]`);
                if (firstCellNextRow) {
                    firstCellNextRow.focus();
                }
            }
        } else if (e.key === 'Escape') {
            // Cancel edit and blur
            cell.blur();
        }
    }

    function sortColumn(colIndex) {
        if (!currentSheetData || !currentSheetData.rows) return;

        // Sort rows by the specified column
        const sortedRows = [...currentSheetData.rows].sort((a, b) => {
            const aVal = a[colIndex] || '';
            const bVal = b[colIndex] || '';

            // Try to parse as numbers
            const aNum = parseFloat(aVal);
            const bNum = parseFloat(bVal);

            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }

            // Sort as strings
            return aVal.toString().localeCompare(bVal.toString());
        });

        // Update data and recreate table
        currentSheetData.rows = sortedRows;
        createTable(currentSheetData.headers, currentSheetData.rows);

        // Show feedback
        updateReasoningSection(`Sorted by column: ${currentSheetData.headers[colIndex]}`);
    }

    function updateCell(row, col, value) {
        fetch('/api/ai-sheets/update-cell', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                row: row,
                col: col,
                value: value
            })
        })
        .catch(error => {
            console.error('Error updating cell:', error);
        });
    }

    function updateFollowUpPrompts() {
        if (!currentSheetData) {
            followUpPrompts.innerHTML = '<div class="text-muted small">Generate a spreadsheet to see suggested follow-up actions</div>';
            return;
        }

        // Generate context-aware follow-up prompts
        const sheetTitle = currentSheetData.title || 'this spreadsheet';
        const headers = currentSheetData.headers || [];
        const rowCount = currentSheetData.rows ? currentSheetData.rows.length : 0;

        const prompts = [
            `Add more rows to ${sheetTitle}`,
            `Add a new column for calculations`,
            `Sort the data by ${headers[0] || 'first column'}`,
            `Add totals and summary statistics`,
            `Create a filtered view of the data`,
            `Add formulas to calculate relationships`,
            `Generate insights from this data`,
            `Add data validation rules`
        ];

        followUpPrompts.innerHTML = '';

        prompts.forEach(prompt => {
            const promptElement = document.createElement('div');
            promptElement.className = 'follow-up-prompt';
            promptElement.textContent = prompt;
            promptElement.addEventListener('click', () => {
                promptInput.value = prompt;
                generateSpreadsheet(prompt, true); // Mark as follow-up
            });
            followUpPrompts.appendChild(promptElement);
        });
    }

    function updateReasoningFromHistory() {
        if (conversationHistory.length === 0) return;

        // Clear existing reasoning
        reasoningContent.innerHTML = '';

        // Add conversation history to reasoning section
        conversationHistory.forEach(msg => {
            if (msg.role === 'assistant' && msg.sheet_data) {
                const timestamp = new Date(msg.timestamp).toLocaleTimeString();
                const reasoningItem = document.createElement('div');
                reasoningItem.className = 'reasoning-item mb-2 p-2 bg-light rounded';
                reasoningItem.innerHTML = `
                    <div class="small text-muted">${timestamp}</div>
                    <div>${msg.content}</div>
                `;
                reasoningContent.appendChild(reasoningItem);
            }
        });

        reasoningContent.scrollTop = reasoningContent.scrollHeight;
    }

    function updateSheetInfo(data) {
        const info = `
            <div><strong>Title:</strong> ${data.title}</div>
            <div><strong>Rows:</strong> ${data.rows.length}</div>
            <div><strong>Columns:</strong> ${data.headers.length}</div>
            <div><strong>Created:</strong> ${new Date().toLocaleString()}</div>
        `;
        sheetInfo.innerHTML = info;
    }

    function updateReasoningSection(message) {
        const timestamp = new Date().toLocaleTimeString();
        const reasoningItem = document.createElement('div');
        reasoningItem.className = 'reasoning-item mb-2 p-2 bg-light rounded';
        reasoningItem.innerHTML = `
            <div class="small text-muted">${timestamp}</div>
            <div>${message}</div>
        `;
        
        if (reasoningContent.children.length === 1 && reasoningContent.children[0].classList.contains('text-center')) {
            reasoningContent.innerHTML = '';
        }
        
        reasoningContent.appendChild(reasoningItem);
        reasoningContent.scrollTop = reasoningContent.scrollHeight;
    }

    function exportToCsv() {
        if (!currentSheetData) {
            showError('No spreadsheet data to export');
            return;
        }
        
        fetch('/api/ai-sheets/export-csv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: currentSheetData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                downloadFile(data.csv_content, data.filename, 'text/csv');
            } else {
                showError('Failed to export CSV: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error exporting CSV:', error);
            showError('Network error occurred while exporting CSV');
        });
    }

    function exportToExcel() {
        if (!currentSheetData) {
            showError('No spreadsheet data to export');
            return;
        }

        fetch('/api/ai-sheets/export-excel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: currentSheetData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                downloadFileFromBase64(data.excel_data, data.filename);
            } else {
                showError('Failed to export Excel: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error exporting Excel:', error);
            showError('Network error occurred while exporting Excel');
        });
    }

    function exportToJson() {
        if (!currentSheetData) {
            showError('No spreadsheet data to export');
            return;
        }

        fetch('/api/ai-sheets/export-json', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                data: currentSheetData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                downloadFile(data.json_content, data.filename, 'application/json');
            } else {
                showError('Failed to export JSON: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error exporting JSON:', error);
            showError('Network error occurred while exporting JSON');
        });
    }

    function downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    function downloadFileFromBase64(base64Data, filename) {
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    function startNewSession() {
        if (confirm('Start a new session? This will clear the current spreadsheet and conversation history.')) {
            // Clear current data
            currentSheetData = null;
            conversationHistory = [];
            isFollowUpMode = false;
            promptInput.value = '';
            promptInput.placeholder = 'Describe the spreadsheet you want to create (e.g., \'Create a budget tracker with categories and monthly expenses\')';

            // Reset UI
            emptyState.style.display = 'flex';
            spreadsheetContent.style.display = 'none';
            loadingState.style.display = 'none';

            // Reset sidebar
            followUpPrompts.innerHTML = '<div class="text-muted small">Generate a spreadsheet to see suggested follow-up actions</div>';
            sheetInfo.innerHTML = 'No spreadsheet loaded';
            reasoningContent.innerHTML = '<div class="text-center text-muted"><i class="fas fa-brain fa-2x mb-3"></i><p>AI reasoning will appear here when generating spreadsheets</p></div>';

            // Call API to clear session
            fetch('/api/ai-sheets/new-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateReasoningSection('🔄 New session started - ready for fresh spreadsheet generation');
                }
            })
            .catch(error => {
                console.error('Error starting new session:', error);
            });
        }
    }

    function showError(message) {
        // Reset button
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic"></i> Generate';
        
        // Show error in reasoning section
        updateReasoningSection(`Error: ${message}`);
        
        // Show empty state
        loadingState.style.display = 'none';
        emptyState.style.display = 'flex';
        spreadsheetContent.style.display = 'none';
    }

    // Global function for quick prompts
    window.quickPrompt = function(prompt) {
        promptInput.value = prompt;
        generateSpreadsheet(prompt);
    };
});
