#!/usr/bin/env python3
"""
Debug Admin Access
Test admin bypass functionality directly
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.admin_service import admin_service
from app.services.credit_service import credit_service

def test_admin_service():
    """Test admin service directly"""
    print("🔍 Testing Admin Service")
    print("=" * 40)
    
    # Test admin email
    test_email = "<EMAIL>"
    is_admin = admin_service.is_admin(test_email)
    print(f"Is {test_email} admin? {is_admin}")
    
    # Test admin status
    admin_status = admin_service.check_user_admin_status("test-user-id")
    print(f"Admin status: {admin_status}")

def test_credit_service():
    """Test credit service with admin bypass"""
    print("\n🔍 Testing Credit Service")
    print("=" * 40)
    
    # Mock session
    from unittest.mock import patch
    
    with patch('flask.session', {'user_email': '<EMAIL>', 'user_id': 'test-admin-id'}):
        # Test get_user_credits
        credits = credit_service.get_user_credits("test-admin-id")
        print(f"Admin credits: {credits}")
        
        # Test consume_credits
        result = credit_service.consume_credits("test-admin-id", "autowave_chat_basic")
        print(f"Consume credits result: {result}")

if __name__ == "__main__":
    test_admin_service()
    test_credit_service()
