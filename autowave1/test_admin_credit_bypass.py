#!/usr/bin/env python3
"""
Test Admin Credit Bypass for All Features
This script tests if admin bypass works for credit consumption
"""

import os
import sys
import requests
import json

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_autowave_chat_admin_bypass():
    """Test admin bypass for AutoWave Chat"""
    print("🧪 Testing AutoWave Chat Admin Bypass")
    print("=" * 50)
    
    try:
        from flask import Flask, session
        from app.services.credit_service import CreditService
        from app.services.admin_service import admin_service
        
        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'
        
        # Initialize credit service
        credit_service = CreditService()
        
        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'
                sess['access_token'] = 'test_admin_token'
                sess['email_confirmed'] = True
            
            with app.test_request_context():
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'
                session['access_token'] = 'test_admin_token'
                session['email_confirmed'] = True
                
                # Test credit consumption
                result = credit_service.consume_credits(
                    user_id='admin-reffynestan-test',
                    task_type='autowave_chat_basic',
                    input_text='Test message',
                    output_text='Test response'
                )
                
                print(f"Credit consumption result: {result}")
                
                if result.get('success') and result.get('admin_bypass'):
                    print("✅ AutoWave Chat admin bypass working!")
                    return True
                else:
                    print("❌ AutoWave Chat admin bypass failed")
                    return False
        
    except Exception as e:
        print(f"❌ Error testing AutoWave Chat: {e}")
        return False

def test_research_lab_admin_bypass():
    """Test admin bypass for Research Lab"""
    print("\n🧪 Testing Research Lab Admin Bypass")
    print("=" * 50)
    
    try:
        from flask import Flask, session
        from app.services.credit_service import CreditService
        
        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'
        
        # Initialize credit service
        credit_service = CreditService()
        
        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'
            
            with app.test_request_context():
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'
                
                # Test credit consumption for research lab
                result = credit_service.consume_credits(
                    user_id='admin-reffynestan-test',
                    task_type='research_lab_basic',
                    input_text='Test research query',
                    output_text='Test research results'
                )
                
                print(f"Credit consumption result: {result}")
                
                if result.get('success') and result.get('admin_bypass'):
                    print("✅ Research Lab admin bypass working!")
                    return True
                else:
                    print("❌ Research Lab admin bypass failed")
                    return False
        
    except Exception as e:
        print(f"❌ Error testing Research Lab: {e}")
        return False

def test_prime_agent_admin_bypass():
    """Test admin bypass for Prime Agent"""
    print("\n🧪 Testing Prime Agent Admin Bypass")
    print("=" * 50)
    
    try:
        from flask import Flask, session
        from app.services.credit_service import CreditService
        
        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'
        
        # Initialize credit service
        credit_service = CreditService()
        
        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'
            
            with app.test_request_context():
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'
                
                # Test credit consumption for prime agent
                result = credit_service.consume_credits(
                    user_id='admin-reffynestan-test',
                    task_type='prime_agent_basic',
                    input_text='Test prime agent task',
                    output_text='Test prime agent results'
                )
                
                print(f"Credit consumption result: {result}")
                
                if result.get('success') and result.get('admin_bypass'):
                    print("✅ Prime Agent admin bypass working!")
                    return True
                else:
                    print("❌ Prime Agent admin bypass failed")
                    return False
        
    except Exception as e:
        print(f"❌ Error testing Prime Agent: {e}")
        return False

def test_agent_wave_admin_bypass():
    """Test admin bypass for Agent Wave (Document Generator)"""
    print("\n🧪 Testing Agent Wave Admin Bypass")
    print("=" * 50)

    try:
        from flask import Flask, session
        from app.decorators.trial_limit import trial_required
        from app.services.admin_service import admin_service

        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'

        # Create a test route with the trial_required decorator
        @trial_required('agent_wave')
        def test_agent_wave():
            return "Agent Wave access granted!"

        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'

            with app.test_request_context():
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'

                print(f"Session user_email: {session.get('user_email')}")
                print(f"Is admin check: {admin_service.is_admin(session.get('user_email'))}")

                try:
                    result = test_agent_wave()
                    print(f"✅ Agent Wave admin bypass working: {result}")
                    return True
                except Exception as e:
                    print(f"❌ Agent Wave admin bypass failed: {e}")
                    return False

    except Exception as e:
        print(f"❌ Error testing Agent Wave: {e}")
        return False

def test_agentic_code_admin_bypass():
    """Test admin bypass for Agentic Code"""
    print("\n🧪 Testing Agentic Code Admin Bypass")
    print("=" * 50)

    try:
        from flask import Flask, session
        from app.services.credit_service import CreditService

        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'

        # Initialize credit service
        credit_service = CreditService()

        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'

            with app.test_request_context():
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'

                # Test credit consumption for agentic code
                result = credit_service.consume_credits(
                    user_id='admin-reffynestan-test',
                    task_type='agentic_code_basic',
                    input_text='Test code generation',
                    output_text='Test code results'
                )

                print(f"Credit consumption result: {result}")

                if result.get('success') and result.get('admin_bypass'):
                    print("✅ Agentic Code admin bypass working!")
                    return True
                else:
                    print("❌ Agentic Code admin bypass failed")
                    return False

    except Exception as e:
        print(f"❌ Error testing Agentic Code: {e}")
        return False

def test_context7_admin_bypass():
    """Test admin bypass for Context7 Tools"""
    print("\n🧪 Testing Context7 Admin Bypass")
    print("=" * 50)

    try:
        from flask import Flask, session
        from app.services.credit_service import CreditService

        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'

        # Initialize credit service
        credit_service = CreditService()

        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'

            with app.test_request_context():
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'

                # Test credit consumption for context7
                result = credit_service.consume_credits(
                    user_id='admin-reffynestan-test',
                    task_type='context7_basic',
                    input_text='Test context7 task',
                    output_text='Test context7 results'
                )

                print(f"Credit consumption result: {result}")

                if result.get('success') and result.get('admin_bypass'):
                    print("✅ Context7 admin bypass working!")
                    return True
                else:
                    print("❌ Context7 admin bypass failed")
                    return False

    except Exception as e:
        print(f"❌ Error testing Context7: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Admin Credit Bypass for All Features")
    print("=" * 60)
    
    # Test each feature
    tests = [
        ("AutoWave Chat", test_autowave_chat_admin_bypass),
        ("Research Lab", test_research_lab_admin_bypass),
        ("Prime Agent", test_prime_agent_admin_bypass),
        ("Agent Wave", test_agent_wave_admin_bypass),
        ("Agentic Code", test_agentic_code_admin_bypass),
        ("Context7 Tools", test_context7_admin_bypass)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n📊 Test Results:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
        if result:
            passed += 1
    
    print(f"\n📋 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All admin bypasses working!")
        print("   <NAME_EMAIL> should have unlimited access.")
    else:
        print("\n⚠️ Some admin bypasses need fixing.")
        print("   Check the failed tests above.")
