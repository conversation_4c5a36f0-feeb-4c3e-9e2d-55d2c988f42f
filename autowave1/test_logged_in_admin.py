#!/usr/bin/env python3
"""
Test Admin Access When Logged In
This simulates what happens when you're logged in as an admin
"""

import os
import sys

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_admin_decorator_with_session():
    """Test admin decorator with simulated logged-in session"""
    print("🧪 Testing Admin Decorator with Logged-in Session")
    print("=" * 50)
    
    try:
        from flask import Flask, session
        from app.decorators.free_user_access import require_subscription_for_page
        from app.services.admin_service import admin_service
        
        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'
        
        # Create a test route with the decorator
        @require_subscription_for_page('Test Premium Page')
        def test_premium_page():
            return "✅ Admin access granted to premium page!"
        
        # Test with admin session
        with app.test_client() as client:
            with client.session_transaction() as sess:
                # Simulate logged-in admin user
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'
                sess['access_token'] = 'test_admin_token'
                sess['email_confirmed'] = True
            
            # Test accessing the protected route
            with app.test_request_context():
                # Set session data for the request context
                session['user_id'] = 'admin-reffynestan-test'
                session['user_email'] = '<EMAIL>'
                session['access_token'] = 'test_admin_token'
                session['email_confirmed'] = True
                
                print(f"Session user_id: {session.get('user_id')}")
                print(f"Session user_email: {session.get('user_email')}")
                print(f"Is admin check: {admin_service.is_admin(session.get('user_email'))}")
                
                try:
                    result = test_premium_page()
                    print(f"✅ SUCCESS: {result}")
                    return True
                except Exception as e:
                    print(f"❌ FAILED: {e}")
                    print(f"Exception type: {type(e).__name__}")
                    return False
        
    except Exception as e:
        print(f"❌ Error in test: {e}")
        return False

def test_all_decorators():
    """Test all the different decorators with admin session"""
    print("\n🧪 Testing All Decorators with Admin Session")
    print("=" * 50)
    
    try:
        from flask import Flask, session, jsonify
        from app.decorators.free_user_access import require_subscription_for_page
        from app.decorators.paywall import require_subscription
        from app.decorators.trial_limit import trial_limit
        from app.services.admin_service import admin_service
        
        # Create Flask app
        app = Flask(__name__)
        app.secret_key = 'test_secret_key'
        
        # Test different decorators
        @require_subscription_for_page('Premium Page')
        def test_premium():
            return "Premium page access granted"
        
        @require_subscription('plus')
        def test_plus_subscription():
            return jsonify({"message": "Plus subscription access granted"})
        
        @trial_limit('test_feature')
        def test_trial_feature():
            return jsonify({"message": "Trial feature access granted"})
        
        results = []
        
        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'admin-reffynestan-test'
                sess['user_email'] = '<EMAIL>'
                sess['access_token'] = 'test_admin_token'
                sess['email_confirmed'] = True
            
            # Test each decorator
            tests = [
                ("Premium Page Decorator", test_premium),
                ("Plus Subscription Decorator", test_plus_subscription),
                ("Trial Limit Decorator", test_trial_feature)
            ]
            
            for test_name, test_func in tests:
                with app.test_request_context():
                    session['user_id'] = 'admin-reffynestan-test'
                    session['user_email'] = '<EMAIL>'
                    session['access_token'] = 'test_admin_token'
                    session['email_confirmed'] = True
                    
                    try:
                        result = test_func()
                        print(f"✅ {test_name}: SUCCESS")
                        results.append(True)
                    except Exception as e:
                        print(f"❌ {test_name}: FAILED - {e}")
                        results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ Error testing decorators: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Admin Access When Logged In")
    print("=" * 60)
    
    # Test basic decorator
    basic_test = test_admin_decorator_with_session()
    
    # Test all decorators
    all_decorators_test = test_all_decorators()
    
    print("\n📊 Test Results:")
    print(f"Basic admin decorator: {'✅ PASS' if basic_test else '❌ FAIL'}")
    print(f"All decorators test: {'✅ PASS' if all_decorators_test else '❌ FAIL'}")
    
    if basic_test and all_decorators_test:
        print("\n🎉 CONCLUSION: Admin bypass works perfectly when logged in!")
        print("   The issue is that you need to log <NAME_EMAIL> first.")
        print("   Once logged in, you should have unlimited access to all agent pages.")
    else:
        print("\n❌ CONCLUSION: There are issues with the admin bypass logic.")
        print("   The decorators are not working as expected.")
