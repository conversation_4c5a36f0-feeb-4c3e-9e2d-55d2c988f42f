#!/usr/bin/env python3
"""
Create Trial Usage Table in Supabase
This script creates the trial_usage table for tracking user trial limits.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    print("❌ Error: supabase package not installed")
    print("Install with: pip install supabase")
    sys.exit(1)

def create_trial_usage_table():
    """Create the trial_usage table in Supabase."""
    
    # Get Supabase credentials
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ Error: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        print("Check your .env file")
        return False
    
    if supabase_key.startswith('your_'):
        print("❌ Error: Please set real Supabase credentials in .env file")
        print("Current SUPABASE_SERVICE_ROLE_KEY starts with 'your_'")
        return False
    
    print(f"🔗 Connecting to Supabase: {supabase_url}")
    
    try:
        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Connected to Supabase successfully")
        
        # Check if table already exists
        try:
            result = supabase.table('trial_usage').select('id').limit(1).execute()
            print("✅ trial_usage table already exists")
            
            # Test the table by checking some data
            test_result = supabase.table('trial_usage').select('*').limit(5).execute()
            print(f"✅ Table working correctly - {len(test_result.data)} test records found")
            
            return True
            
        except Exception as table_error:
            print(f"❌ trial_usage table does not exist: {table_error}")
            print("📋 Please run the following SQL in your Supabase SQL Editor:")
            print("=" * 60)
            
            # Read and display the SQL file
            sql_file_path = os.path.join(os.path.dirname(__file__), 'create_trial_usage_table.sql')
            
            if os.path.exists(sql_file_path):
                with open(sql_file_path, 'r') as f:
                    sql_content = f.read()
                print(sql_content)
            else:
                print("-- Create trial_usage table")
                print("CREATE TABLE IF NOT EXISTS public.trial_usage (")
                print("    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,")
                print("    user_id UUID NOT NULL,")
                print("    page_name TEXT NOT NULL CHECK (page_name IN ('research_lab', 'agent_wave', 'agentic_code', 'prime_agent_task', 'context7_tools')),")
                print("    usage_count INTEGER DEFAULT 0,")
                print("    first_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),")
                print("    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),")
                print("    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),")
                print("    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),")
                print("    UNIQUE(user_id, page_name)")
                print(");")
                print("")
                print("-- Enable RLS")
                print("ALTER TABLE public.trial_usage ENABLE ROW LEVEL SECURITY;")
                print("")
                print("-- Create policy")
                print("CREATE POLICY \"Allow all operations on trial_usage\" ON public.trial_usage")
                print("    FOR ALL USING (true) WITH CHECK (true);")
            
            print("=" * 60)
            print("\n🔗 Go to: https://supabase.com/dashboard/project/[your-project]/sql")
            print("📝 Copy and paste the SQL above, then click 'Run'")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🗄️  AutoWave Trial Usage Table Setup")
    print("=" * 40)
    
    success = create_trial_usage_table()
    
    if success:
        print("\n🎉 Trial usage table is ready!")
        print("📊 Trial limits will now be properly enforced")
        print("🔄 Users will hit limits as configured")
    else:
        print("\n❌ Trial usage table setup failed")
        print("🔧 Please check the instructions above")
    
    print("\n" + "=" * 40)
