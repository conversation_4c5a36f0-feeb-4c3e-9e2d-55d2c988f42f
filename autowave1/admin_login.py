#!/usr/bin/env python3
"""
Admin Login Script
Creates a proper admin <NAME_EMAIL>
"""

import requests
import json
from urllib.parse import urljoin

def login_admin():
    """Login as admin and create proper session"""
    
    base_url = "http://127.0.0.1:5001"
    session = requests.Session()
    
    print("🔐 Admin Login Script")
    print("=" * 50)
    
    # Step 1: Get the login page to establish session
    print("1. Getting login page...")
    login_page_response = session.get(urljoin(base_url, "/auth/login"))
    print(f"   Login page status: {login_page_response.status_code}")
    
    # Step 2: Attempt login
    print("2. Attempting login...")
    login_data = {
        "email": "<EMAIL>",
        "password": "12345678"
    }
    
    login_response = session.post(
        urljoin(base_url, "/auth/login"),
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"   Login status: {login_response.status_code}")
    
    if login_response.status_code == 200:
        try:
            login_result = login_response.json()
            print(f"   Login result: {login_result}")
            
            if login_result.get('success'):
                print("✅ Login successful!")
                
                # Step 3: Test AutoWave Chat
                print("3. Testing AutoWave Chat...")
                chat_response = session.post(
                    urljoin(base_url, "/api/chat"),
                    json={"message": "Hello, this is an admin test"},
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Chat status: {chat_response.status_code}")
                if chat_response.status_code == 200:
                    chat_result = chat_response.json()
                    print("✅ AutoWave Chat working!")
                    print(f"   Response: {chat_result.get('response', 'No response')[:100]}...")
                    print(f"   Credits consumed: {chat_result.get('credits_consumed', 'N/A')}")
                    print(f"   Remaining credits: {chat_result.get('remaining_credits', 'N/A')}")
                else:
                    print(f"❌ Chat failed: {chat_response.text}")
                
                # Step 4: Test Agent Wave (super-agent execute-task)
                print("4. Testing Agent Wave...")
                agent_response = session.post(
                    urljoin(base_url, "/api/super-agent/execute-task"),
                    json={"task_description": "Test admin access to Agent Wave"},
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Agent Wave status: {agent_response.status_code}")
                if agent_response.status_code == 200:
                    agent_result = agent_response.json()
                    print("✅ Agent Wave working!")
                    print(f"   Success: {agent_result.get('success', 'N/A')}")
                    print(f"   Credits consumed: {agent_result.get('credits_consumed', 'N/A')}")
                    print(f"   Admin bypass: {agent_result.get('admin_bypass', 'N/A')}")
                else:
                    print(f"❌ Agent Wave failed: {agent_response.text}")
                
                # Step 5: Show session cookies for browser
                print("5. Session Information:")
                print("   Session cookies:")
                for cookie in session.cookies:
                    print(f"     {cookie.name}: {cookie.value}")
                
                print("\n🎯 Next Steps:")
                print("1. Open your browser")
                print("2. Go to http://127.0.0.1:5001/auth/login")
                print("3. Login with:")
                print("   Email: <EMAIL>")
                print("   Password: 12345678")
                print("4. After login, go to http://127.0.0.1:5001/agent-wave")
                print("5. Try using the Agent Wave interface")
                
            else:
                print(f"❌ Login failed: {login_result.get('error', 'Unknown error')}")
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON response: {login_response.text}")
    else:
        print(f"❌ Login request failed: {login_response.text}")

if __name__ == "__main__":
    login_admin()
