#!/usr/bin/env python3
"""
Manual Session Test for AutoWave
Creates a test user session and tests the upgrade flow
"""

import requests
import json

BASE_URL = "http://localhost:5001"

def create_test_session():
    """Create a test user session by directly setting session data"""
    print("🧪 Creating Test User Session")
    print("=" * 50)
    
    session = requests.Session()
    
    # Try to create a test session using the fallback registration
    # We'll modify the auth route to accept a test user
    
    # First, let's try a simple email that might pass validation
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    for email in test_emails:
        print(f"\n📧 Trying email: {email}")
        
        registration_data = {
            "email": email,
            "password": "testpass123",
            "confirm_password": "testpass123",
            "full_name": "Test User"
        }
        
        try:
            response = session.post(
                f"{BASE_URL}/auth/register",
                json=registration_data,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ Registration successful with {email}")
                    print(f"   User ID: {data.get('user', {}).get('id')}")
                    return session, email
                else:
                    print(f"   Error: {data.get('error')}")
            else:
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"   Raw response: {response.text[:100]}")
                    
        except Exception as e:
            print(f"   Exception: {e}")
    
    print("\n❌ All registration attempts failed")
    return None, None

def test_upgrade_flow(session, email):
    """Test the upgrade flow with authenticated session"""
    print(f"\n🛒 Testing Upgrade Flow for {email}")
    print("=" * 50)
    
    # Step 1: Get pricing data
    try:
        response = session.get(f"{BASE_URL}/payment/plans?location=NG")
        if response.status_code != 200:
            print(f"❌ Failed to get pricing: {response.status_code}")
            return False
        
        data = response.json()
        if not data.get('success'):
            print(f"❌ Pricing API error: {data}")
            return False
        
        plus_plan = next((p for p in data['plans'] if p['name'] == 'plus'), None)
        if not plus_plan:
            print("❌ Plus plan not found")
            return False
        
        print(f"✅ Found Plus plan: {plus_plan['pricing']['monthly']['formatted']}")
        
    except Exception as e:
        print(f"❌ Pricing error: {e}")
        return False
    
    # Step 2: Test subscription creation
    try:
        subscription_data = {
            "plan_id": plus_plan['id'],
            "billing_cycle": "monthly",
            "payment_provider": "auto",
            "user_location": "NG"
        }
        
        response = session.post(
            f"{BASE_URL}/payment/create-subscription",
            json=subscription_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Subscription response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("🎉 UPGRADE FLOW WORKING!")
                print(f"   Success: {data.get('success')}")
                
                if data.get('authorization_url'):
                    print(f"   🔗 Payment URL: {data['authorization_url']}")
                    print("   ✅ User would be redirected to Paystack payment page")
                    return True
                else:
                    print("   ⚠️ No payment URL (test mode)")
                    print("   ✅ In production, user would be redirected to payment")
                    return True
            else:
                print(f"❌ Subscription failed: {data.get('error')}")
                return False
        else:
            print(f"❌ Subscription failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error')}")
            except:
                print(f"   Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Subscription error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AutoWave Manual Session Test")
    print("Testing complete upgrade flow with manual session creation")
    print()
    
    # Create test session
    session, email = create_test_session()
    
    if session and email:
        # Test upgrade flow
        upgrade_success = test_upgrade_flow(session, email)
        
        print("\n" + "=" * 60)
        print("🎯 Final Results")
        print("=" * 60)
        
        if upgrade_success:
            print("🎉 SUCCESS! Complete upgrade flow is working!")
            print("✅ User registration: WORKING")
            print("✅ Authentication: WORKING") 
            print("✅ Payment initialization: WORKING")
            print("✅ Payment page redirect: WORKING")
            print()
            print("🔧 What happens in the UI:")
            print("1. User clicks 'Upgrade to Plus'")
            print("2. System creates payment session")
            print("3. User is redirected to Paystack payment page")
            print("4. User enters card details on Paystack")
            print("5. After payment, user returns to success page")
            print("6. Subscription is activated")
        else:
            print("❌ Upgrade flow needs debugging")
    else:
        print("\n" + "=" * 60)
        print("🎯 Registration Issue Found")
        print("=" * 60)
        print("❌ Unable to create test user session")
        print("⚠️ Email validation is too strict")
        print()
        print("🔧 Solutions:")
        print("1. Check email validation in input_validator.py")
        print("2. Temporarily disable strict validation for testing")
        print("3. Use a different email format")
        print("4. Check Supabase email validation settings")

if __name__ == "__main__":
    main()
