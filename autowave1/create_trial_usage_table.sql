-- Create Trial Usage Table for AutoWave
-- This table tracks trial usage per user per page to enforce limits

-- Create trial_usage table
CREATE TABLE IF NOT EXISTS public.trial_usage (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    page_name TEXT NOT NULL CHECK (page_name IN ('research_lab', 'agent_wave', 'agentic_code', 'prime_agent_task', 'context7_tools')),
    usage_count INTEGER DEFAULT 0,
    first_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, page_name)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trial_usage_user_id ON public.trial_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_trial_usage_page_name ON public.trial_usage(page_name);
CREATE INDEX IF NOT EXISTS idx_trial_usage_user_page ON public.trial_usage(user_id, page_name);

-- Enable Row Level Security
ALTER TABLE public.trial_usage ENABLE ROW LEVEL SECURITY;

-- Create RLS policy (allow all operations for now)
DROP POLICY IF EXISTS "Allow all operations on trial_usage" ON public.trial_usage;
CREATE POLICY "Allow all operations on trial_usage" ON public.trial_usage
    FOR ALL USING (true) WITH CHECK (true);

-- Insert some test data to verify the setup
INSERT INTO public.trial_usage (user_id, page_name, usage_count) 
VALUES 
    ('746f9f78-9355-4b9a-a07d-91ba194fd2ef', 'research_lab', 0),
    ('746f9f78-9355-4b9a-a07d-91ba194fd2ef', 'agent_wave', 0),
    ('746f9f78-9355-4b9a-a07d-91ba194fd2ef', 'agentic_code', 0)
ON CONFLICT (user_id, page_name) DO NOTHING;

-- Verify the setup
SELECT 'trial_usage' as table_name, count(*) as row_count FROM public.trial_usage;
