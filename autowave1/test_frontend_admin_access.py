#!/usr/bin/env python3
"""
Frontend Admin Access Test
This script tests admin access on the actual frontend by simulating login and testing features
"""

import requests
import json
import time

def test_frontend_admin_access():
    """Test admin access on the frontend"""
    base_url = "http://127.0.0.1:5001"
    
    print("🧪 Testing Frontend Admin Access")
    print("=" * 50)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Try to access the home page
    print("\n1. Accessing home page...")
    try:
        response = session.get(base_url)
        print(f"✅ Home page: Status {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Home page not accessible: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing home page: {e}")
        return False
    
    # Step 2: Try to access AutoWave Chat without login (should redirect)
    print("\n2. Testing AutoWave Chat without login...")
    try:
        response = session.get(f"{base_url}/autowave", allow_redirects=False)
        print(f"✅ AutoWave without login: Status {response.status_code}")
        
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f"✅ Correctly redirects to: {location}")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing AutoWave: {e}")
    
    # Step 3: Simulate admin login by setting session cookies manually
    print("\n3. Simulating admin login...")
    try:
        # Try to access the login page first
        login_response = session.get(f"{base_url}/auth/login")
        print(f"Login page: Status {login_response.status_code}")
        
        # Manually set session data (this simulates being logged in as admin)
        # We'll use the session endpoint if it exists, or try to set cookies
        admin_session_data = {
            'user_id': 'admin-reffynestan-frontend-test',
            'user_email': '<EMAIL>',
            'access_token': 'test_admin_token',
            'email_confirmed': True
        }
        
        # Try to set session via a test endpoint (if it exists)
        try:
            session_response = session.post(f"{base_url}/test/set_session", 
                                          json=admin_session_data,
                                          headers={'Content-Type': 'application/json'})
            print(f"Session setup: Status {session_response.status_code}")
        except:
            print("⚠️ No test session endpoint available")
        
        print("✅ Admin session simulated")
        
    except Exception as e:
        print(f"❌ Error simulating login: {e}")
    
    # Step 4: Test AutoWave Chat with admin session
    print("\n4. Testing AutoWave Chat with admin session...")
    try:
        response = session.get(f"{base_url}/autowave")
        print(f"AutoWave with admin: Status {response.status_code}")
        
        if response.status_code == 200:
            print("✅ AutoWave Chat accessible!")
            # Check if the page contains admin indicators
            if 'admin' in response.text.lower() or 'unlimited' in response.text.lower():
                print("✅ Admin indicators found in page")
            else:
                print("⚠️ No admin indicators found")
        else:
            print(f"❌ AutoWave Chat not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing AutoWave Chat: {e}")
    
    # Step 5: Test API endpoints directly with admin session
    print("\n5. Testing API endpoints with admin session...")
    
    # Test chat API
    try:
        chat_data = {
            'message': 'Test admin access',
            'conversation_id': 'test-admin-conversation'
        }
        
        response = session.post(f"{base_url}/api/chat/message",
                               json=chat_data,
                               headers={'Content-Type': 'application/json'})
        
        print(f"Chat API: Status {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat API accessible!")
            print(f"Response: {result.get('message', 'No message')[:100]}...")
        else:
            print(f"❌ Chat API error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing Chat API: {e}")
    
    # Step 6: Test Agent Wave (Document Generator)
    print("\n6. Testing Agent Wave...")
    try:
        response = session.get(f"{base_url}/agent-wave")
        print(f"Agent Wave: Status {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Agent Wave accessible!")
        else:
            print(f"❌ Agent Wave not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing Agent Wave: {e}")
    
    # Step 7: Test document generation API
    print("\n7. Testing Document Generation API...")
    try:
        doc_data = {
            'content': 'Generate a test document for admin access verification',
            'page_count': 1
        }
        
        response = session.post(f"{base_url}/api/document/generate",
                               json=doc_data,
                               headers={'Content-Type': 'application/json'})
        
        print(f"Document API: Status {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Document Generation API accessible!")
            print(f"Success: {result.get('success', False)}")
        else:
            print(f"❌ Document API error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing Document API: {e}")
    
    print("\n" + "=" * 50)
    print("Frontend test completed!")
    print("Check the results above to see if admin access is working.")
    
    return True

if __name__ == "__main__":
    test_frontend_admin_access()
