{"timestamp": 1750709156.026334, "data": {"title": "iPhone 15 pro or pro max why or why not? | AppleVis", "content": "iPhone 15 pro or pro max why or why not? | AppleVis Skip to main content AppleVis Search Search Menu Main navigation Apps Forum Blog More Podcast Guides Hardware & Accessory Reviews Bug Tracker Developer Resources Log in Breadcrumb Home Forums Apple Hardware and Compatible Accessories iPhone 15 pro or pro max why or why not? By <PERSON> , 28  September,  2023 Forum Apple Hardware and Compatible Accessories Okay I have a SE 3.  I want something better.  do I go 15 pro or pro max?  Why would you particularly choose the phone?  I would like to especially hear from those that have the new phones.  I'm of course open if you don't have the phone hearing your thoughts but I would love the thoughts of those that have the devices. Options Log in or register to post comments Comments 15 Pro By <PERSON> on Thursday, September 28, 2023 - 22:56 Firstly, sorry if you see the original of this, I misread your original post. I am planning on upgrading to the 15 pro and I'm also stuck on this problem. I currently have a 13, and I'm reasonably happy with the battery even at 84% capacity. That said, people say the pro max feels lighter and narrower than previous models, so i'm thinking of giving it a try. I would honestly suggest going to a store and holding both models if you can. I've had the previous version of your phone (the SE 2020), and going from that to a 13 was a big jump. It will be an even bigger jump to go to a pro max. If you are reasonably happy with the size and just want a new phone, pro is probably going to be your best bet. There are MagSafe batteries and chargers you can use to keep your phone powered, so the bigger battery is nice but not an absolute must. Also, be aware that Apple is selling the pro max with 256GB to start, whether you want it or not. There is no 128GB pro max. So if you are fine with 128GB of storage, you're going to pay an unnecessary amount of extra money for storage you never asked for. storage By Dennis Long on Friday, September 29, 2023 - 05:39 I would get the 256 of either the pro or pro max. 15 Pro By Julian on Friday, September 29, 2023 - 05:57 Just like you, I had the SE-3, and decided to upgrade to the 15 Pro. I don't like huge phones so the size of the pro is better for me. So far, the battery life is great. At the end of the day my battery is around 60 - 70% without plugging it in at all throughout the day. @ Julian By Dennis Long on Friday, September 29, 2023 - 06:38 What do  you use your phone for? How do the speakers sound? Do  you use Eloquence how does it sound if you use it? Max By Amir Soleimani on Friday, September 29, 2023 - 07:37 I've seen both devices here. If battery life is your major concern, definitely go with the 15 Pro Max. Even iPhone 15 Plus has better battery life compared with the 15 Pro, but the Max is the best in this regard. Moreover, the 5x zoom in the 15 Pro Max might not be useful for the visually impaired at this moment, but one day specialist apps for the blind might be able to utilize it. So the Max offers a better camera which might prove more useful in the future. Difference in weight and size of the 15 pro By Peter Holdstock on Friday, September 29, 2023 - 12:42 For me, I went from the 14 pro to the 15 pro and was surprised at how much lighter the 15 pro feels. A small difference on paper feels quite significant in hand. As does the very small reduction in width. Feels much nicer in my pocket. The action button is great. I have Mindset to voice memo which, without the voice memo app open, starts recording on the first press and hold, and stops on the second. The because it’s just one button, I turn VoiceOver speech off first so not heard on the recording at all.  hold is probably less than half a second so very quick and simple. There isn’t much difference in the speakers, but if anything, I would say the quality on the 15 pro isn’t quite as good as the 14 pro but it’s not really noticeable. @Dennis Long By Julian on Friday, September 29, 2023 - 22:48 I use my phone for all kinds of stuff. Eloquence is my TTS of choice and it sounds better than it did on the SE-3.  Same for the speakers. I have absolutely no regrets about my decision to go with the 15 Pro. Oh, and I went from 128GB to 256Gb since apps and OS updates seem to bet larger with time, and I plan to keep it for several years. If I was able to upgrade By Dominic on Sunday, October 1, 2023 - 08:16 I wouldn’t go for the 15. I wouldn’t go for the 15 pro. I wouldn’t go for the 15 promax. Just a jeopardise. This warm topic I’d go for the 15+. I don’t need the extra perks of a pro Phone. I don’t need these good camera features. I don’t need the dynamic island I don’t need the action button I don’t need the triple camera system I don’t need the fancy Matt glass back I don’t need the titanium I don’t need any of that all I want is a fun that work for me and do what I want to do for next. You know like 567 years. The iPhone 15+ has 6.7 inch display . And I heard it actually has better b...", "url": "https://www.applevis.com/forum/hardware-accessories/iphone-15-pro-or-pro-max-why-or-why-not", "html": "<html lang=\"en\" dir=\"ltr\" style=\"--color--primary-hue:240;--color--primary-saturation:100%;--color--primary-lightness:50\" class=\" js\" data-once=\"drupal-dialog-deprecation-listener\"><head>\n    <meta charset=\"utf-8\">\n<meta name=\"description\" content=\"Okay I have a SE 3. I want something better. do I go 15 pro or pro max? Why would you particularly choose the phone? I would like to especially hear from those that have the new phones. I'm of course open if you don't have the phone hearing your thoughts but I would love the thoughts of those that have the devices.\">\n<link rel=\"canonical\" href=\"https://www.applevis.com/forum/hardware-accessories/iphone-15-pro-or-pro-max-why-or-why-not\">\n<meta name=\"Generator\" content=\"Drupal 10 (https://www.drupal.org)\">\n<meta name=\"MobileOptimized\" content=\"width\">\n<meta name=\"HandheldFriendly\" content=\"true\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<script>var ct_check_js_val = 'ab56c3e943404e88368d3a69243de991';var drupal_ac_antibot_cookie_value = 'd77625ea8e0d66eb8e413c41f400da2bdff1bf48d9e07cec27f5b29fd1c9390d';var ct_use_cookies = 1;var ct_use_alt_cookies = 0;var ct_capture_buffer = 0;</script>\n<link rel=\"icon\" href=\"/sites/default/files/logo64x64.ico\" type=\"image/vnd.microsoft.icon\">\n\n    <title>iPhone 15 pro or pro max why or why not? | AppleVis</title>\n    <link rel=\"stylesheet\" media=\"all\" href=\"/sites/default/files/css/css_75nbK1hjQ35NlXbFk9N3M-SUCTmttUOs9c4qCGDMNE4.css?delta=0&amp;language=en&amp;theme=applevis&amp;include=eJxtjEEOwyAMBD9EwpPQhrjUjcEII1R-33Dppb3sYWa0qFVosPkkekA261O4JBdVBNX4EI2Xj9rIPQTJr9nv4gp44e1UeFDTO8iZSrcvKBic0FnLVhtntPlPGUUt55KmkSEh08kI69_8L9r7kzI5m9Yp-wNGHw97Svg\">\n<link rel=\"stylesheet\" media=\"all\" href=\"/sites/default/files/css/css_PeLFV073HtUoFLK77lvfhdImrmVuHsM-rBBR68AiCbc.css?delta=1&amp;language=en&amp;theme=applevis&amp;include=eJxtjEEOwyAMBD9EwpPQhrjUjcEII1R-33Dppb3sYWa0qFVosPkkekA261O4JBdVBNX4EI2Xj9rIPQTJr9nv4gp44e1UeFDTO8iZSrcvKBic0FnLVhtntPlPGUUt55KmkSEh08kI69_8L9r7kzI5m9Yp-wNGHw97Svg\">\n\n    <script type=\"application/json\" data-drupal-selector=\"drupal-settings-json\">{\"path\":{\"baseUrl\":\"\\/\",\"pathPrefix\":\"\",\"currentPath\":\"node\\/35617\",\"currentPathIsAdmin\":false,\"isFront\":false,\"currentLanguage\":\"en\"},\"pluralDelimiter\":\"\\u0003\",\"suppressDeprecationErrors\":true,\"ajaxPageState\":{\"libraries\":\"eJxtjlGOwjAMRC-UNEeqHNcEUyeOYlPo7WlXaD-An5HmPWtk6F1oY0tFNINEw8HdLcAn9124lYBC0BxkTdAzeuz3LIwBVQS6cRbFNaEOOlCt1Dwt495BpneNeY8b04NGuAiUdMZ0LK8z3OAZimoRmv0w5YjPPv0dqfBGQ9N70v5Bg40LOGuLfXCFsf9SRqhtOaUpMshcaWGYzycsfaPJr1Qp2G5ONWUwegEgmXfZ\",\"theme\":\"applevis\",\"theme_token\":null},\"ajaxTrustedUrl\":{\"\\/search\":true},\"gtag\":{\"tagId\":\"G-PSEEXTXQ9K\",\"consentMode\":false,\"otherIds\":[\"\"],\"events\":[],\"additionalConfigInfo\":[]},\"collapsiblock\":{\"active_pages\":false,\"slide_speed\":200,\"cookie_lifetime\":-1},\"field_group\":{\"html_element\":{\"mode\":\"full\",\"context\":\"view\",\"settings\":{\"classes\":\"\",\"id\":\"\",\"element\":\"div\",\"show_label\":true,\"label_element\":\"h2\",\"label_element_classes\":\"\",\"attributes\":\"\",\"effect\":\"none\",\"speed\":\"fast\"}}},\"user\":{\"uid\":0,\"permissionsHash\":\"de5ea5165b6ac84c6351506e83b4f2f4de78a0af6c2931b53fc7eb9114cc6ba3\"}}</script>\n<script src=\"/sites/default/files/js/js_WuVCrXaq40nMElziRPNvazr6y2Q20O9rNNG00E9ybz0.js?scope=header&amp;delta=0&amp;language=en&amp;theme=applevis&amp;include=eJxdjEsOwjAMRC-Uz5Eqx5jI1I0tJw309hRUsWAz0nszGjATmtxzFS0gsaOzjR5QCNoAWTNYwRFtL8IYUEXAOhdRXDOq06m2jdrIN98NJF0YyxEn05M83AVq_kQSbusCD3iFqlqFlnE29Yx_Tt-RCk9yzddl_4kGkysM1hbNeQM_3gdvTQc\"></script>\n<script src=\"/modules/google_tag/js/gtag.js?sxoho9\"></script><script async=\"\" src=\"https://www.googletagmanager.com/gtag/js?id=G-PSEEXTXQ9K\" type=\"text/javascript\"></script>\n\n    \n<link rel=\"preload\" href=\"/core/themes/olivero/fonts/metropolis/Metropolis-Regular.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"\">\n<link rel=\"preload\" href=\"/core/themes/olivero/fonts/metropolis/Metropolis-SemiBold.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"\">\n<link rel=\"preload\" href=\"/core/themes/olivero/fonts/metropolis/Metropolis-Bold.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"\">\n<link rel=\"preload\" href=\"/core/themes/olivero/fonts/lora/lora-v14-latin-regular.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"\">\n    <noscript><link rel=\"stylesheet\" href=\"/core/themes/olivero/css/components/navigation/nav-primary-no-js.css?sxoho9\" />\n</noscript>\n  </head>\n  <body class=\"path-node page-node-type-forum\">\n        <a href=\"#main-content\" class=\"visually-hidden focusable skip-link\">\n      Skip to main content\n    </a>\n    \n      <div class=\"dialog-off-canvas-main-canvas\" data-off-canvas-main-canvas=\"\">\n    \n<div id=\"page-wrapper\" class=\"page-wrapper\">\n  <div id=\"page\">\n\n          <header id=\"header\" class=\"site-header\" data-drupal-selector=\"site-header\" role=\"banner\" data-once=\"navigation\">\n\n                <div class=\"site-header__fixable\" data-drupal-selector=\"site-header-fixable\">\n          <div class=\"site-header__initial\">\n            <button class=\"sticky-header-toggle\" data-drupal-selector=\"sticky-header-toggle\" role=\"switch\" aria-controls=\"site-header__inner\" aria-label=\"Sticky header\" aria-checked=\"false\">\n              <span class=\"sticky-header-toggle__icon\">\n                <span></span>\n                <span></span>\n                <span></span>\n              </span>\n            </button>\n          </div>\n\n                    <div id=\"site-header__inner\" class=\"site-header__inner\" data-drupal-selector=\"site-header-inner\">\n            <div class=\"container site-header__inner__container\">\n\n              \n\n\n<div id=\"block-applevis-site-branding\" role=\"banner\" aria-label=\"Banner\" class=\"site-branding--bg-white site-branding block block-system block-system-branding-block\">\n  \n    \n    <div class=\"site-branding__inner\">\n              <div class=\"site-branding__text\">\n        <div class=\"site-branding__name\">\n          <a href=\"/\" title=\"Home\" rel=\"home\">AppleVis</a>\n        </div>\n      </div>\n      </div>\n</div>\n\n\n<div class=\"views-exposed-form form--inline my-search-block block block-views block-views-exposed-filter-blocksite-search-page-1\" data-drupal-selector=\"views-exposed-form-site-search-page-1\" id=\"block-applevis-exposedformsite-searchpage-1-3\" role=\"search\" aria-label=\"Site search\">\n  <div id=\"collapsiblock-wrapper-applevis_exposedformsite_searchpage_1_3\" class=\"collapsiblockTitle collapsiblockTitleCollapsed\" data-collapsiblock-action=\"3\" data-once=\"collapsiblock\"><button id=\"#collapse-applevis_exposedformsite_searchpage_1_3\" aria-controls=\"collapse-applevis_exposedformsite_searchpage_1_3-content\" aria-expanded=\"false\">\n      <h2 class=\"block__title\">Search</h2>\n    </button></div>\n      <div class=\"block__content collapsiblockContent collapsiblockContentCollapsed\" id=\"collapse-applevis_exposedformsite_searchpage_1_3-content\" style=\"display: none;\">\n      <form action=\"/search\" method=\"get\" id=\"views-exposed-form-site-search-page-1\" accept-charset=\"UTF-8\">\n  <div class=\"js-form-item form-item js-form-type-textfield form-item-key js-form-item-key\">\n      <label for=\"edit-key\" class=\"form-item__label\">Search</label>\n        <input data-drupal-selector=\"edit-key\" type=\"text\" id=\"edit-key\" name=\"key\" value=\"\" size=\"30\" maxlength=\"128\" class=\"form-text form-element form-element--type-text form-element--api-textfield\">\n\n        </div>\n<div data-drupal-selector=\"edit-actions\" class=\"form-actions js-form-wrapper form-wrapper\" id=\"edit-actions\"><input class=\"button--primary button js-form-submit form-submit\" data-drupal-selector=\"edit-submit-site-search\" type=\"submit\" id=\"edit-submit-site-search\" value=\"Search\">\n</div>\n\n\n</form>\n\n    </div>\n  </div>\n\n<div class=\"header-nav-overlay\" data-drupal-selector=\"header-nav-overlay\"></div>\n\n\n                              <div class=\"mobile-buttons\" data-drupal-selector=\"mobile-buttons\">\n                  <button class=\"mobile-nav-button\" data-drupal-selector=\"mobile-nav-button\" aria-label=\"Main Menu\" aria-controls=\"header-nav\" aria-expanded=\"false\">\n                    <span class=\"mobile-nav-button__label\">Menu</span>\n                    <span class=\"mobile-nav-button__icon\"></span>\n                  </button>\n                </div>\n\n                <div id=\"header-nav\" class=\"header-nav\" data-drupal-selector=\"header-nav\">\n                  \n<nav id=\"block-applevis-main-menu\" role=\"navigation\" aria-label=\"Main menu\" class=\"primary-nav block block-menu navigation menu--main\" aria-labelledby=\"block-applevis-main-menu-menu\">\n            \n  <h2 class=\"visually-hidden block__title\" id=\"block-applevis-main-menu-menu\">Main navigation</h2>\n  \n        \n\n\n          \n        \n    <ul class=\"menu primary-nav__menu primary-nav__menu--level-1\" data-drupal-selector=\"primary-nav-menu--level-1\" data-once=\"olivero-automatic-mobile-nav\">\n            \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-1\">\n                              \n                      <a href=\"/apps\" title=\"Browse and search our App Directories\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-1\" data-drupal-selector=\"primary-nav-menu-link-has-children\" data-drupal-link-system-path=\"node/27484\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-1\">Apps</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-1\">\n                              \n                      <a href=\"/forum\" title=\"\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-1\" data-drupal-selector=\"primary-nav-menu-link-has-children\" data-drupal-link-system-path=\"forum\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-1\">Forum</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-1\">\n                              \n                      <a href=\"/blog\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-1\" data-drupal-selector=\"primary-nav-menu-link-has-children\" data-drupal-link-system-path=\"blog\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-1\">Blog</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--button primary-nav__menu-item--level-1 primary-nav__menu-item--has-children\" data-drupal-selector=\"primary-nav-menu-item-has-children\">\n                              \n          \n            <button title=\"More community resources\" class=\"primary-nav__menu-link primary-nav__menu-link--button primary-nav__menu-link--level-1 primary-nav__menu-link--has-children\" aria-controls=\"primary-menu-item-1234\" aria-expanded=\"false\" data-drupal-selector=\"primary-nav-submenu-toggle-button\" type=\"button\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-1\">More</span>\n          </button>\n\n                                  \n              <span data-drupal-selector=\"primary-nav-menu-🥕\" class=\"primary-nav__menu-🥕\"></span>\n    \n    <ul class=\"menu primary-nav__menu primary-nav__menu--level-2\" data-drupal-selector=\"primary-nav-menu--level-2\" id=\"primary-menu-item-1234\">\n            \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-2\">\n                              \n                      <a href=\"/podcasts\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-2\" data-drupal-link-system-path=\"podcasts\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-2\">Podcast</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-2\">\n                              \n                      <a href=\"/guides\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-2\" data-drupal-link-system-path=\"guides\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-2\">Guides</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-2\">\n                              \n                      <a href=\"/reviews\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-2\" data-drupal-link-system-path=\"reviews\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-2\">Hardware &amp; Accessory Reviews</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-2\">\n                              \n                      <a href=\"/bugs\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-2\" data-drupal-link-system-path=\"node/12823\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-2\">Bug Tracker</span>\n          </a>\n\n            \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-2\">\n                              \n                      <a href=\"/developers\" title=\"Information For App Developers\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-2\" data-drupal-link-system-path=\"node/22\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-2\">Developer Resources</span>\n          </a>\n\n            \n                  </li>\n          </ul>\n  \n                  </li>\n      \n                          \n        \n        \n        <li class=\"primary-nav__menu-item primary-nav__menu-item--link primary-nav__menu-item--level-1\">\n                              \n                      <a href=\"/user/login\" class=\"primary-nav__menu-link primary-nav__menu-link--link primary-nav__menu-link--level-1\" data-drupal-selector=\"primary-nav-menu-link-has-children\" data-drupal-link-system-path=\"user/login\">            <span class=\"primary-nav__menu-link-inner primary-nav__menu-link-inner--level-1\">Log in</span>\n          </a>\n\n            \n                  </li>\n          </ul>\n  \n\n\n  </nav>\n\n\n                  \n                </div>\n                          </div>\n          </div>\n        </div>\n      </header>\n    \n    <div id=\"main-wrapper\" class=\"layout-main-wrapper layout-container\">\n      <div id=\"main\" class=\"layout-main\">\n        <div class=\"main-content\">\n          <a id=\"main-content\" tabindex=\"-1\"></a>\n          \n          <div class=\"main-content__container container\">\n            \n\n  <div class=\"region region--highlighted grid-full layout--pass--content-medium\">\n    <div data-drupal-messages-fallback=\"\" class=\"hidden messages-list\"></div>\n\n  </div>\n\n            \n\n  <div class=\"region region--breadcrumb grid-full layout--pass--content-medium\">\n    \n\n<div id=\"block-applevis-breadcrumbs\" class=\"block block-system block-system-breadcrumb-block\">\n  \n    \n      <div class=\"block__content\">\n        <nav class=\"breadcrumb\" role=\"navigation\" aria-labelledby=\"system-breadcrumb\">\n    <h2 id=\"system-breadcrumb\" class=\"visually-hidden\">Breadcrumb</h2>\n    <div class=\"breadcrumb__content\">\n      <ol class=\"breadcrumb__list\">\n                  <li class=\"breadcrumb__item\">\n                          <a href=\"/\" class=\"breadcrumb__link\">Home</a>\n                      </li>\n                  <li class=\"breadcrumb__item\">\n                          <a href=\"/forum\" class=\"breadcrumb__link\">Forums</a>\n                      </li>\n                  <li class=\"breadcrumb__item\">\n                          <a href=\"/forum/hardware-accessories\" class=\"breadcrumb__link\">Apple Hardware and Compatible Accessories</a>\n                      </li>\n              </ol>\n    </div>\n  </nav>\n\n    </div>\n  </div>\n\n  </div>\n\n\n                          <main role=\"main\">\n                \n\n  <div class=\"region region--content-above grid-full layout--pass--content-medium\">\n    \n\n<div id=\"block-applevis-page-title\" class=\"block block-core block-page-title-block\">\n  \n  \n\n  <h1 class=\"title page-title\">\n<span>iPhone 15 pro or pro max why or why not?</span>\n</h1>\n\n\n  \n</div>\n\n  </div>\n\n                \n\n  <div class=\"region region--content grid-full layout--pass--content-medium\" id=\"content\">\n    \n\n<div id=\"block-applevis-content\" class=\"block block-system block-system-main-block\">\n  \n    \n      <div class=\"block__content\">\n      \n\n<article data-history-node-id=\"35617\" class=\"node node--type-forum node--view-mode-full\">\n  <header class=\"\">\n    \n          \n          <div class=\"node__meta\">\n          <p>\n\t    <span>\n              By\n\t      <a href=\"/users/17914\" title=\"View user profile.\" hreflang=\"en\">Dennis Long</a>, 28  September,  2023\n            </span>\n\t  </p>\n                      <div class=\"node__author-image\">\n              <div>\n  </div>\n\n            <p></p>\n                    \n      </div>\n      </div></header>\n  <div class=\"node__content\">\n        \n  <div class=\"field field--name-taxonomy-forums field--type-entity-reference field--label-inline clearfix\">\n    <div class=\"field__label\">Forum</div>\n              <div class=\"field__item\">Apple Hardware and Compatible Accessories</div>\n          </div>\n\n            <div class=\"text-content clearfix field field--name-body field--type-text-with-summary field--label-hidden field__item\"><p>Okay I have a SE 3.  I want something better.  do I go 15 pro or pro max?  Why would you particularly choose the phone?  I would like to especially hear from those that have the new phones.  I'm of course open if you don't have the phone hearing your thoughts but I would love the thoughts of those that have the devices.</p>\n</div>\n      \n<div>\n  <h2>Options</h2>\n    <ul class=\"links inline\">\n          <li><a href=\"/user/login?destination=/forum/hardware-accessories/iphone-15-pro-or-pro-max-why-or-why-not%23comment-form\">Log in</a> or <a href=\"/user/register?destination=/forum/hardware-accessories/iphone-15-pro-or-pro-max-why-or-why-not%23comment-form\">register</a> to post comments</li>\n      </ul>\n  </div>\n\n<section data-drupal-selector=\"comments\" class=\"comments\" data-once=\"comments\">\n\n      \n    <h2 class=\"comments__title\">Comments</h2>\n    \n  \n  \n<article data-comment-user-id=\"412\" id=\"comment-154705\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154705#comment-154705\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">15 Pro</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1695942146\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Simon Jaeger</span> on Thursday, September 28, 2023 - 22:56</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>Firstly, sorry if you see the original of this, I misread your original post.<br>\nI am planning on upgrading to the 15 pro and I'm also stuck on this problem. I currently have a 13, and I'm reasonably happy with the battery even at 84% capacity. That said, people say the pro max feels lighter and narrower than previous models, so i'm thinking of giving it a try. I would honestly suggest going to a store and holding both models if you can. I've had the previous version of your phone (the SE 2020), and going from that to a 13 was a big jump. It will be an even bigger jump to go to a pro max. If you are reasonably happy with the size and just want a new phone, pro is probably going to be your best bet. There are MagSafe batteries and chargers you can use to keep your phone powered, so the bigger battery is nice but not an absolute must.<br>\nAlso, be aware that Apple is selling the pro max with 256GB to start, whether you want it or not. There is no 128GB pro max. So if you are fine with 128GB of storage, you're going to pay an unnecessary amount of extra money for storage you never asked for.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-154715\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154715#comment-154715\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">storage</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1695965962\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Friday, September 29, 2023 - 05:39</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I would get the 256 of either the pro or pro max.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"22195\" id=\"comment-154716\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154716#comment-154716\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">15 Pro</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1695967079\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Julian</span> on Friday, September 29, 2023 - 05:57</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>Just like you, I had the SE-3, and decided to upgrade to the 15 Pro. I don't like huge phones so the size of the pro is better for me. So far, the battery life is great. At the end of the day my battery is around 60 - 70% without plugging it in at all throughout the day.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-154717\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154717#comment-154717\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">@ Julian </a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1695969492\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Friday, September 29, 2023 - 06:38</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>What do  you use your phone for? How do the speakers sound?<br>\nDo  you use Eloquence how does it sound if you use it?</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"1560\" id=\"comment-154719\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154719#comment-154719\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Max</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1695973055\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Amir Soleimani</span> on Friday, September 29, 2023 - 07:37</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I've seen both devices here. If battery life is your major concern, definitely go with the 15 Pro Max. Even iPhone 15 Plus has better battery life compared with the 15 Pro, but the Max is the best in this regard. Moreover, the 5x zoom in the 15 Pro Max might not be useful for the visually impaired at this moment, but one day specialist apps for the blind might be able to utilize it. So the Max offers a better camera which might prove more useful in the future.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"4\" id=\"comment-154735\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154735#comment-154735\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Difference in weight and size of the 15 pro</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1695991356\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Peter Holdstock</span> on Friday, September 29, 2023 - 12:42</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>For me, I went from the 14 pro to the 15 pro and was surprised at how much lighter the 15 pro feels. A small difference on paper feels quite significant in hand. As does the very small reduction in width. Feels much nicer in my pocket. The action button is great. I have Mindset to voice memo which, without the voice memo app open, starts recording on the first press and hold, and stops on the second. The because it’s just one button, I turn VoiceOver speech off first so not heard on the recording at all.  hold is probably less than half a second so very quick and simple. There isn’t much difference in the speakers, but if anything, I would say the quality on the 15 pro isn’t quite as good as the 14 pro but it’s not really noticeable.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"22195\" id=\"comment-154780\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154780#comment-154780\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">@Dennis Long</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696027722\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Julian</span> on Friday, September 29, 2023 - 22:48</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I use my phone for all kinds of stuff. Eloquence is my TTS of choice and it sounds better than it did on the SE-3.  Same for the speakers. I have absolutely no regrets about my decision to go with the 15 Pro. Oh, and I went from 128GB to 256Gb since apps and OS updates seem to bet larger with time, and I plan to keep it for several years.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"30010\" id=\"comment-154922\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154922#comment-154922\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">If I was able to upgrade</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696148183\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dominic</span> on Sunday, October 1, 2023 - 08:16</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I wouldn’t go for the 15. I wouldn’t go for the 15 pro. I wouldn’t go for the 15 promax. Just a jeopardise. This warm topic I’d go for the 15+. I don’t need the extra perks of a pro Phone. I don’t need these good camera features. I don’t need the dynamic island I don’t need the action button I don’t need the triple camera system I don’t need the fancy Matt glass back I don’t need the titanium I don’t need any of that all I want is a fun that work for me and do what I want to do for next. You know like 567 years. The iPhone 15+ has 6.7 inch display . And I heard it actually has better battery life than the promax but yeah I don’t need any of those flashy features that more of the most expensive Apple phones have I just don’t see any reasons for them like it’s not that I’m it’s not as if I’m gonna throw my iPhone off of 10 story building or something I don’t need it to be all too durable considering I’ll be considering I’ll get a case of it. I don’t need it to have long battery life because I’m pro I’m mostly near charger every day . Sorry for my rant</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"3198\" id=\"comment-154928\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154928#comment-154928\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Plus size</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696150956\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Saqib</span> on Sunday, October 1, 2023 - 09:02</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I'm going for the 15 Plus as I don't really care about the Pro camera features and there is a £300 difference in price here in the UK! The 15 Plus is £899, where the 15 Pro Max is £1199. Battery tests have revealed that the Plus size lasts 2 hours longer, clocking in at 13 hours of continuous usage.  My main reason for upgrading is because I want to switch to an iPhone which has an USB port.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"23842\" id=\"comment-154980\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154980#comment-154980\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">15 Pro Max</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696182098\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Manuel</span> on Sunday, October 1, 2023 - 17:41</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I'm going to upgrade to the 15 Pro Max. Unfortunately, I have to wait until my phone arrives. :-)\nThe reasons why choose the Pro Max over the regular Pro are the following:</p>\n\n<ul>\n<li>Better battery life (Apple says 29 hours of video playback, compared to 23 hours in the regular pro model)</li>\n<li>Future-proof: As Amir pointed out, the camera enhancement could be useful one day. I remember me two years ago when I bought the regular 13 model. I said, \"No, I don't know what I need the better camera for\". Just 6 months later, the door detection feature was introduced and other apps which use the capability of LiDAR gradually came out.</li>\n<li>Better speakers: Because of the size, the Pro Max can output better sound quality. I don't need this for regular audio playback, but if I'm walking outside, higher volume is greatly appreciated.</li>\n<li>Size itself: This is very individual, but I prefer a bigger phone in my hands. I had an iPhone 7 Plus from 2017 on and then went with the regular 13. I don't know why, but somehow I like the bigger screen more. ;-)</li>\n<li>Storage: This is only a valid point for this year's series of Pro Models. Here in Germany, I could get a 15 Pro with 256 GB for 1,329 €, the 15 Pro Max with 256 GB (which is the basic configuration) for 1,449 €. That's definitely worth it if I'm already investing soo much money in a new phone.</li>\n</ul></div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"68\" id=\"comment-154996\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/154996#comment-154996\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">iPhone 15 Pro</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696190207\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Michael Hansen</span> on Sunday, October 1, 2023 - 19:56</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  \n            <div class=\"field field--name-user-picture field--type-image field--label-hidden field__item\">  <img loading=\"lazy\" src=\"/sites/default/files/badges/editorial_4.png\" width=\"98\" height=\"98\" alt=\"Member of the AppleVis Editorial Team\">\n\n</div>\n      </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I have been getting the largest handsets Apple has to offer since the iPhone 6 Plus, upgrading every year.  Battery life was my reasoning.  This year, I went with the iPhone 15 Pro, in no small part because of the price difference between the Pro and Pro Max models this year.  Prior to purchase, my research of battery life specifications for previous Plus/Pro Max models suggested that what battery life was only possible with the largest-screen models of several years ago is now similar to what you can get on the Pro model today.  In real life usage, my research and guestimation has proven accurate.  The phone is wonderfully easy to hold in one hand compared to the Pro Max.  The speaker sounds smaller than a Pro Max model, of course, but there is definitely some base.  Coming from the iPhone 14 Pro Max, I notice a performance improvement with VoiceOver responsiveness using Eloquence.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-155017\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155017#comment-155017\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">@Michael Hansen </a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696193004\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Sunday, October 1, 2023 - 20:43</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>How much less battery do you get?  is the speaker drop off a big one compared to the pro max?</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"2949\" id=\"comment-155022\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155022#comment-155022\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Battery difference.</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696195540\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Andy Lane</span> on Sunday, October 1, 2023 - 21:25</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>It’s between 20% and 25% higher capacity in the Pro Max. I’ve always had the Pro MAX or largest phones available except the 10. I have to admit though, I’m always jealous of the Pro’s size. It’s just so much nicer to hold.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"68\" id=\"comment-155023\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155023#comment-155023\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">iPhone 15 Pro Battery Life</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696196482\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Michael Hansen</span> on Sunday, October 1, 2023 - 21:41</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  \n            <div class=\"field field--name-user-picture field--type-image field--label-hidden field__item\">  <img loading=\"lazy\" src=\"/sites/default/files/badges/editorial_4.png\" width=\"98\" height=\"98\" alt=\"Member of the AppleVis Editorial Team\">\n\n</div>\n      </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>Hi Dennis,</p>\n<p>With recent Pro Max models, I was usually finishing my day at around 75% battery power, give or take.  I noticed a drop in battery life after I upgraded my iPhone 14 Pro Max to iOS 17 over the summer, and that didn't really improve appreciably even with the later betas.  I was usually finishing the day in the upper 60's to the lower 70's percent.  On my iPhone 15 Pro, set up as a new device without restoring, I am usually finishing the day in the 50-60% range.  One day last week I finished at 45%, and yesterday I finished at 41%.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"1560\" id=\"comment-155024\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155024#comment-155024\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Tom's Guide battery comparison</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696196569\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Amir Soleimani</span> on Sunday, October 1, 2023 - 21:42</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>According to them - and based on their criteria, the 15 Pro lasts 10 hours and 53 minutes, while the 15 Pro Max lasts 14 hours and 2 minutes.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-155025\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155025#comment-155025\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">what does that translate to</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696196904\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Sunday, October 1, 2023 - 21:48</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>What does that translate to roughly in real world useage?</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"30010\" id=\"comment-155026\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155026#comment-155026\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Amir</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696197018\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dominic</span> on Sunday, October 1, 2023 - 21:50</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>What is the criteria. Remember, they do different things as us. They don’t play video games, they most likely have the screen. Brightness on really high.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"2949\" id=\"comment-155027\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155027#comment-155027\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Wow thats a big difference.</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696197085\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Andy Lane</span> on Sunday, October 1, 2023 - 21:51</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>It’s going to be an even bigger difference for us because the screen on the pro max will take more power than on the pro. I’d imagine most of us have screen curtain on. So we’re powering all the same stuff on both phones except on the pro max the screen will use more power than the pro. It would be interesting to get real numbers.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-155028\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155028#comment-155028\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">pro max it looks like has better battery and speakers</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696197116\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Sunday, October 1, 2023 - 21:51</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>It looks like the pro max has better speakers and battery unfortunately</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-155045\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155045#comment-155045\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">it would be interesting to get battery stats from a VO point </a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696214104\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Monday, October 2, 2023 - 02:35</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>It would be interesting to get stats  from those that use voiceover.  This could go a few ways.  The pro max may not give longer battery life because of the  bigger screen.  It could also be the reverse and give better battery life because of having screen curtain on ETC.  Has anyone done a comparisom from a blindness prospective?  these sites like toms guide are good but the screen brightness is way turned up they probably do some gaming and other things we wouldn't do.  Thanks.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"13160\" id=\"comment-155149\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155149#comment-155149\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Blind Benchmarking</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696328968\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Brian</span> on Tuesday, October 3, 2023 - 10:29</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>Apologies if this has already been said, but I would love to see some kind of benchmark results from a blindness perspective on all the iPhone 15 variations.<br>\nThings like:<br>\n• multimedia playback -- How long can you play audio and video for? Streaming vs downloaded content?<br>\n• Transfer rates vs battery drain -- upload and download usage.<br>\n• Standard data usage -- web surfing, email interfacing, texting, etc.<br>\n• All of the above on Wi-Fi, or on your Data -- is the battery drain noticeable?<br>\n• Non internet apps -- just running apps regularly that do not necessarily require data; such as games, Ebooks, etc. </p>\n<p>All the above both with and without Screen Curtain. </p>\n<p>Thoughts?</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-155168\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155168#comment-155168\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Speaker Volume Test: iPhone 15 Pro Max vs iPhone 14 Pro Max </a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696343621\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Tuesday, October 3, 2023 - 14:33</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I saw this on Mastodon this morning have a look.<br>\n<a href=\"https://www.iclarified.com/91599/speaker-volume-test-iphone-15-pro-max-vs-iphone-14-pro-max-video\">https://www.iclarified.com/91599/speaker-volume-test-iphone-15-pro-max-vs-iphone-14-pro-max-video</a><br>\nWhat does this mean in the real world?  I'm not  huge big in to audio can anyone translate this into useable numbers?</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"17914\" id=\"comment-155169\" class=\"comment js-comment comment--level-1 by-node-author\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155169#comment-155169\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">@Brian I agree with you </a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696343742\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Dennis Long</span> on Tuesday, October 3, 2023 - 14:35</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>Brian I completely agree with you.  I would love to see this.  I'm leaning towards the pro max.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"23842\" id=\"comment-155179\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155179#comment-155179\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Speaker Volume</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696353704\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Manuel</span> on Tuesday, October 3, 2023 - 17:21</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>The article that Dennis Long provided suggests that the speakers in the 15 Pro Max should be slightly but noticeably louder. However, don't expect a surprise.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"13160\" id=\"comment-155221\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155221#comment-155221\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">15 Pro</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696400804\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Brian</span> on Wednesday, October 4, 2023 - 06:26</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I am still debating with myself over the 15 Pro. However, until Apple gets iOS 17 under control with regards to non visual accessibility, I will hang on to my trusty ole iPhone SE2.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n<article data-comment-user-id=\"2949\" id=\"comment-155224\" class=\"comment js-comment comment--level-1\" role=\"article\" data-drupal-selector=\"comment\">\n     <div class=\"comment__title\">\n               \n        <h3><a href=\"/comment/155224#comment-155224\" class=\"permalink\" rel=\"bookmark\" hreflang=\"en\">Speakers.</a></h3>\n        \n          </div>\n  <span class=\"hidden\" data-comment-timestamp=\"1696403076\"></span>\n\n  <div class=\"comment__text-wrapper\">\n    <footer class=\"comment__meta\">\n      <p class=\"comment__author\">By <span>Andy Lane</span> on Wednesday, October 4, 2023 - 07:04</p>\n  <div class=\"comment__picture-wrapper\">\n    <div class=\"comment__picture\">\n      <div>\n  </div>\n\n    </div>\n  </div>\n                </footer>\n    <div class=\"comment__title comment__content\">\n      \n            <div class=\"text-content field field--name-comment-body field--type-text-long field--label-hidden field__item comment__text-content\"><p>I’m not certain on my numbers but I think 6DBa is a doubling of sound energy. In Dennis’s article the increase from 14 pro max to 15 pro max was 2DBa. This would suggest a 33% increase in sound energy but thats not the full story. Human hearing operates on a log scale so that most definitely doesn’t mean 30% higher volume. It also doesn’t tell you anything about qualitative metrics, which frequencies carry that extra energy or anything about how the speaker sounds. I’d guess it’s an improvement though which is great. It’s not like Apple to go for louder but worse speakers in fact they are known for always choosing great speakers, even incredible speakers considering the physical constraints. I also saw an opinion that the 15 pro sounded a little better than the 14 pro which agrees with these numbers. What I’d be interested in is the 15 pro vs 15 pro max. In the 14 Pro and pro max I’m pretty sure the speakers are the same which I find odd considering the extra space they have to work with in the pro max.</p>\n</div>\n      <ul class=\"links inline comment__links\">\n          <li class=\"comment__links-item\"></li>\n      </ul>\n    </div>\n  </div>\n</article>\n\n\n  \n</section>\n\n  </div>\n  </article>\n\n    </div>\n  </div>\n\n  </div>\n\n              </main>\n                        \n          </div>\n        </div>\n        <div class=\"social-bar\">\n          \n        </div>\n      </div>\n    </div>\n\n    <footer class=\"site-footer\">\n      <div class=\"site-footer__inner container\">\n        \n\n  <div class=\"region region--footer-top grid-full layout--pass--content-medium\">\n    <div class=\"region--footer_top__inner\">\n      <nav id=\"block-applevis-secondarymenu-2\" role=\"navigation\" aria-label=\"Secondary menu\" class=\"block block-menu navigation menu--secondary-menu\" aria-labelledby=\"block-applevis-secondarymenu-2-menu\">\n            \n  <h2 class=\"visually-hidden block__title\" id=\"block-applevis-secondarymenu-2-menu\">Site Information</h2>\n  \n        \n\n\n          <ul class=\"menu secondary-nav__menu secondary-nav__menu--level-1\">\n            \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-1\">\n          <a href=\"/about\" title=\"About AppleVis\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-1\" data-drupal-link-system-path=\"node/18\">About AppleVis</a>\n\n                  </li>\n      \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-1 secondary-nav__menu-item--has-children\">\n          <a href=\"https://www.bemyeyes.com/business/about/\" title=\"The Story About Be My Eyes\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-1 secondary-nav__menu-link--has-children\">About Be My Eyes</a>\n\n                                <ul class=\"menu secondary-nav__menu secondary-nav__menu--level-2\">\n            \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-2\">\n          <a href=\"https://apps.apple.com/us/app/be-my-eyes/id905177575\" title=\"Download Be My Eyes for iOS From the App Store\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-2\">Download Be My Eyes From the App Store</a>\n\n                  </li>\n      \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-2\">\n          <a href=\"https://www.bemyeyes.com/community/\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-2\">Latest News and Updates</a>\n\n                  </li>\n          </ul>\n  \n                  </li>\n      \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-1\">\n          <a href=\"/newsletter\" title=\"Newsletters\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-1\" data-drupal-link-system-path=\"node/637\">Newsletter</a>\n\n                  </li>\n      \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-1\">\n          <a href=\"/help\" title=\"Answers to some frequently asked questions about AppleVis\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-1\" data-drupal-link-system-path=\"faq\">FAQ</a>\n\n                  </li>\n      \n                          \n        \n        \n        <li class=\"secondary-nav__menu-item secondary-nav__menu-item--link secondary-nav__menu-item--level-1\">\n          <a href=\"/contact\" class=\"secondary-nav__menu-link secondary-nav__menu-link--link secondary-nav__menu-link--level-1\" data-drupal-link-system-path=\"contact\">Contact</a>\n\n                  </li>\n          </ul>\n  \n\n\n  </nav>\n\n    </div>\n  </div>\n\n        \n\n  <div class=\"region region--footer-bottom grid-full layout--pass--content-medium\">\n    \n\n<div id=\"block-applevis-copyright-2\" class=\"block block-block-content block-block-contentd6b10b43-4ba8-4405-a2da-64cda88aecf2\">\n  \n    \n      <div class=\"block__content\">\n      \n            <div class=\"text-content clearfix field field--name-body field--type-text-with-summary field--label-hidden field__item\"><center><p>Unless stated otherwise, all content is copyright AppleVis. All rights reserved. <a href=\"https://www.applevis.com/copyright\">© 2025</a> | <a href=\"https://www.applevis.com/accessibility\">Accessibility</a> | <a href=\"https://www.applevis.com/terms\">Terms</a> | <a href=\"https://www.applevis.com/privacy\">Privacy</a> | <a href=\"https://www.bemyeyes.com/\">A Be My Eyes Company</a>\n</p></center></div>\n      \n    </div>\n  </div>\n\n  </div>\n\n      </div>\n    </footer>\n\n    <div class=\"overlay\" data-drupal-selector=\"overlay\"></div>\n\n  </div>\n</div>\n\n  </div>\n\n    \n    <script src=\"/sites/default/files/js/js_P4Ac7qlYN2ZPyt3jiTd3Ra5I0DQ9SBsaUwFYVwPV2lo.js?scope=footer&amp;delta=0&amp;language=en&amp;theme=applevis&amp;include=eJxdjEsOwjAMRC-Uz5Eqx5jI1I0tJw309hRUsWAz0nszGjATmtxzFS0gsaOzjR5QCNoAWTNYwRFtL8IYUEXAOhdRXDOq06m2jdrIN98NJF0YyxEn05M83AVq_kQSbusCD3iFqlqFlnE29Yx_Tt-RCk9yzddl_4kGkysM1hbNeQM_3gdvTQc\"></script>\n<script src=\"https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js\"></script>\n<script src=\"/modules/contrib/collapsiblock/theme/dist/collapsiblock.js?sxoho9\" type=\"module\"></script>\n<script src=\"/sites/default/files/js/js_FUsNWd2zATVRQbuwWiBV-KGidL6UyvjieLflcQvqIJA.js?scope=footer&amp;delta=3&amp;language=en&amp;theme=applevis&amp;include=eJxdjEsOwjAMRC-Uz5Eqx5jI1I0tJw309hRUsWAz0nszGjATmtxzFS0gsaOzjR5QCNoAWTNYwRFtL8IYUEXAOhdRXDOq06m2jdrIN98NJF0YyxEn05M83AVq_kQSbusCD3iFqlqFlnE29Yx_Tt-RCk9yzddl_4kGkysM1hbNeQM_3gdvTQc\"></script>\n\n  <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'95469a44bd4873b6',t:'MTc1MDcwOTE1Mi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height=\"1\" width=\"1\" style=\"position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;\"></iframe><script defer=\"\" src=\"https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015\" integrity=\"sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==\" data-cf-beacon=\"{&quot;rayId&quot;:&quot;95469a44bd4873b6&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;4724d3fc8fbc40b09252b32a342933fb&quot;}\" crossorigin=\"anonymous\"></script>\n\n\n<div id=\"drupal-live-announce\" class=\"visually-hidden\" aria-live=\"polite\" aria-busy=\"false\"></div><div id=\"drupal-modal\" class=\"ui-front\" style=\"display: none;\"></div></body></html>", "images": [{"src": "https://www.applevis.com/sites/default/files/badges/editorial_4.png", "alt": "Member of the AppleVis Editorial Team", "width": "98", "height": "98"}, {"src": "https://www.applevis.com/sites/default/files/badges/editorial_4.png", "alt": "Member of the AppleVis Editorial Team", "width": "98", "height": "98"}], "success": true, "screenshot": "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"}}