{"timestamp": 1752269595.981568, "data": {"title": "\n\tAmazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025: By <PERSON><PERSON><PERSON>\n", "content": "Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025: By <PERSON><PERSON><PERSON> Sign in Sign up News Latest news Company updates Long reads TV Research Events All Conferences Webinars Popular Community Community latest Latest expert opinions Groups Search members Jobs APIs Sign in Sign up News Back News Latest news Company updates Long reads TV Research Events Back Events All Conferences Webinars Popular Community Back Community Community latest Latest expert opinions Groups Search members Jobs APIs payments markets retail wholesale wealth regulation crime crypto sustainable startups devops identity security cloud ai Home Community Serhii Bondarenko Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025 Community Your feed Latest expert opinions Groups Join the Community 23,441 Expert opinions 42,351 Total members 311 New members (last 30 days) 178 New opinions (last 30 days) 29,126 Total comments Join Sign in Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025 1 Like 1 17 June 2025 Be the first to comment <PERSON><PERSON><PERSON> Artificial Intelegence Tickeron Location Weiden Followers 9 Opinions 60 Follow Unfollow Amazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, consistently shaping market trends through innovation and scale. As of June 17, 2025, AMZN’s stock has exhibited robust performance, driven by its\n strategic investments in artificial intelligence (AI), cloud infrastructure, and operational efficiencies. This article provides a comprehensive financial analysis of AMZN’s recent market movements, key news driving its performance, a comparison with a correlated\n stock, insights into trading with inverse ETFs, and the role of AI-driven tools in navigating its volatility. Recent Stock Performance and Financial Metrics Over the five trading days ending June 8, 2025, AMZN stock surged by 6.75%, outpacing the broader market and reflecting strong investor confidence in Amazon’s growth trajectory. This momentum continued into mid-June, with the stock rising 1.57%\n on June 16, 2025, according to The Motley Fool. Amazon’s year-to-date performance has been remarkable, with shares rebounding 42% from a 52-week low of $151.76, fueled by record earnings projections for 2025 and 2026. For the first quarter of 2025, Amazon reported a 62% year-over-year increase in earnings per share (EPS), with net sales reaching $155.7 billion, a 9% increase from the prior year. Amazon Web Services (AWS), the company’s cloud computing division,\n posted $29.3 billion in sales, up 17% year-over-year, though margins softened slightly to 39.5%. Operating income climbed 20% to $18.4 billion, surpassing estimates of $17.48 billion. Looking ahead, Amazon’s guidance for Q2 2025 projects net sales between\n $159 billion and $164 billion, representing approximately 9% growth at the midpoint, with operating income expected between $13 billion and $17.5 billion. The company’s forward price-to-earnings (P/E) ratio has contracted significantly, dropping from 90 in 2021 to 30 as of March 2025, while trading at 17 times operating cash flow, its lowest ever. This suggests AMZN may be undervalued relative to\n its growth potential, particularly given its 50% revenue growth and doubled operating margins since 2021. However, free cash flow declined 48% over the last twelve months, reflecting heavy capital expenditures, including a $100 billion+ investment planned\n for 2025, primarily in AI and AWS infrastructure. Market News Driving AMZN’s Performance on June 17, 2025 Several macroeconomic and company-specific developments have influenced AMZN’s stock movement as of June 17, 2025. On June 16, the broader market saw gains, with the Dow, S&P 500, and Nasdaq rising amid optimism over contained geopolitical tensions\n between Israel and Iran, boosting risk appetite. Amazon benefited from this sentiment, particularly due to its exposure to global trade. The company’s e-commerce operations, which account for nearly 40% of U.S. online sales, are poised to capitalize on easing\n trade tensions, as Amazon sells and facilitates imported goods. Additionally, Amazon’s partnership with Roku, announced on June 16, 2025, to create the “largest authenticated Connected TV (CTV) footprint” in the U.S. through Amazon Ads, drove a 10% surge in Roku’s stock and further bolstered AMZN’s advertising\n segment, which grew 18% year-over-year in Q1 2025. Amazon’s increasing reliance on AI to optimize operations, including robotics in its fulfillment network and custom silicon for AI workloads, has also garnered Wall Street attention, with analysts projecting\n new highs for AMZN in the second half of 2025. However, risks persist. High tariffs on Chinese imports, as noted by Zacks, could pressure Amazon’s retail margins, though the company’s diversified revenue streams—particularly AWS—mitigate this exposure. Softer-...", "url": "https://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025", "html": "<html lang=\"en\" xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>\n\tAmazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025: By <PERSON><PERSON><PERSON>\n</title><link rel=\"icon\" href=\"/assets/favicons/favicon.ico\" sizes=\"any\"><link rel=\"icon\" href=\"/assets/favicons/favicon.svg\" type=\"image/svg+xml\"><link rel=\"apple-touch-icon\" href=\"/assets/favicons/apple-touch-icon.png\"><link rel=\"manifest\" href=\"/assets/favicons/site.webmanifest\"><meta name=\"theme-color\" content=\"#4a4a4a\"><link href=\"/app_themes/earlgrey/css/bootstrap/bootstrap.min.css\" rel=\"stylesheet\"><link href=\"/app_themes/earlgrey/css/main.min.css\" rel=\"stylesheet\">\n\n        \n        <script type=\"text/javascript\" async=\"\" src=\"https://www.googletagmanager.com/gtag/js?id=G-Z5T999100T&amp;cx=c&amp;gtm=45He5791v830119766za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********~*********\"></script><script async=\"\" src=\"https://www.googletagmanager.com/gtm.js?id=GTM-P64CHFM\"></script><script src=\"/scripts/jquery-3.0.0.min.js\"></script>\n\n        <meta content=\"Amazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, co...\" name=\"description\"><meta content=\"Finextra,news,online,bank,banking,technology,finance,financial,fin,tech,fintech,IT,breaking,latest,retail,transaction,trade,execution,headlines,blockchain,digital,investment,mobile,business,challenger,payments,regtech,insurtech,services\" name=\"keywords\"><meta content=\"Finextra\" name=\"Author\"><meta name=\"google-site-verification\" content=\"s2szUe9egW7AXhBrmyYGhHy0-vIyvQhGQMzLSaJ4XrI\"><meta name=\"google-site-verification\" content=\"NxBXi6GUTnq3QxX3d3mgUEqeeC6JZEKX4AW5DqsvJm8\"><meta name=\"msvalidate.01\" content=\"E3C577580AA2C01DCA56EB6ADC99C096\"><meta name=\"yandex-verification\" content=\"2729e345bc4c5778\"><meta property=\"og:site_name\" content=\"Finextra Research\"><meta name=\"twitter:site\" content=\"@finextra\"><meta name=\"twitter:creator\" content=\"@finextra\"><meta name=\"twitter:image\" content=\"https://www.finextra.com/about/finextra-logo.png\">\n\n\n\n\n\n        \n        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n        })(window,document,'script','dataLayer','GTM-P64CHFM');</script>\n        \n\n        <!-- Hotjar Tracking Code for https://www.finextra.com -->\n        <script>\n            (function(h,o,t,j,a,r){\n                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n                h._hjSettings={hjid:3084044,hjsv:6};\n                a=o.getElementsByTagName('head')[0];\n                r=o.createElement('script');r.async=1;\n                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n                a.appendChild(r);\n            })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n        </script><script async=\"\" src=\"https://static.hotjar.com/c/hotjar-3084044.js?sv=6\"></script>\n\n\n\n\n\n        \n\n<script type=\"application/ld+json\">\n{\n\"@context\": \"http://schema.org\",\n \"@type\": \"BreadcrumbList\", \n \"itemListElement\": [\n{\n    \"@type\": \"ListItem\",\n    \"position\": 1,\n    \"name\": \"Home\",\n    \"item\": \"https://www.finextra.com\" \n}\n\n,{\n    \"@type\": \"ListItem\",\n    \"position\": 2,\n    \"name\": \"Community\",\n    \"item\": \"https://www.finextra.com/community\" \n}\n\n,{\n    \"@type\": \"ListItem\",\n    \"position\": 3,\n    \"name\": \"Serhii Bondarenko\",\n    \"item\": \"https://www.finextra.com/bloggers/160358\" \n}\n\n,{\n    \"@type\": \"ListItem\",\n    \"position\": 4,\n    \"name\": \"blog-article\",\n    \"item\": \"https://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\" \n}\n\n]\n}\n</script>\n<link rel=\"canonical\" href=\"https://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\"><meta property=\"og:type\" content=\"article\"><meta property=\"og:title\" content=\"Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025: By Serhii Bondarenko\"><meta property=\"og:description\" content=\"Amazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, co...\"><meta property=\"og:url\" content=\"https://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\"><meta property=\"og:image\" content=\"https://www.finextra.com/finextra-images/member_photos/thumb_160358_serhii_bondarenko_18.jpg\"><meta property=\"article:publisher\" content=\"https://www.facebook.com/Finextra-152506594761939/\"><meta property=\"article:published_time\" content=\"2025-06-17T09:14:35\"><meta property=\"og:updated_time\" content=\"2025-06-17T09:15:05\"><meta name=\"twitter:card\" content=\"summary\"><meta name=\"twitter:description\" content=\"Amazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, co...\"><meta name=\"twitter:title\" content=\"Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025: By Serhii Bondarenko\"><meta name=\"twitter:image\" content=\"https://www.finextra.com/finextra-images/member_photos/thumb_160358_serhii_bondarenko_18.jpg\">\n<script type=\"application/ld+json\">\n{\n\"@context\": \"http://schema.org\",\n \"@type\": \"Article\",\n\"mainEntityOfPage\":{\n  \"@type\":\"WebPage\",\n  \"@id\": \"https://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\"\n},\n\"headline\": \"Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025: By Serhii Bo...\",\n\"image\": {\n  \"@type\": \"ImageObject\",\n  \"url\": \"https://www.finextra.com/finextra-images/member_photos/thumb_160358_serhii_bondarenko_18.jpg\",\n  \"height\": 270,\n  \"width\": 480\n},\n\"author\": {\n\"@type\": \"Person\",\n\"name\": \"Editorial Team\"\n},\n\"datePublished\": \"2025-06-17T09:14:35\",\n \"dateModified\": \"2025-06-17T09:15:05\", \n \"publisher\": {\n  \"@type\": \"Organization\",\n  \"name\": \"Finextra\",\n  \"logo\": {\n    \"@type\": \"ImageObject\",\n    \"url\": \"https://www.finextra.com/about/finextra-logo.png\",\n    \"width\": 512,\n    \"height\": 512\n  }\n},\n\"description\": \"Amazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, co...\"\n}\n</script>\n\n<script type=\"text/javascript\">\n $(document).ready(function() {\n  $.ajax({\n    type: \"POST\", \n     url: \"/webservices/webservice.asmx/incrementviewcount\", \n    data: \"{iD:126421, articleID:28693}\",\n    contentType: \"application/json; charset=utf-8\",\n    dataType: \"json\" \n  });\n  });\n</script>\n<script async=\"\" src=\"https://script.hotjar.com/modules.3128f1ee3ce5b65c4961.js\" charset=\"utf-8\"></script><link rel=\"stylesheet\" id=\"stlsht\" href=\"https://widgets.jobbio.com/partner_fluid_widgets_v1.6.1/assets/css/v1_6_1.css\"><link rel=\"stylesheet\" id=\"customFont\" href=\"https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&amp;display=swap\"><script id=\"amply-imp\" src=\"https://widgets.jobbio.com/partner_fluid_widgets_v1.6.1/assets/js/imp.min.js\"></script><script id=\"ind-imp\" src=\"https://widgets.jobbio.com/partner_fluid_widgets_v1.6.1/assets/js/ind-imp.min.js\"></script></head>\n    <body>\n        \n        <noscript><iframe src=\"https://www.googletagmanager.com/ns.html?id=GTM-P64CHFM\" height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe></noscript>\n        \n        <form method=\"post\" action=\"/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\" id=\"aspnetForm\">\n<div>\n<input type=\"hidden\" name=\"__EVENTTARGET\" id=\"__EVENTTARGET\" value=\"\">\n<input type=\"hidden\" name=\"__EVENTARGUMENT\" id=\"__EVENTARGUMENT\" value=\"\">\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"/wEPDwUKLTk3NjQxODY4Mg8WAh4TVmFsaWRhdGVSZXF1ZXN0TW9kZQIBZGQhVbo56ge55pOQkfvok4L01SdtOzL6wetmigebWSgfaQ==\">\n</div>\n\n<script type=\"text/javascript\">\n//<![CDATA[\nvar theForm = document.forms['aspnetForm'];\nif (!theForm) {\n    theForm = document.aspnetForm;\n}\nfunction __doPostBack(eventTarget, eventArgument) {\n    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {\n        theForm.__EVENTTARGET.value = eventTarget;\n        theForm.__EVENTARGUMENT.value = eventArgument;\n        theForm.submit();\n    }\n}\n//]]>\n</script>\n\n\n<script src=\"/WebResource.axd?d=ORikNVkoK-ZrjXDXU0LPXkSyn9d-TE9Cl3NYN4ip5lgRD86YyBGWYJMcpemDJh7Xxz5O3nXT5M-wspGzFf1PqP0h5xlEQ9zXjzeOoC1xBgc1&amp;t=638628243619783110\" type=\"text/javascript\"></script>\n\n<div>\n\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"E31F3143\">\n\t<input type=\"hidden\" name=\"__PREVIOUSPAGE\" id=\"__PREVIOUSPAGE\" value=\"9dz7tZ7VAGctGGRcsvP9jf326lzabOZW7lceAhs39MCFr65PCN4j8UJy-aVqtIC735hF4TV-aY5Gp4N_NPiNb-ubQmHlDkchSSRAur6bCrE1\">\n\t<input type=\"hidden\" name=\"__EVENTVALIDATION\" id=\"__EVENTVALIDATION\" value=\"/wEdAAqUQ+yuQAM0N4OveDLSjN6ca8/hRlq5DLsz84mtqkSVuAusj593PSccTAt+yf5UjbCKd7TSGF0c/GMkDNB947o63fTVTRAVY3Jd84MUbJ+MPPRElBlBtx0kAlvg/b6UgqXpN7kXyNYSop53RMdop888vHPYg97asFivSS34KaiIMaCn/6qZVEiCOkNzXIF6YdVQbLQ2PcHcvatHciNgDLFzNOBqz8aJuVedrU9mg8kLrq4WgM4MVPk3xzt8HJkYd9A=\">\n</div>\n\n            \n            \n\n\n<script type=\"text/javascript\">\n<!--\n\n    function testValue(updateElement)\n    {\n        //checks for a value in a text box\n        //returns true or false\n        var obj = document.getElementById(updateElement);\n\n        if (obj.getAttribute)\n        {\n            if (obj.value) {\n                if (obj.value.length > 0) {\n                    return true;\n                }\n            }\n        }\n        //got this far, then no search term\n        //alert('about to fail');\n        return false;\n    }\n-->\n</script>\n\n\n    <header>\n        <div class=\"container header-top-section\">\n            <div class=\"row\">\n                <div class=\"col\">\n\n                    <div id=\"headertoprow\" class=\"d-flex align-items-center\">\n                        <div class=\"flex-grow-1\">\n                            <div class=\"d-inline-flex align-items-center\">\n                                <div id=\"headerlogoc\">\n                                    <a href=\"/\"><img class=\"header-logo\" src=\"/app_themes/earlgrey/images/finextra-logo.svg\" alt=\"Finextra Research\"></a>\n                                </div>\n                                <div id=\"livebuttonc2\">\n                                    \n                                 \n                                </div>                                \n\n                            </div>\n                        </div>\n                        \n                        <div class=\"d-flex align-items-center user-menu-section\">\n                            <div id=\"searchmenuc\" class=\"\">\n                                <div id=\"mainmenusearchbutton\" class=\"search-button\"> \n                                    <a href=\"#\" title=\"Search\" class=\"button button-secondary button-icon-only\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg'); mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg');\"></span></a>\n                                </div>\n                                <div id=\"mainmenusearchbar\" class=\"search-bar\">\n                                    <div class=\"header-menu-search-bar\">\n                                        \n                                        <div class=\"site-search-bar full-width\" onkeypress=\"javascript:return WebForm_FireDefaultButton(event, 'ctl00_ctl00_ctl22_bSearch')\">\n\t\n                                            <div class=\"search-input-container\">\n                                            <input name=\"ctl00$ctl00$ctl22$tSearch\" type=\"search\" id=\"ctl00_ctl00_ctl22_tSearch\" class=\"search-input\" required=\"\" placeholder=\"Search Finextra...\">\n                                                <button class=\"search-clear-button\" type=\"reset\"></button>\n                                            </div>\n\n                                            <a onclick=\"return testValue('ctl00_ctl00_ctl22_tSearch');\" id=\"ctl00_ctl00_ctl22_bSearch\" class=\"search-button\" href=\"javascript:__doPostBack('ctl00$ctl00$ctl22$bSearch','')\">\n                                                <span class=\"search-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg'); mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg');\">\n                                                </span>\n                                            </a>\n\n                                        \n</div>\n                                        \n                                    </div>\n                                </div>\n                                <div id=\"mainmenuexpandedsearchmenu\" class=\"\" style=\"display: grid;\"> \n                                    <div class=\"header-menu-search-bar\">\n                                        \n                                        <div class=\"site-search-bar full-width\" onkeypress=\"javascript:return WebForm_FireDefaultButton(event, 'ctl00_ctl00_ctl22_bMobSearch')\">\n\t\n                                                <div class=\"search-input-container\">\n                                                    <input name=\"ctl00$ctl00$ctl22$tMobSearch\" type=\"search\" id=\"ctl00_ctl00_ctl22_tMobSearch\" class=\"search-input\" required=\"\" placeholder=\"Search Finextra...\" tabindex=\"-1\">\n                                                    <button class=\"search-clear-button\" type=\"reset\"></button>\n                                                </div>\n                                                <a onclick=\"return testValue('ctl00_ctl00_ctl22_tMobSearch');\" id=\"ctl00_ctl00_ctl22_bMobSearch\" class=\"search-button\" href=\"javascript:__doPostBack('ctl00$ctl00$ctl22$bMobSearch','')\" tabindex=\"-1\">\n                                                    <span class=\"search-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg'); mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg');\">\n                                                    </span>\n                                                </a>\n                                        \n</div>\n                                        <div class=\"header-menu-search-bar-close\">\n                                            <a id=\"mainmenuexpandedsearchmenuclosebutton\" href=\"#\" title=\"Close\" class=\"button button-secondary button-icon-only\" tabindex=\"-1\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/editing/Cross.svg'); mask-image: url('/app_themes/earlgrey/images/icons/editing/Cross.svg');\"></span></a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n\n                            <div id=\"ctl00_ctl00_ctl22_pMember\">\n\t \n                                \n\n                                \n\n                                \n                            <a href=\"/community/login.aspx?returnurl=%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\" rel=\"nofollow\" id=\"headerProfileButton\" class=\"button button-small button-secondary button-icon-only\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/profile-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/profile-1.svg');\"></span></a>\n</div>\n                            <div id=\"headerSignInButton\" class=\"d-none\">\n\t\n                                <a class=\"button button-icon-left button-secondary \" href=\"/community/login.aspx\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/Profile-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/Profile-1.svg');\"></span><span class=\"button-text\">Sign in</span></a>\n                            \n</div>\n                            <div id=\"ctl00_ctl00_ctl22_dRegister\">\n                                <a id=\"headerSignUpButton\" href=\"/community/register.aspx\" class=\"button button-primary\"><span class=\"button-text\" rel=\"nofollow\">Sign up</span></a>\n                            </div>\n\n                         </div>\n\n                    </div>\n\n                </div>\n            </div>\n\n            <div class=\"row\">\n                <div id=\"livebuttonc1\" class=\"col-12\">\n                    \n\n                </div>\n            </div>\n\n\n            <div class=\"row main-nav-c1-row\"> \n                <div id=\"mainnavc1\" class=\"col-12\">\n                    <nav id=\"mainnavbar\" type=\"Main Navigation\">\n                        <div id=\"desktopnav\">\n                            <div class=\"main-nav-fader-left\"></div>\n                            <div id=\"mainnavburger\">\n                                <a id=\"mobileNavBurgerButton\" title=\"MobileNavBurger\" class=\"button button-secondary button-icon-only\" role=\"button\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/menus/Menu.svg'); mask-image: url('/app_themes/earlgrey/images/icons/menus/Menu.svg');\"></span></a>\n                            </div>\n                            <!-- Developer note: Ensure no white space is around a href content text -->\n                            <!-- Desktop Navbar content menu items start -->\n                            <div id=\"mainnavdesktop\" class=\"navbar\">\n                                <ul id=\"ctl00_ctl00_ctl22_ulDesktopNav\" class=\"navbar-nav\"><li class=\"nav-item dropdown\"><a href=\"/\" id=\"dVnews\" class=\"nav-link dropdown-toggle\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">News</a><ul class=\"dropdown-menu\"><li><a href=\"/latest-news\" class=\"dropdown-item\">Latest news</a></li><li><a href=\"/latest-announcements\" class=\"dropdown-item\">Company updates</a></li><li><a href=\"/latest-long-reads\" class=\"dropdown-item\">Long reads</a></li></ul></li><li class=\"nav-item\"><a href=\"/latest-videos\" id=\"dVtv\" class=\"nav-link\">TV</a></li><li class=\"nav-item\"><a href=\"/research\" id=\"dVresearch\" class=\"nav-link\">Research</a></li><li class=\"nav-item dropdown\"><a href=\"/events/timeline\" id=\"dVevents\" class=\"nav-link dropdown-toggle\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">Events</a><ul class=\"dropdown-menu\"><li><a href=\"/events/timeline\" class=\"dropdown-item\">All</a></li><li><a href=\"/events/timeline?eventtype=conference\" class=\"dropdown-item\">Conferences</a></li><li><a href=\"/events/timeline?eventtype=webinar\" class=\"dropdown-item\">Webinars</a></li><li><a href=\"/events/top/\" class=\"dropdown-item\">Popular</a></li></ul></li><li class=\"nav-item dropdown\"><a href=\"/community\" id=\"dVcommunity\" class=\"nav-link dropdown-toggle active\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">Community</a><ul class=\"dropdown-menu\"><li><a href=\"/community\" class=\"dropdown-item\">Community latest</a></li><li><a href=\"/latest-blogs\" class=\"dropdown-item\">Latest expert opinions</a></li><li><a href=\"/blogs/groups.aspx\" class=\"dropdown-item\">Groups</a></li><li><a href=\"/community/members/search.aspx\" class=\"dropdown-item\">Search members</a></li></ul></li><li class=\"nav-item\"><a href=\"/jobs\" id=\"dVjobs\" class=\"nav-link\">Jobs</a></li><li class=\"nav-item\"><a href=\"/open-banking-apis\" id=\"dVapis\" class=\"nav-link\">APIs</a></li></ul>\n                            </div>\n                            <!-- Desktop Navbar content menu items end -->\n                        </div>\n                        <div id=\"mobilenavBackground\" class=\"mobilenav-background mobile-nav-background-no-cover\">\n                            <div id=\"mobilenav\">                     \n                                <!-- Mobile Navbar content menu items start -->\n                                <div id=\"mobilenavtop\" class=\"mobile-nav-top\">\n                                    <div class=\"mobile-nav-top-actions\">\n                                        <div class=\"mobile-nav-top-actions-close\">\n                                            <a id=\"mobileNavBurgerButtonClose\" title=\"Close\" class=\"button button-secondary button-icon-only\" role=\"button\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/editing/Cross.svg'); mask-image: url('/app_themes/earlgrey/images/icons/editing/Cross.svg');\"></span></a>\n                                        </div>\n                                        <div class=\"mobile-nav-top-actions-profile\">\n                                            <div class=\"me-3\">\n                                                <a href=\"/community/login.aspx?returnurl=%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\" id=\"ctl00_ctl00_ctl22_mobileSignIn\" class=\"button button-secondary\"><span class=\"button-text\" role=\"button\">Sign in</span></a>\n                                            </div>\n                                            <div>\n                                                <a href=\"/community/register.aspx\" id=\"ctl00_ctl00_ctl22_mobileSignUp\" class=\"button button-primary\"><span class=\"button-text\" role=\"button\">Sign up</span></a>\n                                                \n                                            </div>\n                                        </div>\n                                    </div>\n                                    <div class=\"mobile-nav-top-search\">\n                                        <div class=\"mobile-nav-top-search-bar\">\n                                            \n                                            <div class=\"site-search-bar full-width\" onkeypress=\"javascript:return WebForm_FireDefaultButton(event, 'ctl00_ctl00_ctl22_bMobSearch2')\">\n\t\n                                                    <div class=\"search-input-container\">\n                                                        <input name=\"ctl00$ctl00$ctl22$tMobSearch2\" type=\"search\" id=\"ctl00_ctl00_ctl22_tMobSearch2\" class=\"search-input\" required=\"\" placeholder=\"Search Finextra...\">\n                                                        <button class=\"search-clear-button\" type=\"reset\"></button>\n                                                    </div>\n                                                    <a onclick=\"return testValue('ctl00_ctl00_ctl22_tMobSearch2');\" id=\"ctl00_ctl00_ctl22_bMobSearch2\" class=\"search-button\" href=\"javascript:__doPostBack('ctl00$ctl00$ctl22$bMobSearch2','')\">\n                                                        <span class=\"search-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg'); mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Search.svg');\">\n                                                        </span>\n                                                    </a>\n                                            \n</div>\n                                        </div>\n                                    </div>\n                                    <div class=\"mobile-nav-top-live\">\n                                        \n                                        \n                                    </div>\n                                </div>\n                                <ul id=\"ctl00_ctl00_ctl22_ulMobileNav\" class=\"navbar-nav\" style=\"min-height: calc(100% + 0px);\"><li class=\"nav-item dropdown\"><a href=\"/\" id=\"mvnews\" class=\"nav-link dropdown-toggle\" role=\"button\" aria-expanded=\"false\">News</a><ul class=\"dropdown-menu\"><li><a href=\"#\" class=\"dropdown-item dropdown-item-back\"><span class=\"mobile-nav-back-caret\"></span><span class=\"mobile-nav-back-text\">Back</span></a></li><li><a href=\"/\" class=\"dropdown-item dropdown-item-parent\">News</a></li><li><a href=\"/latest-news\" class=\"dropdown-item\">Latest news</a></li><li><a href=\"/latest-announcements\" class=\"dropdown-item\">Company updates</a></li><li><a href=\"/latest-long-reads\" class=\"dropdown-item\">Long reads</a></li></ul></li><li class=\"nav-item\"><a href=\"/latest-videos\" id=\"mvtv\" class=\"nav-link\">TV</a></li><li class=\"nav-item\"><a href=\"/research\" id=\"mvresearch\" class=\"nav-link\">Research</a></li><li class=\"nav-item dropdown\"><a href=\"/events/timeline\" id=\"mvevents\" class=\"nav-link dropdown-toggle\" role=\"button\" aria-expanded=\"false\">Events</a><ul class=\"dropdown-menu\"><li><a href=\"#\" class=\"dropdown-item dropdown-item-back\"><span class=\"mobile-nav-back-caret\"></span><span class=\"mobile-nav-back-text\">Back</span></a></li><li><a href=\"/events/timeline\" class=\"dropdown-item dropdown-item-parent\">Events</a></li><li><a href=\"/events/timeline\" class=\"dropdown-item\">All</a></li><li><a href=\"/events/timeline?eventtype=conference\" class=\"dropdown-item\">Conferences</a></li><li><a href=\"/events/timeline?eventtype=webinar\" class=\"dropdown-item\">Webinars</a></li><li><a href=\"/events/top/\" class=\"dropdown-item\">Popular</a></li></ul></li><li class=\"nav-item dropdown\"><a href=\"/community\" id=\"mvcommunity\" class=\"nav-link dropdown-toggle\" role=\"button\" aria-expanded=\"false\">Community</a><ul class=\"dropdown-menu\"><li><a href=\"#\" class=\"dropdown-item dropdown-item-back\"><span class=\"mobile-nav-back-caret\"></span><span class=\"mobile-nav-back-text\">Back</span></a></li><li><a href=\"/community\" class=\"dropdown-item dropdown-item-parent\">Community</a></li><li><a href=\"/community\" class=\"dropdown-item\">Community latest</a></li><li><a href=\"/latest-blogs\" class=\"dropdown-item\">Latest expert opinions</a></li><li><a href=\"/blogs/groups.aspx\" class=\"dropdown-item\">Groups</a></li><li><a href=\"/community/members/search.aspx\" class=\"dropdown-item\">Search members</a></li></ul></li><li class=\"nav-item\"><a href=\"/jobs\" id=\"mvjobs\" class=\"nav-link\">Jobs</a></li><li class=\"nav-item\"><a href=\"/open-banking-apis\" id=\"mvapis\" class=\"nav-link\">APIs</a></li></ul>\n                                <!-- Mobile Navbar content menu items end -->\n                            </div>\n                        </div>\n                    </nav>\n                </div>\n                <div class=\"main-nav-fader-right\">\n                </div>\n            </div>\n        </div>\n\n        \n        <div class=\"header-taxonomy-nav-section brand-gradient-category-nav\">\n            <div class=\"container taxonomy-nav-scroll-fix\">\n                <div class=\"row\">\n                    <div id=\"taxonomynavc1\" class=\"col-12\">\n\n                        <nav id=\"taxonomynavbar\" class=\"taxonomy-navbar\" type=\"Category Navigation\">\n                            <ul id=\"ctl00_ctl00_ctl22_ulChannels\" class=\"nav\"><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/payments\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">payments</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/markets\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">markets</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/retail\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">retail</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/wholesale\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">wholesale</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/wealth\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">wealth</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/regulation\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">regulation</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/crime\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">crime</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/crypto\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">crypto</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/sustainable\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">sustainable</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/startups\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">startups</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/devops\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">devops</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/identity\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">identity</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/security\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">security</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/cloud\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">cloud</span></a></li><li class=\"nav-item taxonomy-nav-item\"><a href=\"/channel/ai\" class=\"nav-link tax-button-default tax-button-slanted\"><span class=\"tax-button-slanted-content\">ai</span></a></li><li class=\"nav-item\"><span class=\"tax-button-spacer\"></span></li><li id=\"taxonomynavbutton\" class=\"taxonomy-nav-button\" style=\"display: block;\"><a><span id=\"taxonomynavbuttonicon\" class=\"taxonomy-nav-button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/editing/Add.svg'); mask-image: url('/app_themes/earlgrey/images/icons/editing/Add.svg')\"></span></a></li></ul>\n                        </nav>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n\n\n    </header>\n\n\n                \n            <main>\n                \n    \n        <!--hero etc -->\n    \n\n\n\n    <!--breadcrumb etc -->\n    \n\n\n\n    <!--Content etc -->\n    \n    <!-- Article Body -->\n        <div class=\"main--container community-opinion\">\n            <!-- Breadcrumb -->\n            \n\n    <section class=\"section--breadcrumb\">\n        <div class=\"container\">\n            <div class=\"row\">\n                <div class=\"col-12\">\n                    <nav aria-label=\"breadcrumb\" class=\"breadcrumb-container\">\n                        <ol id=\"ctl00_ctl00_body_main_Trail_list\" class=\"breadcrumb\">\n                        <li class=\"breadcrumb-item\"><a href=\"https://www.finextra.com\">Home</a></li><li class=\"breadcrumb-item\"><a href=\"https://www.finextra.com/community\">Community</a></li><li class=\"breadcrumb-item\"><a href=\"https://www.finextra.com/bloggers/160358\">Serhii Bondarenko</a></li><li class=\"breadcrumb-item active\" aria-current=\"page\">Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025</li></ol>\n                    </nav>\n                </div>\n            </div>\n        </div>\n    </section>\n            <!-- END: Breadcrumb -->\n            <!-- Community heading -->\n            <section class=\"section--community-heading\">\n                <div class=\"container\">\n                    <div class=\"row\">\n                        <div class=\"col-12\">\n                            \n\n    \n    <div class=\"main-title--row main-title--row-navigation\">\n        <div class=\"listing-heading listing-heading-gradient listing-heading-gradient-underline\">\n            \n            <p id=\"ctl00_ctl00_body_main_ListingHeader_pTitle\" class=\"h2\"><a href=\"/community\" aria-label=\"Community homepage\">Community</a></p>\n        </div>\n        <div class=\"listing-links-column community-heading-links\">\n            <ul>\n                <li><a id=\"ctl00_ctl00_body_main_ListingHeader_hCommunity\" href=\"/community\">Your feed</a></li>\n                <li><a id=\"ctl00_ctl00_body_main_ListingHeader_hCompany\" class=\"active\" href=\"/latest-blogs\">Latest expert opinions</a></li>\n                <li><a id=\"ctl00_ctl00_body_main_ListingHeader_hGroups\" href=\"/blogs/groups.aspx\">Groups</a></li>\n            </ul>\n        </div>\n    </div>\n    \n\n\n                        </div>\n                    </div>\n                </div>\n            </section>\n\n            \n            <!-- Article Published Bar -->\n            \n\n            <!-- Article Preview Bar -->\n            \n\n\n            <!-- Join community - Below heading only visible on @ < 768 breakpoint and Not logged in -->\n            <section class=\"section--community-join-community-main-container sticky-top\">\n                <div class=\"container\">\n                    \n\n    <div class=\"join-community d-md-none\">\n        <h2 class=\"h4 join-community-title\">Join the Community</h2>\n        <div class=\"join-community-postStats\">\n            <div class=\"join-community-postStat join-community-postStat-mobile\">\n                <div class=\"join-community-postStat-desc\">23,441</div>\n                <div class=\"join-community-postStat-title\">Expert opinions</div>\n            </div>\n            <div class=\"join-community-postStat join-community-postStat-mobile\">\n                <div class=\"join-community-postStat-desc\">42,351</div>\n                <div class=\"join-community-postStat-title\">Total members</div>\n            </div>\n            <div class=\"join-community-postStat\">\n                <div class=\"join-community-postStat-desc\">311</div>\n                <div class=\"join-community-postStat-title\">New members (last 30 days)</div>\n            </div>\n            <div class=\"join-community-postStat\">\n                <div class=\"join-community-postStat-desc\">178</div>\n                <div class=\"join-community-postStat-title\">New opinions (last 30 days)</div>\n            </div>\n            <div class=\"join-community-postStat\">\n                <div class=\"join-community-postStat-desc\">29,126</div>\n                <div class=\"join-community-postStat-title\">Total comments</div>\n            </div>\n        </div>\n        <div class=\"button-container join-community-buttons\">\n            <a class=\"button button-icon-left button-primary button-center button-small\" href=\"/community/register.aspx\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/Community.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/Community.svg');\"></span><span class=\"button-text\">Join</span></a>\n            <a class=\"button button-icon-left button-secondary button-center button-small\" href=\"/community/login.aspx\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/Login.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/Login.svg');\"></span><span class=\"button-text\">Sign in</span></a>\n        </div>\n    </div>\n                </div>\n            </section>\n\n            <!-- Community opinion content -->\n            <section class=\"section--community-opinion\">\n                <div class=\"container\">\n                    <div class=\"row\">\n                        <div class=\"col-12 col-lg-9 opinion-main-column\">\n\n                            <div class=\"row\">\n                                <div class=\"col-12\">\n\n                                    \n\n<!-- H1 heading -->\n<h1>Amazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025</h1>\n\n<!-- Timestamp / Comments shoutout-->\n<div class=\"opinion-intro-stats card-baseline\">\n    \n\n    \n    <span class=\"card-button card-like-button\">\n\n\n        \n        <a id=\"aLikeButton\" class=\"button button-small button-icon-left button-secondary button-center button-transparent button-like\" href=\"javascript:WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(&quot;ctl00$ctl00$body$main$HeaderForm$LikeButton$aLikeButton&quot;, &quot;&quot;, false, &quot;&quot;, &quot;/community/login.aspx?returnurl=%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&quot;, false, true))\">\n            <span class=\"button-icon\"></span>\n            <span id=\"likeTally\" class=\"button-text\">1 Like</span></a>\n\n    </span>\n    \n\n\n\n    <span class=\"card-button card-linkedin-button\">\n        <a id=\"ctl00_ctl00_body_main_HeaderForm_LinkedInButton_aLiButton\" class=\"button button-small button-icon-left button-secondary button-center button-transparent btnLinkedIn \" aria-label=\"Share to: LinkedIn\" title=\"Share to: LinkedIn\">\n            <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/LinkedIn.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/LinkedIn.svg');\"></span>\n            <span id=\"liButtonResult\" class=\"button-text\">1</span>\n        </a>\n    </span>\n    <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-06-17 09:14:35Z\">17 June 2025</time>\n    <a href=\"#comments\" class=\"card-comments card-icon messages\">Be the first to comment</a>\n    \n</div>\n\n\n\n\n\n\n\n\n\n\n\n                                        \n\n\n            <div class=\"section--article-mainContent-authorbox\">\n                <div class=\"article-authorDetails article-authorDetails-mainColumn\">\n\n                    <div class=\"article-authorDetails-bio\">\n                        <div class=\"article-authorDetails-bio-image\">\n\n                            <a id=\"ctl00_ctl00_body_main_BlogProfileForm_hBlogger\" aria-label=\"link to author: Serhii Bondarenko\" href=\"/bloggers/160358\"><div class=\"blue-tick blue-tick-bottom-9\"><img id=\"ctl00_ctl00_body_main_BlogProfileForm_iBlogger\" class=\"card-img-round\" src=\"../../finextra-images/member_photos/thumb_160358_serhii_bondarenko_18.jpg\" alt=\"Serhii Bondarenko\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n\n                        </div>\n                        <div class=\"article-authorDetails-bio-desc\">\n                            <div class=\"article-authorDetails-bioGroup\">\n                                <div class=\"article-authorDetails-bio-desc-name\"><span class=\"h4\">Serhii Bondarenko</span></div>\n                                <div class=\"article-authorDetails-bio-desc-jobTitle\">Artificial Intelegence</div>\n                                <span id=\"ctl00_ctl00_body_main_BlogProfileForm_lbCompany\" class=\"article-authorDetails-bio-desc-companyName\">Tickeron</span> \n                            </div>\n\n                            <div id=\"ctl00_ctl00_body_main_BlogProfileForm_dAuthorDetails\" class=\"article-authorDetails-detailsGroup\">\n                                <div class=\"article-authorDetails-postStats\">\n                                    <div class=\"article-authorDetails-postStat\">\n                                        <div class=\"article-authorDetails-social-link\">\n                                            <div class=\"article-authorDetails-postStat-title\">Location</div>\n                                            <div class=\"article-authorDetails-postStat-desc\">Weiden</div>\n                                        </div>\n                                    </div>\n                                    <div class=\"article-authorDetails-postStat\">\n                                        <div class=\"article-authorDetails-social-link\">\n                                            <div class=\"article-authorDetails-postStat-title\">Followers</div>\n                                            <div class=\"article-authorDetails-postStat-desc\">9</div>\n                                        </div>\n                                    </div>\n                                    <div class=\"article-authorDetails-postStat\">\n                                        <div class=\"article-authorDetails-social-link\">\n                                            <div class=\"article-authorDetails-postStat-title\">Opinions</div>\n                                            <div class=\"article-authorDetails-postStat-desc\">60</div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n\n                            <div class=\"article-authorDetails-buttonsGroup\">\n\n                                <div class=\"button-container article-authorDetails-buttons\">\n\n                                    <a id=\"ctl00_ctl00_body_main_BlogProfileForm_bFollow\" class=\"button button-small button-icon-left button-secondary button-center follow-author \" href=\"/community/login.aspx?returnurl=/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\">\n                                        <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionFollow-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionFollow-1.svg');\"></span>\n                                        <span class=\"button-text\">Follow</span>\n                                    </a>\n                                    <a id=\"ctl00_ctl00_body_main_BlogProfileForm_bUnFollow\" class=\"button button-small button-icon-left button-secondary button-center unfollow-author d-none\" href=\"javascript:__doPostBack('ctl00$ctl00$body$main$BlogProfileForm$bUnFollow','')\">\n                                        <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionUnFollow-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionUnFollow-1.svg');\"></span>\n                                        <span class=\"button-text\">Unfollow</span>\n                                    </a>\n\n                                </div>\n                            </div>\n\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n\n\n\n\n\n\n                                </div>\n                                <div class=\"col-12 col-md-9 col-lg-8 section--article-body\">\n\n                                    \n\n\n<div class=\"opinion-article-content article-content alt-body-copy\">\n    \n<p dir=\"auto\">Amazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, consistently shaping market trends through innovation and scale. As of June 17, 2025, AMZN’s stock has exhibited robust performance, driven by its\n strategic investments in artificial intelligence (AI), cloud infrastructure, and operational efficiencies. This article provides a comprehensive financial analysis of AMZN’s recent market movements, key news driving its performance, a comparison with a correlated\n stock, insights into trading with inverse ETFs, and the role of AI-driven tools in navigating its volatility.</p>\n<h2 dir=\"auto\">Recent Stock Performance and Financial Metrics</h2>\n<p dir=\"auto\">Over the five trading days ending June 8, 2025, AMZN stock surged by 6.75%, outpacing the broader market and reflecting strong investor confidence in Amazon’s growth trajectory. This momentum continued into mid-June, with the stock rising 1.57%\n on June 16, 2025, according to The Motley Fool. Amazon’s year-to-date performance has been remarkable, with shares rebounding 42% from a 52-week low of $151.76, fueled by record earnings projections for 2025 and 2026.</p>\n<p dir=\"auto\">For the first quarter of 2025, Amazon reported a 62% year-over-year increase in earnings per share (EPS), with net sales reaching $155.7 billion, a 9% increase from the prior year. Amazon Web Services (AWS), the company’s cloud computing division,\n posted $29.3 billion in sales, up 17% year-over-year, though margins softened slightly to 39.5%. Operating income climbed 20% to $18.4 billion, surpassing estimates of $17.48 billion. Looking ahead, Amazon’s guidance for Q2 2025 projects net sales between\n $159 billion and $164 billion, representing approximately 9% growth at the midpoint, with operating income expected between $13 billion and $17.5 billion.</p>\n<p dir=\"auto\">The company’s forward price-to-earnings (P/E) ratio has contracted significantly, dropping from 90 in 2021 to 30 as of March 2025, while trading at 17 times operating cash flow, its lowest ever. This suggests AMZN may be undervalued relative to\n its growth potential, particularly given its 50% revenue growth and doubled operating margins since 2021. However, free cash flow declined 48% over the last twelve months, reflecting heavy capital expenditures, including a $100 billion+ investment planned\n for 2025, primarily in AI and AWS infrastructure.</p>\n<h2 dir=\"auto\">Market News Driving AMZN’s Performance on June 17, 2025</h2>\n<p dir=\"auto\">Several macroeconomic and company-specific developments have influenced AMZN’s stock movement as of June 17, 2025. On June 16, the broader market saw gains, with the Dow, S&amp;P 500, and Nasdaq rising amid optimism over contained geopolitical tensions\n between Israel and Iran, boosting risk appetite. Amazon benefited from this sentiment, particularly due to its exposure to global trade. The company’s e-commerce operations, which account for nearly 40% of U.S. online sales, are poised to capitalize on easing\n trade tensions, as Amazon sells and facilitates imported goods.</p>\n<p dir=\"auto\">Additionally, Amazon’s partnership with Roku, announced on June 16, 2025, to create the “largest authenticated Connected TV (CTV) footprint” in the U.S. through Amazon Ads, drove a 10% surge in Roku’s stock and further bolstered AMZN’s advertising\n segment, which grew 18% year-over-year in Q1 2025. Amazon’s increasing reliance on AI to optimize operations, including robotics in its fulfillment network and custom silicon for AI workloads, has also garnered Wall Street attention, with analysts projecting\n new highs for AMZN in the second half of 2025.</p>\n<p dir=\"auto\">However, risks persist. High tariffs on Chinese imports, as noted by Zacks, could pressure Amazon’s retail margins, though the company’s diversified revenue streams—particularly AWS—mitigate this exposure. Softer-than-expected inflation data and\n hopes for Federal Reserve rate cuts, reported on June 12, 2025, further supported growth stocks like AMZN, as lower interest rates reduce borrowing costs for capital-intensive investments.</p>\n<h2 dir=\"auto\">Comparison with Correlated Stock: Microsoft (MSFT)</h2>\n<p dir=\"auto\">Amazon’s stock performance is closely correlated with other mega-cap technology companies, particularly Microsoft (NASDAQ: MSFT), due to their shared dominance in cloud computing and AI. Over the five trading days ending June 8, 2025, MSFT gained\n 5.2%, trailing AMZN’s 6.75% increase, indicating Amazon’s stronger short-term momentum. Both companies benefit from the surging demand for AI infrastructure, with Microsoft’s Azure competing directly with AWS. However, Amazon’s broader exposure to e-commerce\n and advertising provides a diversified revenue base, giving it an edge in certain market conditions. While Microsoft’s cloud growth is robust, AWS’s projected 17-20% year-over-year growth and record-high margins of 39.5% in Q1 2025 underscore Amazon’s leadership\n in the cloud sector. Investors eyeing AMZN may find MSFT a complementary holding, but Amazon’s multi-sector presence makes it a more versatile growth play.</p>\n<h2 dir=\"auto\">Trading AMZN with Inverse ETFs</h2>\n<p dir=\"auto\">For traders seeking to hedge or capitalize on AMZN’s volatility, inverse ETFs like the ProShares Short QQQ (PSQ) offer a strategic tool. PSQ is designed to deliver daily inverse performance to the Nasdaq-100, which includes AMZN as a major holding.\n Given AMZN’s high correlation with the Nasdaq-100 (beta of approximately 1.2), PSQ provides a near-perfect anti-correlation, making it ideal for short-term strategies. When AMZN rallies, PSQ typically declines, and vice versa, allowing traders to profit from\n downward movements without shorting the stock directly. However, inverse ETFs carry higher risks due to daily rebalancing, which can lead to compounding losses in volatile markets. Traders must employ disciplined risk management, using tools like stop-loss\n orders, to mitigate these risks. Pairing AMZN with PSQ enables a balanced approach, capturing gains from bullish trends while hedging against potential corrections.</p>\n<h2 dir=\"auto\">AI-Driven Trading and Market Insights</h2>\n<p dir=\"auto\">The integration of AI into financial markets has transformed how investors approach stocks like AMZN. Advanced platforms leverage machine learning to analyze vast datasets, identifying patterns that human traders might miss. One such platform,\n led by CEO Sergey Savastiouk, utilizes Financial Learning Models (FLMs) to combine technical analysis with AI, offering precise entry and exit signals for high-liquidity stocks like AMZN. These tools, including user-friendly trading bots and Double Agents\n that detect both bullish and bearish signals, empower traders to navigate AMZN’s volatility with greater confidence. For instance, the Moving Average Convergence Divergence (MACD) for AMZN turned positive on June 6, 2025, signaling a bullish trend that AI-driven\n tools could have capitalized on. Such innovations highlight the growing role of AI in optimizing trading strategies.</p>\n<h2 dir=\"auto\">Outlook and Strategic Considerations</h2>\n<p dir=\"auto\">Amazon’s stock remains a compelling option for investors seeking exposure to technology, e-commerce, and cloud computing. Its robust Q1 2025 performance, driven by AWS growth and operational efficiencies, positions it for further gains, though\n heavy capital expenditures and potential tariff pressures warrant caution. The company’s AI investments, including a 70% increase in in-house chip shipments projected for 2025, signal long-term growth potential. Traders can leverage AMZN’s momentum while using\n inverse ETFs like PSQ for hedging, supported by AI-driven tools to enhance decision-making. As market optimism grows with easing trade and geopolitical tensions, AMZN is well-positioned to lead the next phase of the tech bull market, potentially hitting new\n highs by year-end</p>\n\n</div>\n\n\n\n\n\n\n\n\n\n                                    <!-- Opinion Article footer -->\n                                    <div class=\"opinion-article-footer\">\n\n                                        <div class=\"d-md-none\">\n                                            <div class=\"button button-secondary content-box  mobile\"><h4>External</h4><p>This content is provided by an external author without editing by Finextra. It expresses the views and opinions of the author.</p></div>\n                                        </div>\n\n                                        \n                                        \n\n    <!-- Views and report bar -->\n    <div class=\"card-views-and-report\">\n        <div>\n            <span class=\"card-eye card-icon eye\">7417</span>\n        </div>\n        <div>\n            <a href=\"mailto:<EMAIL>?subject=Abuse%20report&amp;body=Abuse%20report%20for%20https://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025%0D%0DPlease%20take%20a%20look%20at%20this%20item.\" id=\"ctl00_ctl00_body_main_ViewCounter_hReport\" class=\"button button-icon-left button-secondary button-center no-border\" rel=\"no-follow\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Warning.svg'); mask-image: url('/app_themes/earlgrey/images/icons/generalUI/Warning.svg');\"></span><span class=\"button-text\">Report</span></a>\n        </div>\n    </div>\n\n\n\n\n                                        \n                                        \n\n\n        <script src=\"/App_Themes/EarlGrey/scripts/modules/site/UpdateShareStatsScript.js\"></script>  \n       \n        <script>\nvar twLink = \"https://twitter.com/intent/tweet?text=Amazon+(AMZN)+Analysis%3a+Performance%2c+Market+Dynamics%2c+and+AI-Driven+Insights+as+of+June+17%2c+2025&url=https%3a%2f%2fwww.finextra.com%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&via=Finextra\";\nvar fbLink = \"http://www.facebook.com/sharer.php?u=https%3a%2f%2fwww.finextra.com%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\";\nvar liLink = \"http://www.linkedin.com/shareArticle?mini=true&url=https%3a%2f%2fwww.finextra.com%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&title=Amazon+(AMZN)+Analysis%3a+Performance%2c+Market+Dynamics%2c+and+AI-Driven+Insights+as+of+June+17%2c+2025&summary=Amazon+(NASDAQ%3a+AMZN)+remains+a+titan+in+the+technology%2c+e-commerce%2c+and+cloud+computing+sectors%2c+co...&source=Finextra\";\nvar emLink = \"mailto:?subject=Article%20on%20Finextra&body=Thought%20you%20might%20be%20interested%20in%20this%20item%20on%20Finextra.com.%0D%0Dhttps://www.finextra.com/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025%0D%0DAmazon (AMZN) Analysis: Performance, Market Dynamics, and AI-Driven Insights as of June 17, 2025%0D%0DAmazon (NASDAQ: AMZN) remains a titan in the technology, e-commerce, and cloud computing sectors, co...\";\nvar reLink = \"http://reddit.com/submit?url=https%3a%2f%2fwww.finextra.com%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&title=Amazon+(AMZN)+Analysis%3a+Performance%2c+Market+Dynamics%2c+and+AI-Driven+Insights+as+of+June+17%2c+2025\";\nvar iItemID = 28693;\nvar iStoryType = 4;\n</script>\n\n\n\n    <div class=\"likesharebar\">\n        \n        <div class=\"likesharebar-group\">\n            <div id=\"ctl00_ctl00_body_main_ShareItemForm_dLikes\" class=\"likesharebar-item likesharebar-likes\">\n\n\n\n                <a id=\"aLikeShareButton\" class=\"button button-icon-left button-secondary button-center button-fullWidth button-like\" href=\"javascript:WebForm_DoPostBackWithOptions(new WebForm_PostBackOptions(&quot;ctl00$ctl00$body$main$ShareItemForm$aLikeShareButton&quot;, &quot;&quot;, false, &quot;&quot;, &quot;/community/login.aspx?returnurl=%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&quot;, false, true))\">\n                    <span class=\"button-icon\"></span>                    \n                    <span id=\"likeShareTally\" class=\"button-text\">1 Like</span></a>\n\n\n            </div>\n        </div>\n        \n        <div class=\"likesharebar-group\">\n            <div id=\"ctl00_ctl00_body_main_ShareItemForm_dHeading\" class=\"likesharebar-item likesharebar-title col-xl-2\">\n                <h2 class=\"h4\">Share</h2>\n            </div>\n            <div class=\"likesharebar-item\">\n                <a id=\"ctl00_ctl00_body_main_ShareItemForm_aTw\" class=\"button button-icon-left button-secondary button-center button-fullWidth btnTwitter\" aria-label=\"Share to: Twitter\" title=\"Share to: Twitter\">\n                    <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/Twitter.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/Twitter.svg');\"></span>\n                    <span class=\"button-text\" id=\"twitterResult\">2</span>\n                </a>\n            </div>\n            <div class=\"likesharebar-item\">\n                <a id=\"ctl00_ctl00_body_main_ShareItemForm_aLi\" class=\"button button-icon-left button-secondary button-center button-fullWidth btnLinkedIn\" aria-label=\"Share to: LinkedIn\" title=\"Share to: LinkedIn\">\n                    <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/LinkedIn.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/LinkedIn.svg');\"></span>\n                    <span class=\"button-text\" id=\"liResult\">1</span>\n                </a>\n            </div>\n            <div class=\"likesharebar-item\">                \n                <a id=\"ctl00_ctl00_body_main_ShareItemForm_aFb\" class=\"button button-icon-left button-secondary button-center button-fullWidth btnFacebook\" aria-label=\"Share to: Facebook\" title=\"Share to: Facebook\">\n                    <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/facebook.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/facebook.svg');\"></span>\n                    <span class=\"button-text\" id=\"fbResult\">1</span>\n                </a>\n            </div>\n            <div class=\"likesharebar-item\">\n                <a id=\"ctl00_ctl00_body_main_ShareItemForm_aRe\" class=\"button button-icon-left button-secondary button-center button-fullWidth btnReddit button-noshares\" aria-label=\"Share to: Reddit\" title=\"Share to: Reddit\">\n                    <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/Reddit.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/Reddit.svg');\"></span>\n                    <span class=\"button-text\" id=\"reResult\">&nbsp;</span>\n                </a>\n            </div>\n            <div class=\"likesharebar-item\">\n                <a id=\"ctl00_ctl00_body_main_ShareItemForm_aEm\" class=\"button button-icon-left button-secondary button-center button-fullWidth btnEmail button-noshares\" aria-label=\"Share to: Email\" title=\"Share to: Email\">\n                    <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/Mail-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/Mail-1.svg');\"></span>\n                    <span class=\"button-text\" id=\"emResult\">&nbsp;</span>\n                </a>\n            </div>\n        </div>\n    </div>\n\n                                        \n                                        <div id=\"ctl00_ctl00_body_main_CompanyChannelKeyword_dContainer\" class=\"additional-info\">\n        <div class=\"row\">\n            \n            <div id=\"ctl00_ctl00_body_main_CompanyChannelKeyword_dCh\" class=\"col-12  additional-info-column\">\n                <h3 id=\"ctl00_ctl00_body_main_CompanyChannelKeyword_headerChannels\" class=\"h6\">Channels</h3>\n                <div class=\"info-icon channel\">\n                    \n                            <a id=\"ctl00_ctl00_body_main_CompanyChannelKeyword_rChannels_ctl00_hChannel\" aria-label=\"Link to channel: /Artificial Intelligence\" href=\"../../channel/ai\">/artificial intelligence</a>\n                        \n                            <a id=\"ctl00_ctl00_body_main_CompanyChannelKeyword_rChannels_ctl01_hChannel\" aria-label=\"Link to channel: /Markets\" href=\"../../channel/markets\">/markets</a>\n                        \n                </div>\n            </div>\n            \n        </div>\n    </div>\n\n\n                                        \n                                        \n\n    <div class=\"card card--group card-round-border card--group--standalone-section\">\n        <div class=\"card-body-container\">\n            <div class=\"card-icon card-hero-icon groups\">&nbsp;</div>\n            <h3 class=\"card-title h4\">                \n                <a href=\"/community-group/120/artificial-intelligence-and-financial-services\" id=\"ctl00_ctl00_body_main_JoinGroupForm_aTitle\" aria-label=\"Artificial Intelligence and Financial Services\">Artificial Intelligence and Financial Services</a>\n            </h3>\n            <p class=\"card-text\">Artificial Intelligence and Financial Services</p>\n            <div class=\"card-join-group-button\">\n                <a href=\"/community-group/120/artificial-intelligence-and-financial-services?joingroup=yes\" id=\"ctl00_ctl00_body_main_JoinGroupForm_aJoin\" class=\"button button-small button-icon-left button-secondary\">\n                    <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/joinGroup.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/joinGroup.svg'); \"></span>\n                    <span class=\"button-text\">Join group</span>\n                </a>\n            </div>\n            <p class=\"card-baseline\">\n                <span class=\"card-icon posts\">462 opinions</span>\n                <span class=\"card-icon profile\">127 members</span>\n                <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-07-07 20:28:30Z\">07 July 2025</time>\n\n            </p>\n        </div>\n    </div>\n\n\n\n\n                                        \n                                        \n\n\n    <script src=\"/App_Themes/EarlGrey/scripts/modules/site/ThumbUpScript.js\"></script>\n    <script src=\"https://cdn.tiny.cloud/1/4xo06f3s0qainagweu40hyhkl8jnipw1bjvvp6w4i6mu0mxb/tinymce/6/tinymce.min.js\" referrerpolicy=\"origin\"></script>\n    <script type=\"text/javascript\">\n        tinymce.init({\n            selector: 'textarea#ctl00_ctl00_body_main_CommentsForm_tbBody',\n            plugins: 'link linkchecker autolink powerpaste',\n            menubar: false,\n            toolbar: 'bold italic underline | link | removeformat',\n            powerpaste_allow_local_images: false,\n            powerpaste_word_import: 'clean',\n            powerpaste_html_import: 'clean',\n            statusbar: false,\n            contextmenu: false, // Context menu disabled to enable long press paste on mobile devices\n            link_default_target: '_blank',\n            link_default_protocol: 'https',\n            link_assume_external_targets: true,\n        });\n\n        function add_comment() {\n            let commentBox = document.querySelector(\".tox-tinymce\");\n            let commentBoxContent = tinymce.get(\"ctl00_ctl00_body_main_CommentsForm_tbBody\").getContent();\n            let commentError = document.getElementById(\"errComment\");\n\n            commentBoxContent = commentBoxContent.replace(/&nbsp;/g, \"\");\n\n            if (commentBoxContent == \"\" || commentBoxContent == \"<p></p>\") {\n                commentBox.classList.add('error');\n                commentError.innerText = \"Comments cannot be blank\";\n                return false;\n            } else {\n                commentError.innerText = \"\";\n                commentBox.classList.remove('error');\n                return true;\n            }\n            return false;\n        }        \n    </script>\n\n    \n    <a name=\"comments\"></a>\n    <div id=\"ctl00_ctl00_body_main_CommentsForm_comment\" class=\"comments-container\" name=\"comment\">\n        <h2 class=\"h4\">Comments:  (0)</h2>\n\n        \n\n        \n        <div id=\"ctl00_ctl00_body_main_CommentsForm_pnlNotLoggedIn\" class=\"comments-signin\">\n\t\n            <a href=\"/community/login.aspx?returnurl=%2fblogposting%2f28693%2famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\" id=\"ctl00_ctl00_body_main_CommentsForm_hSignIn\" class=\"button button-icon-left button-secondary button-center\" rel=\"nofollow\">\n                <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/Login.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/Login.svg');\"></span>\n                <span id=\"ctl00_ctl00_body_main_CommentsForm_sSignIn\" class=\"button-text\">Sign in to comment</span>\n            </a>\n        \n</div>\n\n        \n        \n\n        \n    </div>\n\n\n\n                                        <!-- Author box with Opinions  -->\n                                        <div id=\"ctl00_ctl00_body_main_dMemberInfoTwo\" class=\"section--article-opinionAuthor\">\n                                            <div class=\"row\">\n                                                <div class=\"col-12\">\n                                                    <div id=\"ctl00_ctl00_body_main_ArticleProfileForm_pAuthorDetails\" class=\"article-authorDetails\">\n\t\n            <div class=\"article-authorDetails-bio\">\n                <div class=\"article-authorDetails-bio-image\">\n                    <a id=\"ctl00_ctl00_body_main_ArticleProfileForm_hBlogger\" aria-label=\"link to author: Serhii Bondarenko\" href=\"/bloggers/160358\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_ArticleProfileForm_iBlogger\" class=\"card-img-round\" src=\"../../finextra-images/member_photos/thumb_160358_serhii_bondarenko_18.jpg\" alt=\"Serhii Bondarenko\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                </div>\n                <div id=\"ctl00_ctl00_body_main_ArticleProfileForm_dAuthorDetails\" class=\"article-authorDetails-bio-desc\">\n                    <a id=\"ctl00_ctl00_body_main_ArticleProfileForm_hAuthor\" class=\"article-authorDetails-bio-desc-name\" aria-label=\"link to author: Serhii Bondarenko\" href=\"/bloggers/160358\">Serhii Bondarenko</a>\n                    <div class=\"article-authorDetails-bio-desc-jobTitle\">Artificial Intelegence</div>\n                    <span id=\"ctl00_ctl00_body_main_ArticleProfileForm_lbCompany\" class=\"article-authorDetails-bio-desc-companyName\">Tickeron</span> \n\n\n                    <div class=\"article-authorDetails-details\">\n                        <div class=\"article-authorDetails-detailsGroup\">\n                            <div class=\"article-authorDetails-postStats\">\n                                <div class=\"article-authorDetails-postStat\">\n                                    <div class=\"article-authorDetails-postStat-title\">Member since</div>\n                                    <div class=\"article-authorDetails-postStat-desc\">13 Feb 2024</div>\n                                </div>\n                                <div class=\"article-authorDetails-postStat\">\n                                    <div class=\"article-authorDetails-postStat-title\">Location</div>\n                                    <div class=\"article-authorDetails-postStat-desc\">Weiden</div>\n                                </div>\n                            </div>\n                        </div>\n                        <div class=\"article-authorDetails-detailsGroup\">\n                            <div class=\"article-authorDetails-postStats\">\n                                <div class=\"article-authorDetails-postStat\">\n                                    <a href=\"#\" class=\"article-authorDetails-social-link\">\n                                        <div class=\"article-authorDetails-postStat-title\">Followers</div>\n                                        <div class=\"article-authorDetails-postStat-desc\">9</div>\n                                    </a>\n                                </div>\n                                <div class=\"article-authorDetails-postStat\">\n                                    <a href=\"#\" class=\"article-authorDetails-social-link\">\n                                        <div class=\"article-authorDetails-postStat-title\">Following</div>\n                                        <div class=\"article-authorDetails-postStat-desc\">1</div>\n                                    </a>\n                                </div>\n                                <div class=\"article-authorDetails-postStat\">\n                                    <a href=\"#\" class=\"article-authorDetails-social-link\">\n                                        <div class=\"article-authorDetails-postStat-title\">Opinions</div>\n                                        <div class=\"article-authorDetails-postStat-desc\">60</div>\n                                    </a>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div class=\"button-container\">\n                        <a id=\"ctl00_ctl00_body_main_ArticleProfileForm_bFollow\" class=\"button button-small button-icon-left button-secondary follow-author \" href=\"/community/login.aspx?returnurl=/blogposting/28693/amazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025\">\n                            <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionFollow-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionFollow-1.svg');\"></span>\n                            <span class=\"button-text\">Follow</span>\n                        </a>\n                        <a id=\"ctl00_ctl00_body_main_ArticleProfileForm_bUnFollow\" class=\"button button-small button-icon-left button-secondary unfollow-author d-none\" href=\"javascript:__doPostBack('ctl00$ctl00$body$main$ArticleProfileForm$bUnFollow','')\">\n                            <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionUnFollow-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/ActionUnFollow-1.svg');\"></span>\n                            <span class=\"button-text\">Unfollow</span>\n                        </a>\n\n                    </div>\n\n\n                </div>\n            </div>\n\n\n\n    \n</div>\n                                                                                     \n    \n<div class=\"article-authorDetails-bio\">\n    <div class=\"article-authorDetails-bio-image\">\n\n    </div>\n\n    <div class=\"article-authorDetails-bio-desc\">\n        <div class=\"article-authorDetails-opinion-items--list\">\n            <!-- Long Read Items-->\n                \n                                            \n                    <div class=\"card\">\n                        <div class=\"row\">\n                            <div class=\"col-12 card-body-container\">\n                                <div class=\"card-body\">\n                                    <h3 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_LatestSummaryAuthor_rItems_ctl00_hTitle\" aria-label=\"Read article: IonQ ( $IONQ ) Stock Analysis: The Quantum Leap That Stunned Wall Street\" href=\"/blogposting/28871/ionq--ionq--stock-analysis-the-quantum-leap-that-stunned-wall-street\">IonQ ( $IONQ ) Stock Analysis: The Quantum Leap That Stunned Wall Street</a>\n                                    </h3>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-07-10 11:53:22Z\">10 July</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    \n                                            \n                    <div class=\"card\">\n                        <div class=\"row\">\n                            <div class=\"col-12 card-body-container\">\n                                <div class=\"card-body\">\n                                    <h3 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_LatestSummaryAuthor_rItems_ctl01_hTitle\" aria-label=\"Read article: FingerMotion’s 52% Rally: How AI-Driven Strategies and Sector Correlations Are Shaping Returns\" href=\"/blogposting/28600/fingermotions-52-rally-how-ai-driven-strategies-and-sector-correlations-are-shaping-returns\">FingerMotion’s 52% Rally: How AI-Driven Strategies and Sector Correlations Are Shaping Returns</a>\n                                    </h3>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-06-02 16:14:23Z\">02 June</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    \n                                            \n                    <div class=\"card\">\n                        <div class=\"row\">\n                            <div class=\"col-12 card-body-container\">\n                                <div class=\"card-body\">\n                                    <h3 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_LatestSummaryAuthor_rItems_ctl02_hTitle\" aria-label=\"Read article: Microsoft (MSFT) Stock Analysis: AI Leadership, Market Movements, and Financial Outlook May 28, 2025\" href=\"/blogposting/28568/microsoft-msft-stock-analysis-ai-leadership-market-movements-and-financial-outlook-may-28-2025\">Microsoft (MSFT) Stock Analysis: AI Leadership, Market Movements, and Financial Outlook May 28, 2025</a>\n                                    </h3>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-05-28 15:05:16Z\">28 May</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    \n                                            \n                    <div class=\"card\">\n                        <div class=\"row\">\n                            <div class=\"col-12 card-body-container\">\n                                <div class=\"card-body\">\n                                    <h3 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_LatestSummaryAuthor_rItems_ctl03_hTitle\" aria-label=\"Read article: FingerMotion Inc. ($FNGR): A Rising Star in China's Mobile Payment Sector Amid Market Volatility\" href=\"/blogposting/28543/fingermotion-inc-fngr-a-rising-star-in-chinas-mobile-payment-sector-amid-market-volatility\">FingerMotion Inc. ($FNGR): A Rising Star in China's Mobile Payment Sector Amid Market Volatility</a>\n                                    </h3>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-05-26 12:15:23Z\">26 May</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    \n                                            \n                    <div class=\"card\">\n                        <div class=\"row\">\n                            <div class=\"col-12 card-body-container\">\n                                <div class=\"card-body\">\n                                    <h3 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_LatestSummaryAuthor_rItems_ctl04_hTitle\" aria-label=\"Read article: Microsoft (MSFT) Stock Analysis: Market, AI Leadership, and Financial Outlook as of May 23, 2025\" href=\"/blogposting/28529/microsoft-msft-stock-analysis-market-ai-leadership-and-financial-outlook-as-of-may-23-2025\">Microsoft (MSFT) Stock Analysis: Market, AI Leadership, and Financial Outlook as of May 23, 2025</a>\n                                    </h3>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-05-23 11:14:41Z\">23 May</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                \n\n            <!-- CTA -->\n            <div class=\"cta-row cta-all-opinions\">\n                <a id=\"ctl00_ctl00_body_main_LatestSummaryAuthor_hSeeAll\" href=\"/community/live-opinions/160358\">See all Opinions from Serhii</a>\n            </div>\n        </div>\n    </div>\n</div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <!-- More expert opinions -->\n                                        \n \n\n     <div class=\"section--article-expertOpinions\">\n        <h4>More expert opinions</h4>\n            <!-- top 4 community items -->\n            \n                    <div class=\"card card--post-small card--expert-opinion\">\n                        <div class=\"row\">\n                            <div class=\"col-3 card-profile-container\">\n                                <div class=\"card-img-round\">\n\n                                    <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl00_hBlogger\" aria-label=\"link to author: Srinathprasanna Neelagiri Chettiyar Shanmugam\" href=\"/bloggers/161887\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl00_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_161887_srinathprasanna_neelagiri%20chettiyar%20shanmugam_9.jpg\" alt=\"Srinathprasanna Neelagiri Chettiyar Shanmugam\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                                </div>\n                            </div>\n                            <div class=\"col-9 card-body-container\">\n                                <div class=\"card-body\">\n                                    <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl00_hMember\" aria-label=\"link to author: Srinathprasanna Neelagiri Chettiyar Shanmugam\" href=\"/bloggers/161887\">Srinathprasanna Neelagiri Chettiyar Shanmugam</a>  Manager - Banking and Financial Services at Aspire Systems</p>\n                                    <h4 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl00_hTitle\" aria-label=\"Read article: Beyond the AI Demo: Unpacking the Hidden Costs and Human Toll\" href=\"/blogposting/28881/beyond-the-ai-demo-unpacking-the-hidden-costs-and-human-toll\">Beyond the AI Demo: Unpacking the Hidden Costs and Human Toll</a>\n                                    </h4>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon clock\" datetime=\"2025-07-11 16:45:36Z\">5 hours</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                \n                    <div class=\"card card--post-small card--expert-opinion\">\n                        <div class=\"row\">\n                            <div class=\"col-3 card-profile-container\">\n                                <div class=\"card-img-round\">\n\n                                    <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl01_hBlogger\" aria-label=\"link to author: Prakash Bhudia\" href=\"/bloggers/161197\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl01_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_161197_prakash_bhudia_19.jpg\" alt=\"Prakash Bhudia\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                                </div>\n                            </div>\n                            <div class=\"col-9 card-body-container\">\n                                <div class=\"card-body\">\n                                    <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl01_hMember\" aria-label=\"link to author: Prakash Bhudia\" href=\"/bloggers/161197\">Prakash Bhudia</a>  HOD – Product &amp; Growth at Deriv</p>\n                                    <h4 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl01_hTitle\" aria-label=\"Read article: Bitcoin’s institutional glow-up could trigger the next big rally\" href=\"/blogposting/28880/bitcoins-institutional-glow-up-could-trigger-the-next-big-rally\">Bitcoin’s institutional glow-up could trigger the next big rally</a>\n                                    </h4>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon clock\" datetime=\"2025-07-11 13:18:12Z\">9 hours</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                \n                    <div class=\"card card--post-small card--expert-opinion\">\n                        <div class=\"row\">\n                            <div class=\"col-3 card-profile-container\">\n                                <div class=\"card-img-round\">\n\n                                    <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl02_hBlogger\" aria-label=\"link to author: Mikko Anderssen\" href=\"/bloggers/169422\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl02_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_169422_mikko_anderssen.png\" alt=\"Mikko Anderssen\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                                </div>\n                            </div>\n                            <div class=\"col-9 card-body-container\">\n                                <div class=\"card-body\">\n                                    <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl02_hMember\" aria-label=\"link to author: Mikko Anderssen\" href=\"/bloggers/169422\">Mikko Anderssen</a>  CEO at Scila</p>\n                                    <h4 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl02_hTitle\" aria-label=\"Read article: From fragmentation to foundation: Why real-time risk needs a unified harmonised train\" href=\"/blogposting/28878/from-fragmentation-to-foundation-why-real-time-risk-needs-a-unified-harmonised-train\">From fragmentation to foundation: Why real-time risk needs a unified harmonised train</a>\n                                    </h4>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon clock\" datetime=\"2025-07-11 09:09:19Z\">13 hours</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                \n                    <div class=\"card card--post-small card--expert-opinion\">\n                        <div class=\"row\">\n                            <div class=\"col-3 card-profile-container\">\n                                <div class=\"card-img-round\">\n\n                                    <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl03_hBlogger\" aria-label=\"link to author: Farnam Rami\" href=\"/bloggers/169414\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl03_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_169414_farnam_rami_151.jpg\" alt=\"Farnam Rami\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                                </div>\n                            </div>\n                            <div class=\"col-9 card-body-container\">\n                                <div class=\"card-body\">\n                                    <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl03_hMember\" aria-label=\"link to author: Farnam Rami\" href=\"/bloggers/169414\">Farnam Rami</a>  Founder at DevPay Turkey</p>\n                                    <h4 class=\"card-title h6\">\n                                        <a id=\"ctl00_ctl00_body_main_Top4Choice_rBlogs_ctl03_hTitle\" aria-label=\"Read article: Designing for the Next Decade: A Founder’s View on Building Digital Finance Beyond Borders\" href=\"/blogposting/28876/designing-for-the-next-decade-a-founders-view-on-building-digital-finance-beyond-borders\">Designing for the Next Decade: A Founder’s View on Building Digital Finance Beyond Borders</a>\n                                    </h4>\n                                    <p class=\"card-baseline\">\n                                        <time class=\"card-timestamp card-icon clock\" datetime=\"2025-07-11 05:47:39Z\">16 hours</time>\n                                        \n                                    </p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                \n        <div class=\"expert-opinions-more-link\"><a href=\"/latest-blogs\" id=\"ctl00_ctl00_body_main_Top4Choice_aAll\" aria-label=\"Link to all community opinions\">See all opinions</a></div>\n    </div>\n\n\n                                    </div>\n\n                                </div>\n                                <div class=\"col-12 col-md-3 col-lg-4\">\n                                    <div class=\"d-none d-md-block\">\n                                        <div class=\"button button-secondary content-box  desktop\"><h4>External</h4><p>This content is provided by an external author without editing by Finextra. It expresses the views and opinions of the author.</p></div>\n                                    </div>\n                                    \n\n    <div class=\"join-community sticky-top d-none d-md-block\">\n        <h2 class=\"h4 join-community-title\">Join the Community</h2>\n        <div class=\"join-community-postStats\">\n            <div class=\"join-community-postStat join-community-postStat-mobile\">\n                <div class=\"join-community-postStat-desc\">23,441</div>\n                <div class=\"join-community-postStat-title\">Expert opinions</div>\n            </div>\n            <div class=\"join-community-postStat join-community-postStat-mobile\">\n                <div class=\"join-community-postStat-desc\">42,351</div>\n                <div class=\"join-community-postStat-title\">Total members</div>\n            </div>\n            <div class=\"join-community-postStat\">\n                <div class=\"join-community-postStat-desc\">311</div>\n                <div class=\"join-community-postStat-title\">New members (last 30 days)</div>\n            </div>\n            <div class=\"join-community-postStat\">\n                <div class=\"join-community-postStat-desc\">178</div>\n                <div class=\"join-community-postStat-title\">New opinions (last 30 days)</div>\n            </div>\n            <div class=\"join-community-postStat\">\n                <div class=\"join-community-postStat-desc\">29,126</div>\n                <div class=\"join-community-postStat-title\">Total comments</div>\n            </div>\n        </div>\n        <div class=\"button-container join-community-buttons\">\n            <a class=\"button button-icon-left button-primary button-center button-small\" href=\"/community/register.aspx\">\n                <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/Community.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/Community.svg');\"></span><span class=\"button-text\">Join</span>\n            </a>\n            <a class=\"button button-icon-left button-secondary button-center button-small\" href=\"/community/login.aspx\">\n                <span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/Login.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/Login.svg');\"></span><span class=\"button-text\">Sign in</span>\n            </a>\n        </div>\n    </div>\n                                </div>\n\n                            </div>\n\n                        </div>\n\n                        <div class=\"d-none d-lg-block col-12 col-lg-3\">\n                            <div class=\"section--article-trending\">\n                                \n    <h3 class=\"h3\">Trending</h3>\n    <div class=\"blog-posts\">\n\n        \n                <!-- Blog Post Item -->\n                <div class=\"card card--post-small\">\n                    <div class=\"row\">\n                        <div class=\"col-3 card-profile-container\">\n                            <div class=\"card-img-round\">\n                                <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl00_hBlogger\" aria-label=\"link to author: Galong Yao\" href=\"/bloggers/168469\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl00_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_168469_galong_yao_8.jpg\" alt=\"Galong Yao\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                            </div>\n                        </div>\n                        <div class=\"col-9 card-body-container\">\n                            <div class=\"card-body\">\n                                <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl00_hMember\" aria-label=\"link to author: Galong Yao\" href=\"/bloggers/168469\">Galong Yao</a> CGO at Bamboodt</p>\n                                <h4 class=\"card-title h6\">\n                                    <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl00_hTitle\" aria-label=\"Read article: Alipay Tap: How Alipay’s New NFC Experience Is Redefining Offline Payments in China\" href=\"/blogposting/28848/alipay-tap-how-alipays-new-nfc-experience-is-redefining-offline-payments-in-china\">Alipay Tap: How Alipay’s New NFC Experience Is Redefining Offline Payments in China</a>\n                                </h4>\n                                <p class=\"card-baseline\">\n                                    <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-07-08 09:25:56Z\">08 July</time>\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            \n                <!-- Blog Post Item -->\n                <div class=\"card card--post-small\">\n                    <div class=\"row\">\n                        <div class=\"col-3 card-profile-container\">\n                            <div class=\"card-img-round\">\n                                <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl01_hBlogger\" aria-label=\"link to author: Alex Kreger\" href=\"/bloggers/alex-kreger\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl01_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_86481_alex_kreger_16.jpg\" alt=\"Alex Kreger\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                            </div>\n                        </div>\n                        <div class=\"col-9 card-body-container\">\n                            <div class=\"card-body\">\n                                <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl01_hMember\" aria-label=\"link to author: Alex Kreger\" href=\"/bloggers/alex-kreger\">Alex Kreger</a> Founder and CEO at UXDA Financial UX Design</p>\n                                <h4 class=\"card-title h6\">\n                                    <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl01_hTitle\" aria-label=\"Read article: AI Becomes the Banker: 21 Case Studies Transforming Digital Banking CX\" href=\"/blogposting/28841/ai-becomes-the-banker-21-case-studies-transforming-digital-banking-cx\">AI Becomes the Banker: 21 Case Studies Transforming Digital Banking CX</a>\n                                </h4>\n                                <p class=\"card-baseline\">\n                                    <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-07-07 09:16:40Z\">07 July</time>\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            \n                <!-- Blog Post Item -->\n                <div class=\"card card--post-small\">\n                    <div class=\"row\">\n                        <div class=\"col-3 card-profile-container\">\n                            <div class=\"card-img-round\">\n                                <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl02_hBlogger\" aria-label=\"link to author: Anjna McGettrick\" href=\"/bloggers/169237\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl02_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_169237_anjna_mcgettrick_50.jpg\" alt=\"Anjna McGettrick\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                            </div>\n                        </div>\n                        <div class=\"col-9 card-body-container\">\n                            <div class=\"card-body\">\n                                <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl02_hMember\" aria-label=\"link to author: Anjna McGettrick\" href=\"/bloggers/169237\">Anjna McGettrick</a> Global Head of Strategy Implementations at Onnec</p>\n                                <h4 class=\"card-title h6\">\n                                    <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl02_hTitle\" aria-label=\"Read article: Building the Backbone: Future-Proofing Fintech Infrastructure for the Next Decade\" href=\"/blogposting/28840/building-the-backbone-future-proofing-fintech-infrastructure-for-the-next-decade\">Building the Backbone: Future-Proofing Fintech Infrastructure for the Next Decade</a>\n                                </h4>\n                                <p class=\"card-baseline\">\n                                    <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-07-07 08:31:12Z\">07 July</time>\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            \n                <!-- Blog Post Item -->\n                <div class=\"card card--post-small\">\n                    <div class=\"row\">\n                        <div class=\"col-3 card-profile-container\">\n                            <div class=\"card-img-round\">\n                                <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl03_hBlogger\" aria-label=\"link to author: Nkahiseng Ralepeli\" href=\"/bloggers/164578\"><div class=\"blue-tick\"><img id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl03_iBlogger\" class=\"card-img-round\" src=\"/finextra-images/member_photos/thumb_164578_nkahiseng_ralepeli_143.jpg\" alt=\"Nkahiseng Ralepeli\" style=\"border-width:0px;\"><div class=\"blue-tick-svg\"></div></div></a>\n                            </div>\n                        </div>\n                        <div class=\"col-9 card-body-container\">\n                            <div class=\"card-body\">\n                                <p class=\"card-profile\"><a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl03_hMember\" aria-label=\"link to author: Nkahiseng Ralepeli\" href=\"/bloggers/164578\">Nkahiseng Ralepeli</a> VP of Product: Digital Assets at Absa Bank, CIB.</p>\n                                <h4 class=\"card-title h6\">\n                                    <a id=\"ctl00_ctl00_body_main_TopTrending_rBlogs_ctl03_hTitle\" aria-label=\"Read article: Tokenised Stocks: What They Are, What They Aren't, and Why They Matter\" href=\"/blogposting/28839/tokenised-stocks-what-they-are-what-they-arent-and-why-they-matter\">Tokenised Stocks: What They Are, What They Aren't, and Why They Matter</a>\n                                </h4>\n                                <p class=\"card-baseline\">\n                                    <time class=\"card-timestamp card-icon calendar\" datetime=\"2025-07-07 07:14:58Z\">07 July</time>\n                                </p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            \n\n    </div>\n                            </div>\n                        </div>\n                    </div>\n\n                </div>\n            </section>\n\n            \n\n\n                <!-- Start: Jobs - Now hiring -->\n                <section class=\"section--jobs section--jobs--dark jobs--nowhiring\">\n                    <div class=\"container\">\n                        <div class=\"row\">\n                            <div class=\"col\">\n                                <div class=\"section--jobs--container\">\n                                    <div class=\"section--jobs--heading\">\n                                        <h2 class=\"section--jobs--heading--title\">Now Hiring</h2>\n                                    </div>\n                                    <div class=\"section--jobs--content col-sm-12 offset-md-1 col-md-10 offset-lg-2 col-lg-8\">\n                                        <div class=\"jobs--nowhiring--content\">\n                                            <div id=\"masonry-widget\" class=\"jobbioapp resolution-2\"><div class=\"v1_6 type-3\">\n      <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?widget=true&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n      <div class=\"top-s\">Find Your Place In The World <a href=\"https://amply.co\" target=\"_blank\">BY Amply</a></div>\n      <div class=\"masonry horizontal company multiple\">          \n        <div class=\"list\" id=\"masonry-company-multiple-masonry-widget\"><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=24873&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/splink\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/24873-banner-v575febeaa9132.jpg)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1680278710832.jpg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">splink</span>\n          <span class=\"mt3 ellipsis-1\">Dublin</span>\n          <span class=\"mt3 ellipsis-1\">1 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4544190&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/vanguard-3\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721327114737.jpg)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721326789974.jpeg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">Vanguard</span>\n          <span class=\"mt3 ellipsis-1\">Valley Forge</span>\n          <span class=\"mt3 ellipsis-1\">79 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4544592&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/rsm-us-llp-2\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721152616257.jpg)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721152340790.jpeg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">RSM US LLP</span>\n          <span class=\"mt3 ellipsis-1\">Chicago</span>\n          <span class=\"mt3 ellipsis-1\">128 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4545602&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/bny\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1738171387449.png)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1732551844445.jpeg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">BNY</span>\n          <span class=\"mt3 ellipsis-1\">New York</span>\n          <span class=\"mt3 ellipsis-1\">13 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4545683&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/forvis-mazars\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721169352738.png)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721169319749.jpeg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">Forvis Mazars</span>\n          <span class=\"mt3 ellipsis-1\">Amsterdam</span>\n          <span class=\"mt3 ellipsis-1\">43 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4545716&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/natwest-3\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721244658501.jpg)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1721244323784.jpeg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">NatWest</span>\n          <span class=\"mt3 ellipsis-1\">Edinburgh</span>\n          <span class=\"mt3 ellipsis-1\">678 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4567786&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/intuit-4\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1724923961718.png)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-1724923588523.png)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">Intuit</span>\n          <span class=\"mt3 ellipsis-1\">Mountain View</span>\n          <span class=\"mt3 ellipsis-1\">294 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div><div class=\"h-item\">\n        <img class=\"amply-pxl\" data-src=\"https://api.amply-widgets.com/pixels/amply.gif?company_id=4583835&amp;source=finextra-jobs_masonry_companies_widget&amp;url=https%3A%2F%2Fwww.finextra.com%2Fblogposting%2F28693%2Famazon-amzn-analysis-performance-market-dynamics-and-ai-driven-insights-as-of-june-17-2025&amp;uuid=wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9\" width=\"0\" height=\"0\" style=\"display:block;margin-bottom:-1px\">\n        <a href=\"https://www.finextra.jobs/company/barclays-bank-plc-2\" class=\"item\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-*************.png)\" target=\"_blank\" onclick=\"trkclk('finextra-jobs_masonry_companies_widget', 'wgt-0b2f09ca-f75f-4e65-a409-2e8c037b7cf9')\">\n          <span class=\"img\" style=\"background-image:url(https://d1avm1cbyhi830.cloudfront.net/fit-in/images2/topic/new/image-*************.jpeg)\">&nbsp;</span>\n          <span class=\"mt2 mfw6 ellipsis-2\">Barclays Bank PLC</span>\n          <span class=\"mt3 ellipsis-1\">United Kingdom</span>\n          <span class=\"mt3 ellipsis-1\">1054 Jobs</span>\n          <span class=\"btn\">See More</span>\n        </a>\n      </div></div>\n        <div style=\"text-align: center\">\n          <a href=\"https://www.finextra.jobs/?source=companies_widget\" target=\"_blank\" class=\"more\">See More Companies</a>\n        </div>\n      </div>\n    </div></div>\n                                        </div>\n                                    </div>\n                                    <div class=\"section--jobs--footer\">\n                                        <div class=\"section--jobs--buttons\">\n                                            <a class=\"button button-icon-right button-primary\" href=\"https://www.finextra.jobs/search/companies\" target=\"_blank\"><span class=\"button-text\">All companies</span><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/arrows/arrowRight.svg'); mask-image: url('/app_themes/earlgrey/images/icons/arrows/arrowRight.svg');\"></span></a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n                <!-- End: Jobs - Now hiring -->\n\n        <script defer=\"\" src=\"https://widgets.jobbio.com/partner_fluid_widgets_v1.6.1/display.min.js\" id=\"jobbio-display-script\"></script>\n\n        <script type=\"text/javascript\">\n            document.addEventListener('DOMContentLoaded', function () {\n\n                jobbio_display.widget({\n                    slug: 'finextra-jobs',\n                    container: 'masonry-widget',\n                    style: 'masonry',\n                    count: 8,\n                    type: 'multiple',\n                    content: 'companies',\n                    searchTerm: 'fintech'\n                });\n\n            }, false);\n        </script>\n\n        </div>\n\n\n\n\n    <!--below  -->\n    \n    \n    \n    <span id=\"ctl00_ctl00_body_lower_tracking\"><script type=\"text/javascript\">\nvar trackMem=0;\nvar trackVal=28693;\nvar trackType=4;\nvar trackSource='';\n</script>\n<script src=\"/assets/script/incrementers/articletracking.js\"></script>\n</span> \n\n\n\n\n                  \n            </main>                \n\n            <!-- Start: cookie policy popup -->\n            \n\n    <div class=\"cookie-policy active\">\n        <div class=\"box cookie-policy--container\">\n            <div class=\"cookie-policy--container--content\">\n                <p class=\"p-compact-p-md\">Welcome to Finextra. We use cookies to help us to deliver our services. You may change your preferences at our <a href=\"/about/cookie-centre.aspx\" alt=\"Cookie centre\">Cookie Centre</a>.</p>\n                <p class=\"p-compact-p-md\">Please read our <a href=\"/about/privacy-policy.aspx\" alt=\"Privacy policy\">Privacy Policy</a>.</p>\n            </div>\n            <div class=\"cookie-policy--container--buttons\">\n                <a class=\"button button-primary button-cookie-policy-accept\" href=\"#\"><span class=\"button-text\">Accept</span></a>\n            </div>\n        </div>\n    </div>\n            <!-- End: cookie policy popup -->\n\n            <!-- Scripts -->\n            <script src=\"/app_themes/earlgrey/scripts/bootstrap/bootstrap.min.js\" type=\"text/javascript\" defer=\"\"></script>        \n            <script src=\"/app_themes/earlgrey/scripts/merged/site.min.js\" defer=\"\"></script>\n            <script src=\"/app_themes/earlgrey/scripts/merged/bundle.min.js\" defer=\"\"></script>\n\n            \n            \n        <!--scripts  -->\n    \n    \n \n            \n            \n\n\n        <!-- Footer -->\n        <footer class=\"footer footer-gradient-bg\">\n            <div class=\"container\">\n                <div class=\"row\">\n                    <div class=\"col-12\">\n                        <div class=\"row footer-row-logo\">\n                            <div class=\"column col-12\">\n                                <a href=\"/\"><img class=\"footer-logo\" src=\"/App_Themes/EarlGrey/images/finextra-logo-alt.svg\" alt=\"Finextra\"></a>\n                            </div>\n                            <div class=\"col-12\">\n                                <div class=\"footer-separator\"></div>\n                            </div>\n                        </div>\n                        <div class=\"row footer-row-links\">\n                            <div class=\"column col-6 col-md-3 order-1 order-md-1\">\n                                <h3>Finextra</h3>\n                                <ul>\n                                    <li><a href=\"/about/finextra.aspx\" aria-label=\"Link to: About\">About</a></li>\n                                </ul>\n                                <h3>Community</h3>\n                                <ul>\n                                    <li><a href=\"/community/simple-terms.aspx\" aria-label=\"Link to: Rules\">Rules</a></li>\n                                    <li><a onclick=\"window.open('mailto:<EMAIL>');\" aria-label=\"Link to: Contact the community team\" class=\"cursor-pointer\">Contact the community team</a></li>\n                                </ul>\n                            </div>\n                            <div class=\"column col-6 col-md-3 order-3 order-md-2\">\n                                <h3>News</h3>\n                                <ul>\n                                    <li><a href=\"/about/finextra.aspx\" aria-label=\"Link to: Guidance\">Guidance</a></li>\n                                    <li><a onclick=\"window.open('mailto:<EMAIL>');\" aria-label=\"Link to: Contact the news desk\" class=\"cursor-pointer\">Contact the news desk</a></li>\n                                </ul>\n                                <h3>Sales</h3>\n                                <ul>\n                                    <li><a onclick=\"window.open('mailto:<EMAIL>');\" aria-label=\"Link to: Media pack\" class=\"cursor-pointer\">Media pack</a></li>\n                                    <li><a onclick=\"window.open('mailto:<EMAIL>');\" aria-label=\"Link to: Contact the sales team\" class=\"cursor-pointer\">Contact the sales team</a></li>\n                                </ul>\n                            </div>\n                            <div class=\"column col-6 col-md-3 order-2 order-md-3\">\n                                <h3>Get involved</h3>\n                                <ul>\n                                    <li><a href=\"/live-at/2024/ebaday.aspx\" aria-label=\"Link to: Finextra Live@EBAday\">Finextra Live@</a></li>\n                                    <li><a href=\"/events/timeline?eventtype=webinar\" aria-label=\"Link to: Webinars\">Webinars</a></li>\n                                    <li><a href=\"/latest-videos\" aria-label=\"Link to: Finextra TV\">Finextra TV</a></li>\n                                    <li><a href=\"/research\" aria-label=\"Link to: Research\">Research</a></li>\n                                    <li><a href=\"https://www.finextra.jobs\" aria-label=\"Link to: Finextra.jobs\" target=\"_blank\">Finextra.jobs</a></li>\n                                </ul>\n                            </div>\n                            <div class=\"column col-6 col-md-3 order-4 order-md-4\">\n                                <h3>Events</h3>\n                                <ul>\n\n                                    <li><a href=\"https://www.sustainablefinance.live\" aria-label=\"Link to: Sustainable Finance Live\" target=\"_blank\">Sustainable Finance Live</a></li>\n                                    <li><a href=\"https://www.nextgennordics.com\" aria-label=\"Link to: NextGen Nordics\" target=\"_blank\">NextGen Nordics</a></li>\n                                    <li><a href=\"https://www.ebaday.com\" aria-label=\"Link to: EBAday\" target=\"_blank\">EBAday</a></li>\n                                    <li><a href=\"https://www.nextgenai.world/\" aria-label=\"Link to: NextGenAI\" target=\"_blank\">NextGen:AI</a></li>\n                                </ul>\n                            </div>\n                        </div>\n                        <div class=\"row footer-row-buttons\">\n                            <div class=\"col-12 col-lg-8\" style=\"width:auto; margin-right: auto;\">\n                                <span id=\"ctl00_ctl00_FooterControl_sNotLogged\" class=\"primary-buttons\">\n                                    <a class=\"button button-icon-left button-primary\" href=\"/community/register.aspx\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/communityevents/Community.svg'); mask-image: url('/app_themes/earlgrey/images/icons/communityevents/Community.svg');\"></span><span class=\"button-text\">Join the community</span></a>\n                                    <a class=\"button button-icon-left button-primary\" href=\"/community/members/edit-my-interests.aspx\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/other/Mail-1.svg'); mask-image: url('/app_themes/earlgrey/images/icons/other/Mail-1.svg');\"></span><span class=\"button-text\">Register for news alerts</span></a>\n                                </span>\n                                \n                                <span class=\"social-buttons\">\n                                    <a class=\"button button-icon-only button-dark\" href=\"https://www.linkedin.com/company/finextra/\" target=\"_blank\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/LinkedIn.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/LinkedIn.svg');\"></span></a>\n                                    <a class=\"button button-icon-only button-dark\" href=\"https://twitter.com/finextra\" target=\"_blank\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/Twitter.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/Twitter.svg');\"></span></a>\n                                    <a class=\"button button-icon-only button-dark\" href=\"/rss/rss.aspx\"><span class=\"button-icon\" style=\"-webkit-mask-image: url('/app_themes/earlgrey/images/icons/social/RSS.svg'); mask-image: url('/app_themes/earlgrey/images/icons/social/RSS.svg');\"></span></a>\n                                </span>\n                            </div>\n                            <div class=\"col-12 col-lg-4\" style=\"width: auto;\">\n                                <span class=\"app-buttons\">\n                                    <a href=\"https://apps.apple.com/us/app/finextra-research-news/id1551174789\" aria-label=\"Link to: Our app on the Apple App store\" target=\"_blank\"><img src=\"/app_themes/earlgrey/images/logos/apple.png\" alt=\"Apple App Store\"></a>\n                                    <a href=\"https://play.google.com/store/apps/details?id=finextra.android.news\" aria-label=\"Link to: Our app on the Google App store\" target=\"_blank\"><img src=\"/app_themes/earlgrey/images/logos/google.png\" alt=\"Google App Store\"></a>\n                                </span>\n                            </div>\n                            <div class=\"col-12\">\n                                <div class=\"footer-separator\"></div>\n                            </div>\n                        </div>\n                        <div class=\"row footer-row-copyright\">\n                            <div class=\"column col-12 col-md-6\">\n                                <p><a href=\"/about/copyright.aspx\" aria-label=\"Link to: Copyright\">© Finextra Research 2025</a></p>\n                            </div>\n                            <div class=\"column col-12 col-md-6\">\n                                <p><a href=\"/community/terms-of-use.aspx\" aria-label=\"Link to: Terms of use\">Terms of use</a><a href=\"/about/privacy-policy.aspx\" aria-label=\"Link to: Privacy Policy\">Privacy Policy</a><a href=\"/about/cookie-centre.aspx\" aria-label=\"Link to: Cookie Centre\">Cookie Centre</a></p>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </footer>\n        </form> \n    \n\n<iframe id=\"_hjSafeContext_66303796\" title=\"_hjSafeContext\" tabindex=\"-1\" aria-hidden=\"true\" src=\"about:blank\" style=\"display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;\"></iframe><script id=\"clktrk\">function trkclk(source, uuid){\n        var data = {\n          source: source,\n          uuid: uuid\n        };\n                        \n        fetch('https://api.amply-widgets.com/clicks', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(data)\n        })\n      };\n      function trkrdt(job_id, source, redirecting, redirect_link, uuid){\n        if(redirecting){\n          var data = {\n            job_id: job_id,\n            source: source,\n            link: redirect_link,\n            uuid: uuid\n          };\n          \n          fetch('https://api.amply-widgets.com/jobs/redirect', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(data)\n          })\n        }\n      }</script></body></html>", "images": [{"src": "https://www.finextra.com/finextra-images/member_photos/thumb_160358_ser<PERSON><PERSON>_bond<PERSON><PERSON>_18.jpg", "alt": "<PERSON><PERSON><PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_160358_ser<PERSON><PERSON>_bond<PERSON><PERSON>_18.jpg", "alt": "<PERSON><PERSON><PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_161887_srina<PERSON><PERSON><PERSON><PERSON>_neelagiri%20chet<PERSON>yar%20shanmugam_9.jpg", "alt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_161197_prakash_bhu<PERSON>_19.jpg", "alt": "<PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_169422_mik<PERSON>_and<PERSON><PERSON>.png", "alt": "<PERSON><PERSON><PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_169414_farnam_rami_151.jpg", "alt": "Farnam Rami", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_168469_galong_yao_8.jpg", "alt": "Galong Yao", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_86481_alex_kreger_16.jpg", "alt": "<PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_169237_anjna_mcgettrick_50.jpg", "alt": "<PERSON><PERSON><PERSON>", "width": 0, "height": 0}, {"src": "https://www.finextra.com/finextra-images/member_photos/thumb_164578_n<PERSON><PERSON><PERSON>_ralepeli_143.jpg", "alt": "<PERSON><PERSON><PERSON><PERSON>", "width": 0, "height": 0}], "success": true, "screenshot": "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***************************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"}}