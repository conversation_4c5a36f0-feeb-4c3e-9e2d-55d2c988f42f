{"timestamp": 1755263208.614689, "data": {"title": "Validating Connectivity and Policies Using the Access Tester", "content": "Validating Connectivity and Policies Using the Access Tester Skip Headers Oracle® Fusion Middleware Administrator's Guide for Oracle Access Manager with Oracle Security Token Service 11 g Release 1 (11.1.1) Part Number E15478-05 Home Book List Contents Index Contact Us Previous Next View PDF 14 Validating Connectivity and Policies Using the Access Tester The Oracle Access Manager Access Tester enables IT professionals and administrators to simulate interactions between registered OAM Agents and OAM 11g Servers to help troubleshoot issues involving agent connections and to test policy definitions. This chapter introduces the Oracle Access Manager Access Tester and how to use it. The following topics are provided: Prerequisites Introduction to the OAM 11g Access Tester Installing and Starting the Access Tester Introduction to the Access Tester Console and Navigation Testing Connectivity and Policies from the Access Tester Console Creating and Managing Test Cases and Scripts Evaluating Scripts, Log File, and Statistics Prerequisites Before you can perform tasks in this chapter: Ensure that the Oracle Access Manager Console and OAM Server are running. Confirm the application domain and policies for one or more resources, as described in Chapter 13 . Introduction to the OAM 11g Access Tester The Access Tester is a portable, stand-alone Java application that ships with Oracle Access Manager 11g. The Access Tester provides a functional interface between an individual IT professional or administrator and the OAM Server. IT professionals can use the Access Tester to verify connectivity and troubleshoot problems with the physical deployment. Application administrators can use the Access Tester to perform a quick validation of policies. In this chapter, the term \"administrator\" represents any individual who is using the Access Tester. The Access Tester can be used from any computer having a network connection to the OAM Server. Both a graphical user interface (known as the Tester Console in this chapter) and a command-line interface are provided. Command line mode enables complete automation of test script execution in single or multi-client mode environments. By appearing to be a real agent, the Access Tester helps with policy configuration design and troubleshooting, and sometimes with troubleshooting OAM Server responsiveness. When using the Access Tester, you must appear to be the real end user; the Access Tester does not actually communicate with a real end user. To use the Access Tester, you must understand and administer authentication and authorization policies for an application or resource that is protected by Oracle Access Manager 11g. The Access Tester enables you to: Configure a request to be sent to the OAM Server that emulates what a real agent would send to the OAM Server in a real environment. Send your request to the OAM Server and receives a response that is the same as the response that would received by a real Agent. The Access Tester uses the OAM Access Protocol (OAP) API to send requests over the OAP channel to the OAM Proxy running as part of the OAM Server. The OAM Server processes the request and returns a response. Process and display the server response. Proceed in the manner a real agent would to handle the response. For example, if a Webgate determines that a resource is protected by a certificate authentication scheme, then it must obtain the end user's certificate from the http SSL connection. In the case of a certificate authentication scheme, you must point the Access Tester to a certificate to be used as the end user's credentials. In addition to simulating the Agent while performing functions in the previous list, the Access Tester enables you to: Review performance characteristics of intended policy changes Track the latency of authentication and authorization requests Stress test the OAM Server to establish low- and high-performance watermarks relative to desired user loads, and to size back-end hardware Stress test the policy server by running multiple concurrent tests (multi-threaded mode) with command-line mode only. Establish performance metrics and measuring on an ongoing basis to prove desired outcomes During basic operations, the Access Tester does not make any determination about the Server response and whether it is a right or wrong response (for instance, whether or not resource X is protected, or user Y is authorized to access resource X). When operating the Access Tester, you must be aware of the policy configuration to determine if a specific response is appropriate. The Access Tester offers advanced functionality that enables you to group a number of individual requests into a test script that can be sent to the OAM Server for processing. The output of such a test run can be captured by the Access Tester and used to compare against a similar document containing \"known good\" responses. In this way, the Access Tester can be used for automated testing of policy configur...", "url": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/tester.htm", "html": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\"\n    \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n<html lang=\"en\" xml:lang=\"en\" xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=us-ascii\" />\n<meta http-equiv=\"Content-Language\" content=\"en\" />\n<meta http-equiv=\"Content-Style-Type\" content=\"text/css\" />\n<meta http-equiv=\"Content-Script-Type\" content=\"text/javascript\" />\n<meta name=\"robots\" content=\"all\" scheme=\"http://www.robotstxt.org/\" />\n<meta name=\"generator\" content=\"Oracle DARB XHTML Converter (Mode = document) - Version 1.0.3\" />\n<meta name=\"Date\" content=\"2011-07-15T14:59:6Z\" />\n<meta name=\"doctitle\" content=\"Oracle&reg; Fusion Middleware Administrator's Guide for Oracle Access Manager with Oracle Security Token Service 11g Release 1 (11.1.1)\" />\n<meta name=\"partno\" content=\"E15478-05\" />\n<meta name=\"docid\" content=\"AIAAG\" />\n<link rel=\"Start\" href=\"../../index.htm\" title=\"Home\" type=\"text/html\" />\n<link rel=\"Copyright\" href=\"../../dcommon/html/cpyr.htm\" title=\"Copyright\" type=\"text/html\" />\n<link rel=\"Stylesheet\" href=\"../../dcommon/css/blafdoc.css\" title=\"Default\" type=\"text/css\" />\n<script type=\"text/javascript\" src=\"http://www.oracle.com/pls/fa10/doccd_js?path=doc.1111/e15478/tester.htm\">\n</script>\n<link rel=\"Contents\" href=\"toc.htm\" title=\"Contents\" type=\"text/html\" />\n<link rel=\"Index\" href=\"index.htm\" title=\"Index\" type=\"text/html\" />\n<link rel=\"Prev\" href=\"app_domn.htm\" title=\"Previous\" type=\"text/html\" />\n<link rel=\"Next\" href=\"logout.htm\" title=\"Next\" type=\"text/html\" />\n<link rel=\"alternate\" href=\"../e15478.pdf\" title=\"PDF version\" type=\"application/pdf\" />\n<title>Validating Connectivity and Policies Using the Access Tester</title>\n<script>window.ohcglobal || document.write('<script src=\"/en/dcommon/js/global.js\">\\x3C/script>')</script></head>\n<body>\n<div class=\"header\">\n<div class=\"zz-skip-header\"><a name=\"top\" id=\"top\" href=\"#BEGIN\">Skip Headers</a></div>\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n<tr>\n<td align=\"left\" valign=\"top\"><b>Oracle&reg; Fusion Middleware Administrator's Guide for Oracle Access Manager with Oracle Security Token Service<br />\n11<i>g</i> Release 1 (11.1.1)</b><br />\nPart Number E15478-05</td>\n<td valign=\"bottom\" align=\"right\">\n<table class=\"icons oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"245\">\n<tr>\n<td align=\"center\" valign=\"top\"><a href=\"../../index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/doclib.gif\" alt=\"Go to Documentation Home\" /><br />\n<span class=\"icon\">Home</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../nav/portal_booklist.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/booklist.gif\" alt=\"Go to Book List\" /><br />\n<span class=\"icon\">Book List</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"toc.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/toc.gif\" alt=\"Go to Table of Contents\" /><br />\n<span class=\"icon\">Contents</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/index.gif\" alt=\"Go to Index\" /><br />\n<span class=\"icon\">Index</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../dcommon/html/feedback.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/feedbck2.gif\" alt=\"Go to Feedback page\" /><br />\n<span class=\"icon\">Contact Us</span></a></td>\n</tr>\n</table>\n</td>\n</tr>\n</table>\n<hr />\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n<tr>\n<td align=\"left\" valign=\"top\">\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"98\">\n<tr>\n<td align=\"center\" valign=\"top\"><a href=\"app_domn.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/leftnav.gif\" alt=\"Go to previous page\" /><br />\n<span class=\"icon\">Previous</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"logout.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/rightnav.gif\" alt=\"Go to next page\" /><br />\n<span class=\"icon\">Next</span></a></td>\n</tr>\n</table>\n</td>\n<td align=\"right\" valign=\"top\" style=\"font-size: 90%\"><a href=\"../e15478.pdf\">View PDF</a></td>\n</tr>\n</table>\n<a name=\"BEGIN\" id=\"BEGIN\"></a></div>\n<div class=\"IND\"><!-- End Header --><a id=\"CACIAFDB\" name=\"CACIAFDB\"></a><a id=\"AIAAG1954\" name=\"AIAAG1954\"></a>\n<h1 class=\"chapter\"><span class=\"secnum\">14</span> Validating Connectivity and Policies Using the Access Tester</h1>\n<p>The Oracle Access Manager Access Tester enables IT professionals and administrators to simulate interactions between registered OAM Agents and OAM 11g Servers to help troubleshoot issues involving agent connections and to test policy definitions. This chapter introduces the Oracle Access Manager Access Tester and how to use it. The following topics are provided:</p>\n<ul>\n<li>\n<p><a href=\"#CACCAIHJ\">Prerequisites</a></p>\n</li>\n<li>\n<p><a href=\"#CACJCIHF\">Introduction to the OAM 11g Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACBEJDC\">Installing and Starting the Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACCHEFH\">Introduction to the Access Tester Console and Navigation</a></p>\n</li>\n<li>\n<p><a href=\"#CACEFCDD\">Testing Connectivity and Policies from the Access Tester Console</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBIEED\">Evaluating Scripts, Log File, and Statistics</a></p>\n</li>\n</ul>\n<a id=\"CACCAIHJ\" name=\"CACCAIHJ\"></a><a id=\"AIAAG1955\" name=\"AIAAG1955\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Prerequisites</h2>\n<p>Before you can perform tasks in this chapter:</p>\n<ul>\n<li>\n<p>Ensure that the Oracle Access Manager Console and OAM Server are running.</p>\n</li>\n<li>\n<p>Confirm the application domain and policies for one or more resources, as described in <a href=\"app_domn.htm#BABBAEHB\">Chapter 13</a>.</p>\n</li>\n</ul>\n</div>\n<!-- class=\"sect1\" -->\n<a id=\"CACJCIHF\" name=\"CACJCIHF\"></a><a id=\"AIAAG1956\" name=\"AIAAG1956\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Introduction to the OAM 11g Access Tester</h2>\n<p>The Access Tester is a portable, stand-alone Java application that ships with Oracle Access Manager 11g. The Access Tester provides a functional interface between an individual IT professional or administrator and the OAM Server.</p>\n<p>IT professionals can use the Access Tester to verify connectivity and troubleshoot problems with the physical deployment. Application administrators can use the Access Tester to perform a quick validation of policies. In this chapter, the term \"administrator\" represents any individual who is using the Access Tester.</p>\n<p>The Access Tester can be used from any computer having a network connection to the OAM Server. Both a graphical user interface (known as the Tester Console in this chapter) and a command-line interface are provided. Command line mode enables complete automation of test script execution in single or multi-client mode environments.</p>\n<p>By appearing to be a real agent, the Access Tester helps with policy configuration design and troubleshooting, and sometimes with troubleshooting OAM Server responsiveness. When using the Access Tester, you must appear to be the real end user; the Access Tester does not actually communicate with a real end user.</p>\n<p>To use the Access Tester, you must understand and administer authentication and authorization policies for an application or resource that is protected by Oracle Access Manager 11g.</p>\n<p>The Access Tester enables you to:</p>\n<ul>\n<li>\n<p>Configure a request to be sent to the OAM Server that emulates what a real agent would send to the OAM Server in a real environment.</p>\n</li>\n<li>\n<p>Send your request to the OAM Server and receives a response that is the same as the response that would received by a real Agent. The Access Tester uses the OAM Access Protocol (OAP) API to send requests over the OAP channel to the OAM Proxy running as part of the OAM Server. The OAM Server processes the request and returns a response.</p>\n</li>\n<li>\n<p>Process and display the server response.</p>\n</li>\n<li>\n<p>Proceed in the manner a real agent would to handle the response. For example, if a Webgate determines that a resource is protected by a certificate authentication scheme, then it must obtain the end user's certificate from the http SSL connection.</p>\n<p>In the case of a certificate authentication scheme, you must point the Access Tester to a certificate to be used as the end user's credentials.</p>\n</li>\n</ul>\n<p>In addition to simulating the Agent while performing functions in the previous list, the Access Tester enables you to:</p>\n<ul>\n<li>\n<p>Review performance characteristics of intended policy changes</p>\n</li>\n<li>\n<p>Track the latency of authentication and authorization requests</p>\n</li>\n<li>\n<p>Stress test the OAM Server to establish low- and high-performance watermarks relative to desired user loads, and to size back-end hardware</p>\n</li>\n<li>\n<p>Stress test the policy server by running multiple concurrent tests (multi-threaded mode) with command-line mode only.</p>\n</li>\n<li>\n<p>Establish performance metrics and measuring on an ongoing basis to prove desired outcomes</p>\n</li>\n</ul>\n<p>During basic operations, the Access Tester does not make any determination about the Server response and whether it is a right or wrong response (for instance, whether or not resource X is protected, or user Y is authorized to access resource X). When operating the Access Tester, you must be aware of the policy configuration to determine if a specific response is appropriate.</p>\n<p>The Access Tester offers advanced functionality that enables you to group a number of individual requests into a test script that can be sent to the OAM Server for processing. The output of such a test run can be captured by the Access Tester and used to compare against a similar document containing \"known good\" responses. In this way, the Access Tester can be used for automated testing of policy configuration against errant changes.</p>\n<p>Additionally, the Access Tester provides a multi-threaded capability designed to stress test the policy server. In the multi-threaded approach, you identify the number of virtual test clients to connect to the policy server and the number of iterations that each virtual client should execute a test script. This enables you to stress test the policy server.</p>\n<p>For more information, see the following topics in this chapter:</p>\n<ul>\n<li>\n<p><a href=\"#CACJBIAA\">About OAM Agent and Server Interoperability</a></p>\n</li>\n<li>\n<p><a href=\"#CACBICAC\">About Access Tester Security and Processing</a></p>\n</li>\n<li>\n<p><a href=\"#CACIICEH\">About Access Tester Modes and Administrator Interactions</a></p>\n</li>\n</ul>\n<a id=\"CACJBIAA\" name=\"CACJBIAA\"></a><a id=\"AIAAG1957\" name=\"AIAAG1957\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About OAM Agent and Server Interoperability</h3>\n<p>The two primary types of actors in the OAM architecture are the policy servers (OAM Servers) and OAM policy enforcement agents (Webgates or Access Clients). In the security world, Agents represent the policy enforcement point (PEP), while OAM Servers represent the policy decision point (PDP):</p>\n<ul>\n<li>\n<p>The Agent plays the role of a gatekeeper to secure resources such as http-based applications and manage all interactions with the user who is trying to access that resource. This is accomplished according to access control policies maintained on the policy server (OAM Server).</p>\n</li>\n<li>\n<p>The role of the OAM Server is to provide policy, identity, and session services to the Agent to properly secure application resources, authenticate and authorize users, and manage user sessions.</p>\n</li>\n</ul>\n<p>This core OAM product architecture revolves around the following exchanges, which drive the interaction between the Agent and OAM Server. To expose inter-operability and the key decision points, <a href=\"#CACDAIFG\">Figure 14-1</a> illustrates a typical OAM Agent and OAM Server interaction during a user's request for a resource.</p>\n<div class=\"figure\"><a id=\"CACDAIFG\" name=\"CACDAIFG\"></a><a id=\"AIAAG1958\" name=\"AIAAG1958\"></a>\n<p class=\"titleinfigure\">Figure 14-1 OAM Agent (PEP) and OAM Server (PDP) Inter-operability</p>\n<img width=\"569\" height=\"545\" src=\"img/aiaag_jd_109.gif\" alt=\"OAM Agent, OAM Server Inter-operability\" title=\"OAM Agent, OAM Server Inter-operability\" longdesc=\"img_text/aiaag_jd_109.htm\" /><br />\n<a id=\"sthref430\" name=\"sthref430\" href=\"img_text/aiaag_jd_109.htm\">Description of \"Figure 14-1 OAM Agent (PEP) and OAM Server (PDP) Inter-operability\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<p>The following overview outlines the processing that occurs between OAM Agents and OAM Servers. During testing, the Access Tester emulates the Agent and communicates with the OAM Server while the administrator emulates the end user.</p>\n<a id=\"AIAAG1959\" name=\"AIAAG1959\"></a>\n<p class=\"subhead2\">Process overview: Interoperability between OAM Agents and OAM Servers</p>\n<ol>\n<li>\n<p>Establish server connectivity: The registered OAM Agent connects to the OAM Server.</p>\n</li>\n<li>\n<p>The user requests accesses to a resource.</p>\n</li>\n<li>\n<p>Validate resource protection: The Agent forwards the request to the OAM Server to determine if the resource is protected.</p>\n<p>Protected: The OAM Server responds with the type of credentials required.</p>\n</li>\n<li>\n<p>User credentials: Establishing the user identity enables tracking for Audit and SSO purposes, and conveyance to the application. For this, the Agent prompts the user for his credentials.</p>\n</li>\n<li>\n<p>Authenticate user credentials: The Agent forwards the supplied user credentials to the OAM Server for validation.</p>\n<p>Authentication Success: The Agent forwards the resource request to the OAM Server.</p>\n</li>\n<li>\n<p>Authorize user access to a resource: The Agents must first determine if the user is allowed to access the resource by forwarding the request for access to the OAM Server for authorization policy evaluation.</p>\n</li>\n<li>\n<p>The Agent grants or denies access based on the policy response.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACBICAC\" name=\"CACBICAC\"></a><a id=\"AIAAG1960\" name=\"AIAAG1960\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About Access Tester Security and Processing</h3>\n<p>This topic provides information about secure communications, connections, storage, input, iogging, and Analysis.</p>\n<p>Secure Communication: The Access Tester supports Open, Simple, or Cert connection modes for communication with the OAM Server:</p>\n<ul>\n<li>\n<p>Open mode: No security on the physical connection</p>\n</li>\n<li>\n<p>Simple mode: The physical connection is encrypted using built-in certificates. With Simple mode, you are asked to enter the Global Pass Phrase that is configured for the OAM Server.</p>\n</li>\n<li>\n<p>Cert mode: The physical connection is encrypted using a field-provided certificates. Access Tester Cert Mode requires:</p>\n<ul>\n<li>\n<p>Configuring the agent (either existing or new) for Cert mode communication.</p>\n</li>\n<li>\n<p>Obtaining certificates for the agent being emulated.</p>\n</li>\n</ul>\n</li>\n</ul>\n<p>Access Tester Cert Mode requires two JKS key stores, created using the importcert tool from the supplied PEM (BASE64-encoded ASCII) certificates: aaa_trust.pem, aaa_key.pem, aaa_cert.pem:</p>\n<ul>\n<li>\n<p>A Trust Store (file containing the JKS key store with the root CA certificate) is required.</p>\n</li>\n<li>\n<p>A Key Store (file containint the JKS key store with the agent's private key and certificate) is required.</p>\n</li>\n<li>\n<p>A Key Store Password is used to encrypt the Key Store with the agent certificates.</p>\n</li>\n</ul>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"keytool.htm#BHBGHIFC\">Appendix E, \"Securing Communication for Oracle Access Manager 11g\"</a> for details about Simple and Cert mode configuration for OAM Server and clients (Webgates)</p>\n</li>\n<li>\n<p><a href=\"#CACCHEFH\">\"Introduction to the Access Tester Console and Navigation\"</a></p>\n</li>\n</ul>\n</div>\n<p><span class=\"bold\">Connections</span>: The Access Tester encrypts all password-type values that it saves to configuration files and test cases. Access Tester validates whether the pool contains valid connections. Cache flush requests are sent over an established connection (not an out-of-band connection to delete the user session (to simulate logout) over OAP. Using an already established connection can improve performance.</p>\n<p><span class=\"bold\">Persistent Storage</span>: The Access Tester manages a number of data structures that require persistent storage between Access Tester invocations. XML-file-based storage is provided for the following types of information:</p>\n<ul>\n<li>\n<p>Configuration data to minimize data entry between invocations of the application (OamTestConfiguration)</p>\n</li>\n<li>\n<p>Test scripts consisting of captured test cases (OamTestScriptCase)</p>\n</li>\n<li>\n<p>Statistical data representing execution metric from a test run (OamTestStats)</p>\n</li>\n</ul>\n<p><span class=\"bold\">XML Files for Input, Logging, and Analysis</span>: The Access Tester uses a single XML schema to define all the XML documents it generates. The following XML files are produced when you run the Access Tester to process test scripts:</p>\n<ul>\n<li>\n<p>Configuration Script: config.xml is the output file generated using the Save Configuration command within the Access Tester. The name of this document is used within the input script to provide proper connection information to the Access Tester running in command line mode. For details, see <a href=\"#CHDJFEGD\">\"About the Saved Connection Configuration File\"</a>.</p>\n</li>\n<li>\n<p>Input Script: script.xml represents a script that is generated by the Access Tester after capturing one or more test cases. For details, see <a href=\"#CHDBEHJC\">\"About the Generated Input Test Script\"</a>.</p>\n</li>\n<li>\n<p>Target Output Script: oamtest_target.xml is generated by running the Access Tester in command line mode and specifying the input script. For details, see <a href=\"#CHDJAIHC\">\"About the Target Output File Containing Test Run Results\"</a>. For example: <code><span class=\"codeinlinebold\">-Dscript.scriptfile=\"script.xml\" -jar oamtest.jar</span></code></p>\n</li>\n<li>\n<p>Statistics: oamtest_stats.xml is generated together with the output script. For details, see <a href=\"#CHDFGJGC\">\"About the Statistics Document\"</a>.</p>\n</li>\n<li>\n<p>Execution Log: lamtest_log.log is generated together with the output script. For details, see <a href=\"#CHDIEHJD\">\"About the Execution Log\"</a>.</p>\n</li>\n</ul>\n<p>For more information, see <a href=\"#CACIICEH\">\"About Access Tester Modes and Administrator Interactions\"</a>.</p>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACIICEH\" name=\"CACIICEH\"></a><a id=\"AIAAG1961\" name=\"AIAAG1961\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About Access Tester Modes and Administrator Interactions</h3>\n<p>This topic describes modes, interactions, and the jar files needed to start and run the Access Tester.</p>\n<p><span class=\"bold\">Console:</span> The Access Tester provides a single window for interactions with the user. All Access Tester operations are available in the main window, which performs as a central dashboard where users can submit specific details for the test case and view responses.</p>\n<p><span class=\"bold\">Command Line and Scripts:</span> You can use the Access Tester command line and develop test scripts, which you can run interactively or in batches for computerized execution to maximize productivity and minimize costs and resources.</p>\n<p><span class=\"bold\">Startup and Run Time JAR Files</span>: The Access Tester requires nap-api.jar in the same directory as the main jar oamtest.jar, which is used to start the application.</p>\n<p><span class=\"bold\">Interactions:</span> Regardless of the mode you choose for running the Access Tester, your primary interactions with the Access Tester include:</p>\n<ul>\n<li>\n<p>Issuing Requests and Reviewing Results</p>\n<p>You use the Access Tester to issue requests to the OAM Server to validate resource protection, policy configuration, user authentication, and user authorization. You can immediately analyze test case results and also retain the data for longer-term analysis, if needed.</p>\n</li>\n</ul>\n<ul>\n<li>\n<p>Managing Test Scripts</p>\n<p>You can build test scripts by capturing the data generated by test execution, which is available as stand-alone documents. You can run the test script for manual or automated analysis. The Access Tester provides for some automated analysis after each test run, while collecting full set of statistics to enable analysis after the fact.</p>\n</li>\n<li>\n<p>Managing OAM Server Connectivity</p>\n<p>You can manage application settings that include server connection information.</p>\n</li>\n</ul>\n<p><a href=\"#CACJGGIE\">Figure 14-2</a> depicts the flow of information during operations in both Console and command-line modes. Details follow the figure. Advanced operations include building and executing test scripts.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nCommand-line mode enables complete automation of test script execution in single or multi-client mode environments. The Access Tester exposes a control mechanism to configure test runs without having to change \"known good\" input test scripts which are available in read-only mode.</div>\n<div class=\"figure\"><a id=\"CACJGGIE\" name=\"CACJGGIE\"></a><a id=\"AIAAG1962\" name=\"AIAAG1962\"></a>\n<p class=\"titleinfigure\">Figure 14-2 User Interactions with the Access Tester</p>\n<img width=\"657\" height=\"563\" src=\"img/aiaag_jd_110.gif\" alt=\"Access Tester User Interactions\" title=\"Access Tester User Interactions\" longdesc=\"img_text/aiaag_jd_110.htm\" /><br />\n<a id=\"sthref431\" name=\"sthref431\" href=\"img_text/aiaag_jd_110.htm\">Description of \"Figure 14-2 User Interactions with the Access Tester\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#CACEJGDH\">Table 14-1</a> describes the process flow of information during both Tester Console mode operations and command-line mode operations.</p>\n<div class=\"tblhruleformalwidemax\"><a id=\"AIAAG1963\" name=\"AIAAG1963\"></a><a id=\"sthref432\" name=\"sthref432\"></a><a id=\"CACEJGDH\" name=\"CACEJGDH\"></a>\n<p class=\"titleintable\">Table 14-1 User Interactions Using Tester Console Mode versus Command Line Mode Operations</p>\n<table class=\"HRuleFormalWideMax\" title=\"User Interactions Using Tester Console Mode versus Command Line Mode Operations\" summary=\"User Interactions with the Tester\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"rows\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"*\" />\n<col width=\"50%\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t4\">Tester Console mode</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t4\">Command Line Mode</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t4\" headers=\"r1c1-t4\">\n<p>The user starts the Access Tester from the command line.</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t4 r1c2-t4\">\n<p>The user or a shell script starts the Access Tester in command line mode.</p>\n<p>Cert mode for secure communication: The keystores are specified in the OamTestConfiguration.xml file containing previously saved configuration information.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t4\" headers=\"r1c1-t4\">\n<p>The user opens a previously saved OamTestConfiguration.xml file to populate the application fields and minimize data entry, including server connection fields. <span class=\"bold\">Alternatively</span>, the user can use the Tester Console and enter data manually</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t4 r1c2-t4\">\n<p>The Access Tester starts processing test cases based on the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t4\" headers=\"r1c1-t4\">\n<p>The user clicks the Connect button to open the connection with the OAM Server.</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t4 r1c2-t4\">\n<p>The Access Tester opens a connection with the OAM Server based on details in the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t4\" headers=\"r1c1-t4\">\n<p>Resource Protection: The user performs steps in a sequence to validate resource protection, authenticate user credentials, and authorize user access.</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t4 r1c2-t4\">\n<p>Resource Protection: The Access Tester starts processing test cases based on the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t4\" headers=\"r1c1-t4\">\n<p>When the test completes, the Access Tester generates:</p>\n<ul>\n<li>\n<p>A script with results</p>\n</li>\n<li>\n<p>A file with execution statistics including information about mismatched responses</p>\n</li>\n<li>\n<p>A log file detailing processing flow</p>\n</li>\n</ul>\n</td>\n<td align=\"left\" headers=\"r6c1-t4 r1c2-t4\">\n<p>Once the script completes, the Access Tester generates:</p>\n<ul>\n<li>\n<p>A script with results</p>\n</li>\n<li>\n<p>A file with execution statistics including information about mismatched responses</p>\n</li>\n<li>\n<p>A log file detailing processing flow</p>\n</li>\n</ul>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t4\" headers=\"r1c1-t4\">\n<p>The user repeats steps as needed to complete validation</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t4 r1c2-t4\">\n<p>The user repeats steps as needed to complete validation.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t4\" headers=\"r1c1-t4\">\n<p>In Cert mode, you will be prompted to identify the necessary keystores.</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t4 r1c2-t4\">\n<p>In Cert mode, the keystores are specified in the XML file containing previously saved configuration information.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblhruleformalwidemax\" -->\n<p>The following overview outlines the tasks involved with using the Access Tester, and the topics where more information can be found in this chapter.</p>\n<a id=\"AIAAG1964\" name=\"AIAAG1964\"></a>\n<p class=\"subhead2\">Task overview: Testing OAM 11g connections and policies includes</p>\n<ol>\n<li>\n<p>Review the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACBEJDC\">Installing and Starting the Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACCHEFH\">Introduction to the Access Tester Console and Navigation</a></p>\n</li>\n</ul>\n</li>\n<li>\n<p>Perform and capture tests using the Access Tester Console as described in <a href=\"#CACEFCDD\">\"Testing Connectivity and Policies from the Access Tester Console\"</a>:</p>\n</li>\n<li>\n<p>Proceed to <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a></p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACBEJDC\" name=\"CACBEJDC\"></a><a id=\"AIAAG1965\" name=\"AIAAG1965\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Installing and Starting the Access Tester</h2>\n<p>The Access Tester consists of two jar files that can be used from any computer, either within or outside the WebLogic Server domain. This section describes how to install the Access Tester, which involves copying the Access Tester jar files to a computer from which you want to run tests. The Access Tester must be started from a command line regardless of the mode you choose for test input: Tester Console mode or command line mode. This section is divided into the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACEEIBJ\">Installing the Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACBHCIJ\">About Access Tester Supported System Properties</a></p>\n</li>\n<li>\n<p><a href=\"#CACHABCG\">Starting the Tester Without System Properties For Use in Tester Console Mode</a></p>\n</li>\n<li>\n<p><a href=\"#CACGJBHA\">Starting the Access Tester with System Properties For Use in Command Line Mode</a></p>\n</li>\n</ul>\n<a id=\"CACEEIBJ\" name=\"CACEEIBJ\"></a><a id=\"AIAAG1966\" name=\"AIAAG1966\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Installing the Access Tester</h3>\n<p>This topic describes how to install the Access Tester for use on any computer. Following installation, the Access Tester is ready to use. No additional setup is required.</p>\n<a id=\"AIAAG1967\" name=\"AIAAG1967\"></a>\n<p class=\"subhead2\">To install the Access Tester</p>\n<ol>\n<li>\n<p>Ensure that the computer from which the tester will be run includes JDK/JRE 6. For example, you can test for Java as follows:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -version\n</pre>\n<p>The previous command returns the following information:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java version \"1.6.0_18\"\nJava(TM) SE Runtime Environment (build 1.6.0_18-b07)\nJava HotSpot(TM) Client VM (build 16.0-b13, mixed mode)\n</pre></li>\n<li>\n<p>On a computer hosting the OAM Server, locate and copy the Access Tester Jar files. For example:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">Oracle_HOME/oam/server/tester/oamtest.jar \nOracle_HOME/oam/server/tester/nap-api.jar \n</pre></li>\n<li>\n<p>Store the jar file copies together in the same directory on any computer from which you want to run the Access Tester.</p>\n</li>\n<li>\n<p>Cert Mode: If the OAM Server communication mode is Cert, ensure that the computer from which you will run the Access Tester includes the same keystores that are defined on the agent registration page of the Oracle Access Manager Console. See <a href=\"agents.htm#CIHBFGIC\">Chapter 9</a>.</p>\n</li>\n<li>\n<p>Proceed as follows, depending on your environment and requirements:</p>\n<ul>\n<li>\n<p><a href=\"#CACHABCG\">Starting the Tester Without System Properties For Use in Tester Console Mode</a> enables you to manually drive requests.</p>\n</li>\n<li>\n<p><a href=\"#CACGJBHA\">Starting the Access Tester with System Properties For Use in Command Line Mode</a></p>\n</li>\n<li>\n<p><a href=\"#CACHGIHD\">Executing a Test Script</a> enables you to use a test script that has been created against a \"Known Good\" policy configuration and marked as \"Known Good\"</p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACBHCIJ\" name=\"CACBHCIJ\"></a><a id=\"AIAAG1968\" name=\"AIAAG1968\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About Access Tester Supported System Properties</h3>\n<p>The Access Tester supports a number of configuration options that are used for presentation or during certain aspects of testing. These options are specified at startup using the Java-D mechanism, as shown in <a href=\"#CACFBJDC\">Table 14-2</a>, which describes all supported system properties.</p>\n<div class=\"tblformal\"><a id=\"AIAAG1969\" name=\"AIAAG1969\"></a><a id=\"sthref433\" name=\"sthref433\"></a><a id=\"CACFBJDC\" name=\"CACFBJDC\"></a>\n<p class=\"titleintable\">Table 14-2 Access Tester Supported System Properties</p>\n<table class=\"Formal\" title=\"Access Tester Supported System Properties\" summary=\"Access Tester System Properties\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"26%\" />\n<col width=\"23%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t5\">Property</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t5\">Access Tester Mode</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c3-t5\">Description and Command Syntax</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t5\" headers=\"r1c1-t5\">\n<p>log.traceconnfile</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t5 r1c2-t5\">\n<p>Tester Console and Command Line modes</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t5 r1c3-t5\">\n<p>Logs connection details to the specified file name.</p>\n<p>-Dlog.traceconnfile=\"&lt;file-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t5\" headers=\"r1c1-t5\">\n<p>display.fontname</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t5 r1c2-t5\">\n<p>Tester Console mode</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t5 r1c3-t5\">\n<p>Starts the Access Tester with the specified font. This could be useful in compensating for differences in display resolution.</p>\n<p>- Ddisplay.fontname =\"&lt;font-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t5\" headers=\"r1c1-t5\">\n<p>display.fontsize</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t5 r1c2-t5\">\n<p>Tester Console mode</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t5 r1c3-t5\">\n<p>Starts the Access Tester with the specified font size. This could be useful in compensating for differences in display resolution.</p>\n<p>- Ddisplay.fontsize =\"&lt;font-size&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t5\" headers=\"r1c1-t5\">\n<p>display.usesystem</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t5 r1c2-t5\">\n<p>Tester Console mode</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t5 r1c3-t5\">\n<p>Starts the Access Tester with the default font name and size (Dialog font, size 10).</p>\n<p>- Ddisplay.usesystem</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t5\" headers=\"r1c1-t5\">\n<p>script.scriptfile</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t5 r1c3-t5\">\n<p>Runs the script &lt;file-name&gt; in command line mode.</p>\n<p>-Dscript.scriptfile=\"&lt;file-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t5\" headers=\"r1c1-t5\">\n<p>control.configfile</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t5 r1c3-t5\">\n<p>Overwrites script's \"configfile\" attribute containing the absolute path to the configuration XML file with the connection information. The Access Tester uses the configuration file to establish a connection to the Policy Server indicated by Connection element.</p>\n<p>-Dcontrol.config=\"&lt;file-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t5\" headers=\"r1c1-t5\">\n<p>control.testname</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t5 r1c3-t5\">\n<p>Overwrites script's \"testname\" attribute of the Control element containing a string representing a name of the test series to be used in naming output script, stats, and log files. Output log files begin with &lt;testname&gt;_&lt;testnumber&gt;.</p>\n<p>-Dcontrol.testname=\"&lt;String&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t5\" headers=\"r1c1-t5\">\n<p>control.testnumber</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t5 r1c3-t5\">\n<p>Specifies the control number to be used in naming output script, stats, and log files. Output log files begin with &lt;testname&gt;_&lt;testnumber&gt;.</p>\n<p>-Dcontrol.testnumber=\"&lt;String&gt;\".</p>\n<p>Although the auto generated string is a 7 digit number based on current local time (2 character minutes + 2 character seconds + 3 character hundredths), any string can be used to denote the control number as long as it can be used in a filename.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t5\" headers=\"r1c1-t5\">\n<p>control.ignorecontent</p>\n</td>\n<td align=\"left\" headers=\"r10c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r10c1-t5 r1c3-t5\">\n<p>Overwrites script's \"ignorecontent\" attribute of the Control element indicating the Access Tester should ignore differences in Content between the original test case and current results.</p>\n<p>-Dcontrol.testname=\"true|false\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t5\" headers=\"r1c1-t5\">\n<p>control.displayiterationstats</p>\n</td>\n<td align=\"left\" headers=\"r11c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r11c1-t5 r1c3-t5\">\n<p>Controls whether or not to display intermediate statistics after each iteration of the test run.</p>\n<p>-Dcontrol.displayiterationstats=&#x201D;true|false&#x201D;</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r12c1-t5\" headers=\"r1c1-t5\">\n<p>control.loopback</p>\n</td>\n<td align=\"left\" headers=\"r12c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r12c1-t5 r1c3-t5\">\n<p>Runs the Access Tester in loopback mode to test the Access Tester for internal regressions against a known good script. Used for unit testing the Access Tester.</p>\n<p>-Dcontrol.loopback=\"true\"</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformal\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHABCG\" name=\"CACHABCG\"></a><a id=\"AIAAG1970\" name=\"AIAAG1970\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Starting the Tester Without System Properties For Use in Tester Console Mode</h3>\n<p>To manually drive (and capture) requests and view real-time response through the graphical user interface, start the tester in Tester Console mode. This procedure omits all system properties, even though several can be used with Tester Console mode.</p>\n<p>The jar file defines the class to be started by default; no class name need be specified. Ensure that the nap-api.jar is present in the same directory as oamtest.jar.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"#CACBHCIJ\">\"About Access Tester Supported System Properties\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACGJBHA\">\"Starting the Access Tester with System Properties For Use in Command Line Mode\"</a></p>\n</li>\n</ul>\n</div>\n<a id=\"AIAAG1971\" name=\"AIAAG1971\"></a>\n<p class=\"subhead2\">To start the Access Tester in console mode without system properties</p>\n<ol>\n<li>\n<p>From the directory containing the Access Tester jar files, enter the following command:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -jar oamtest.jar\n</pre></li>\n<li>\n<p>Use the -help option to list all the options available for the oamtest command-line tool.</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -jar oamtest.jar -help\n</pre></li>\n<li>\n<p>Proceed to one of the following topics for more information:</p>\n<ul>\n<li>\n<p><a href=\"#CACCHEFH\">Introduction to the Access Tester Console and Navigation</a></p>\n</li>\n<li>\n<p><a href=\"#CACEFCDD\">Testing Connectivity and Policies from the Access Tester Console</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACGJBHA\" name=\"CACGJBHA\"></a><a id=\"AIAAG1972\" name=\"AIAAG1972\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Starting the Access Tester with System Properties For Use in Command Line Mode</h3>\n<p>This section is divided into the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACDGHJE\">About the Access Tester Command Line Mode</a></p>\n</li>\n<li>\n<p><a href=\"#CACHABCG\">Starting the Tester Without System Properties For Use in Tester Console Mode</a></p>\n</li>\n</ul>\n<a id=\"CACDGHJE\" name=\"CACDGHJE\"></a><a id=\"AIAAG1973\" name=\"AIAAG1973\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h4 class=\"sect3\">About the Access Tester Command Line Mode</h4>\n<p>To run a test script, or to customize Access Tester operations, you must start the tester in command line mode and include system properties using the Java -D option.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACBHCIJ\">\"About Access Tester Supported System Properties\"</a></div>\n<p>When running in command line mode, the Access Tester returns completion codes that can be used by shell scripts to manage test runs. When you run the Access Tester in Console mode, you do not need to act upon codes that might be returned by the Access Tester.</p>\n<p>Shell scripts that wrap the Access Tester to execute specific test cases must be able to recognize and act upon exit codes communicated by the Access Tester. In command line mode, the Access Tester exits using System.Exit (N), where N can be one of the following codes:</p>\n<ul>\n<li>\n<p>0 indicates successful completion of all test cases with no mismatches. This also includes a situation where no test cases are defined in the input script.</p>\n</li>\n<li>\n<p>3 indicates successful completion of all test cases with at least one mismatch.</p>\n</li>\n<li>\n<p>1 indicates that an error prevented the Access Tester from running or completing test cases. This includes conditions such as No input script specified, Unable to read the input script, Unable to establish server connection, Unable to generate the target script.</p>\n</li>\n</ul>\n<p>These exit codes can be picked up by shell scripts ($? In Bourne shell) designed to drive the Access Tester to execute specific test cases.</p>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"AIAAG1974\" name=\"AIAAG1974\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" --><a id=\"sthref434\" name=\"sthref434\"></a>\n<h4 class=\"sect3\">Starting the Access Tester with System Properties</h4>\n<p>Use the following procedure to start the Access Tester in command line mode and specify any number of configuration options using the Java-D mechanism.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACBHCIJ\">\"About Access Tester Supported System Properties\"</a></div>\n<a id=\"AIAAG1975\" name=\"AIAAG1975\"></a>\n<p class=\"subhead2\">To start the Access Tester with system properties or for use in command line mode</p>\n<ol>\n<li>\n<p>From the directory containing the Access Tester jar files, enter the command with the appropriate system properties for your environment. For example:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -Dscript.scriptfile=\"\\tests\\script.xml\" -Dcontrol.ignorecontent=\"true\" \n-jar oamtest.jar\n</pre></li>\n<li>\n<p>After startup, proceed to one of the following topics for more information:</p>\n<ul>\n<li>\n<p><a href=\"#CACEFCDD\">Testing Connectivity and Policies from the Access Tester Console</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACCHEFH\" name=\"CACCHEFH\"></a><a id=\"AIAAG1976\" name=\"AIAAG1976\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Introduction to the Access Tester Console and Navigation</h2>\n<p>This section introduces the Access Tester Console, navigation, and controls.</p>\n<p><a href=\"#CACBCBEF\">Figure 14-3</a> shows the fixed-size Access Tester Console. This is the window through which users can interact with the application if the Access Tester is started in Console mode. The window can not be resized. Details follow the screen.</p>\n<div class=\"figure\"><a id=\"CACBCBEF\" name=\"CACBCBEF\"></a><a id=\"AIAAG1977\" name=\"AIAAG1977\"></a>\n<p class=\"titleinfigure\">Figure 14-3 Access Tester Console</p>\n<img width=\"672\" height=\"601\" src=\"img/access_test_1.gif\" alt=\"Access Tester Console\" title=\"Access Tester Console\" longdesc=\"img_text/access_test_1.htm\" /><br />\n<a id=\"sthref435\" name=\"sthref435\" href=\"img_text/access_test_1.htm\">Description of \"Figure 14-3 Access Tester Console\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<p>At the top of the main window are the menu names within a menu bar. Under the menu bar is the tool bar. All of the commands represented by buttons in the tool bar are also available as menu commands.The Access Tester Console is divided into four panels, described in <a href=\"#CHDFJBHC\">Table 14-3</a>.</p>\n<div class=\"tblhruleformalmax\"><a id=\"AIAAG1978\" name=\"AIAAG1978\"></a><a id=\"sthref436\" name=\"sthref436\"></a><a id=\"CHDFJBHC\" name=\"CHDFJBHC\"></a>\n<p class=\"titleintable\">Table 14-3 Access Tester Console Panels</p>\n<table class=\"HRuleFormalMax\" title=\"Access Tester Console Panels\" summary=\"Access Tester Panels\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"rows\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"28%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t9\">Panel Name</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t9\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t9\" headers=\"r1c1-t9\">\n<p>Server Connection</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t9 r1c2-t9\">\n<p>Provides fields for the information required to establish a connection to the OAM Server (a single primary server and a single secondary server), and the Connect button:</p>\n<p>See also: <a href=\"#CACCEFED\">\"Establishing a Connection Between the Access Tester and the OAM Server\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t9\" headers=\"r1c1-t9\">\n<p>Protected Resource URI</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t9 r1c2-t9\">\n<p>Provides information about a resource whose protected status needs to be validated. The Validate button is used to submit the Validate Resource server request.</p>\n<p>See also: <a href=\"#CACEGDHA\">\"Validating Resource Protection from the Access Tester Console\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t9\" headers=\"r1c1-t9\">\n<p>User Identity</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t9 r1c2-t9\">\n<p>Provides information about a user whose credentials need to be authenticated. The Authenticate button is used to submit the Authenticate User server request.</p>\n<p>See also: <a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t9\" headers=\"r1c1-t9\">\n<p>Status Messages</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t9 r1c2-t9\">\n<p>Provides a scrollable status message area containing messages displayed by the application in response to user gestures. The Authorize button is used to submit the Authorize User server request.</p>\n<p>See also: <a href=\"#CACHGAEC\">\"Observing Request Latency\"</a>.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblhruleformalmax\" -->\n<p>Text fields support right-clicking to display the Edit menu and drag-and-drop operations using the mouse and cursor.</p>\n<p>There are four primary buttons through which you submit test requests to the OAM Server. Each button acts as a trigger to initiate the named action described in <a href=\"#CACEGHGE\">Table 14-4</a>.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1979\" name=\"AIAAG1979\"></a><a id=\"sthref437\" name=\"sthref437\"></a><a id=\"CACEGHGE\" name=\"CACEGHGE\"></a>\n<p class=\"titleintable\">Table 14-4 Command Buttons in Access Tester Panels</p>\n<table class=\"FormalMax\" title=\"Command Buttons in Access Tester Panels\" summary=\"Access Tester Buttons\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"22%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t10\">Panel Button</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t10\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t10\" headers=\"r1c1-t10\">\n<p>Connect</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t10 r1c2-t10\">\n<p>Submits connection information and initiates connecting.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t10\" headers=\"r1c1-t10\">\n<p>Validate</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t10 r1c2-t10\">\n<p>Submits information provided in the Protected Resource URI panel and initiates validation of protection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t10\" headers=\"r1c1-t10\">\n<p>Authenticate</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t10 r1c2-t10\">\n<p>Submits information provided in the User Identity panel and initiates authentication confirmation.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t10\" headers=\"r1c1-t10\">\n<p>Authorize</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t10 r1c2-t10\">\n<p>Submits information provided in the User Identity panel and initiates authorization confirmation.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" -->\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACIJEEB\">\"Access Tester Menus and Command Buttons\"</a></div>\n<a id=\"CACIJEEB\" name=\"CACIJEEB\"></a><a id=\"AIAAG1980\" name=\"AIAAG1980\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Access Tester Menus and Command Buttons</h3>\n<p><a href=\"#CACEBDBI\">Table 14-5</a> identifies additional Access Tester Console buttons and their use. All command buttons provide a tip when the cursor is on the button.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1981\" name=\"AIAAG1981\"></a><a id=\"sthref438\" name=\"sthref438\"></a><a id=\"CACEBDBI\" name=\"CACEBDBI\"></a>\n<p class=\"titleintable\">Table 14-5 Additional Access Tester Buttons</p>\n<table class=\"FormalMax\" title=\"Additional Access Tester Buttons\" summary=\"Access Tester Tool Bar Buttons\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t12\">Command Buttons</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t12\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/load_ac.gif\" alt=\"Load Configuration command button\" title=\"Load Configuration command button\" /></td>\n<td align=\"left\" headers=\"r2c1-t12 r1c2-t12\">\n<p>Loads connection configuration details that were saved to an XML file (config.xml, by default).</p>\n<p>You can refresh the information in the Console by clicking this button.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"34\" src=\"img/ac_save_cfg.gif\" alt=\"Access Tester Save Configuration\" title=\"Access Tester Save Configuration\" /></td>\n<td align=\"left\" headers=\"r3c1-t12 r1c2-t12\">\n<p>Saves connection configuration details to a file (default name, config.xml). You can add the name of this document to the input script to provide proper connection information to the Access Tester running in command line mode.</p>\n<p>The Save command button at the bottom of the Console saves the content of the Status Message panel to a log file.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"35\" src=\"img/ac_clear.gif\" alt=\"Access Tester Clear Field\" title=\"Access Tester Clear Field\" /></td>\n<td align=\"left\" headers=\"r4c1-t12 r1c2-t12\">\n<p>Clears fields on a panel containing the icon. Tool bar action clears all fields except connection fields if the connection has already been established.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/ac_capture.gif\" alt=\"Surrounding text describes ac_capture.gif.\" title=\"Surrounding text describes ac_capture.gif.\" /> <img width=\"34\" height=\"28\" src=\"img/ac_capture.gif\" alt=\"Access Tester Capture Last Request\" title=\"Access Tester Capture Last Request\" /></td>\n<td align=\"left\" headers=\"r5c1-t12 r1c2-t12\">\n<p>Captures the last named request to the capture queue with the corresponding response received from the OAM Server. Together, the request and response create a test case.</p>\n<p>The capture queue status at the bottom of the Console is updated to reflect the number of test cases in the queue.</p>\n<p>You can save the contents of the capture queue to create a test script containing multiple test cases using the Generate Script command on the Test menu or a command button.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"30\" src=\"img/ac_gen_script.gif\" alt=\"Access Tester Generate Script\" title=\"Access Tester Generate Script\" /></td>\n<td align=\"left\" headers=\"r6c1-t12 r1c2-t12\">\n<p>Generates a test script that includes every test case currently in the capture queue, and asks if the queue should be cleared. Do not clear the queue until all your test cases have been captured and saved to a test script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"34\" src=\"img/ac_run_script.gif\" alt=\"Access Tester Run Script\" title=\"Access Tester Run Script\" /></td>\n<td align=\"left\" headers=\"r7c1-t12 r1c2-t12\">\n<p>Runs a test script against the current OAM Server. The Status message window is populated with the execution status as the script progresses through each test case.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/ac_import_uri.gif\" alt=\"Access Tester Import UIR\" title=\"Access Tester Import UIR\" /></td>\n<td align=\"left\" headers=\"r8c1-t12 r1c2-t12\">\n<p>Imports a copied URI from the clipboard after parsing it to populate fields in the URI panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/ac_show_pw.gif\" alt=\"Access Tester Display Password\" title=\"Access Tester Display Password\" /></td>\n<td align=\"left\" headers=\"r9c1-t12 r1c2-t12\">\n<p>Displays a dialog showing the password in clear text</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" -->\n<p>The Access Tester provides the menus described in <a href=\"#BGBJEFBA\">Table 14-6</a>. All menu items have mnemonics that are exposed by holding down the ALT key (on Windows systems). There are also command accelerators (keyboard activation) available using the CTRL-&lt;KEY&gt; combination defined for each menu command.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1982\" name=\"AIAAG1982\"></a><a id=\"sthref439\" name=\"sthref439\"></a><a id=\"BGBJEFBA\" name=\"BGBJEFBA\"></a>\n<p class=\"titleintable\">Table 14-6 Access Tester Menus</p>\n<table class=\"FormalMax\" title=\"Access Tester Menus\" summary=\"Access Tester Menus\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"18%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t13\">Menu Title</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t13\">Menu Commands</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t13\" headers=\"r1c1-t13\">\n<p>File</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t13 r1c2-t13\">\n<ul>\n<li>\n<p>Open Configuration</p>\n</li>\n<li>\n<p>Save Configuration</p>\n</li>\n<li>\n<p>Exit</p>\n</li>\n</ul>\n<p><span class=\"bold\">Note</span>: To minimize the amount of data entry the Save Configuration and Open Configuration menu (and tool bar command buttons) allow for specific Connection, URI, and Identity information to be saved to (and read from) a file. Thus, it becomes fairly simple to manage multiple configurations. Also, the configuration file can be used as input to the Access Tester when you run it in command line mode and execute a test script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t13\" headers=\"r1c1-t13\">\n<p>Edit</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t13 r1c2-t13\">\n<p>Provides standard editing commands, which act on fields:</p>\n<ul>\n<li>\n<p>Cut</p>\n</li>\n<li>\n<p>Copy</p>\n</li>\n<li>\n<p>Paste</p>\n</li>\n<li>\n<p>Clear all fields</p>\n</li>\n<li>\n<p>Import URI fields from a saved URL</p>\n</li>\n</ul>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t13\" headers=\"r1c1-t13\">\n<p>Test</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t13 r1c2-t13\">\n<ul>\n<li>\n<p>Capture last \"...\" request (for example, Capture last \"authorize\" request)</p>\n</li>\n<li>\n<p>Save test script</p>\n</li>\n<li>\n<p>Run test script</p>\n</li>\n</ul>\n<p><span class=\"bold\">Note</span>: You can use functions here to capture the last request and response to create a test case that you can save to a test script to be run at a later time.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t13\" headers=\"r1c1-t13\">\n<p>Help</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t13 r1c2-t13\">\n<p>The command About, which displays usage information.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACEFCDD\" name=\"CACEFCDD\"></a><a id=\"AIAAG1983\" name=\"AIAAG1983\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Testing Connectivity and Policies from the Access Tester Console</h2>\n<p>This section describes how to perform quick spot checks using the Access Tester in Console mode with OAM Servers.</p>\n<p>Spot checks or troubleshooting connections between the Agent and OAM Server can help you assess whether the Agent can communicate with the OAM Server, which is especially helpful after an upgrade or product migration. Spot checks or troubleshooting resource protection that can be exercised by Agents and OAM Servers can help you develop end-to-end tests of policy configuration during the application lifecycle.</p>\n<p>The following overview identifies the tasks and sequence to be performed and where to locate additional information about each task.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nYou can capture each request and response pair to create a test case, and save the test cases to a script file that can be run later. For details, see <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</div>\n<a id=\"AIAAG1984\" name=\"AIAAG1984\"></a>\n<p class=\"subhead2\">Task overview: Performing spot checks from the Access Tester Console</p>\n<ol>\n<li>\n<p>Start the Access Tester, as described in <a href=\"#CACBEJDC\">\"Installing and Starting the Access Tester\"</a>.</p>\n</li>\n<li>\n<p>Add relevant details to the Server Connection panel and click Connect, as described in <a href=\"#CACCEFED\">\"Establishing a Connection Between the Access Tester and the OAM Server\"</a>.</p>\n</li>\n<li>\n<p>Enter or import details into the Protected Resource URI pane and click Validate, as described in <a href=\"#CACEGDHA\">\"Validating Resource Protection from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>Add relevant details to the User Identity panel and click Authenticate, as described in <a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>After successful authentication, click Authorize in the User Identity panel, as described in <a href=\"#CACJIADH\">\"Testing User Authorization from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>Check the latency of requests, as described in <a href=\"#CACHGAEC\">\"Observing Request Latency\"</a>.</p>\n</li>\n</ol>\n<a id=\"CACCEFED\" name=\"CACCEFED\"></a><a id=\"AIAAG1985\" name=\"AIAAG1985\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Establishing a Connection Between the Access Tester and the OAM Server</h3>\n<p>Before you can send a request to the OAM Server you must establish a connection between the Access Tester and the server. This section describes how to establish that connectivity.</p>\n<ul>\n<li>\n<p><a href=\"#BGBFHDDJ\">About the Connection Panel</a></p>\n</li>\n<li>\n<p><a href=\"#BGBDEIFA\">Connecting the Access Tester with the OAM Server</a></p>\n</li>\n</ul>\n<a id=\"BGBFHDDJ\" name=\"BGBFHDDJ\"></a><a id=\"AIAAG1986\" name=\"AIAAG1986\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About the Connection Panel</h4>\n<p>You enter required information for the OAM Server and the Agent you are emulating in the Access Tester Connection panel and then click the Connect button. The Tester initiates the connection, and displays the status in the Status Messages panel. Once the connection is established, it is used for all further operations.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Caution:</p>\nOnce the connection is established, it cannot be changed until you restart the Access Tester Console.</div>\n<p><a href=\"#CACEDDJD\">Figure 14-4</a> illustrates the Server Connection panel and controls. This panel contains information needed to establish a connection to the OAM Server's Proxy port.</p>\n<div class=\"figure\"><a id=\"CACEDDJD\" name=\"CACEDDJD\"></a><a id=\"AIAAG1987\" name=\"AIAAG1987\"></a>\n<p class=\"titleinfigure\">Figure 14-4 Server Connection Panel in the Access Tester</p>\n<img width=\"663\" height=\"141\" src=\"img/ac_svr_connect.gif\" alt=\"Access Tester Server Connection Panel\" title=\"Access Tester Server Connection Panel\" longdesc=\"img_text/ac_svr_connect.htm\" /><br />\n<a id=\"sthref440\" name=\"sthref440\" href=\"img_text/ac_svr_connect.htm\">Description of \"Figure 14-4 Server Connection Panel in the Access Tester\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#CACFIECI\">Table 14-7</a> describes the information needed to establish the connection. The source of your values is the Oracle Access Manager Console, System Configuration tab.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1988\" name=\"AIAAG1988\"></a><a id=\"sthref441\" name=\"sthref441\"></a><a id=\"CACFIECI\" name=\"CACFIECI\"></a>\n<p class=\"titleintable\">Table 14-7 Connection Panel Information</p>\n<table class=\"FormalMax\" title=\"Connection Panel Information\" summary=\"Access Tester Connection Details\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t16\">Fields</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t16\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t16\" headers=\"r1c1-t16\">\n<p>IP Address</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t16 r1c2-t16\">\n<p>The IP Address of the Primary and Secondary OAM Proxy listens on for this set of tests.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you enter values for only the Primary OAM Proxy. The Secondary OAM Proxy is needed only if you want to test failover between the primary and secondary OAM Server. However, a more practical use of the Secondary Server is reserved for later use, when the OAP API supports load balancing between Primary and Secondary OAM Server.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t16\" headers=\"r1c1-t16\">\n<p>Port</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t16 r1c2-t16\">\n<p>Enter the port number of the Primary and Secondary OAM Server.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t16\" headers=\"r1c1-t16\">\n<p>Max Conn</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t16 r1c2-t16\">\n<p>The maximum number of physical connection (TCP) sockets the Access Tester will use. Access Tester emulates a single threaded Agent.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you accept the default value, 1.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t16\" headers=\"r1c1-t16\">\n<p>Min Conn</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t16 r1c2-t16\">\n<p>The minimum number of physical connection (TCP) sockets the Access Tester will use. The Access Tester emulates a single threaded Agent.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you accept the default value, 1.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t16\" headers=\"r1c1-t16\">\n<p>Timeout</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t16 r1c2-t16\">\n<p>The number of milliseconds the Access Tester should wait for the connection to be established or to receive a response from the OAM Server.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you accept the default value.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t16\" headers=\"r1c1-t16\">\n<p>Mode</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t16 r1c2-t16\">\n<p>The level of communication security that is designated for the Agent to be emulated.</p>\n<ul>\n<li>\n<p><a id=\"sthref442\" name=\"sthref442\"></a>Open--No special configuration needed for this mode.</p>\n</li>\n<li>\n<p><a id=\"sthref443\" name=\"sthref443\"></a>Simple--Presents a field for the global pass phrase set for the OAM Server. See Also: <a href=\"keytool.htm#CIHIIJCH\">\"Retrieving the Global Passphrase for Simple Mode\"</a>.</p>\n</li>\n<li>\n<p><a id=\"sthref444\" name=\"sthref444\"></a>Cert--Presents a Configure Certs ... button that opens a dialog asking for the following:</p>\n<p>Trust Store (Root Store Alias): A file containing the JKS key store with the root CA certificate.</p>\n<p>Key Store: A file containing the JKS key store with the agent's private key and certificate. Currently, the agent certificate is used for encrypting the connection and not the agent identification.</p>\n<p>Key Store Password: The password used to encrypt the Key Store with the agent certificates.</p>\n</li>\n</ul>\n<p>See Also: <a href=\"#CACBICAC\">\"About Access Tester Security and Processing\"</a>, and <a href=\"keytool.htm#CIHBCGIA\">\"Generating Client Keystores for OAM Tester in Cert Mode\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t16\" headers=\"r1c1-t16\">\n<p>Agent ID</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t16 r1c2-t16\">\n<p>Enter the identity of the OAM Agent the Tester is simulating.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t16\" headers=\"r1c1-t16\">\n<p>Agent Password</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t16 r1c2-t16\">\n<p>Enter the password for the OAM Agent the Tester is simulating, if there is one configured.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t16\" headers=\"r1c1-t16\"><img width=\"25\" height=\"25\" src=\"img/ac_svr_connect_q.gif\" alt=\"Surrounding text describes ac_svr_connect_q.gif.\" title=\"Surrounding text describes ac_svr_connect_q.gif.\" /></td>\n<td align=\"left\" headers=\"r10c1-t16 r1c2-t16\">\n<p>Click ? beside the Agent Password field for help.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t16\" headers=\"r1c1-t16\"><img width=\"39\" height=\"38\" src=\"img/ac_yes.gif\" alt=\"Access Tester Yes\" title=\"Access Tester Yes\" /></td>\n<td align=\"left\" headers=\"r11c1-t16 r1c2-t16\">\n<p>The green check mark beside the Connect button indicates a \"Yes\" response; the connection is made. The Status Messages panel also indicates a \"Yes\" response for the connection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r12c1-t16\" headers=\"r1c1-t16\"><img width=\"39\" height=\"38\" src=\"img/ac_no.gif\" alt=\"Access Tester No\" title=\"Access Tester No\" /></td>\n<td align=\"left\" headers=\"r12c1-t16 r1c2-t16\">\n<p>The red circle beside the Connect button indicates a \"No\" response; no connection exists. The Status Messages panel also indicates a \"No\" response for the connection.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" -->\n<p>After entering information and establishing a connection, you can save details to a configuration file that can be re-used later.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACCEFED\">\"Establishing a Connection Between the Access Tester and the OAM Server\"</a></div>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"BGBDEIFA\" name=\"BGBDEIFA\"></a><a id=\"AIAAG1989\" name=\"AIAAG1989\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Connecting the Access Tester with the OAM Server</h4>\n<p>Use the following procedure to submit your connection details for the OAM Server.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nCert mode requires the presense of keystores generated as described in <a href=\"keytool.htm#BHBGHIFC\">Appendix E, \"Securing Communication for Oracle Access Manager 11g\"</a></div>\n<a id=\"AIAAG2212\" name=\"AIAAG2212\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBEJDC\">Installing and Starting the Access Tester</a></p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#BGBFHDDJ\">\"About the Connection Panel\"</a></div>\n<a id=\"AIAAG1990\" name=\"AIAAG1990\"></a>\n<p class=\"subhead2\">To test connectivity between the Access Tester and the OAM Server</p>\n<ol>\n<li>\n<p>Start the Access Tester, as described in <a href=\"#CACBEJDC\">\"Installing and Starting the Access Tester\"</a>.</p>\n</li>\n<li>\n<p>In the Server Connection Panel (<a href=\"#CACFIECI\">Table 14-7</a>), enter:</p>\n<ul>\n<li>\n<p>Primary and secondary OAM Proxy details</p>\n</li>\n<li>\n<p>Timeout period</p>\n</li>\n<li>\n<p>Communication encryption mode</p>\n</li>\n<li>\n<p>Agent details</p>\n</li>\n</ul>\n</li>\n<li>\n<p>Click the Connect button.</p>\n</li>\n<li>\n<p>Beside the Connect button, look for the green check mark indicating the connection is established.</p>\n</li>\n<li>\n<p>In the Status Messages panel, verify a Yes response.</p>\n<p>Not Successful: If there is a problem connecting to the OAM Server, ensure that you entered all connection information correctly (IP address and port, Agent name and password, connection mode and related certificates and passwords, as needed).</p>\n<p>If the connection still cannot be made, start the Access Tester Console using the Trace Connection command mode and look for additional details in the connection log. Also, ask the Administrator of the OAM Server to review the policy server log.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACEGDHA\" name=\"CACEGDHA\"></a><a id=\"AIAAG1991\" name=\"AIAAG1991\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Validating Resource Protection from the Access Tester Console</h3>\n<p>Before a user can access a resource, the Agent must first validate that the resource is protected. Using the Access Tester, you can act as the Agent to have the OAM Server validate whether or not the given URI is protected and communicate the response to the Access Tester, as described here.</p>\n<ul>\n<li>\n<p><a href=\"#CACCJGHJ\">About the Protected Resource URI Panel</a></p>\n</li>\n<li>\n<p><a href=\"#CACBFEHJ\">Validating Resource Protection</a></p>\n</li>\n</ul>\n<a id=\"CACCJGHJ\" name=\"CACCJGHJ\"></a><a id=\"AIAAG1992\" name=\"AIAAG1992\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About the Protected Resource URI Panel</h4>\n<p>You must enter required information for the resource you want to validate in the Access Tester Protected Resource URI panel, and then click the Validate button.</p>\n<p>To minimize data entry, you can import long URIs that you have copied from a browser and then click the Import URI command button. The Tester parses the URI saved to the clipboard and populates the URI fields in the Access Tester.</p>\n<p><a href=\"#CACCABHB\">Figure 14-5</a> illustrates the panel where you enter the URI details to validate that the resource is protected. When combined, the URI fields follow RFC notation. For example: <code><span class=\"codeinlineitalic\">http://oam_server1:7777/index.html</span></code>.</p>\n<div class=\"figure\"><a id=\"CACCABHB\" name=\"CACCABHB\"></a><a id=\"AIAAG1993\" name=\"AIAAG1993\"></a>\n<p class=\"titleinfigure\">Figure 14-5 Protected Resource URI Panel in the Access Tester</p>\n<img width=\"665\" height=\"112\" src=\"img/ac_protected_res.gif\" alt=\"Access Tester Protected Resource URI Panel\" title=\"Access Tester Protected Resource URI Panel\" longdesc=\"img_text/ac_protected_res.htm\" /><br />\n<a id=\"sthref445\" name=\"sthref445\" href=\"img_text/ac_protected_res.htm\">Description of \"Figure 14-5 Protected Resource URI Panel in the Access Tester\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#CACFCHBA\">Table 14-8</a> describes the information needed to perform this validation.</p>\n<div class=\"tblformal\"><a id=\"AIAAG1994\" name=\"AIAAG1994\"></a><a id=\"sthref446\" name=\"sthref446\"></a><a id=\"CACFCHBA\" name=\"CACFCHBA\"></a>\n<p class=\"titleintable\">Table 14-8 Protected Resource URI Panel Fields and Controls</p>\n<table class=\"Formal\" title=\"Protected Resource URI Panel Fields and Controls \" summary=\"Access Tester Validate Resource Protection\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t20\">Field or Control</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t20\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t20\" headers=\"r1c1-t20\">\n<p>Scheme</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t20 r1c2-t20\">\n<p>Enter http or https, depending on the communication security specified for the resource.</p>\n<p><span class=\"bold\">Note</span>: The Access Tester supports only http or https resources. You cannot use the Access Tester to test policies that protect custom non-http resources.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t20\" headers=\"r1c1-t20\">\n<p>Host</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t20 r1c2-t20\">\n<p>Enter a valid host name for the resource.</p>\n<p><span class=\"bold\">Note</span>: Your &lt;<span class=\"italic\">host:port</span>&gt; combination specified in the Access Tester must match one of the Host Identifiers defined in the Oracle Access Manager Console. If the host identifier is not recognized, OAM cannot validate resource protection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t20\" headers=\"r1c1-t20\">\n<p>Port</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t20 r1c2-t20\">\n<p>Enter a valid port for the URI.</p>\n<p><span class=\"bold\">Note</span>: The &lt;host:port&gt; combination specified in the Access Tester must match one of the Host Identifiers as defined in the OAM Server. If the host identifier is not recognized, OAM cannot validate resource protection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t20\" headers=\"r1c1-t20\">\n<p>Resource</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t20 r1c2-t20\">\n<p>Enter the Resource component of the URI (/index.htm in the example). This resource should match a resource defined for an authentication and authorization policy in the Oracle Access Manager Console.</p>\n<p><span class=\"bold\">Note</span>: If protected, the resource identifier that you provide here must match the one specified in an authorization policy in the Oracle Access Manager Console.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t20\" headers=\"r1c1-t20\"><img width=\"34\" height=\"28\" src=\"img/ac_import_uri.gif\" alt=\"Access Tester Import UIR\" title=\"Access Tester Import UIR\" /></td>\n<td align=\"left\" headers=\"r6c1-t20 r1c2-t20\">\n<p>Click this button to parse and import a URI that is saved on a clipboard.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t20\" headers=\"r1c1-t20\">\n<p>Operation</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t20 r1c2-t20\">\n<p>Select the operational component of the URI from the list provided in the Access Tester. The OAM Server does not distinguish between different actions, however. Therefore, leaving this set to Get should suffice.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t20\" headers=\"r1c1-t20\">\n<p>Get Auth Scheme</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t20 r1c2-t20\">\n<p>Check this box to request the OAM Server to return details about the Authentication Scheme that is used to secure the protected resource. If the URI is protected, this information is displayed in the Status Messages panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t20\" headers=\"r1c1-t20\">\n<p>Validate</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t20 r1c2-t20\">\n<p>Click the Validate button to submit the request to the OAM Server. When the response is received, the Access Tester displays it in the Status Messages panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t20\" headers=\"r1c1-t20\"><img width=\"39\" height=\"38\" src=\"img/ac_yes.gif\" alt=\"Access Tester Yes\" title=\"Access Tester Yes\" /></td>\n<td align=\"left\" headers=\"r10c1-t20 r1c2-t20\">\n<p>A green check mark appearing beside the Validate button indicates a \"Yes\" response; the resource is protected. The Status Messages panel provides the redirect URL for the resource and that credentials are expected.</p>\n<p><span class=\"bold\">Note</span>: If you checked the Get Auth Scheme box, the name and level of the Authentication Scheme that protects this resource are also provided in the Status Messages panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t20\" headers=\"r1c1-t20\"><img width=\"39\" height=\"38\" src=\"img/ac_no.gif\" alt=\"Access Tester No\" title=\"Access Tester No\" /></td>\n<td align=\"left\" headers=\"r11c1-t20 r1c2-t20\">\n<p>A red circle appearing beside the Validate button indicates that the resource is not protected. A No response will also appear in the Status Messages.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformal\" -->\n<p>You can capture each request and response pair to create a test case, and save multiple test cases to a script file that can be run later.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"#CACEGDHA\">\"Validating Resource Protection from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a></p>\n</li>\n</ul>\n</div>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"CACBFEHJ\" name=\"CACBFEHJ\"></a><a id=\"AIAAG1995\" name=\"AIAAG1995\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Validating Resource Protection</h4>\n<p>Use the following procedure to submit your resource information to the OAM Server and verify responses in the Status Messages panel.</p>\n<a id=\"AIAAG2213\" name=\"AIAAG2213\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACCEFED\">Establishing a Connection Between the Access Tester and the OAM Server</a></p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACCJGHJ\">\"About the Protected Resource URI Panel\"</a></div>\n<a id=\"AIAAG1996\" name=\"AIAAG1996\"></a>\n<p class=\"subhead2\">To confirm that a resource is protected</p>\n<ol>\n<li>\n<p>In the Access Tester Protected Resource URI panel, enter or import your own resource information (<a href=\"#CACFCHBA\">Table 14-8</a>).</p>\n</li>\n<li>\n<p>Click the Validate button to submit the request.</p>\n</li>\n<li>\n<p>Review Access Tester output, including the relevant data about the resource such as how the resource is protected, level of protection, and so on.</p>\n</li>\n<li>\n<p>Beside the Validate button, look for the green check mark indicating the resource is protected.</p>\n</li>\n<li>\n<p>In the Status Messages panel, verify the redirect URL, authentication scheme, and that credentials are expected.</p>\n</li>\n<li>\n<p>Capture the request and response to create a test case for use later, as described in <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</p>\n</li>\n<li>\n<p>Retain the URI to minimize data entry and server processing using one of the following methods.</p>\n</li>\n<li>\n<p>Proceed to <a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a></p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHGIEC\" name=\"CACHGIEC\"></a><a id=\"AIAAG1997\" name=\"AIAAG1997\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Testing User Authentication from the Access Tester Console</h3>\n<p>This topic provides the following information:</p>\n<ul>\n<li>\n<p><a href=\"#BGBHJJJG\">About the User Identity Panel</a></p>\n</li>\n<li>\n<p><a href=\"#BGBDGJEF\">Testing User Credential Authentication</a></p>\n</li>\n</ul>\n<a id=\"BGBHJJJG\" name=\"BGBHJJJG\"></a><a id=\"AIAAG1998\" name=\"AIAAG1998\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h4 class=\"sect3\">About the User Identity Panel</h4>\n<p>Before a user can access a resource, the Agent must validate the user's identity based on the defined authentication policy on the OAM Server. Using the Access Tester, you can act as the Agent to have the OAM Server authenticate a specific userID for the protected resource. All relevant authentication responses are considered during this policy evaluation.</p>\n<p><a href=\"#BGBEGHAI\">Figure 14-6</a> illustrates the Access Tester panel where you enter the information needed to test authentication.</p>\n<div class=\"figure\"><a id=\"BGBEGHAI\" name=\"BGBEGHAI\"></a><a id=\"AIAAG1999\" name=\"AIAAG1999\"></a>\n<p class=\"titleinfigure\">Figure 14-6 Access Tester User Identity Panel</p>\n<img width=\"652\" height=\"115\" src=\"img/ac_user_id.gif\" alt=\"Access Tester User Identity Panel\" title=\"Access Tester User Identity Panel\" longdesc=\"img_text/ac_user_id.htm\" /><br />\n<a id=\"sthref447\" name=\"sthref447\" href=\"img_text/ac_user_id.htm\">Description of \"Figure 14-6 Access Tester User Identity Panel\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#BGBEFAJB\">Table 14-9</a> describes the information you must provide.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2000\" name=\"AIAAG2000\"></a><a id=\"sthref448\" name=\"sthref448\"></a><a id=\"BGBEFAJB\" name=\"BGBEFAJB\"></a>\n<p class=\"titleintable\">Table 14-9 Access Tester User Identity Panel Fields and Controls</p>\n<table class=\"FormalMax\" title=\"Access Tester User Identity Panel Fields and Controls\" summary=\"Access Tester User Identity Panel\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"29%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t23\">Field or Control</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t23\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t23\" headers=\"r1c1-t23\">\n<p>IP Address</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t23 r1c2-t23\">\n<p>Enter the IP Address of the user whose credentials are being validated. All Agents communicating with the OAM Server send the IP address of the end user.</p>\n<p>Default: The IP address that is filled in belongs to the computer from which the Access Tester is run.</p>\n<p>To test a policy that requires a real user IP address, replace the default IP address with the real IP address.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t23\" headers=\"r1c1-t23\">\n<p>User Name</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t23 r1c2-t23\">\n<p>Enter the userID of the individual whose credentials are being validated.</p>\n<p>Note: The Access Tester enables or disables the username and password fields if the resource is protected by an authentication scheme that requires those credentials. Similarly the Access Tester enables or disables the certificate field if the resource is protected by an authentication scheme that requires a user's X509 certificate.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t23\" headers=\"r1c1-t23\">\n<p>Password</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t23 r1c2-t23\">\n<p>Enter the password of the individual whose credentials are being validated.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t23\" headers=\"r1c1-t23\">\n<p>?</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t23 r1c2-t23\">\n<p>Click this button to display the password in clear text within a popup window.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t23\" headers=\"r1c1-t23\">\n<p>User Certificate Store</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t23 r1c2-t23\">\n<p>The PEM format file containing the X.509 certificate of the user whose credentials should be authenticated.</p>\n<p>If the URI is protected by the X509 Authentication Scheme then the Tester will use the PEM-formatted X509 certificate as a credential instead of or in addition to the username/password. The X509 cert may also be used for authorization if security policies are so configured on the OAM Server.</p>\n<p>Note: For certificate-based authentication to work, the OAM Server must be properly configured with root CA certificates and SSL keystore certificates. See <a href=\"keytool.htm#BHBGHIFC\">Appendix E</a> for details about securing communication between OAM 11g Servers and Webgates.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t23\" headers=\"r1c1-t23\">\n<p>...</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t23 r1c2-t23\">\n<p>Click this button to browse the file system for the user certificate store path.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t23\" headers=\"r1c1-t23\">\n<p>Authenticate</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t23 r1c2-t23\">\n<p>Click the Authenticate button to submit the request to the OAM Server and look for a response in the Status Messages panel.</p>\n<p>Note: The type of credentials supplied (username/password or X.509 certificate) must match the requirements of the authentication scheme that protects the URI.</p>\n<p>Note: For certificate-based authentication, the OAM Server deployment must be properly configured with certificates as described in <a href=\"keytool.htm#BHBGHIFC\">Appendix E</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t23\" headers=\"r1c1-t23\">\n<p>Authorize</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t23 r1c2-t23\">\n<p>After the user's credentials are validated, you can click the Authorize button to submit the request for the resource to the OAM Server. Check the Status Messages panel for a response.</p>\n<p>This request submits information collected in the URI and Identity panels to the OAM Server to decide if the user defined on the Identity panel can access the resource defined on the URI panel. The server returns Yes (user can access the resource) or No (user can not access the resource). The OAM Server might return additional information such as actions (responses) that the real Agent would normally handle.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t23\" headers=\"r1c1-t23\"><img width=\"39\" height=\"38\" src=\"img/ac_yes.gif\" alt=\"Access Tester Yes\" title=\"Access Tester Yes\" /></td>\n<td align=\"left\" headers=\"r10c1-t23 r1c2-t23\">\n<p>A green check mark appearing beside the Authenticate button indicates authentication success; The Status Messages panel also indicates \"yes\" authentication was successful, and provides the user DN and session id.</p>\n<p>A green check mark appearing beside the Authorize button indicates authorization success; The Status Messages panel also indicates \"yes\" authorization was successful, and provides application domain details.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t23\" headers=\"r1c1-t23\"><img width=\"39\" height=\"38\" src=\"img/ac_no.gif\" alt=\"Access Tester No\" title=\"Access Tester No\" /></td>\n<td align=\"left\" headers=\"r11c1-t23 r1c2-t23\">\n<p>A red circle appearing beside the Authenticate button indicates authentication failure; The Status Messages panel also indicates \"no\" authentication was not successful.</p>\n<p>A red circle appearing beside the Authorize button indicates authorization failure; The Status Messages panel also indicates \"no\" authorization was not successful.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" -->\n<p>You can capture each request and response pair to create a test case, and save multiple test cases to a script file that can be run later.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a></p>\n</li>\n</ul>\n</div>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"BGBDGJEF\" name=\"BGBDGJEF\"></a><a id=\"AIAAG2001\" name=\"AIAAG2001\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Testing User Credential Authentication</h4>\n<p>Use the following procedure to submit the end user credentials to the OAM Server and verify authentication. All relevant authentication responses are considered during this policy evaluation.</p>\n<a id=\"AIAAG2214\" name=\"AIAAG2214\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACEGDHA\">Validating Resource Protection from the Access Tester Console</a> with URI information retained in the Console.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#BGBHJJJG\">\"About the User Identity Panel\"</a></div>\n<a id=\"AIAAG2002\" name=\"AIAAG2002\"></a>\n<p class=\"subhead2\">To test user credential authentication</p>\n<ol>\n<li>\n<p>In the Access Tester User Identity panel, enter information for the user to be authenticated (<a href=\"#BGBEFAJB\">Table 14-9</a>).</p>\n</li>\n<li>\n<p>Click the Authenticate button to submit the request.</p>\n</li>\n<li>\n<p>Beside the Authenticate button, look for the green check mark indicating the user is authenticated.</p>\n<p><span class=\"bold\">Not Successful</span>: Confirm that you entered the correct userID and password and try again. Also, check the Oracle Access Manager Console for an active user session that you might need to end, as described in <a href=\"session.htm#CJHJHHAB\">Chapter 7</a>.</p>\n</li>\n<li>\n<p>Capture the request and response to create a test case for use later, as described in <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</p>\n</li>\n<li>\n<p>Retain the URI and user identity information and proceed to <a href=\"#CACJIADH\">\"Testing User Authorization from the Access Tester Console\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACJIADH\" name=\"CACJIADH\"></a><a id=\"AIAAG2003\" name=\"AIAAG2003\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Testing User Authorization from the Access Tester Console</h3>\n<p>Before a user can access a resource, the Agent must validate the user's permissions based on defined policies on the OAM Server. Using the Access Tester, you can act as the Agent to have the OAM Server validate whether or not the authenticated user identity can be authorized to access the resource.</p>\n<p>Use the following procedure to verify the authenticated end user's authorization for the resource. All relevant authorization constraints and responses are considered during this policy evaluation.</p>\n<a id=\"AIAAG2215\" name=\"AIAAG2215\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACHGIEC\">Testing User Authentication from the Access Tester Console</a> with all information retained in the Console.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#BGBHJJJG\">\"About the User Identity Panel\"</a></div>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nOnce the protected resource URI is confirmed and the user's identity is authenticated from the Access Tester, no further information is needed. You simply click the Authorize button to submit the request. However, if the resource is changed to another you must start the sequence anew and validate, then authenticate, and then authorize.</div>\n<a id=\"AIAAG2004\" name=\"AIAAG2004\"></a>\n<p class=\"subhead2\">To test user authorization</p>\n<ol>\n<li>\n<p>In the Access Tester User Identity panel, confirm the user is authenticated (<a href=\"#BGBEFAJB\">Table 14-9</a>).</p>\n</li>\n<li>\n<p>In the Access Tester User Identity panel, click the Authorization button.</p>\n</li>\n<li>\n<p>Beside the Authorization button, look for the green check mark indicating the user is authorized.</p>\n<p><span class=\"bold\">Not Successful</span>: Confirm the authorization policy using the Oracle Access Manager Console.</p>\n</li>\n<li>\n<p>In the Status Messages panel (or execution log file), verify details about the test run.</p>\n</li>\n<li>\n<p>Capture the request and response to create a test case for use later, as described in <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</p>\n</li>\n<li>\n<p>Proceed to:</p>\n<ul>\n<li>\n<p><a href=\"#CACHGAEC\">Observing Request Latency</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBIEED\">Evaluating Scripts, Log File, and Statistics</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHGAEC\" name=\"CACHGAEC\"></a><a id=\"AIAAG2005\" name=\"AIAAG2005\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Observing Request Latency</h3>\n<p>To understand OAM Server performance you must know how well the OAM Server handles requests passed by the Agent. While there are many ways to expose a server's metrics, it is sometimes useful to expose server performance from the standpoint of the Agent. Using the Access Tester, you can do just that as described here.</p>\n<a id=\"AIAAG2216\" name=\"AIAAG2216\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBEJDC\">\"Installing and Starting the Access Tester\"</a></p>\n<a id=\"AIAAG2006\" name=\"AIAAG2006\"></a>\n<p class=\"subhead2\">Task overview: Observing request latency includes</p>\n<ol>\n<li>\n<p><a href=\"#CACBFEHJ\">\"Validating Resource Protection\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIADH\">\"Testing User Authorization from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p>Check latency information in the execution logfile as shown here, as well as in other files generated during a test run. For example:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">...\n[2/3/10 11:03 PM][info] Summary statistics\n[2/3/10 11:03 PM][info] Matched 4 of 4, avg latency 232ms vs 238ms\n[2/3/10 11:03 PM][info] Validate: matched 2 of 2, avg latency 570ms vs 578ms\n[2/3/10 11:03 PM][info] Authenticate: matched 1 of 1, avg latency 187ms vs 187ms\n[2/3/10 11:03 PM][info] Authorize: matched 1 of 1, avg latency 172ms vs 188ms\n...\n</pre></li>\n<li>\n<p>Proceed to:</p>\n<ul>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBIEED\">Evaluating Scripts, Log File, and Statistics</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACJIAEF\" name=\"CACJIAEF\"></a><a id=\"AIAAG2007\" name=\"AIAAG2007\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Creating and Managing Test Cases and Scripts</h2>\n<p>Test management refers to the creation of repeatable tests that can be executed at any time by an individual administrator or system. Quick spot checks are very useful and effective in troubleshooting current issues. However, a more predictable and repeatable approach to validating server and policy configuration is often necessary. This approach can include testing OAM Server configuration for regressions after a product revision, or during a policy development and QA cycle.</p>\n<p>To be useful such tests must allow for multiple use cases to be executed as group. Once the test scripts have been designed and validated as correct, replaying the tests against the OAM Server helps identify regressions in a policy configuration.</p>\n<p>This section provides the information you need to perform test management in the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACJIHFH\">About Test Cases and Test Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#BGBDDBAC\">Capturing Test Cases</a></p>\n</li>\n<li>\n<p><a href=\"#CACBICDG\">Generating an Input Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CHDIDJEB\">Personalizing an Input Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CACHGIHD\">Executing a Test Script</a></p>\n</li>\n</ul>\n<a id=\"CACJIHFH\" name=\"CACJIHFH\"></a><a id=\"AIAAG2008\" name=\"AIAAG2008\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About Test Cases and Test Scripts</h3>\n<p>A test case is created from the request sent to, and response data received from, the OAM Server using the Access Tester. Among other data elements, a test case includes request latency and other identifying information that enables analysis and comparison of old and new test cases.</p>\n<p>Once captured, the test case can be replayed without new input, and then new results can be compared with old results. If the old results are marked as \"known good\" then deviations from those results constitute failed test cases.</p>\n<p>The test case workflow is illustrated by <a href=\"#CHDECCJD\">Figure 14-7</a>.</p>\n<div class=\"figure\"><a id=\"CHDECCJD\" name=\"CHDECCJD\"></a><a id=\"AIAAG2009\" name=\"AIAAG2009\"></a>\n<p class=\"titleinfigure\">Figure 14-7 Test Case Workflow</p>\n<img width=\"605\" height=\"425\" src=\"img/aiaag_jd_108.gif\" alt=\"Test Case Workflow\" title=\"Test Case Workflow\" longdesc=\"img_text/aiaag_jd_108.htm\" /><br />\n<a id=\"sthref449\" name=\"sthref449\" href=\"img_text/aiaag_jd_108.htm\">Description of \"Figure 14-7 Test Case Workflow\"</a><br />\n<br /></div>\n<!-- class=\"figure\" -->\n<a id=\"AIAAG2010\" name=\"AIAAG2010\"></a>\n<p class=\"subhead2\">Task overview: Creating and managing a test case</p>\n<p>From the Access Tester Console, you can connect to the OAM Server and manually conduct individual tests. You can save the request to the capture queue after a request is sent and the response is received from the OAM Server. You can continue capturing additional test cases before generating a test script and clearing the capture queue. If you exit the Access Tester before saving the capture queue, you are asked if the test cases should be saved to a script before exiting. Oracle recommends that you do not clear the queue until all your test cases have been captured.</p>\n<p>Once you have the test script, you can run it from either the Access Tester Console or from the command line.</p>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"BGBDDBAC\" name=\"BGBDDBAC\"></a><a id=\"AIAAG2011\" name=\"AIAAG2011\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Capturing Test Cases</h3>\n<p>You can save each test case to a capture queue after sending the request from the Access Tester to the OAM Server and receiving the response. You can capture as many individual test cases as you need before generating a test script that will automate running the group of test cases. For instance, the following outlines three test cases that must be captured individually:</p>\n<ul>\n<li>\n<p>A validation request and response</p>\n</li>\n<li>\n<p>An authentication request and response</p>\n</li>\n<li>\n<p>An authorization request and response</p>\n</li>\n</ul>\n<p><a href=\"#BGBHEDIE\">Table 14-10</a> describes the location of the capture options.</p>\n<div class=\"tblformal\"><a id=\"AIAAG2012\" name=\"AIAAG2012\"></a><a id=\"sthref450\" name=\"sthref450\"></a><a id=\"BGBHEDIE\" name=\"BGBHEDIE\"></a>\n<p class=\"titleintable\">Table 14-10 Access Tester Capture Request Options</p>\n<table class=\"Formal\" title=\"Access Tester Capture Request Options\" summary=\"Access Tester Capture Request\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t28\">Location</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t28\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t28\" headers=\"r1c1-t28\">\n<p>Test menu</p>\n<p>Capture last \"...\" request</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t28 r1c2-t28\">\n<p>Select this command from the Test menu to add the last request issued and results received to the capture queue (for inclusion in a test script later).</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t28\" headers=\"r1c1-t28\"><img width=\"34\" height=\"28\" src=\"img/ac_capture.gif\" alt=\"Capture last request\" title=\"Capture last request\" /></td>\n<td align=\"left\" headers=\"r3c1-t28 r1c2-t28\">\n<p>Select this command button from the tool bar to add the last request issued and results received to the capture queue (for inclusion in a test script later).</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformal\" -->\n<p>If you exit the Access Tester before saving the capture queue, you are asked if the test cases should be saved to a script before exiting. Do not clear the Access Tester capture queue until all your test cases have been captured.</p>\n<a id=\"AIAAG2013\" name=\"AIAAG2013\"></a>\n<p class=\"subhead2\">To capture one or more test cases</p>\n<ol>\n<li>\n<p>Initiate a request from the Access Tester Console, as described in <a href=\"#CACEFCDD\">\"Testing Connectivity and Policies from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>After receiving the response, click the Capture last \"...\" request command button in the tool bar (or choose it from the Test menu).</p>\n</li>\n<li>\n<p>Confirm the capture in the Status Messages panel and note the Capture Queue test case count at the bottom of the Console, as shown here.</p>\n<img width=\"711\" height=\"239\" src=\"img/ac_capt_queue.gif\" alt=\"Capture Queue Message\" title=\"Capture Queue Message\" /><br /></li>\n<li>\n<p>Repeat steps 1, 2, and 3 to capture in the queue each test case that you need for your test script.</p>\n</li>\n<li>\n<p>Proceed to <a href=\"#CACBICDG\">\"Generating an Input Test Script\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACBICDG\" name=\"CACBICDG\"></a><a id=\"AIAAG2014\" name=\"AIAAG2014\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Generating an Input Test Script</h3>\n<p>A test script is a collection of individual test cases that were captured using the Access Tester Console. When individual test cases are grouped together, it becomes possible to automate test coverage to validate policy configuration for a specific application or site.</p>\n<p>You can create a test script to be used as input to the Access Tester and drive automated processing of multiple test cases. The Generate Script option enables you to create an XML file test script and clear the capture queue. If you exit the Access Tester before saving the capture queue, you are asked if the test cases should be saved to a script before exiting.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nDo not clear the capture queue until you have captured all the test cases you want to include in the script.</div>\n<a id=\"AIAAG2015\" name=\"AIAAG2015\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" --><a id=\"sthref451\" name=\"sthref451\"></a>\n<h4 class=\"sect3\">About Generating an Input Test Script</h4>\n<p>You can create a test script to be used as input to the Access Tester and drive automated processing of multiple test cases. Such a script must follow these rules:</p>\n<ul>\n<li>\n<p>Allows possible replay by a person or system</p>\n</li>\n<li>\n<p>Allows possible replay against different policy servers w/o changing the script, to enable sharing of test scripts to drive different Policy Servers</p>\n</li>\n<li>\n<p>Allows comparison of test execution results against \"Known Good\" results</p>\n</li>\n</ul>\n<p>Following are the locations of the Generate Script command.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2016\" name=\"AIAAG2016\"></a><a id=\"sthref452\" name=\"sthref452\"></a><a id=\"sthref453\" name=\"sthref453\"></a>\n<p class=\"titleintable\">Table 14-11 Generate Script Command</p>\n<table class=\"FormalMax\" title=\"Generate Script Command\" summary=\"Generate Script Options\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t30\">Location of the Command</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t30\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t30\" headers=\"r1c1-t30\">\n<p>Test menu</p>\n<p>Generate Script</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t30 r1c2-t30\">\n<p>Select Generate Script from the Test menu to initiate creation of the script containing your captured test cases.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t30\" headers=\"r1c1-t30\"><img width=\"34\" height=\"30\" src=\"img/ac_gen_script.gif\" alt=\"Access Tester Generate Script\" title=\"Access Tester Generate Script\" /></td>\n<td align=\"left\" headers=\"r3c1-t30 r1c2-t30\">\n<p>Select the Generate Script command button from the tool bar to initiate creation of the script containing your captured test cases. After you specify or select a name for your script, you are asked if the capture queue should be cleared. Do not clear the capture queue until all your test cases are saved to a script.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect3\" -->\n<a id=\"AIAAG2017\" name=\"AIAAG2017\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" --><a id=\"sthref454\" name=\"sthref454\"></a>\n<h4 class=\"sect3\">Generating an Input Test Script</h4>\n<a id=\"AIAAG2217\" name=\"AIAAG2217\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#BGBDDBAC\">Capturing Test Cases</a></p>\n<a id=\"AIAAG2018\" name=\"AIAAG2018\"></a>\n<p class=\"subhead2\">To record a test script containing captured test cases</p>\n<ol>\n<li>\n<p>Perform and capture each request that you want in the script, as described in <a href=\"#BGBDDBAC\">\"Capturing Test Cases\"</a>.</p>\n</li>\n<li>\n<p>Click the Generate Script command button in the tool bar (or choose it from the Test menu to include all captured test cases.</p>\n</li>\n<li>\n<p>In the new dialog box, select or enter the name of your new XML script file and then click Save.</p>\n</li>\n<li>\n<p>Click Yes to overwrite an existing file (or No to dismiss the window and give the file a new name).</p>\n</li>\n<li>\n<p>In the Save Waning dialog box, click No to retain the capture queue and continue adding test cases to your script (or click Yes to clear the queue of all test cases).</p>\n</li>\n<li>\n<p>Confirm the location of the test script before you exit the Access Tester.</p>\n</li>\n<li>\n<p>Personalize the test script to include details such as who, when, and why the script was developed, as described next.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDIDJEB\" name=\"CHDIDJEB\"></a><a id=\"AIAAG2019\" name=\"AIAAG2019\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Personalizing an Input Test Script</h3>\n<p>This section describes how to personalize and customize a test script.</p>\n<ul>\n<li>\n<p><a href=\"#CHDEEJAI\">About Customizing a Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CHDHAEAG\">Customizing a Test Script</a></p>\n</li>\n</ul>\n<a id=\"CHDEEJAI\" name=\"CHDEEJAI\"></a><a id=\"AIAAG2020\" name=\"AIAAG2020\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About Customizing a Test Script</h4>\n<p>The control block of a test script is used to tag the script and specify information to be used during the execution of a test. You might want to include details about who created the script and when and why the script was created. You might also want to customize the script using one or more control parameters.</p>\n<p>The Access Tester provides command line \"control\" parameters to change processing of the script without changing the script. (test name, test number, and so on). This enables you to configure test runs without having to change \"known good\" input test scripts. <a href=\"#CHDCHDHI\">Table 14-12</a> describes the control elements and how to customize these.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2021\" name=\"AIAAG2021\"></a><a id=\"sthref455\" name=\"sthref455\"></a><a id=\"CHDCHDHI\" name=\"CHDCHDHI\"></a>\n<p class=\"titleintable\">Table 14-12 Test Script Control Parameters</p>\n<table class=\"FormalMax\" title=\"Test Script Control Parameters\" summary=\"Controls Parameters to Customize a Test Script\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"31%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t31\">Control Parameter</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t31\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t31\" headers=\"r1c1-t31\">\n<p>i<code>gnorecontent=true</code></p>\n</td>\n<td align=\"left\" headers=\"r2c1-t31 r1c2-t31\">\n<p>Ignores differences in the Content section of the use case when comparing the original OAM Server response to the current response. The default is to compare the Content sections. This parameter can be overwritten by a command line property when running in the command line mode.</p>\n<p>Default: false (Compare Content sections).</p>\n<p>Values: true or false</p>\n<p>In command line mode, use ignorecontent=true to over ride the specified value in the Control section of the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t31\" headers=\"r1c1-t31\">\n<p>testname=\"oamtest\"</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t31 r1c2-t31\">\n<p>Specifies a prefix to add to file names in the \"results bundle\" as described in the previous section.</p>\n<p>In command line mode, use Testname=name to over ride the specified value in the Control section.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t31\" headers=\"r1c1-t31\">\n<p>configfile=\"config.xml\"</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t31 r1c2-t31\">\n<p>Specifies the absolute path to a configuration XML file that was previously created by the Access Tester.</p>\n<p>In command line mode, this file is used by the Access Tester to locate connection details to establish a server connection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t31\" headers=\"r1c1-t31\">\n<p>numthreads=\"1\"</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t31 r1c2-t31\">\n<p>Indicates the number of threads (virtual clients) that will be started by the Access Tester to run multiple copies of the test script. Each thread opens its own pool of connections to the policy server. This feature is designed for stress testing the Policy Server, and is available only in command line mode.</p>\n<p>Default: 1</p>\n<p>Note that when running a test script in GUI mode, the number of threads is ignored and only one thread is started to perform a single iteration of the test script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t31\" headers=\"r1c1-t31\">\n<p>numiterations=\"1\"</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t31 r1c2-t31\">\n<p>Indicates the number of iterations that will be performed by the Access Tester. This feature is designed for stress testing and longevity testing the Policy Server and is available only in command line mode.</p>\n<p>Default: 1</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect3\" -->\n<a id=\"CHDHAEAG\" name=\"CHDHAEAG\"></a><a id=\"AIAAG2022\" name=\"AIAAG2022\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Customizing a Test Script</h4>\n<a id=\"AIAAG2218\" name=\"AIAAG2218\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBICDG\">Generating an Input Test Script</a></p>\n<a id=\"AIAAG2023\" name=\"AIAAG2023\"></a>\n<p class=\"subhead2\">To customize a test script</p>\n<ol>\n<li>\n<p>Locate and open the test script that was generated by the Access Tester.</p>\n</li>\n<li>\n<p>Add any details that you need to customize or personalize the script.</p>\n</li>\n<li>\n<p>Save the file and proceed to <a href=\"#CACHGIHD\">\"Executing a Test Script\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHGIHD\" name=\"CACHGIHD\"></a><a id=\"AIAAG2024\" name=\"AIAAG2024\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Executing a Test Script</h3>\n<p>Once a test script has been created against a \"Known Good\" policy configuration and marked as \"Known Good\", it is important to drive the Access Tester using the script rather than specifying each test manually using the Console. This section provides the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CHDEDECG\">About Test Script Execution</a></p>\n</li>\n<li>\n<p><a href=\"#CHDJDHBA\">Running a Test Script</a></p>\n</li>\n</ul>\n<a id=\"CHDEDECG\" name=\"CHDEDECG\"></a><a id=\"AIAAG2025\" name=\"AIAAG2025\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About Test Script Execution</h4>\n<p>You can interactively execute tests scripts from within the Access Tester Console, or use automated test runs performed by command scripts. Automated test runs can be scheduled by the operating system or a harness such as Apache JMeter, and executed without manual intervention. Other than lack of human input in command line mode, the two execution modes are identical.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nA script such as .bat (Windows) or .sh (Unix) executes a test script in command line mode. Once a test script is created, it can be executed using either the Run Script menu command or the Access Tester command line.</div>\n<p><a href=\"#CHDIJHEE\">Table 14-13</a> describes the commands to execute a test script.</p>\n<div class=\"tblformal\"><a id=\"AIAAG2026\" name=\"AIAAG2026\"></a><a id=\"sthref456\" name=\"sthref456\"></a><a id=\"CHDIJHEE\" name=\"CHDIJHEE\"></a>\n<p class=\"titleintable\">Table 14-13 Run Test Script Commands</p>\n<table class=\"Formal\" title=\"Run Test Script Commands\" summary=\"Run a Test Script\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t33\">Location</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t33\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t33\" headers=\"r1c1-t33\">\n<p>Test menu</p>\n<p>Run Script</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t33 r1c2-t33\">\n<p>Select the Run Script command from the Test menu to begin running a saved test script against the current policy server. The Status message panel is populated with the execution status as the script progresses.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t33\" headers=\"r1c1-t33\"><img width=\"34\" height=\"34\" src=\"img/ac_run_script.gif\" alt=\"Access Tester Run Script\" title=\"Access Tester Run Script\" /></td>\n<td align=\"left\" headers=\"r3c1-t33 r1c2-t33\">\n<p>Select the Run Script command button from the tool bar to begin running a saved test script against the current policy server. The Status message panel is populated with the execution status as the script progresses.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t33\" headers=\"r1c1-t33\">\n<p>Command line mode</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t33 r1c2-t33\">\n<p>A script such as .bat (Windows) or .sh (Unix) executes a test script in command line mode. Once a test script is created, it can be executed using either the Run Script menu command or the Access Tester command line.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformal\" -->\n<p>The following overview describes how the Access Tester operates when running a test. Other than lack of human input in command line mode, the two execution modes are identical.</p>\n<a id=\"AIAAG2027\" name=\"AIAAG2027\"></a>\n<p class=\"subhead2\">Process overview: Access Tester behavior when running a test script</p>\n<ol>\n<li>\n<p>The Access Tester loads the input xml file.</p>\n<p>In command line mode, the Access Tester opens the configuration XML file defined within the input test script's Control element.</p>\n</li>\n<li>\n<p>The Access Tester connects to the primary and secondary OAM Proxy using information in the Server Connection panel of the Console.</p>\n<p>In command line mode, the Access Tester uses information in the Connection element of the configuration XML file.</p>\n</li>\n<li>\n<p>In command line mode, the Access Tester checks the Control elements in the input script XML file to ensure none have been overwritten on the command line (command line values take precedence).</p>\n</li>\n<li>\n<p>For each original test case defined in the script, the Access Tester:</p>\n<ol>\n<li>\n<p>Creates a new target test case.</p>\n</li>\n<li>\n<p>Sends the original request to the OAM Server and collects the response.</p>\n</li>\n<li>\n<p>Makes the following comparisons:</p>\n<p>Compares the new response to the original response.</p>\n<p>Compares response codes and marks as \"mismatched\" any new target test case where response codes differ from the original test case. For instance, if the original Validate returned \"Yes\", and now returns \"No\", a mismatch is marked.</p>\n<p>When response codes are identical, and \"the ignorecontent\" control parameter is \"false\", the Access Tester compares Content (the name of the Authentication scheme or post authorization actions that are logged after each request). If Content sections differ, the new target test case is marked \"mismatched\".</p>\n</li>\n<li>\n<p>Collect new elapsed time and store it in the target use case.</p>\n</li>\n<li>\n<p>Build a new target test case containing the full state of the last server request and the same unique ID (UUID) as the original test case.</p>\n</li>\n<li>\n<p>Update the internal statistics table with statistics for the target test case (request type, elapsed time, mismatched, and so on).</p>\n</li>\n</ol>\n</li>\n<li>\n<p>After completing all the input test cases, the Access Tester:</p>\n<ol>\n<li>\n<p>Displays summary results.</p>\n</li>\n<li>\n<p>Obtains and combines the <span class=\"italic\">testname</span> and <span class=\"italic\">testnumber</span>, and generates a name for the \"results bundle\" (three files whose names start with &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>&gt;.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nShell scripts can automate generating the bundle by providing testname and testnumber command line parameters.</div>\n<p>Obtain <span class=\"italic\">testname</span> from the command line parameter. If not specified in the command line, use the <span class=\"italic\">testname</span> element of the input script's Control block.</p>\n<p>Obtain <span class=\"italic\">testnumber</span> from the command line parameter. If not specified, <span class=\"italic\">testnumber</span> defaults to a 7-character numeric string based on the current local time: 2 character minutes, 2 character seconds, 3 character hundredths.</p>\n</li>\n<li>\n<p>Generates the \"results bundle\": three files whose names start with &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>&gt;:</p>\n<p>The target XML script contains the new test cases: &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>_results.xml.</p>\n<p>The statistics XML file contains a summary and detailed statistics of the entire test run, plus those test cases marked as \"mismatched\": &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>_stats.xml</p>\n<p>The execution log file contains information from the Status Message panel: &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>_log.log.</p>\n</li>\n<li>\n<p>When running in multi-threaded mode, only the statistics XML file and execution log file will be generated.</p>\n</li>\n<li>\n<p>In command line mode, the Access Tester exits with the exit code as described in <a href=\"#CACDGHJE\">\"About the Access Tester Command Line Mode\"</a>.</p>\n</li>\n</ol>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"CHDJDHBA\" name=\"CHDJDHBA\"></a><a id=\"AIAAG2028\" name=\"AIAAG2028\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h4 class=\"sect3\">Running a Test Script</h4>\n<a id=\"AIAAG2219\" name=\"AIAAG2219\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBICDG\">Generating an Input Test Script</a></p>\n<a id=\"AIAAG2029\" name=\"AIAAG2029\"></a>\n<p class=\"subhead2\">To run a test script</p>\n<ol>\n<li>\n<p>Confirm the location of the saved test script before exiting the Access Tester., as described in <a href=\"#CACBICDG\">\"Generating an Input Test Script\"</a>.</p>\n</li>\n<li>\n<p>Submit the test script for processing using one of the following methods:</p>\n<ul>\n<li>\n<p>From the Access Tester Console, click the Run Script command button in the tool bar (or select Run Script from the Test menu), then follow the prompts and observe messages in the Status Message panel as the script executes.</p>\n</li>\n<li>\n<p>From the command line, specify your test script with the desired system properties, as described in <a href=\"#CACGJBHA\">\"Starting the Access Tester with System Properties For Use in Command Line Mode\"</a>.</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -Dscript.scriptfile=\"\\tests\\script.xml\" -Dcontrol.ignorecontent=\"true\" \n-jar oamtest.jar\n</pre></li>\n</ul>\n</li>\n<li>\n<p>Review the log and output files and perform additional analysis after the Access Tester compares newly generated results with results captured in the input script, as described in <a href=\"#CACBIEED\">\"Evaluating Scripts, Log File, and Statistics\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACBIEED\" name=\"CACBIEED\"></a><a id=\"AIAAG2030\" name=\"AIAAG2030\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Evaluating Scripts, Log File, and Statistics</h2>\n<p>This section provides the following information:</p>\n<ul>\n<li>\n<p><a href=\"#CHDDDHCG\">About Evaluating Test Results</a></p>\n</li>\n<li>\n<p><a href=\"#CHDJFEGD\">About the Saved Connection Configuration File</a></p>\n</li>\n<li>\n<p><a href=\"#CHDBEHJC\">About the Generated Input Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CHDJAIHC\">About the Target Output File Containing Test Run Results</a></p>\n</li>\n<li>\n<p><a href=\"#CHDFGJGC\">About the Statistics Document</a></p>\n</li>\n<li>\n<p><a href=\"#CHDIEHJD\">About the Execution Log</a></p>\n</li>\n</ul>\n<a id=\"CHDDDHCG\" name=\"CHDDDHCG\"></a><a id=\"AIAAG2031\" name=\"AIAAG2031\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About Evaluating Test Results</h3>\n<p>At the end of a test run a \"results bundle\" gets generated containing three documents:</p>\n<ul>\n<li>\n<p>Target script: An XML document containing new test cases</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nThe target script is not created if the Access Tester is configured to run in multi-threaded mode.</div>\n</li>\n<li>\n<p>Execution log: A text file containing the messages displayed during script execution</p>\n</li>\n<li>\n<p>Execution statistics: An XML document containing test metrics and a list of mismatched elements</p>\n</li>\n</ul>\n<p>The matching pair of test cases in the original and target scripts shares the test case ID. This ID is represented by a UUID value, which makes it possible to compare individual test cases in the original script with those in the target script. For more information, see <a href=\"#CHDBEHJC\">\"About the Generated Input Test Script\"</a>.</p>\n<p>The statistics document contains the summary and detail statistics, as well as a list of test cases that did not match. The detailed statistics can be used for further analysis or to keep a historical trail of results. The summary statistics are the same statistics displayed at the end of the test run and can be used to quickly assess the state of a test run. The list of mismatched test cases as created in the statistics document contains test case IDs that have triggered mismatch and includes the reason for the mismatch, as seen in <a href=\"#CHDCCJID\">Table 14-14</a>.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2032\" name=\"AIAAG2032\"></a><a id=\"sthref457\" name=\"sthref457\"></a><a id=\"CHDCCJID\" name=\"CHDCCJID\"></a>\n<p class=\"titleintable\">Table 14-14 Mismatched Results Reasons in the Statistics Document</p>\n<table class=\"FormalMax\" title=\"Mismatched Results Reasons in the Statistics Document\" summary=\"Test Results for Evaluation\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t36\">Reason for a MisMatch</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t36\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t36\" headers=\"r1c1-t36\">\n<p>Result</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t36 r1c2-t36\">\n<p>The test cases did not match because of the difference in OAM Server response codes (Yes versus No).</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t36\" headers=\"r1c1-t36\">\n<p>Content</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t36 r1c2-t36\">\n<p>The test cases did not match because of the differences in the specific data values that were returned by the OAM Server. The specific values from the last test run that have triggered the mismatch are included.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br /></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDJFEGD\" name=\"CHDJFEGD\"></a><a id=\"AIAAG2033\" name=\"AIAAG2033\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Saved Connection Configuration File</h3>\n<p>This is the output files that is saved using the Save Configuration command on the File menu; the default file name is config.xml. This connection configuration file includes details that were specified in the Access Tester Console, Server Connection panel.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nAn input test script file is also generated as described in the following topic. The name of the configuration file is used in the input test script to ensure that running the Access Tester in command line mode picks up connection information defined in the connection file.</div>\n<div class=\"example\"><a id=\"AIAAG2034\" name=\"AIAAG2034\"></a><a id=\"sthref458\" name=\"sthref458\"></a>\n<p class=\"titleinexample\">Example 14-1 Connection Configuration File</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">&lt;?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?&gt;\n&lt;oamtestconfig xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n    &lt;connection timeout=\"30000\" minnconn=\"1\" mode=\"open\"&gt;\n        &lt;agent password=\"00030d05101b050c42\" name=\"<span class=\"italic\">agent1</span>\"/&gt;\n        &lt;keystore rootstore=\"\" keystore_password=\"\" keystore=\"\" \nglobal_passphrase=\"\"/&gt;\n        &lt;primary&gt;\n            &lt;server maxconn=\"1\" port=\"<span class=\"italic\">2100</span>\" addr=\"<span class=\"italic\">oam_server1</span>\"/&gt;\n        &lt;/primary&gt;\n        &lt;secondary&gt;\n            &lt;server maxconn=\"1\" port=\"0\" addr=\"\"/&gt;\n        &lt;/secondary&gt;\n    &lt;/connection&gt;\n    &lt;uri getauthscheme=\"true\"&gt;\n        &lt;scheme&gt;http&lt;/scheme&gt;\n        &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n        &lt;port&gt;7777&lt;/port&gt;\n        &lt;resource&gt;/index.html&lt;/resource&gt;\n        &lt;operation&gt;Get&lt;/operation&gt;\n    &lt;/uri&gt;\n    &lt;identity&gt;\n        &lt;id&gt;<span class=\"italic\">admin1</span>&lt;/id&gt;\n        &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n        &lt;certstore&gt;&lt;/certstore&gt;\n        &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n    &lt;/identity&gt;\n&lt;/oamtestconfig&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDBEHJC\" name=\"CHDBEHJC\"></a><a id=\"AIAAG2035\" name=\"AIAAG2035\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Generated Input Test Script</h3>\n<p>The input test script is generated by using the Access Tester and capturing your own test cases. The \"configfile\" attribute of the \"Control\" element is updated after creation to specify the connection configuration file to be used in command line mode for establishing a connection to the OAM Server.</p>\n<div class=\"example\"><a id=\"AIAAG2036\" name=\"AIAAG2036\"></a><a id=\"sthref459\" name=\"sthref459\"></a>\n<p class=\"titleinexample\">Example 14-2 Generated Input Test Script</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">&lt;?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?&gt;\n&lt;oamtestscript xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n    &lt;history description=\"Manually generated using agent 'agent1'\" \ncreatedon=\"2010-02-03T22:28:00.468-05:00\" createdby=\"test_user\"/&gt;\n    &lt;control numthreads=\"1\" numiterations=\"1\" ignorecontent=\"false\" \ntestname=\"samplerun1\" configfile=\"config.xml\"/&gt;\n    &lt;cases numcases=\"4\"&gt;\n        &lt;case uuid=\"465a4fda-d814-4ab7-b81b-f3f1cd72bbc0\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"984\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;\n                    &lt;line type=\"auth.scheme.id\"&gt;LDAPScheme&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.level\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.required.creds\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.redirect.url\"&gt;http://dadvmh0172.us.oracle.com:14100/oam/server/&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"009b44e3-1a94-4bfc-a0c3-84a38a9e0f2a\"&gt;\n            &lt;request code=\"Authenticate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;certstore&gt;&lt;/certstore&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"187\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 10(CredentialsAccepted) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;\n                    &lt;line type=\"user.dn\"&gt;cn=weblogic,dc=us,dc=oracle,dc=com&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"84fe9b06-86d1-47df-a399-6311990743c3\"&gt;\n            &lt;request code=\"Authorize\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;certstore&gt;&lt;/certstore&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"188\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 8(Allow) Minor code: 2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"61579e47-5532-42c3-bbc7-a00828256bf4\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"false\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"172\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n    &lt;/cases&gt;\n&lt;/oamtestscript&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDJAIHC\" name=\"CHDJAIHC\"></a><a id=\"AIAAG2037\" name=\"AIAAG2037\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Target Output File Containing Test Run Results</h3>\n<p>This example was generated by running the Access Tester in command line mode and specifying the script.xml file as input to execute the 4 captured test cases:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">Dscript.scriptfile=\"script.xml\" -jar oamtest.jar\n</pre>\n<p>Notice the various sections in <a href=\"#CHDFBBIH\">Example 14-3</a>. As shown in the execution log, this test run found no mismatches, and shows that 4 out of 4 requests matched.</p>\n<div class=\"example\"><a id=\"CHDFBBIH\" name=\"CHDFBBIH\"></a><a id=\"AIAAG2038\" name=\"AIAAG2038\"></a>\n<p class=\"titleinexample\">Example 14-3 Output File Generated During a Test Run</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">&lt;?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?&gt;\n&lt;oamtestscript xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n    &lt;history description=\"Generated from script 'script.xml' using agent 'agent1'\" \ncreatedon=\"2010-02-03T23:03:02.171-05:00\" createdby=\"test_user\"/&gt;\n    &lt;control numthreads=\"1\" numiterations=\"1\" ignorecontent=\"false\" \ntestname=\"oamtest\" configfile=\"\"/&gt;\n    &lt;cases numcases=\"4\"&gt;\n        &lt;case uuid=\"465a4fda-d814-4ab7-b81b-f3f1cd72bbc0\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"969\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;\n                    &lt;line type=\"auth.scheme.id\"&gt;LDAPScheme&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.level\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.required.creds\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.redirect.url\"&gt;http://dadvmh0172.us.oracle.com:14100/oam/server/\n&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"009b44e3-1a94-4bfc-a0c3-84a38a9e0f2a\"&gt;\n            &lt;request code=\"Authenticate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;certstore&gt;&lt;/certstore&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"187\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 10(CredentialsAccepted) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;    \n                    &lt;line type=\"user.dn\"&gt;cn=weblogic,dc=us,dc=oracle,dc=com&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"84fe9b06-86d1-47df-a399-6311990743c3\"&gt;\n            &lt;request code=\"Authorize\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;certstore&gt;&lt;/certstore&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"172\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 8(Allow) Minor code: 2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"61579e47-5532-42c3-bbc7-a00828256bf4\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"false\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"171\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n    &lt;/cases&gt;\n&lt;/oamtestscript&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDFGJGC\" name=\"CHDFGJGC\"></a><a id=\"AIAAG2039\" name=\"AIAAG2039\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Statistics Document</h3>\n<p>The statistics file (_stats.xml) is generated together with the target output script during the test run identified in the Execution log. The script.xml file was used as input to execute the 4 captured test cases. The test run found no mismatches, and shows that 4 out of 4 requests matched.</p>\n<p>A sample statistics document is shown in <a href=\"#CHDICAGE\">Example 14-4</a>. The various sections that provide statistics for this run, which you can compare against statistics for an earlier \"known good\" run.</p>\n<div class=\"example\"><a id=\"CHDICAGE\" name=\"CHDICAGE\"></a><a id=\"AIAAG2040\" name=\"AIAAG2040\"></a>\n<p class=\"titleinexample\">Example 14-4 Sample Statistics Document</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">A sample statistics document is shown here. Notice, \n&lt;oamteststats xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n     &lt;history description=\"Generated from script 'script.xml' using agent \n       'agent1'\" createdon=\"2010-02-03T23:03:02.171-05:00\" createdby=\"test_user\"/&gt;\n     &lt;summary&gt;\n          &lt;total&gt;\n              &lt;nummatched&gt;4&lt;/nummatched&gt;\n              &lt;numtotal&gt;4&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;238&lt;/avgelapsedsource\n              &lt;avgelapsedtarget&gt;232&lt;/avgelapsedtarget&gt;\n          &lt;/total&gt;\n          &lt;validate&gt;\n              &lt;nummatched&gt;2&lt;/nummatched&gt;\n              &lt;numtotal&gt;2&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;578&lt;/avgelapsedsource&gt;\n              &lt;avgelapsedtarget&gt;570&lt;/avgelapsedtarget&gt;\n          &lt;/validate&gt;\n          &lt;authenticate&gt;\n              &lt;nummatched&gt;1&lt;/nummatched&gt;\n              &lt;numtotal&gt;1&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;187&lt;/avgelapsedsource&gt;\n              &lt;avgelapsedtarget&gt;187&lt;/avgelapsedtarget&gt;\n          &lt;/authenticate&gt;\n          &lt;authorize&gt;\n              &lt;nummatched&gt;1&lt;/nummatched&gt;\n              &lt;numtotal&gt;1&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;188&lt;/avgelapsedsource&gt;\n              &lt;avgelapsedtarget&gt;172&lt;/avgelapsedtarget&gt;\n          &lt;/authorize&gt;\n          &lt;summary&gt;\n          &lt;detail&gt;\n               &lt;source&gt;\n                    &lt;validate&gt;\n                       &lt;yes&gt;2&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;1156&lt;/elapsed&gt;\n                    &lt;/validate&gt;\n                &lt;authenticate&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;187&lt;/elapsed&gt;\n               &lt;/authenticate&gt;\n               &lt;authorize&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;188&lt;/elapsed&gt;\n               &lt;/authorize&gt;\n          &lt;/source&gt;\n          &lt;target&gt;\n               &lt;validate&gt;\n                       &lt;yes&gt;2&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;1140&lt;/elapsed&gt;\n               &lt;/validate&gt;\n          &lt;authenticate&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;187&lt;/elapsed&gt;\n          &lt;/authenticate&gt;\n          &lt;authorize&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;172&lt;/elapsed&gt;\n          &lt;/authorize&gt;\n      &lt;target&gt;\n      &lt;/detail&gt;\n    &lt;mismatch numcases=\"0\"/&gt;\n&lt;/oamteststats&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDIEHJD\" name=\"CHDIEHJD\"></a><a id=\"AIAAG2041\" name=\"AIAAG2041\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Execution Log</h3>\n<p>This sample execution log was generated together with the target output script during a test run using script.xml to execute 4 test cases. The test run found no mismatches, and shows that 4 out of 4 requests matched.</p>\n<p>As you review this example, notice the information provided which is the same as the information you see in the Status Messages panel of the Access Tester. Notice the test cases, test name, connection configuration file, agent name, connection status, request validation status, authentication scheme, redirect URL, credentials expected, authentication status and user DN, session ID, authorization status, validation status, and summary statistics. Also notice that the target script and statistics document were generated by this run.</p>\n<div class=\"example\"><a id=\"AIAAG2042\" name=\"AIAAG2042\"></a><a id=\"sthref460\" name=\"sthref460\"></a>\n<p class=\"titleinexample\">Example 14-5 Execution Log</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">[2/3/10 11:02 PM][info] Setting up to run script '<span class=\"italic\">script.xml</span>'\n[2/3/10 11:02 PM][info] Loading test cases and control parameters from script\n[2/3/10 11:02 PM][info] Loaded 4 cases\n[2/3/10 11:02 PM][info] Control data for this test run:\n[2/3/10 11:02 PM][info] Test name : '<span class=\"italic\">samplerun1</span>'\n[2/3/10 11:02 PM][info] Configuration file : '<span class=\"italic\">config.xml</span>'\n[2/3/10 11:02 PM][info] Ignore content : 'false'\n[2/3/10 11:02 PM][info] Loading server configuration from file\n[2/3/10 11:02 PM][info] Loaded server configuration\n[2/3/10 11:02 PM][info] Connecting to server as agent '<span class=\"italic\">oam_agent1</span>'\n[2/3/10 11:03 PM][info][request] Connect : Yes\n...\n[2/3/10 11:03 PM][info] Test '<span class=\"italic\">samplerun1</span>' will process 4 cases\n[2/3/10 11:03 PM][info][request] Validate : Yes\n[2/3/10 11:03 PM][info] Authentication scheme : <span class=\"italic\">LDAPScheme</span>, level : <span class=\"italic\">2</span>\n[2/3/10 11:03 PM][info] Redirect URL : \nhttp://<span class=\"italic\">oam_server1</span>.us.company.com:2100/server/\n[2/3/10 11:03 PM][info] Credentials expected: <span class=\"italic\">0x01 (password)</span>\n[2/3/10 11:03 PM][info][request] Authenticate : Yes\n[2/3/10 11:03 PM][info] User DN : cn=<span class=\"italic\">admin</span>1,dc=us,dc=<span class=\"italic\">company</span>,dc=com\n[2/3/10 11:03 PM][info] Session ID : -1\n[2/3/10 11:03 PM][info][request] Authorize : Yes\n[2/3/10 11:03 PM][info][request] Validate : Yes\n[2/3/10 11:03 PM][info] Summary statistics\n[2/3/10 11:03 PM][info] Matched 4 of 4, avg latency 232ms vs 238ms\n[2/3/10 11:03 PM][info] Validate: matched 2 of 2, avg latency 570ms vs 578ms\n[2/3/10 11:03 PM][info] Authenticate: matched 1 of 1, avg latency 187ms vs 187ms\n[2/3/10 11:03 PM][info] Authorize: matched 1 of 1, avg latency 172ms vs 188ms\n[2/3/10 11:03 PM][info] Generated target script 'samplerun1_0302171__target.xml'\n[2/3/10 11:03 PM][info] Generated statistics log 'samplerun1_0302171__stats.xml'\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" --></div>\n<!-- class=\"ind\" -->\n<div class=\"footer\">\n<hr />\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n<col width=\"33%\" />\n<col width=\"*\" />\n<col width=\"33%\" />\n<tr>\n<td align=\"left\">\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"98\">\n<tr>\n<td align=\"center\" valign=\"top\"><a href=\"app_domn.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/leftnav.gif\" alt=\"Go to previous page\" /><br />\n<span class=\"icon\">Previous</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"logout.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/rightnav.gif\" alt=\"Go to next page\" /><br />\n<span class=\"icon\">Next</span></a></td>\n</tr>\n</table>\n</td>\n<td style=\"font-size: 90%\" align=\"center\" class=\"copyrightlogo\"><img width=\"144\" height=\"18\" src=\"../../dcommon/gifs/oracle.gif\" alt=\"Oracle\" /><br />\nCopyright&nbsp;&copy;&nbsp;2000, 2011,&nbsp;Oracle&nbsp;and/or&nbsp;its&nbsp;affiliates.&nbsp;All&nbsp;rights&nbsp;reserved.<br />\n<a href=\"../../dcommon/html/cpyr.htm\">Legal Notices</a></td>\n<td align=\"right\">\n<table class=\"icons oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"245\">\n<tr>\n<td align=\"center\" valign=\"top\"><a href=\"../../index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/doclib.gif\" alt=\"Go to Documentation Home\" /><br />\n<span class=\"icon\">Home</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../nav/portal_booklist.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/booklist.gif\" alt=\"Go to Book List\" /><br />\n<span class=\"icon\">Book List</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"toc.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/toc.gif\" alt=\"Go to Table of Contents\" /><br />\n<span class=\"icon\">Contents</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/index.gif\" alt=\"Go to Index\" /><br />\n<span class=\"icon\">Index</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../dcommon/html/feedback.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/feedbck2.gif\" alt=\"Go to Feedback page\" /><br />\n<span class=\"icon\">Contact Us</span></a></td>\n</tr>\n</table>\n</td>\n</tr>\n</table>\n</div>\n<noscript>\n<p>Scripting on this page enhances content navigation, but does not change the content in any way.</p>\n</noscript>\n\n<!-- Start SiteCatalyst code -->\n<script type=\"text/javascript\" language=\"JavaScript\" src=\"http://www.oracle.com/ocom/groups/systemobject/@mktg_admin/documents/systemobject/s_code_download.js\"></script>\n<script type=\"text/javascript\" language=\"JavaScript\" src=\"http://www.oracle.com/ocom/groups/systemobject/@mktg_admin/documents/systemobject/s_code.js\"></script>\n\n<!-- ********** DO NOT ALTER ANYTHING BELOW THIS LINE ! *********** -->\n<!--  Below code will send the info to Omniture server -->\n<script type=\"text/javascript\" language=\"javascript\">var s_code=s.t();if(s_code)document.write(s_code)</script>\n\n<!-- End SiteCatalyst code -->\n</body>\n</html>\n", "images": [{"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/aiaag_jd_109.gif", "alt": "OAM Agent, OAM Server Inter-operability", "width": "569", "height": "545"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/aiaag_jd_110.gif", "alt": "Access Tester User Interactions", "width": "657", "height": "563"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/access_test_1.gif", "alt": "Access Tester Console", "width": "672", "height": "601"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/ac_svr_connect.gif", "alt": "Access Tester Server Connection Panel", "width": "663", "height": "141"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/ac_protected_res.gif", "alt": "Access Tester Protected Resource URI Panel", "width": "665", "height": "112"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/ac_user_id.gif", "alt": "Access Tester User Identity Panel", "width": "652", "height": "115"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/aiaag_jd_108.gif", "alt": "Test Case Workflow", "width": "605", "height": "425"}, {"src": "https://docs.oracle.com/cd/E15586_01/doc.1111/e15478/img/ac_capt_queue.gif", "alt": "Capture Queue Message", "width": "711", "height": "239"}], "success": true}}