{"timestamp": 1753009324.522198, "data": {"title": "\n            A systematic literature survey on recent trends in stock market prediction - PMC\n        ", "content": "A systematic literature survey on recent trends in stock market prediction - PMC Skip to main content An official website of the United States government Here's how you know Here's how you know Official websites use .gov A .gov website belongs to an official\n                            government organization in the United States. Secure .gov websites use HTTPS A lock ( Lock Locked padlock icon ) or https:// means you've safely\n                                connected to the .gov website. Share sensitive\n                                information only on official, secure websites. Service Alert: Planned Maintenance beginning July 25th Most services will be unavailable for 24+ hours starting 9 PM EDT. Learn more about the maintenance . Search Log in Dashboard Publications Account settings Log out Search… Search NCBI Primary site navigation Search Logged in as: Dashboard Publications Account settings Log in Search PMC Full-Text Archive Search in PMC Advanced Search Journal List User Guide New Try this search in PMC Beta Search View on publisher site Download PDF Add to Collections Cite Permalink PERMALINK Copy As a library, NLM provides access to scientific literature. Inclusion in an NLM database does not imply endorsement of, or agreement with,\n    the contents by NLM or the National Institutes of Health. Learn more: PMC Disclaimer | PMC Copyright Notice PeerJ Comput Sci . 2024 Jan 31;10:e1700. doi: 10.7717/peerj-cs.1700 Search in PMC Search in PubMed View in NLM Catalog Add to search A systematic literature survey on recent trends in stock market prediction Prakash Balasubramanian Prakash Balasubramanian 1 School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India Find articles by Prakash Balasubramanian 1, ✉ , Chinthan P Chinthan P 2 School of Mechanical Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India Find articles by Chinthan P 2 , Saleena Badarudeen Saleena Badarudeen 1 School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India Find articles by Saleena Badarudeen 1 , Harini Sriraman Harini Sriraman 1 School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India Find articles by Harini Sriraman 1 Editor: Sándor Szénási Author information Article notes Copyright and License information 1 School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India 2 School of Mechanical Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India ✉ Corresponding author. Received 2023 Jan 24; Accepted 2023 Oct 25; Collection date 2024. © 2024 Balasubramanian et al. This is an open access article distributed under the terms of the Creative Commons Attribution License , which permits unrestricted use, distribution, reproduction and adaptation in any medium and for any purpose provided that it is properly attributed. For attribution, the original author(s), title, publication source (PeerJ Computer Science) and either DOI or URL of the article must be cited. PMC Copyright notice PMCID: PMC10909160  PMID: 38435546 Abstract Prediction of the stock market is a challenging and time-consuming process. In recent times, various research analysts and organizations have used different tools and techniques to analyze and predict stock price movements. During the early days, investors mainly depend on technical indicators and fundamental parameters for short-term and long-term predictions, whereas nowadays many researchers started adopting artificial intelligence-based methodologies to predict stock price movements. In this article, an exhaustive literature study has been carried out to understand multiple techniques employed for prediction in the field of the financial market. As part of this study, more than hundreds of research articles focused on global indices and stock prices were collected and analyzed from multiple sources. Further, this study helps the researchers and investors to make a collective decision and choose the appropriate model for better profit and investment based on local and global market conditions. Keywords: Machine learning, Deep learning, Stock market prediction, Artificial intelligence Introduction Researchers from a range of disciplines are conducting studies on stock market forecasting. Many financial experts have attempted to address the problem of forecasting the upward and downward movements of the stock market, but have had only sporadic success. It is now more feasible than ever before to do so with the technology’s rapid advancements in computation capability, storage capacity, and algorithm accuracy. For stock market forecasting, researchers have experimented with a variety of strategies, algorithms, and attribute combinations. The characteristic of a prediction model depends on market-specific variables. The stock market plays a critical role to the rapid economic expansion of developing...", "url": "https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/", "html": "<html lang=\"en\" class=\"\"><head>\n\n        <meta charset=\"UTF-8\">\n        <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n        <meta name=\"HandheldFriendly\" content=\"True\">\n        <meta name=\"MobileOptimized\" content=\"320\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n        \n        \n\n        \n        \n  <link rel=\"stylesheet\" href=\"/static/assets/style-a68b4900.css\">\n<script type=\"text/javascript\" async=\"\" src=\"https://www.googletagmanager.com/gtag/js?id=G-DP2X732JSX&amp;l=pingerDataLayer&amp;cx=c&amp;gtm=45He57g1v847342615za200&amp;tag_exp=101509157~102015666~103116026~103200004~103233427~104684208~104684211\"></script><script async=\"\" src=\"https://www.googletagmanager.com/gtm.js?id=GTM-PC9B6M3&amp;l=pingerDataLayer\" id=\"pingerInjectedGTM\"></script><script type=\"module\" crossorigin=\"\" src=\"/static/assets/base_style-0a3f24ce.js\"></script>\n\n  <link rel=\"stylesheet\" href=\"/static/assets/style-ef962842.css\">\n<link rel=\"stylesheet\" href=\"/static/assets/style-3ade8b5c.css\">\n<script type=\"module\" crossorigin=\"\" src=\"/static/assets/article_style-d757a0dd.js\"></script>\n\n  \n  \n    <style>\n  \n  \n  @media screen and (min-width: 64em) {\n    div.pmc-wm {\n      background: repeat-y;\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='350' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cfilter x='-.02' y='0' width='1.05' height='1' id='c'%3E%3CfeFlood flood-color='%23FFF'/%3E%3CfeComposite in='SourceGraphic'/%3E%3C/filter%3E%3Ctext id='b' font-family='Helvetica' font-size='11pt' style='opacity:1;fill:%23005ea2;stroke:none;text-anchor:middle' x='175' y='14'%3E%3C/text%3E%3Cpath id='a' style='fill:%23005ea2' d='M0 8h350v3H0z'/%3E%3C/defs%3E%3Cuse xlink:href='%23a' transform='rotate(90 10 10)'/%3E%3Cuse xlink:href='%23b' transform='rotate(90 10 10)' filter='url(%23c)'/%3E%3C/svg%3E\");\n      padding-left: 3rem;\n    }\n  }\n</style>\n\n  \n\n\n\n        \n            <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/static/img/favicons/apple-touch-icon.png\">\n            <link rel=\"icon\" type=\"image/png\" sizes=\"48x48\" href=\"/static/img/favicons/favicon-48x48.png\">\n            <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/static/img/favicons/favicon-32x32.png\">\n            <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/static/img/favicons/favicon-16x16.png\">\n            <link rel=\"manifest\" href=\"/static/img/favicons/site.webmanifest\">\n            <link rel=\"mask-icon\" href=\"/static/img/favicons/safari-pinned-tab.svg\" color=\"#0071bc\">\n            <meta name=\"msapplication-config\" content=\"/static/img/favicons/browserconfig.xml\">\n            <meta name=\"theme-color\" content=\"#ffffff\">\n        \n\n        <title>\n            A systematic literature survey on recent trends in stock market prediction - PMC\n        </title>\n\n        \n        \n  \n  <!-- Logging params: Pinger defaults -->\n<meta name=\"ncbi_app\" content=\"cloudpmc-viewer\">\n<meta name=\"ncbi_db\" content=\"pmc\">\n<meta name=\"ncbi_phid\" content=\"4DDED87787CC95A30BD877002E22B045.m_1\">\n<meta name=\"ncbi_pinger_stat_url\" content=\"https://pmc.ncbi.nlm.nih.gov/stat\">\n<!-- Logging params: Pinger custom -->\n<meta name=\"ncbi_pdid\" content=\"article\">\n  \n    <link rel=\"preconnect\" href=\"https://www.google-analytics.com\">\n\n    \n        <link rel=\"preconnect\" href=\"https://cdn.ncbi.nlm.nih.gov\">\n    \n\n    <!-- Include USWDS Init Script -->\n    <script src=\"/static/assets/uswds-init.js\"></script>\n\n\n    <meta name=\"ncbi_domain\" content=\"peerjcs\">\n<meta name=\"ncbi_type\" content=\"fulltext\">\n<meta name=\"ncbi_pcid\" content=\"journal\">\n<meta name=\"ncbi_feature\" content=\"associated_data\">\n<link rel=\"canonical\" href=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/\">\n<meta name=\"robots\" content=\"INDEX,NOFOLLOW,NOARCHIVE\">\n<meta name=\"citation_journal_title\" content=\"PeerJ Computer Science\">\n<meta name=\"citation_title\" content=\"A systematic literature survey on recent trends in stock market prediction\">\n<meta name=\"citation_author\" content=\"Prakash Balasubramanian\">\n<meta name=\"citation_author_institution\" content=\"School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India\">\n<meta name=\"citation_author\" content=\"Chinthan P\">\n<meta name=\"citation_author_institution\" content=\"School of Mechanical Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India\">\n<meta name=\"citation_author\" content=\"Saleena Badarudeen\">\n<meta name=\"citation_author_institution\" content=\"School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India\">\n<meta name=\"citation_author\" content=\"Harini Sriraman\">\n<meta name=\"citation_author_institution\" content=\"School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India\">\n<meta name=\"citation_publication_date\" content=\"2024 Jan 31\">\n<meta name=\"citation_volume\" content=\"10\">\n<meta name=\"citation_firstpage\" content=\"e1700\">\n<meta name=\"citation_doi\" content=\"10.7717/peerj-cs.1700\">\n<meta name=\"citation_pmid\" content=\"38435546\">\n<meta name=\"citation_abstract_html_url\" content=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/\">\n<meta name=\"citation_fulltext_html_url\" content=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/\">\n<meta name=\"citation_pdf_url\" content=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/pdf/peerj-cs-10-1700.pdf\">\n<meta name=\"description\" content=\"Prediction of the stock market is a challenging and time-consuming process. In recent times, various research analysts and organizations have used different tools and techniques to analyze and predict stock price movements. During the early days, ...\">\n<meta name=\"og:title\" content=\"A systematic literature survey on recent trends in stock market prediction\">\n<meta name=\"og:type\" content=\"article\">\n<meta name=\"og:site_name\" content=\"PubMed Central (PMC)\">\n<meta name=\"og:description\" content=\"Prediction of the stock market is a challenging and time-consuming process. In recent times, various research analysts and organizations have used different tools and techniques to analyze and predict stock price movements. During the early days, ...\">\n<meta name=\"og:url\" content=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/\">\n<meta name=\"og:image\" content=\"https://cdn.ncbi.nlm.nih.gov/pmc/cms/images/pmc-card-share.jpg?_=0\">\n<meta name=\"twitter:card\" content=\"summary_large_image\">\n<meta name=\"twitter:site\" content=\"@ncbi\">\n    \n    \n\n    <style>.ncbi-alerts {width: 100%;}.ncbi-alerts .ncbi-alert__shutdown-outer { position: relative; background-color: #f4e3db; border-left: 8px solid #d54309;  background-image: url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' width='126' height='126' viewBox='0 0 126 126'%3E%3Cpath fill='%231B1B1B' d='M117.18,31.592 C111.585,22.006 103.995,14.416 94.409,8.821 C84.821,3.226 74.354,0.429 63.001,0.429 C51.649,0.429 41.18,3.226 31.593,8.821 C22.006,14.415 14.416,22.005 8.821,31.592 C3.225,41.179 0.428,51.649 0.428,63 C0.428,74.351 3.226,84.82 8.82,94.408 C14.415,103.992 22.005,111.584 31.592,117.179 C41.179,122.774 51.648,125.571 63,125.571 C74.352,125.571 84.822,122.774 94.408,117.179 C103.994,111.585 111.584,103.994 117.179,94.408 C122.773,84.82 125.57,74.35 125.57,63 C125.57,51.649 122.773,41.178 117.18,31.592 Z M73.43,102.025 C73.43,102.786 73.184,103.423 72.696,103.939 C72.208,104.455 71.61,104.712 70.903,104.712 L55.26,104.712 C54.554,104.712 53.929,104.441 53.386,103.898 C52.843,103.355 52.572,102.73 52.572,102.025 L52.572,86.546 C52.572,85.84 52.843,85.215 53.386,84.672 C53.929,84.129 54.554,83.858 55.26,83.858 L70.903,83.858 C71.61,83.858 72.209,84.116 72.696,84.631 C73.184,85.149 73.43,85.785 73.43,86.546 L73.43,102.025 Z M73.266,73.999 C73.211,74.542 72.927,75.018 72.412,75.425 C71.895,75.832 71.258,76.035 70.498,76.035 L55.425,76.035 C54.664,76.035 54.012,75.832 53.469,75.425 C52.926,75.018 52.654,74.542 52.654,73.999 L51.269,23.404 C51.269,22.751 51.54,22.263 52.083,21.937 C52.627,21.503 53.279,21.285 54.039,21.285 L71.965,21.285 C72.726,21.285 73.377,21.502 73.92,21.937 C74.463,22.263 74.733,22.752 74.733,23.404 L73.266,73.999 Z'/%3E%3C/svg%3E%0A\");}.ncbi-alerts .ncbi-alert__info-outer { position: relative; background-color: #e7f6f8; border-left: 8px solid #00bde3; background-image: url(\"data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg xmlns='http://www.w3.org/2000/svg' width='126' height='126' viewBox='0 0 126 126'%3E%3Cpath fill='%231B1B1B' d='M117.18,31.592 C111.585,22.006 103.995,14.416 94.409,8.821 C84.821,3.226 74.354,0.429 63.001,0.429 C51.649,0.429 41.179,3.226 31.593,8.821 C22.006,14.415 14.416,22.005 8.821,31.592 C3.225,41.179 0.428,51.649 0.428,63 C0.428,74.352 3.226,84.82 8.82,94.408 C14.415,103.993 22.005,111.584 31.592,117.179 C41.179,122.774 51.648,125.571 63,125.571 C74.352,125.571 84.822,122.774 94.408,117.179 C103.994,111.585 111.584,103.994 117.179,94.408 C122.773,84.821 125.57,74.351 125.57,63 C125.57,51.648 122.773,41.178 117.18,31.592 Z M52.572,16.071 C52.572,15.31 52.816,14.686 53.305,14.197 C53.794,13.709 54.419,13.464 55.179,13.464 L70.823,13.464 C71.583,13.464 72.208,13.709 72.695,14.197 C73.183,14.686 73.429,15.31 73.429,16.071 L73.429,29.107 C73.429,29.867 73.183,30.492 72.695,30.98 C72.208,31.469 71.583,31.713 70.823,31.713 L55.179,31.713 C54.419,31.713 53.794,31.469 53.305,30.98 C52.816,30.492 52.572,29.867 52.572,29.107 L52.572,16.071 Z M83.857,102.107 C83.857,102.867 83.611,103.492 83.124,103.979 C82.637,104.468 82.012,104.712 81.25,104.712 L44.75,104.712 C43.989,104.712 43.365,104.468 42.876,103.979 C42.387,103.491 42.143,102.866 42.143,102.106 L42.143,89.07 C42.143,88.308 42.387,87.685 42.876,87.196 C43.365,86.708 43.99,86.463 44.75,86.463 L52.572,86.463 L52.572,60.392 L44.75,60.392 C43.989,60.392 43.365,60.148 42.876,59.659 C42.387,59.171 42.143,58.546 42.143,57.785 L42.143,44.75 C42.143,43.989 42.387,43.365 42.876,42.876 C43.365,42.387 43.99,42.143 44.75,42.143 L70.823,42.143 C71.583,42.143 72.208,42.387 72.695,42.876 C73.183,43.365 73.429,43.989 73.429,44.75 L73.429,86.464 L81.249,86.464 C82.01,86.464 82.635,86.709 83.123,87.197 C83.61,87.685 83.856,88.31 83.856,89.071 L83.856,102.107 L83.857,102.107 Z'/%3E%3C/svg%3E\");}.ncbi-alerts div[class$=\"-outer\"]{background-position: 28px 20px;  background-size: 32px 32px;  background-repeat: no-repeat;padding:20px 20px 20px 28px;}.ncbi-alerts div[class$=\"-inner\"]{padding-left: 52px; padding-right: 52px;}@media (max-width: 639px){.ncbi-alerts div[class$=\"outer\"]{background-position: 29px 20px;}.ncbi-alerts div[class$=\"-inner\"]{padding-left: 0px; padding-right: 0px; padding-top: 0px;}}.ncbi-alerts button.close{cursor: pointer;position:absolute; top: 10px; right: 20px; width: 36px; height: 32px; border:0; background-color: transparent; background-image: url(\"data:image/svg+xml,%3Csvg version='1.2' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' overflow='visible' preserveAspectRatio='none' viewBox='0 0 24 24' width='32' height='32'%3E%3Cg%3E%3Cpath xmlns:default='http://www.w3.org/2000/svg' id='window-close' d='M14.9,16.42c-0.13,0.13-0.33,0.14-0.47,0.01c0,0-0.01-0.01-0.01-0.01L12,14l-2.43,2.42 c-0.13,0.13-0.33,0.14-0.47,0.01c0,0-0.01-0.01-0.01-0.01L7.58,14.9c-0.13-0.13-0.14-0.33-0.01-0.47c0,0,0.01-0.01,0.01-0.01L10,12 L7.58,9.57C7.45,9.45,7.44,9.24,7.57,9.1c0,0,0.01-0.01,0.01-0.01L9.1,7.58c0.13-0.13,0.33-0.14,0.47-0.01c0,0,0.01,0.01,0.01,0.01 L12,10l2.43-2.43c0.13-0.13,0.33-0.14,0.47-0.01c0,0,0.01,0.01,0.01,0.01l1.51,1.53c0.13,0.13,0.14,0.33,0.01,0.47 c0,0-0.01,0.01-0.01,0.01L14,12l2.43,2.43c0.13,0.13,0.14,0.33,0.01,0.47c0,0-0.01,0.01-0.01,0.01L14.9,16.42L14.9,16.42z M20.84,4.49C20.53,4.17,20.1,3.99,19.66,4H4.34C3.42,4,2.67,4.75,2.67,5.67l0,0v12.66c0,0.92,0.75,1.67,1.67,1.67l0,0h15.32 c0.92,0,1.67-0.75,1.67-1.67l0,0V5.67C21.34,5.23,21.16,4.8,20.84,4.49z' style='fill: rgb(33, 33, 33);' vector-effect='non-scaling-stroke'/%3E%3C/g%3E%3C/svg%3E\");background-size: 32px 32px; background-repeat:no-repeat;background-position:center center}.ncbi-alerts button.close:focus{ outline: 1px dotted #000000;}@media (max-width: 639px){.ncbi-alerts button.close{right: 0px;}}.ncbi-alerts p{margin-bottom: 0px; margin-top: 0px; font-size: 18px; line-height: 28px;}.ncbi-alerts .list-items > p{font-size: 0.94em; display: inline;}.ncbi-alerts .list-items > p a {font-weight: 700;}.ncbi-alerts .list-items > p:not(:last-child)::after{margin:0 8px;content:'|'}@media (min-width: 768px) and (max-width: 991px){.ncbi-alerts .list-items > p:nth-child(2)::after{content:'\\a'!important;white-space: pre;}}@media(max-width: 767px){.ncbi-alerts .list-items > p{display:block;}.ncbi-alerts .list-items > p::after{content:''!important;}}.ncbi-alerts h3 {margin: 2px 0 1rem 0; padding: 0 52px; color: #000; font-size: 20px; font-weight: 700; line-height: 1.3;}@media (max-width: 639px){.ncbi-alerts h3 {padding-left: 42px;}}</style><script type=\"text/javascript\" src=\"https://www.googletagmanager.com/gtag/js?id=G-CSLL4ZEK4L\"></script><script charset=\"utf-8\" src=\"https://siteintercept.qualtrics.com/dxjsmodule/8.4adfaf62a9bbf2d72d65.chunk.js?Q_CLIENTVERSION=2.33.0&amp;Q_CLIENTTYPE=web&amp;Q_BRANDID=pmc.ncbi.nlm.nih.gov\"></script><script charset=\"utf-8\" src=\"https://siteintercept.qualtrics.com/dxjsmodule/5.702eb9d1cd18a1f8aee0.chunk.js?Q_CLIENTVERSION=2.33.0&amp;Q_CLIENTTYPE=web&amp;Q_BRANDID=nlmenterprise\"></script><script charset=\"utf-8\" src=\"https://siteintercept.qualtrics.com/dxjsmodule/1.cba293a2ce32b62fd12d.chunk.js?Q_CLIENTVERSION=2.33.0&amp;Q_CLIENTTYPE=web&amp;Q_BRANDID=nlmenterprise\"></script></head>\n    <body>\n        \n    <a class=\"usa-skipnav \" href=\"#main-content\">\n      Skip to main content\n    </a>\n\n\n        \n            \n\n<section class=\"usa-banner \" aria-label=\"Official website of the United States government\">\n    <div class=\"usa-accordion\">\n        <header class=\"usa-banner__header\">\n            <div class=\"usa-banner__inner\">\n                <div class=\"grid-col-auto\">\n                    <img aria-hidden=\"true\" class=\"usa-banner__header-flag\" src=\"/static/img/us_flag.svg\" alt=\"\">\n                </div>\n\n                <div class=\"grid-col-fill tablet:grid-col-auto\" aria-hidden=\"true\">\n                    <p class=\"usa-banner__header-text\">\n                        An official website of the United States government\n                    </p>\n                    <span class=\"usa-banner__header-action\">Here's how you know</span>\n                </div>\n\n                \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-accordion__button usa-banner__button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           \n           \" aria-expanded=\"false\" aria-controls=\"gov-banner-default\" data-testid=\"storybook-django-banner\">\n    \n        \n\n        \n                    <span class=\"usa-banner__button-text\">Here's how you know</span>\n                \n\n        \n    \n        \n            </button>\n        \n\n\n            </div>\n        </header>\n\n        <div class=\"usa-banner__content usa-accordion__content\" id=\"gov-banner-default\" hidden=\"\">\n            <div class=\"grid-row grid-gap-lg\">\n                <div class=\"usa-banner__guidance tablet:grid-col-6\">\n                    <img class=\"usa-banner__icon usa-media-block__img\" src=\"/static/img/icon-dot-gov.svg\" alt=\"\" aria-hidden=\"true\">\n                    <div class=\"usa-media-block__body\">\n                        <p>\n                            <strong>Official websites use .gov</strong>\n                            <br>\n                            A\n                            <strong>.gov</strong> website belongs to an official\n                            government organization in the United States.\n                        </p>\n                    </div>\n                </div>\n\n                <div class=\"usa-banner__guidance tablet:grid-col-6\">\n                    <img class=\"usa-banner__icon usa-media-block__img\" src=\"/static/img/icon-https.svg\" alt=\"\" aria-hidden=\"true\">\n\n                    <div class=\"usa-media-block__body\">\n                        <p>\n                            <strong>Secure .gov websites use HTTPS</strong>\n                            <br>\n                            A <strong>lock</strong> (\n                            <span class=\"icon-lock\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"52\" height=\"64\" viewBox=\"0 0 52 64\" class=\"usa-banner__lock-image\" role=\"graphics-symbol\" aria-labelledby=\"banner-lock-description\" focusable=\"false\">\n                                    <title id=\"banner-lock-title\">Lock</title>\n                                    <desc id=\"banner-lock-description\">\n                                    Locked padlock icon\n                                    </desc>\n                                    <path fill=\"#000000\" fill-rule=\"evenodd\" d=\"M26 0c10.493 0 19 8.507 19 19v9h3a4 4 0 0 1 4 4v28a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V32a4 4 0 0 1 4-4h3v-9C7 8.507 15.507 0 26 0zm0 8c-5.979 0-10.843 4.77-10.996 10.712L15 19v9h22v-9c0-6.075-4.925-11-11-11z\"></path>\n                                </svg>\n</span>) or <strong>https://</strong> means you've safely\n                                connected to the .gov website. Share sensitive\n                                information only on official, secure websites.\n                            </p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </section><div class=\"ncbi-alerts\" id=\"f328da55-691f-43b2-acea-b30edd498c6a\" role=\"region\" aria-label=\"Alert\" data-hash=\"8805c432a2e832b61e999fae837b592e6ef6c7da4f5c98bf66f0815b819c0fe6\">\n                        <div class=\"ncbi-alert__info-outer\">\n                          <h3>Service Alert: Planned Maintenance beginning July 25th</h3>\n                          <div class=\"ncbi-alert__info-inner\">\n                            <p data-block-key=\"dnhyt\">Most services will be unavailable for 24+ hours starting 9 PM EDT. <a href=\"https://ncbiinsights.ncbi.nlm.nih.gov/2025/07/08/ncbi-website-maintenance-july-26th/\">Learn more about the maintenance</a>.</p>\n                          </div>\n                          \n                        </div>\n                      </div>\n\n        \n\n        \n    \n    \n    \n\n<div class=\"usa-overlay\">\n</div>\n\n\n\n<header class=\"usa-header usa-header--extended usa-header--wide\" data-testid=\"header\" data-header=\"\">\n    <div class=\"ncbi-header\">\n        <div class=\"ncbi-header__container\">\n            \n                <a class=\"ncbi-header__logo-container\" href=\"https://www.ncbi.nlm.nih.gov/\">\n                    <img alt=\"\n                                  NCBI home page\n                              \" class=\"ncbi-header__logo-image\" src=\"/static/img/ncbi-logos/nih-nlm-ncbi--white.svg\">\n                </a>\n            \n\n            <!-- Mobile menu hamburger button -->\n            \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-menu-btn ncbi-header__hamburger-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           \n           \" aria-label=\"Show menu\" data-testid=\"navMenuButton\">\n    \n        \n\n        \n                <svg aria-hidden=\"true\" class=\"ncbi-hamburger-icon\" fill=\"none\" focusable=\"false\" height=\"21\" viewBox=\"0 0 31 21\" width=\"31\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path clip-rule=\"evenodd\" d=\"M0.125 20.75H30.875V17.3333H0.125V20.75ZM0.125 12.2083H30.875V8.79167H0.125V12.2083ZM0.125 0.25V3.66667H30.875V0.25H0.125Z\" fill=\"#F1F1F1\" fill-rule=\"evenodd\"></path>\n                </svg>\n            \n\n        \n    \n        \n            </button>\n        \n\n\n\n            \n                <!-- Desktop buttons-->\n                <div class=\"ncbi-header__desktop-buttons\">\n                    \n                        <!-- Desktop search button -->\n                        \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           usa-button--unstyled ncbi-header__desktop-button\n           \" aria-expanded=\"false\" aria-controls=\"search-field-desktop-navigation\" aria-label=\"Show search overlay\" data-testid=\"toggleSearchPanelButton\" data-toggle-search-panel-button=\"\" data-ga-category=\"header\" data-ga-action=\"NCBI\" data-ga-label=\"header_search_open\">\n    \n        \n\n        \n                            \n\n\n    <svg class=\"usa-icon \" role=\"graphics-symbol\" aria-hidden=\"true\">\n        \n        <use xlink:href=\"/static/img/sprite.svg#search\"></use>\n    </svg>\n\n\n                            Search\n                        \n\n        \n    \n        \n            </button>\n        \n\n\n                    \n\n                    <!-- Desktop login dropdown -->\n                    \n                        <div class=\"ncbi-header__login-dropdown\">\n                            \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           usa-button--unstyled ncbi-header__desktop-button ncbi-header__login-dropdown-button\n           \" aria-expanded=\"false\" aria-controls=\"login-dropdown-menu\" aria-label=\"Show login menu\" data-testid=\"toggleLoginMenuDropdown\" data-desktop-login-button=\"\">\n    \n        \n\n        \n                                \n\n\n    <svg class=\"usa-icon \" role=\"graphics-symbol\" aria-hidden=\"true\">\n        \n        <use xlink:href=\"/static/img/sprite.svg#person\"></use>\n    </svg>\n\n\n\n                                <span data-login-dropdown-text=\"\">Log in</span>\n\n                                <!-- Dropdown icon pointing up -->\n                                \n\n\n    <svg class=\"usa-icon ncbi-header__login-dropdown-icon ncbi-header__login-dropdown-icon--expand-less ncbi-header__login-dropdown-icon--hidden\" role=\"graphics-symbol\" aria-hidden=\"true\" data-login-dropdown-up-arrow=\"\">\n        \n        <use xlink:href=\"/static/img/sprite.svg#expand_less\"></use>\n    </svg>\n\n\n\n                                <!-- Dropdown icon pointing down -->\n                                \n\n\n    <svg class=\"usa-icon ncbi-header__login-dropdown-icon ncbi-header__login-dropdown-icon--expand-more ncbi-header__login-dropdown-icon--hidden\" role=\"graphics-symbol\" aria-hidden=\"true\" data-login-dropdown-down-arrow=\"\">\n        \n        <use xlink:href=\"/static/img/sprite.svg#expand_more\"></use>\n    </svg>\n\n\n                            \n\n        \n    \n        \n            </button>\n        \n\n\n\n                            <!-- Login dropdown menu -->\n                            <ul class=\"usa-nav__submenu ncbi-header__login-dropdown-menu\" id=\"login-dropdown-menu\" data-desktop-login-menu-dropdown=\"\" hidden=\"\">\n                                \n                                    <li class=\"usa-nav__submenu-item\">\n                                        <!-- Uses custom style overrides to render external and document links. -->\n                                        \n\n\n\n\n\n\n\n\n<a href=\"https://www.ncbi.nlm.nih.gov/myncbi/\" class=\"usa-link  \">\n    \n\n    Dashboard\n\n    \n</a>\n\n                                    </li>\n                                \n                                    <li class=\"usa-nav__submenu-item\">\n                                        <!-- Uses custom style overrides to render external and document links. -->\n                                        \n\n\n\n\n\n\n\n\n<a href=\"https://www.ncbi.nlm.nih.gov/myncbi/collections/bibliography/\" class=\"usa-link  \">\n    \n\n    Publications\n\n    \n</a>\n\n                                    </li>\n                                \n                                    <li class=\"usa-nav__submenu-item\">\n                                        <!-- Uses custom style overrides to render external and document links. -->\n                                        \n\n\n\n\n\n\n\n\n<a href=\"https://www.ncbi.nlm.nih.gov/account/settings/\" class=\"usa-link  \">\n    \n\n    Account settings\n\n    \n</a>\n\n                                    </li>\n                                \n                                <li class=\"usa-nav__submenu-item\">\n                                    \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           usa-button--outline ncbi-header__login-dropdown-logout-button\n           \" data-testid=\"desktopLogoutButton\" data-desktop-logout-button=\"\">\n    \n        \n\n        Log out\n\n        \n    \n        \n            </button>\n        \n\n\n                                </li>\n                            </ul>\n                        </div>\n                    \n                </div>\n            \n        </div>\n    </div>\n\n    <!-- Search panel -->\n    \n        <div class=\"ncbi-search-panel ncbi--show-only-at-desktop\" data-testid=\"searchPanel\" data-header-search-panel=\"\" hidden=\"\">\n            <div class=\"ncbi-search-panel__container\">\n                <form action=\"https://www.ncbi.nlm.nih.gov/search/all/\" aria-describedby=\"search-field-desktop-navigation-help-text\" autocomplete=\"off\" class=\"usa-search usa-search--big ncbi-search-panel__form\" data-testid=\"form\" method=\"GET\" role=\"search\">\n                    <label class=\"usa-sr-only\" data-testid=\"label\" for=\"search-field-desktop-navigation\">\n                        Search…\n                    </label>\n                    <input class=\"usa-input\" data-testid=\"textInput\" id=\"search-field-desktop-navigation\" name=\"term\" placeholder=\"Search NCBI\" type=\"search\" value=\"\">\n                    \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"submit\" class=\"usa-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           \n           \" data-testid=\"button\" data-ga-category=\"header\" data-ga-action=\"NCBI\" data-ga-label=\"header_search_button\">\n    \n        \n\n        \n                        <span class=\"usa-search__submit-text\">\n                            Search NCBI\n                        </span>\n                    \n\n        \n    \n        \n            </button>\n        \n\n\n                </form>\n\n                \n            </div>\n        </div>\n    \n\n    <nav aria-label=\"Primary navigation\" class=\"usa-nav\">\n        <p class=\"usa-sr-only\" id=\"primary-navigation-sr-only-title\">\n            Primary site navigation\n        </p>\n\n        <!-- Mobile menu close button -->\n        \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-nav__close ncbi-nav__close-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           \n           \" aria-label=\"Close navigation menu\" data-testid=\"navCloseButton\">\n    \n        \n\n        \n            <img src=\"/static/img/usa-icons/close.svg\" alt=\"Close\">\n        \n\n        \n    \n        \n            </button>\n        \n\n\n\n        \n            <!-- Mobile search component -->\n            <form class=\"usa-search usa-search--small ncbi--hide-at-desktop margin-top-6\" role=\"search\">\n                <label class=\"usa-sr-only\" for=\"search-field\">\n                    Search\n                </label>\n\n                <input class=\"usa-input\" id=\"search-field-mobile-navigation\" type=\"search\" placeholder=\"Search NCBI\" name=\"search\">\n\n                \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"submit\" class=\"usa-button\n           \n\n           \n               \n               \n               \n               \n            \n\n           \n           \n           \n           \" data-ga-category=\"header\" data-ga-action=\"NCBI\" data-ga-label=\"header_search_button\">\n    \n        \n\n        \n                    <!-- This SVG should be kept inline and not replaced with a link to the icon as otherwise it will render in the wrong color -->\n                    <img src=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0Ij48cGF0aCBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTE1LjUgMTRoLS43OWwtLjI4LS4yN0E2LjQ3MSA2LjQ3MSAwIDAgMCAxNiA5LjUgNi41IDYuNSAwIDEgMCA5LjUgMTZjMS42MSAwIDMuMDktLjU5IDQuMjMtMS41N2wuMjcuMjh2Ljc5bDUgNC45OUwyMC40OSAxOWwtNC45OS01em0tNiAwQzcuMDEgMTQgNSAxMS45OSA1IDkuNVM3LjAxIDUgOS41IDUgMTQgNy4wMSAxNCA5LjUgMTEuOTkgMTQgOS41IDE0eiIvPjwvc3ZnPg==\" class=\"usa-search__submit-icon\" alt=\"Search\">\n                \n\n        \n    \n        \n            </button>\n        \n\n\n            </form>\n\n            \n        \n\n        <!-- Primary navigation menu items -->\n        <!-- This usa-nav__inner wrapper is required to correctly style the navigation items on Desktop -->\n        \n\n        \n            <div class=\"ncbi-nav__mobile-login-menu ncbi--hide-at-desktop\" data-mobile-login-menu=\"\" hidden=\"\">\n                <p class=\"ncbi-nav__mobile-login-menu-status\">\n                    Logged in as:\n                    <strong class=\"ncbi-nav__mobile-login-menu-email\" data-mobile-login-email-text=\"\"></strong>\n                </p>\n                <ul class=\"usa-nav__primary usa-accordion\">\n                    \n                        <li class=\"usa-nav__primary-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.ncbi.nlm.nih.gov/myncbi/\" class=\"usa-link  \">\n    \n\n    Dashboard\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"usa-nav__primary-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.ncbi.nlm.nih.gov/myncbi/collections/bibliography/\" class=\"usa-link  \">\n    \n\n    Publications\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"usa-nav__primary-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.ncbi.nlm.nih.gov/account/settings/\" class=\"usa-link  \">\n    \n\n    Account settings\n\n    \n</a>\n\n                        </li>\n                    \n                </ul>\n            </div>\n        \n\n        \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n    \n        <button type=\"button\" class=\"usa-button ncbi-nav__mobile-login-button ncbi--hide-at-desktop\" data-testid=\"mobileLoginButton\" data-mobile-login-button=\"\">Log in</button>\n        \n\n\n    </nav>\n</header>\n\n    \n    \n        \n\n<section class=\"pmc-header pmc-header--basic\" aria-label=\"PMC Header with search box\">\n    <div class=\"pmc-nav-container\">\n        <div class=\"pmc-header__bar\">\n           <div class=\"pmc-header__logo\">\n               <a href=\"/\" title=\"Home\" aria-label=\"PMC Home\"></a>\n           </div>\n            <button type=\"button\" class=\"usa-button usa-button--unstyled pmc-header__search__button\" aria-label=\"Open search\" data-ga-category=\"search\" data-ga-action=\"PMC\" data-ga-label=\"pmc_search_panel_mobile\">\n                <svg class=\"usa-icon width-4 height-4 pmc-icon__open\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#search\"></use>\n                </svg>\n                <svg class=\"usa-icon width-4 height-4 pmc-icon__close\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#close\"></use>\n                </svg>\n            </button>\n        </div>\n        <div class=\"pmc-header__search\">\n            \n\n\n<form class=\"usa-search usa-search--extra usa-search--article-right-column  pmc-header__search__form\" id=\"pmc-search-form\" autocomplete=\"off\" role=\"search\">\n<label class=\"usa-sr-only\" for=\"pmc-search\">Search PMC Full-Text Archive</label>\n<span class=\"autoComplete_wrapper flex-1\">\n<input class=\"usa-input width-full maxw-none\" required=\"required\" placeholder=\"Search PMC Full-Text Archive\" id=\"pmc-search\" type=\"search\" name=\"term\" data-autocomplete-url=\"https://pmc.ncbi.nlm.nih.gov/autocomp/search/autocomp/\" aria-controls=\"autoComplete_list_1\" aria-autocomplete=\"both\" role=\"combobox\" aria-owns=\"autoComplete_list_1\" aria-haspopup=\"true\" aria-expanded=\"false\"><ul id=\"autoComplete_list_1\" role=\"listbox\" hidden=\"\" aria-label=\"Suggestions\"></ul>\n</span>\n<button class=\"usa-button\" type=\"submit\" formaction=\"https://www.ncbi.nlm.nih.gov/pmc/\" data-ga-category=\"search\" data-ga-action=\"PMC\" data-ga-label=\"PMC_search_button\">\n<span class=\"usa-search__submit-text\">Search in PMC</span>\n<img src=\"/static/img/usa-icons-bg/search--white.svg\" class=\"usa-search__submit-icon\" alt=\"Search\">\n</button>\n</form>\n            <div class=\"display-flex flex-column tablet:flex-row tablet:flex-justify flex-justify-center flex-align-center width-full desktop:maxw-44\">\n                <ul class=\"pmc-header__search__menu\">\n                    <li>\n                        <a class=\"usa-link\" href=\"https://www.ncbi.nlm.nih.gov/pmc/advanced/\" data-ga-action=\"featured_link\" data-ga-label=\"advanced_search\">\n                            Advanced Search\n                        </a>\n                    </li>\n                    <li>\n                        \n                            <a class=\"usa-link\" href=\"/journals/\" data-ga-action=\"featured_link\" data-ga-label=\"journal list\">\n                                Journal List\n                            </a>\n                        \n                    </li>\n                    <li>\n                        \n                            <a class=\"usa-link\" href=\"/about/userguide/\" data-ga-action=\"featured_link\" data-ga-label=\"user guide\">\n                                User Guide\n                            </a>\n                        \n                    </li>\n                </ul>\n                \n                    <button form=\"pmc-search-form\" formaction=\"https://pmc.ncbi.nlm.nih.gov/search/\" type=\"submit\" class=\"usa-button usa-button--unstyled hover:text-no-underline text-no-underline width-auto margin-top-1 tablet:margin-top-0\">\n     <span class=\"bg-green-label padding-05 text-white\">New</span><span class=\"text-underline text-primary\">Try this search in PMC Beta Search</span>\n</button>\n                \n            </div>\n        </div>\n    </div>\n</section>\n\n    \n\n\n        \n        \n\n       \n  <div class=\"usa-section padding-top-0 desktop:padding-top-6 pmc-article-section\" data-article-db=\"pmc\" data-article-id=\"10909160\">\n\n    \n\n   \n\n\n\n<div class=\"grid-container pmc-actions-bar is-not-intersecting\" aria-label=\"Actions bar\" role=\"complementary\">\n    <div class=\"grid-row\">\n        <div class=\"grid-col-fill display-flex\">\n             <div class=\"display-flex\">\n                <ul class=\"usa-list usa-list--unstyled usa-list--horizontal\">\n                    <li class=\"margin-right-2 mobile-lg:margin-right-4 display-flex mob\">\n                        <button type=\"button\" class=\"usa-button pmc-sidenav__container__open usa-button--unstyled width-auto display-flex\" aria-label=\"Open resources\" data-extra-class=\"is-visible-resources\" data-ga-category=\"resources_accordion\" data-ga-action=\"click\" data-ga-label=\"mobile_icon\">\n                            <svg class=\"usa-icon width-4 height-4\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                                <use xlink:href=\"/static/img/sprite.svg#more_vert\"></use>\n                            </svg>\n                        </button>\n                    </li>\n                    \n                    <li class=\"margin-right-2 mobile-lg:margin-right-4 display-flex mob\">\n                        <span class=\"usa-tooltip\"><a href=\"https://doi.org/10.7717/peerj-cs.1700\" class=\"usa-link display-flex usa-tooltip__trigger\" role=\"button\" target=\"_blank\" rel=\"noreferrer noopener\" data-position=\"bottom\" aria-label=\"View on publisher site\" data-ga-category=\"actions\" data-ga-action=\"click\" data-ga-label=\"publisher_link_mobile\" aria-describedby=\"tooltip-244843\" tabindex=\"0\">\n                                <svg class=\"usa-icon width-4 height-4\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                                    <use xlink:href=\"/static/img/sprite.svg#launch\"></use>\n                                </svg>\n                        </a><span class=\"usa-tooltip__body\" id=\"tooltip-244843\" role=\"tooltip\" aria-hidden=\"true\">View on publisher site</span></span>\n                    </li>\n                    \n                    \n                        <li class=\"margin-right-2 mobile-lg:margin-right-4 display-flex\">\n                             <span class=\"usa-tooltip\"><a href=\"pdf/peerj-cs-10-1700.pdf\" class=\"usa-link display-flex usa-tooltip__trigger\" role=\"button\" data-position=\"bottom\" aria-label=\"Download PDF\" data-ga-category=\"actions\" data-ga-action=\"click\" data-ga-label=\"pdf_download_mobile\" aria-describedby=\"tooltip-705526\" tabindex=\"0\">\n                                <svg class=\"usa-icon width-4 height-4\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                                    <use xlink:href=\"/static/img/sprite.svg#file_download\"></use>\n                                </svg>\n                            </a><span class=\"usa-tooltip__body\" id=\"tooltip-705526\" role=\"tooltip\" aria-hidden=\"true\">Download PDF</span></span>\n                        </li>\n                    \n                    <li class=\"margin-right-2 mobile-lg:margin-right-4 display-flex\">\n                        <span class=\"usa-tooltip\"><button class=\"usa-button usa-button--unstyled collections-dialog-trigger collections-button display-flex collections-button-empty usa-tooltip__trigger\" data-position=\"bottom\" aria-label=\"Save article in MyNCBI collections.\" data-ga-category=\"actions\" data-ga-action=\"click\" data-ga-label=\"collections_button_mobile\" data-collections-open-dialog-enabled=\"false\" data-collections-open-dialog-url=\"https://account.ncbi.nlm.nih.gov/?back_url=https%3A%2F%2Fpmc.ncbi.nlm.nih.gov%2Farticles%2FPMC10909160%2F%23open-collections-dialog\" data-in-collections=\"false\" aria-describedby=\"tooltip-548182\" tabindex=\"0\">\n                            <svg class=\"usa-icon width-4 height-4 usa-icon--bookmark-full\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                                <use xlink:href=\"/static/img/action-bookmark-full.svg#icon\"></use>\n                            </svg>\n                            <svg class=\"usa-icon width-4 height-4 usa-icon--bookmark-empty\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                                <use xlink:href=\"/static/img/action-bookmark-empty.svg#icon\"></use>\n                            </svg>\n                        </button><span class=\"usa-tooltip__body\" id=\"tooltip-548182\" role=\"tooltip\" aria-hidden=\"true\">Add to Collections</span></span>\n                    </li>\n                    \n                    <li class=\"margin-right-2 mobile-lg:margin-right-4 display-flex\">\n                        <span class=\"usa-tooltip\"><button role=\"button\" class=\"usa-button usa-button--unstyled citation-dialog-trigger display-flex usa-tooltip__trigger\" aria-label=\"Open dialog with citation text in different styles\" data-position=\"bottom\" data-ga-category=\"actions\" data-ga-action=\"open\" data-ga-label=\"cite_mobile\" data-all-citations-url=\"/resources/citations/10909160/\" data-citation-style=\"nlm\" data-download-format-link=\"/resources/citations/10909160/export/\" aria-describedby=\"tooltip-827364\" tabindex=\"0\">\n                            <svg class=\"usa-icon width-4 height-4 usa-icon--bookmark-empty\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                                <use xlink:href=\"/static/img/sprite.svg#format_quote\"></use>\n                            </svg>\n                        </button><span class=\"usa-tooltip__body\" id=\"tooltip-827364\" role=\"tooltip\" aria-hidden=\"true\">Cite</span></span>\n                    </li>\n                    \n                    <li class=\"pmc-permalink display-flex\">\n                         <span class=\"usa-tooltip\"><button type=\"button\" data-position=\"bottom\" class=\"usa-button usa-button--unstyled display-flex usa-tooltip__trigger\" aria-label=\"Show article permalink\" aria-expanded=\"false\" aria-haspopup=\"true\" data-ga-category=\"actions\" data-ga-action=\"open\" data-ga-label=\"permalink_mobile\" aria-describedby=\"tooltip-370270\" tabindex=\"0\">\n                            <svg class=\"usa-icon width-4 height-4\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                                <use xlink:href=\"/static/img/sprite.svg#share\"></use>\n                            </svg>\n                        </button><span class=\"usa-tooltip__body\" id=\"tooltip-370270\" role=\"tooltip\" aria-hidden=\"true\">Permalink</span></span>\n                        \n\n<div class=\"pmc-permalink__dropdown\" hidden=\"\">\n    <div class=\"pmc-permalink__dropdown__container\">\n          <h2 class=\"usa-modal__heading margin-top-0 margin-bottom-2 text-uppercase font-sans-xs\">PERMALINK</h2>\n          <div class=\"pmc-permalink__dropdown__content\">\n              <input type=\"text\" class=\"usa-input\" value=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/\" aria-label=\"Article permalink\">\n              <button class=\"usa-button display-inline-flex pmc-permalink__dropdown__copy__btn margin-right-0\" title=\"Copy article permalink\" data-ga-category=\"save_share\" data-ga-action=\"link\" data-ga-label=\"copy_link\" aria-expanded=\"false\">\n                  <svg class=\"usa-icon\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#content_copy\"></use>\n                  </svg>\n                  <span class=\"margin-left-1\">Copy</span>\n              </button>\n          </div>\n    </div>\n</div>\n                    </li>\n                </ul>\n            </div>\n            <button type=\"button\" class=\"usa-button pmc-sidenav__container__open usa-button--unstyled width-auto display-flex\" aria-label=\"Open article navigation\" data-extra-class=\"is-visible-in-page\" data-ga-category=\"actions\" data-ga-action=\"open\" data-ga-label=\"article_nav_mobile\">\n                <svg class=\"usa-icon width-4 height-4\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#list\"></use>\n                </svg>\n            </button>\n        </div>\n    </div>\n</div>\n    <div class=\"grid-container desktop:padding-left-6\">\n      <div id=\"article-container\" class=\"grid-row grid-gap\">\n        <div class=\"grid-col-12 desktop:grid-col-8 order-2 pmc-layout__content\">\n            <div class=\"grid-container padding-left-0 padding-right-0\">\n                <div class=\"grid-row desktop:margin-left-neg-6\">\n                    <div class=\"grid-col-12\">\n                        <div class=\"pmc-layout__disclaimer\" role=\"complementary\" aria-label=\"Disclaimer note\">\n    As a library, NLM provides access to scientific literature. Inclusion in an NLM database does not imply endorsement of, or agreement with,\n    the contents by NLM or the National Institutes of Health.<br>\n    Learn more:\n    <a class=\"usa-link\" data-ga-category=\"Link click\" data-ga-action=\"Disclaimer\" data-ga-label=\"New disclaimer box\" href=\"/about/disclaimer/\">PMC Disclaimer</a>\n    |\n    <a class=\"usa-link\" data-ga-category=\"Link click\" data-ga-action=\"PMC Copyright Notice\" data-ga-label=\"New disclaimer box\" href=\"/about/copyright/\">\n        PMC Copyright Notice\n    </a>\n</div>\n                    </div>\n                </div>\n                <div class=\"grid-row pmc-wm desktop:margin-left-neg-6\">\n                    <!-- Main content -->\n                    <main id=\"main-content\" class=\"usa-layout-docs__main usa-layout-docs grid-col-12 pmc-layout pmc-prose padding-0\">\n\n                      \n                        <section class=\"pmc-journal-banner text-center line-height-none\" aria-label=\"Journal banner\"><img src=\"https://cdn.ncbi.nlm.nih.gov/pmc/banners/logo-peerjcs.png\" alt=\"PeerJ Computer Science logo\" usemap=\"#pmc-banner-imagemap\" width=\"500\" height=\"75\"><map name=\"pmc-banner-imagemap\"><area alt=\"Link to PeerJ Computer Science\" title=\"Link to PeerJ Computer Science\" shape=\"default\" href=\"https://peerj.com/computer-science/\" target=\"_blank\" rel=\"noopener noreferrer\"></map></section><article lang=\"en\"><section aria-label=\"Article citation and metadata\"><section class=\"pmc-layout__citation font-secondary font-xs\"><div>\n<div class=\"display-inline-block\"><button type=\"button\" class=\"cursor-pointer text-no-underline bg-transparent border-0 padding-0 text-left margin-0 text-normal text-primary\" aria-controls=\"journal_context_menu\" aria-expanded=\"false\">PeerJ Comput Sci</button></div>. 2024 Jan 31;10:e1700. doi: <a href=\"https://doi.org/10.7717/peerj-cs.1700\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">10.7717/peerj-cs.1700</a>\n</div>\n<nav id=\"journal_context_menu\" hidden=\"hidden\"><ul class=\"menu-list font-family-ui\" role=\"menu\">\n<li role=\"presentation\"><a href=\"https://www.ncbi.nlm.nih.gov/pmc/?term=%22PeerJ%20Comput%20Sci%22%5Bjour%5D\" class=\"usa-link\" role=\"menuitem\">Search in PMC</a></li>\n<li role=\"presentation\"><a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22PeerJ%20Comput%20Sci%22%5Bjour%5D\" lang=\"en\" class=\"usa-link\" role=\"menuitem\">Search in PubMed</a></li>\n<li role=\"presentation\"><a href=\"https://www.ncbi.nlm.nih.gov/nlmcatalog?term=%22PeerJ%20Comput%20Sci%22%5BTitle%20Abbreviation%5D\" class=\"usa-link\" role=\"menuitem\">View in NLM Catalog</a></li>\n<li role=\"presentation\"><a href=\"?term=%22PeerJ%20Comput%20Sci%22%5Bjour%5D\" class=\"usa-link\" role=\"menuitem\" data-add-to-search=\"true\">Add to search</a></li>\n</ul></nav></section><section class=\"front-matter\"><div class=\"ameta p font-secondary font-xs\">\n<hgroup><h1>A systematic literature survey on recent trends in stock market prediction</h1></hgroup><div class=\"cg p\">\n<a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22Balasubramanian%20P%22%5BAuthor%5D\" class=\"usa-link\" aria-describedby=\"id1\" aria-expanded=\"false\"><span class=\"name western\">Prakash Balasubramanian</span></a><div hidden=\"hidden\" id=\"id1\">\n<h3><span class=\"name western\">Prakash Balasubramanian</span></h3>\n<div class=\"p\">\n<sup>1</sup>School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India</div>\n<div class=\"p\">Find articles by <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22Balasubramanian%20P%22%5BAuthor%5D\" class=\"usa-link\"><span class=\"name western\">Prakash Balasubramanian</span></a>\n</div>\n</div>\n<sup>1,</sup><sup>✉</sup>, <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22P%20C%22%5BAuthor%5D\" class=\"usa-link\" aria-describedby=\"id2\" aria-expanded=\"false\"><span class=\"name western\">Chinthan P</span></a><div hidden=\"hidden\" id=\"id2\">\n<h3><span class=\"name western\">Chinthan P</span></h3>\n<div class=\"p\">\n<sup>2</sup>School of Mechanical Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India</div>\n<div class=\"p\">Find articles by <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22P%20C%22%5BAuthor%5D\" class=\"usa-link\"><span class=\"name western\">Chinthan P</span></a>\n</div>\n</div>\n<sup>2</sup>, <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22Badarudeen%20S%22%5BAuthor%5D\" class=\"usa-link\" aria-describedby=\"id3\" aria-expanded=\"false\"><span class=\"name western\">Saleena Badarudeen</span></a><div hidden=\"hidden\" id=\"id3\">\n<h3><span class=\"name western\">Saleena Badarudeen</span></h3>\n<div class=\"p\">\n<sup>1</sup>School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India</div>\n<div class=\"p\">Find articles by <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22Badarudeen%20S%22%5BAuthor%5D\" class=\"usa-link\"><span class=\"name western\">Saleena Badarudeen</span></a>\n</div>\n</div>\n<sup>1</sup>, <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22Sriraman%20H%22%5BAuthor%5D\" class=\"usa-link\" aria-describedby=\"id4\" aria-expanded=\"false\"><span class=\"name western\">Harini Sriraman</span></a><div hidden=\"hidden\" id=\"id4\">\n<h3><span class=\"name western\">Harini Sriraman</span></h3>\n<div class=\"p\">\n<sup>1</sup>School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India</div>\n<div class=\"p\">Find articles by <a href=\"https://pubmed.ncbi.nlm.nih.gov/?term=%22Sriraman%20H%22%5BAuthor%5D\" class=\"usa-link\"><span class=\"name western\">Harini Sriraman</span></a>\n</div>\n</div>\n<sup>1</sup>\n</div>\n<div class=\"cg p\">Editor: <span class=\"name western\">Sándor Szénási</span>\n</div>\n<ul class=\"d-buttons inline-list\">\n<li><button class=\"d-button\" aria-controls=\"aip_a\" aria-expanded=\"false\">Author information</button></li>\n<li><button class=\"d-button\" aria-controls=\"anp_a\" aria-expanded=\"false\">Article notes</button></li>\n<li><button class=\"d-button\" aria-controls=\"clp_a\" aria-expanded=\"false\">Copyright and License information</button></li>\n</ul>\n<div class=\"d-panels font-secondary-light\">\n<div id=\"aip_a\" class=\"d-panel p\" style=\"display: none\">\n<div class=\"p\" id=\"aff-1\">\n<sup>1</sup>School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India</div>\n<div id=\"aff-2\">\n<sup>2</sup>School of Mechanical Engineering, Vellore Institute of Technology, Chennai, Tamil Nadu, India</div>\n<div class=\"author-notes p\"><div class=\"fn\" id=\"_fncrsp93pmc__\">\n<sup>✉</sup><p class=\"display-inline\">Corresponding author.</p>\n</div></div>\n</div>\n<div id=\"anp_a\" class=\"d-panel p\" style=\"display: none\"><div class=\"notes p\"><section id=\"historyarticle-meta1\" class=\"history\"><p>Received 2023 Jan 24; Accepted 2023 Oct 25; Collection date 2024.</p></section></div></div>\n<div id=\"clp_a\" class=\"d-panel p\" style=\"display: none\">\n<div>© 2024 Balasubramanian et al.</div>\n<p>This is an open access article distributed under the terms of the <a href=\"https://creativecommons.org/licenses/by/4.0/\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Creative Commons Attribution License</a>, which permits unrestricted use, distribution, reproduction and adaptation in any medium and for any purpose provided that it is properly attributed. For attribution, the original author(s), title, publication source (PeerJ Computer Science) and either DOI or URL of the article must be cited.</p>\n<div class=\"p\"><a href=\"/about/copyright/\" class=\"usa-link\">PMC Copyright notice</a></div>\n</div>\n</div>\n<div>PMCID: PMC10909160&nbsp;&nbsp;PMID: <a href=\"https://pubmed.ncbi.nlm.nih.gov/38435546/\" class=\"usa-link\">38435546</a>\n</div>\n</div></section></section><section aria-label=\"Article content\"><section class=\"body main-article-body\"><section class=\"abstract\" id=\"abstract1\"><h2 data-anchor-id=\"abstract1\"><a id=\"abstract1-anchor\" data-anchor-id=\"abstract1\" class=\"usa-anchor\"></a>Abstract</h2>\n<p>Prediction of the stock market is a challenging and time-consuming process. In recent times, various research analysts and organizations have used different tools and techniques to analyze and predict stock price movements. During the early days, investors mainly depend on technical indicators and fundamental parameters for short-term and long-term predictions, whereas nowadays many researchers started adopting artificial intelligence-based methodologies to predict stock price movements. In this article, an exhaustive literature study has been carried out to understand multiple techniques employed for prediction in the field of the financial market. As part of this study, more than hundreds of research articles focused on global indices and stock prices were collected and analyzed from multiple sources. Further, this study helps the researchers and investors to make a collective decision and choose the appropriate model for better profit and investment based on local and global market conditions.</p>\n<section id=\"kwd-group1\" class=\"kwd-group\"><p><strong>Keywords:</strong> Machine learning, Deep learning, Stock market prediction, Artificial intelligence</p></section></section><section id=\"sec1\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"sec1\"><a id=\"sec1-anchor\" data-anchor-id=\"sec1\" class=\"usa-anchor\"></a>Introduction</h2>\n<p>Researchers from a range of disciplines are conducting studies on stock market forecasting. Many financial experts have attempted to address the problem of forecasting the upward and downward movements of the stock market, but have had only sporadic success. It is now more feasible than ever before to do so with the technology’s rapid advancements in computation capability, storage capacity, and algorithm accuracy. For stock market forecasting, researchers have experimented with a variety of strategies, algorithms, and attribute combinations. The characteristic of a prediction model depends on market-specific variables. The stock market plays a critical role to the rapid economic expansion of developing countries such as India. If the stock market falls, it will impact the growth of the country results in negative economic growth (<a href=\"#ref-39\" class=\"usa-link\" aria-describedby=\"ref-39\" aria-expanded=\"false\">Pillai &amp; Al-Malkawi, 2018</a>). There is a common misperception regarding the stock market is that purchasing and selling shares is a kind of gambling. Due to this reason, majority of people hesitate to take part in investments.</p>\n<p>In earlier days, stock market predictions were majorly based on technical analysis and fundamental analysis. Technical analysis is used to predict the price movement of the stocks on short term basis. Candlesticks and chart patterns were predominantly utilized for technical analysis. There are many technical indicators like simple moving average (SMA), Bollinger band, Relative Strength Index (RSI), super trend <em>etc</em>., were exists but almost all these were considered as lagging indicators. Also, technical analysis does not take into account the fundamental aspects of equity’s key financial record. This is intuitively beneficial for making short-term investing decisions. Fundamental analysis majorly focused on the growth potential of the companies such as earnings, balance sheets, and revenues and so on. The investors compare the present growth of the company with the past earnings data and take their investment decision based on that. Fundamental analysis requires large amount of time and effort to analyze the past data due to which, fundamental analysis becomes least preferred choice among investors.</p>\n<p>In recent times, algorithmic and artificial intelligence (AI) based approaches become popular and widely accepted among the analyst for predicting the trend of stock market. In particular, several machine learning models and neural networks have been mainly utilized for stock market prediction due to their specific characteristics such as non-linearity, time series nature of data, no assumptions, and a data-driven approach. The fundamental idea behind the deep learning technique is to do computations using neural networks. Long-short-term memory (LSTM) is a form of recurrent neural network (RNN) developed to address the challenge of long-term dependency (<a href=\"#ref-1\" class=\"usa-link\" aria-describedby=\"ref-1\" aria-expanded=\"false\">Althelaya, El-Alfy &amp; Mohammed, 2018</a>). The most renowned and promising method includes the deployment of artificial neural network (ANN), and RNN, which are essentially machine learning implementations. Machine learning entails artificial intelligence, which enables the platform to improve and learn from prior experiences without having to be performed repeatedly (<a href=\"#ref-3\" class=\"usa-link\" aria-describedby=\"ref-3\" aria-expanded=\"false\">Budhani, Jha &amp; Budhani, 2012</a>). Conventional machine learning prediction approaches include algorithms such as backward propagation, often known as back propagation losses and many researchers are now employing more collective learning approaches. It would forecast future highs with modest price and time delays, but another framework would forecast future highs using delayed highs.</p>\n<p>Support vector machine (SVM) is a revolutionary neural network model that offers a potential result to the time series analysis. In contrast to so many classic neural networks that use the empirical risk minimization (ERM) concept, SVM uses structural risk minimization (SRM) principle, which seeks to lower the absolute limit of generalization infraction instead of training error (<a href=\"#ref-62\" class=\"usa-link\" aria-describedby=\"ref-62\" aria-expanded=\"false\">Yun, Yoon &amp; Won, 2021</a>). Following this hypothesis, the generalization deviation is constrained by the sum of both the training error and a confidence interval component which is dependent on the Vapnik-Chervonenkis (VC) dimension. This in turn makes SVM outperform other neural networks in terms of generalization performance, in accordance with this analysis. While comparable to classic neural networks, the effective use of SVM is dependent on the modeled data that having a certain degree of regularity. As a result, a basic SVM model would not be suitable for unstructured and complex time series financial data with changing dynamics. The modified SVM with a self-organizing feature map shows better results and outperforms simple SVM model in terms of prediction performance and convergence speed.</p>\n<p>Even today with the availability and support of highly sophisticated tools and resources, forecasting the stock market is still challenging and difficult process owing to its uniqueness, non-linearity, high data rates, and susceptible local and global economic factors (<a href=\"#ref-22\" class=\"usa-link\" aria-describedby=\"ref-22\" aria-expanded=\"false\">Maini &amp; Govinda, 2017</a>). Wavelet neural systems have been implemented in recent decades, blending the advantages of neural networks and wavelet transforms, such as neural network dynamical estimation, institutionalization learning, simple structure, and so on, to make it much more successful in connecting the stock price correlation. Cuckoo Search—WNN combines the benefits of dynamic systems and artificial intelligence capabilities to prevent structural design impairment and faults of easily dropping into the local optimal solution, enabling it to embrace high-frequency information with greater precision, and function in under time with a simple structure (<a href=\"#ref-60\" class=\"usa-link\" aria-describedby=\"ref-60\" aria-expanded=\"false\">Yang &amp; Suash, 2009</a>). WNN, on the other hand, suffers from the limitations of the starting value.</p>\n<p>With the advancement of technology, now-a-days investors are increasingly moving towards automated trading platforms known as algorithmic trading. Algorithmic trading in contrast with discretionary approach helps the analyst to quickly make wiser investing decisions. It may appear that matching the knowledge and integrity of an accomplished analyst who has been involved in the business for decades is an impossible endeavor, but despite the quantity of data accessible and digital transformations, it is quite feasible to develop algorithms that anticipate financial markets. Another method known as sentimental analysis is used for forecasting equity prices based on sentiments on social media feeds or news items, which aid in estimating the overall trend that a certain company’s or industry’s stocks may take focused on a collective opinion (<a href=\"#ref-63\" class=\"usa-link\" aria-describedby=\"ref-63\" aria-expanded=\"false\">Zhao et al., 2018</a>). Digital networks have now grown into a mirror that depicts people’s reactions to any particular incident or piece of news. Any positive or negative public perception of a business organization may have an influence on its stock value. To anticipate the stock market prices of numerous companies using sentiment analysis on social media data, including tweets regarding the company in question. People share their ideas and views about a certain issue, such as news, movies, events, and comments linked to products, <em>via</em> social networking such as Facebook, Twitter, <em>etc</em>. Business analysts and leading investment banks may utilize this information from social networks to get consumer input on their products and use it to enhance their planning, management, and product development strategies. The opinions and comments of users are extracted using sentiment analysis, which categorizes them as positive, negative, and natural sentiment. Though sentiment analysis has been given many different understandings in the literature, in simplest words, it is a method for extracting meaningful content based on an individual’s opinion from unprocessed internet data. The stock’s behavior over time is significantly influenced by news feeds as part of qualitative research. This further demonstrates the close relationship between media and stock market trends (<a href=\"#ref-31\" class=\"usa-link\" aria-describedby=\"ref-31\" aria-expanded=\"false\">Mohan et al., 2019</a>; <a href=\"#ref-5\" class=\"usa-link\" aria-describedby=\"ref-5\" aria-expanded=\"false\">Chen &amp; Chang, 2010</a>; <a href=\"#ref-6\" class=\"usa-link\" aria-describedby=\"ref-6\" aria-expanded=\"false\">Chen &amp; Tanuwijaya, 2011</a>). Twitter is undoubtedly the fastest and most trustworthy means to consume information, it can be asserted with certainty. Like Twitter, Yahoo Finance API is also utilized to fetch the data directly from exchanges and used it for train and tests the algorithm and provides predictions.</p>\n<p>The main objective of this study is to provide an understanding of research techniques and methodologies presently applied in the field of stock market prediction and analysis. This study helps the researchers, investment analyst, and market participants to a greater extent to choose the appropriate methods to predict the stock price movement so that they can take a better financial decision based on the suggestion provided by the chosen technique.</p>\n<p>This article is formulated as: “Introduction” provides a detailed introduction to stock market forecasting, “Research Method” focused on research methods and techniques used in stock market analysis, “Challenges and Discussion” details various challenges and discussion, “Conclusions” concludes the article, and finally “Future Scope” provides future direction.</p></section><section id=\"sec2\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"sec2\"><a id=\"sec2-anchor\" data-anchor-id=\"sec2\" class=\"usa-anchor\"></a>Research method</h2>\n<p>In the past, there are various techniques used among researchers to predict stock market price movements. Whereas in recent times, in addition to existing technical indicators and fundamental attributes, artificial intelligence (AI) based study becomes popular and widely accepted to predict stock price movements. The researchers and analysts employed different machine learning and deep learning models, neural networks, fuzzy logic systems, and sentimental analysis to understand the stock market price movements. There are many research articles, white articles, investment-related blogs, and websites available to assist investors to make wise decisions about their investment decisions.</p>\n<p>The main purpose of this research study is to find empirical evidence in the field of stock market analysis and predictions through existing literature study and statistical data. In this process, five key research questions (RQ1 through RQ5) are considered and this study helps to find the answers to those questions with the help of an exhaustive literature study. A total of over 300 research articles from various multiple sources such as Scopus database, Google scholar, Science Direct, IEEE, and Web of Science were considered. In addition, other formats of input data from social media handle such as Twitter and Facebook, and various authenticated financial websites like money control, Bloomberg, <em>etc</em>., were also been identified for the analysis.</p>\n<p>The research questions formulated as part of this study are as follows:</p>\n<p><strong>RQ1:</strong> What are the research techniques or methodologies employed in recent times to predict stock market movements?</p>\n<p><strong>RQ2:</strong> What are the different sources of datasets considered for the stock market predictions?</p>\n<p><strong>RQ3:</strong> What are the most popular journal publishers available in the domain of stock market investments?</p>\n<p><strong>RQ4:</strong> What countries show research interests in equity/capital market investments?</p>\n<p><strong>RQ5:</strong> What is the most popular evaluation metrics used in a stock market analysis?</p>\n<section id=\"sec3\"><h3 class=\"pmc_sec_title\">Stock market prediction using machine learning techniques</h3>\n<p><a href=\"#ref-54\" class=\"usa-link\" aria-describedby=\"ref-54\" aria-expanded=\"false\">Usmani et al. (2016)</a> used a combination of attributes and Artificial Neural Networks to foresee the stock market volatility of KSE. This study utilizes artificial neural networks such as single and multi-layer perceptron, radial base function (RBF), and SVM for their analysis. This study is based on parameters such as commodity prices, gold and silver prices, market history, news, global currency rates, <em>etc</em>. The news and Twitter feed were given as inputs and were processed to give the outcome as positive and negative. This study concludes that the multi-Layer perceptron algorithm outperformed the other algorithms and also derived that petrol price played the most significant role in the evaluation of the performance of KSE and that the foreign exchange had no effect on the KSE performance.</p>\n<p>A survey has been conducted on efficient regression models in predicting stock market prices based on historical data by <a href=\"#ref-46\" class=\"usa-link\" aria-describedby=\"ref-46\" aria-expanded=\"false\">Sharma, Bhuriya &amp; Singh (2017)</a>. Different regression techniques like polynomial regression, RBF regression, sigmoid regression, and logistic regression (LR) were selected for the survey. It is concluded that a higher range of variables might improve the multiple regression analysis.</p>\n<p><a href=\"#ref-19\" class=\"usa-link\" aria-describedby=\"ref-19\" aria-expanded=\"false\">Kumar et al. (2018)</a> performed stock price prediction with supervised learning procedures like SVM, KNN, Naïve Bayes, random forest (RF), and SoftMax. In this study several technical indicators were integrated with machine learning models. Moving averages for 10 and 50 days were evaluated for feature extraction, the Relative Strength Index (RSI), which indicates if the stock is overvalued or oversold, the rate of change (RoC) to measure the price change from one timeframe to the next, volatility to indicate the scattering of returns for a given firm, the Disparity Index (DI) to measure the relative strength of a selected moving average to the most recent closing price, the stochastic oscillator to outline the position of the trading session to the relative high-low range, momentum indicator Williams % R to outline the level of final closing price relative to the highest point, and volume price trend and Commodity Channel Index (CCI) are calculated to determine the current price level in relation to the median price over a specific time period. The analysis was performed on the past 5–10 years of historical data for Amazon, Bata, Bosch, Cipla, and Eicher motor. The performance of the models was appraised based on evaluation metrics like Accuracy, precision, recall and F-measure. The analysis indicates that for huge data sets, such as Amazon, Bata, and Bosch, the RF topped the other models with respect to accuracy, but for smaller datasets, such as Cipla and Eicher, the Naïve Bayes approach produced the greatest performance in terms of accuracy. The study concludes that by limiting the number of statistical features, the efficiency of the algorithms in predicting stock market movement decreases.</p>\n<p>A similar organized literature survey was conducted by <a href=\"#ref-26\" class=\"usa-link\" aria-describedby=\"ref-26\" aria-expanded=\"false\">Mankar et al. (2018)</a> along with his team, through social sentiments from Twitter. Naïve Bayes and SVM were selected for this classification examination. As part of preprocessing, this study applies Python’s Natural Language Toolkit (NLTK) to compute conditional recurrence and characteristic frequency. It was determined that SVM was the most efficient and viable method for anticipating stock market movements using social sentiments.</p>\n<p>A framework is proposed by <a href=\"#ref-44\" class=\"usa-link\" aria-describedby=\"ref-44\" aria-expanded=\"false\">Sadia et al. (2019)</a> to predict the stock market prices based on historical data. This study utilizes an RF classifier, SVM classifier, and RF algorithm for their analysis. In addition, a confusion matrix has been constructed for the assessment of the models’ performance. Upon measuring the accuracy, it was concluded that the RF algorithm is most suitable for the stock market prediction based on various data points from the historical stock data. <a href=\"#ref-18\" class=\"usa-link\" aria-describedby=\"ref-18\" aria-expanded=\"false\">Kompella, Chilukuri &amp; Kalyana (2019)</a> evaluates the effectiveness of random forest for predicting stock prices with logistic regression based on sentiment analysis. Historical stock data and news headings are given as inputs. The polarity score is calculated using sentiment analysis. Further, several error metrics such as variance score, Mean Absolute Error (MAE), Mean Squared Error (MSE), and Mean Squared Log Error (MSLE) was used to quantify the effectiveness of the algorithm. It is ascertained that the RF algorithm outperforms logistic regression for forecasting the stock market on sentiment classification.</p>\n<p>To forecast the equities listed on the NSE and NYSE, <a href=\"#ref-11\" class=\"usa-link\" aria-describedby=\"ref-11\" aria-expanded=\"false\">Hiransha et al. (2018)</a> employed deep learning models. This study found that neural network models excelled linear models, in particular ARIMA, and was based on the forecast of five stock prices mentioned in the two indexes. Five NYSE-listed big capitalization stocks were chosen for the <a href=\"#ref-55\" class=\"usa-link\" aria-describedby=\"ref-55\" aria-expanded=\"false\">Vijh et al. (2020)</a>, study’s (<a href=\"#ref-55\" class=\"usa-link\" aria-describedby=\"ref-55\" aria-expanded=\"false\">Vijh et al., 2020</a>) closing price prediction analysis. They employed machine learning approaches such as ANN and RF for their analysis. The performance of the models was reviewed using assessment instruments including mean absolute percentage error (MAPE) and root mean square error (RMSE). According to the results obtained, ANN outperformed RF in terms of stock value prediction accuracy.</p>\n<p>In order to forecast a stock market’s future trend based on specific external contributing variables like news and social media posts, <a href=\"#ref-16\" class=\"usa-link\" aria-describedby=\"ref-16\" aria-expanded=\"false\">Khan et al. (2020)</a> established a framework. According to the study findings, the accuracy of stock forecasts is positively impacted by pre-processing stages like the elimination of spam tweets and feature selection. <a href=\"#ref-42\" class=\"usa-link\" aria-describedby=\"ref-42\" aria-expanded=\"false\">Rao, Srinivas &amp; Mohan (2020)</a> conducted another study to analyze stock movement based on comparing several methodologies with their benefits and limitations. The evaluation and comparison of eight supervised machine learning models were used for forecasting the stocks in the Nifty 50 index (<a href=\"#ref-48\" class=\"usa-link\" aria-describedby=\"ref-48\" aria-expanded=\"false\">Singh, 2022</a>). Based on historical data from the previous 25 years, the study was carried out. The study demonstrated that linear regression outperformed neural networks because it handles linear dependence data better than SVM and gradient descent. By using a linear regression model with three-month moving averages and exponential smoothing forecasts, the NYSE stock price movements were examined (<a href=\"#ref-53\" class=\"usa-link\" aria-describedby=\"ref-53\" aria-expanded=\"false\">Umer, Awais &amp; Muzammul, 2019</a>). The outcome demonstrated that forecasts using exponential smoothing outperformed those using linear regression and three-month moving averages.</p>\n<p>A review of stock price movement was conducted by <a href=\"#ref-49\" class=\"usa-link\" aria-describedby=\"ref-49\" aria-expanded=\"false\">Soni, Tewari &amp; Krishnan (2022)</a> by examining numerous machine learning algorithms. For the comparison study, the various types of methodologies, including standard machine learning (ML) techniques, deep learning models, neural networks, time series analysis, and graph-based approaches, were chosen. <a href=\"#ref-43\" class=\"usa-link\" aria-describedby=\"ref-43\" aria-expanded=\"false\">Rouf et al. (2021)</a> undertook a further comparison of stock market forecasts based on research done in the previous 10 years. The types of data used as input, various pre-processing techniques, the machine learning and deep learning models used for predictions were all taken into consideration throughout the analysis. SVM is the better performing machine learning model for stock market analysis, according to the study’s findings. Additionally, other approaches, such as DNN and ANN, offer quicker and more precise projections of stock prices.</p>\n<p>An analysis of the use of several ML algorithms useful in forecasting the future values of equities in the financial sectors has been performed (<a href=\"#ref-32\" class=\"usa-link\" aria-describedby=\"ref-32\" aria-expanded=\"false\">Obthong et al., 2020</a>). For the purpose of forecasting stock price fluctuations, <a href=\"#ref-37\" class=\"usa-link\" aria-describedby=\"ref-37\" aria-expanded=\"false\">Parmar et al. (2018)</a> constructed two models: regression and LSTM. According to the results, LSTM outperformed regression in terms of prediction accuracy. Similar to this, <a href=\"#ref-38\" class=\"usa-link\" aria-describedby=\"ref-38\" aria-expanded=\"false\">Pathak &amp; Pathak (2020)</a> examined four machine learning models for stock market prediction: RF, SVM, KNN, and LR. The study’s findings indicated that random forest outperforms the other algorithms in terms of accuracy, precision, sensitivity (recall), and F-score (F1-score). <a href=\"#ref-21\" class=\"usa-link\" aria-describedby=\"ref-21\" aria-expanded=\"false\">Lokesh et al. (2018)</a> combined sentiment analysis and machine learning algorithms to determine the trend of a particular stock. In addition, based on the derived results, the risk exposure towards the particular company has been determined and notified to the user. <a href=\"#ref-29\" class=\"usa-link\" aria-describedby=\"ref-29\" aria-expanded=\"false\">Mehta, Pandya &amp; Kotecha (2021)</a> integrated sentiment analysis with deep learning models to enhance the prediction accuracy of the stock market. The results indicate that the combination of deep learning models along with sentiment analysis has a positive impact on stock price movement predictions.</p>\n<p>Further on the topic of stock market analysis, <a href=\"#ref-50\" class=\"usa-link\" aria-describedby=\"ref-50\" aria-expanded=\"false\">Strader et al. (2020)</a> performed a systematic literature review (SLR) on four categories: artificial neural networks, support vector machines, genetic algorithms, and other hybrid approaches. This study comes to the conclusion that hybrid approaches are useful in overcoming the shortcomings of a single method, genetic algorithms are used to suggest suitable stocks for a portfolio, artificial neural networks are suitable for stock value index predictions, SVM is useful for forecasting overall trend of indices, and genetic algorithms are used to suggest the suitable stocks in a portfolio. The research articles on stock market forecasting using machine learning approaches are described in <a href=\"#table-1\" class=\"usa-link\">Table 1</a>.</p>\n<section class=\"tw xbox font-sm\" id=\"table-1\"><h4 class=\"obj_head\">Table 1. Stock market prediction using ML techniques.</h4>\n<div class=\"tbl-box p\" tabindex=\"0\"><table class=\"content\" frame=\"hsides\" rules=\"groups\">\n<colgroup span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n</colgroup>\n<thead><tr>\n<th rowspan=\"1\" colspan=\"1\">Authors</th>\n<th rowspan=\"1\" colspan=\"1\">Scope</th>\n<th rowspan=\"1\" colspan=\"1\">Input features</th>\n<th rowspan=\"1\" colspan=\"1\">Feature extraction</th>\n<th rowspan=\"1\" colspan=\"1\">Prediction algorithm</th>\n</tr></thead>\n<tbody>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-54\" class=\"usa-link\" aria-describedby=\"ref-54\" aria-expanded=\"false\">Usmani et al. (2016)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">KSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">SLP/MLP/RBF/SVM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-46\" class=\"usa-link\" aria-describedby=\"ref-46\" aria-expanded=\"false\">Sharma, Bhuriya &amp; Singh (2017)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">PR/RBF/Sigmoid/LR regression</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-19\" class=\"usa-link\" aria-describedby=\"ref-19\" aria-expanded=\"false\">Kumar et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">SVM/RF/KNN/NB/SoftMax</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-26\" class=\"usa-link\" aria-describedby=\"ref-26\" aria-expanded=\"false\">Mankar et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Tweet text</td>\n<td rowspan=\"1\" colspan=\"1\">Chi square test</td>\n<td rowspan=\"1\" colspan=\"1\">NB/SVM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-44\" class=\"usa-link\" aria-describedby=\"ref-44\" aria-expanded=\"false\">Sadia et al. (2019)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Scaled raw data</td>\n<td rowspan=\"1\" colspan=\"1\">RF classifier/SVM classifier/SVM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-18\" class=\"usa-link\" aria-describedby=\"ref-18\" aria-expanded=\"false\">Kompella, Chilukuri &amp; Kalyana (2019)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price Data &amp; News</td>\n<td rowspan=\"1\" colspan=\"1\">Smoothing (polarity score)</td>\n<td rowspan=\"1\" colspan=\"1\">RF algorithm</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-11\" class=\"usa-link\" aria-describedby=\"ref-11\" aria-expanded=\"false\">Hiransha et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE &amp;<br>NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">MLP/RNN/LSTM/CNN</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-55\" class=\"usa-link\" aria-describedby=\"ref-55\" aria-expanded=\"false\">Vijh et al. (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">ANN/RF</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-16\" class=\"usa-link\" aria-describedby=\"ref-16\" aria-expanded=\"false\">Khan et al. (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">KSE, LSE,<br>NASDAQ &amp; NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data, Tweet Text &amp; news</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">RF/ET/GBM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-42\" class=\"usa-link\" aria-describedby=\"ref-42\" aria-expanded=\"false\">Rao, Srinivas &amp; Mohan (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">Holt-Winters/ANN/HMM/ARIMA/RNN</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-48\" class=\"usa-link\" aria-describedby=\"ref-48\" aria-expanded=\"false\">Singh (2022)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Scaled raw data</td>\n<td rowspan=\"1\" colspan=\"1\">ANN/LR/SGD/SVM/AdaBoost/RF/KNN/DT</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-53\" class=\"usa-link\" aria-describedby=\"ref-53\" aria-expanded=\"false\">Umer, Awais &amp; Muzammul (2019)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">LR/3MMA/ES</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-49\" class=\"usa-link\" aria-describedby=\"ref-49\" aria-expanded=\"false\">Soni, Tewari &amp; Krishnan (2022)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Binary features</td>\n<td rowspan=\"1\" colspan=\"1\">PLS Classifier/SMO/ExtRa/ LSTM/ CNN/ ARIMA/GAM using Fourier transformations</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-43\" class=\"usa-link\" aria-describedby=\"ref-43\" aria-expanded=\"false\">Rouf et al. (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data &amp; Tweet text</td>\n<td rowspan=\"1\" colspan=\"1\">Aspect based correlation</td>\n<td rowspan=\"1\" colspan=\"1\">ANN/SVM/NB/GA/FA/DNN/RA/HA</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-32\" class=\"usa-link\" aria-describedby=\"ref-32\" aria-expanded=\"false\">Obthong et al. (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price Data</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">KMeans/SOM/RF/MLP/LSTM/RNN/GA/SVR/MCS/ANN/CART/GP/BSM/GRNN/RBF/BPNN/LR/HMM/SVM/KNN/LR</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-37\" class=\"usa-link\" aria-describedby=\"ref-37\" aria-expanded=\"false\">Parmar et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">Regression/LSTM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-38\" class=\"usa-link\" aria-describedby=\"ref-38\" aria-expanded=\"false\">Pathak &amp; Pathak (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">RF/SVM/KNN/LR</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-21\" class=\"usa-link\" aria-describedby=\"ref-21\" aria-expanded=\"false\">Lokesh et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data, Tweet text</td>\n<td rowspan=\"1\" colspan=\"1\">Toordinal feature extraction</td>\n<td rowspan=\"1\" colspan=\"1\">Machine learning models</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-29\" class=\"usa-link\" aria-describedby=\"ref-29\" aria-expanded=\"false\">Mehta, Pandya &amp; Kotecha (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data, News</td>\n<td rowspan=\"1\" colspan=\"1\">Polarity, Stemming</td>\n<td rowspan=\"1\" colspan=\"1\">LSTM</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"p text-right font-secondary\"><a href=\"table/table-1/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></section></section><section id=\"sec4\"><h3 class=\"pmc_sec_title\">Stock market prediction using neural networks</h3>\n<p><a href=\"#ref-33\" class=\"usa-link\" aria-describedby=\"ref-33\" aria-expanded=\"false\">Olivier (2007)</a> provided an outline of the modeling method using ANN for anticipating stock market prices. This study also addressed the challenges experienced in using neural networks for predicting future stock market changes. A similar study was done by <a href=\"#ref-12\" class=\"usa-link\" aria-describedby=\"ref-12\" aria-expanded=\"false\">Honghai &amp; Haifei (2012)</a>, in which they devised a two-stage neural network for stock market prediction by integrating SVM with empirical mode decomposition. The experimental findings suggest that the integrated model outperforms the simple SVM in terms of prediction performance. <a href=\"#ref-59\" class=\"usa-link\" aria-describedby=\"ref-59\" aria-expanded=\"false\">White (1988)</a> forecasted the IBM daily common stock price using three layers of a feedforward neural network, one input, one hidden layer, and one output layer. The team employed a 5,000-day dataset to perform their analysis. The first 1,000 days of data were utilized for training, while the remaining days were used for testing. The neural network’s performance was unsatisfactory, but they offered useful information for integrating neural networks in forecasting the stock market.</p>\n<p>A neural network model was used to forecast the closing level of the Indian S&amp;P CNX Nifty 50 Index (<a href=\"#ref-23\" class=\"usa-link\" aria-describedby=\"ref-23\" aria-expanded=\"false\">Majumder &amp; Hussian, 2008</a>). The study analyzed 10-year data sets of the S&amp;P CNX Nifty 50 Index final price from January 1, 2000, to December 31, 2009. Four of the 10 years of data were utilized for validation. The authors present an ideal ANN structure, which is a three-layer feedforward hybrid backpropagation neural network with ten input neurons, a hidden layer of five neurons, and one output neuron. In their forecasts, they had the best performance of 89.65% and the lowest precision of 69.72%.</p>\n<p>The authors <a href=\"#ref-27\" class=\"usa-link\" aria-describedby=\"ref-27\" aria-expanded=\"false\">Mehrara et al. (2010)</a> have used moving average indicator to compare the performance of MLP feedforward with backpropagation and group method of data handling (GDMH) with GA in forecasting the stock price index of Tehran Stock Exchange (TEPIX). The results revealed that GDMH with GA outperformed MLFF with a backpropagation network.</p>\n<p>A comparative analysis on the Dow Jones Industrial Average utilizing three methods (MLP, adaptive neuro-fuzzy inference, and generic evolving and pruning RBF neural network) were performed in the study (<a href=\"#ref-40\" class=\"usa-link\" aria-describedby=\"ref-40\" aria-expanded=\"false\">Quah, 2007</a>). The study examined 10 years of information from 1995 to 2004 for 1630 Dow Jones Industrial Average shares. The authors in this study (<a href=\"#ref-25\" class=\"usa-link\" aria-describedby=\"ref-25\" aria-expanded=\"false\">Mandziuk &amp; Jaruszewicz, 2007</a>) implemented a neuro-evolutionary neural network with GA to forecast the short-term stock index of GSE. The study data set included the GSE (DAX), TSE (NIKKEI 225), NYSE (DJIA), and EUR/USD and USD/JPY currency exchange for a 15 years period. Their findings revealed that the neuro-evolutionary technique outperformed alternative testing models. Other neural network architectures, in addition to the feedforward neural network, have been used in stock market prediction. RNN is another type of neural network design in which the network connections form a guided cycle. The result concluded RNN has several internal states that displays dynamic temporal patterns.</p>\n<p><a href=\"#ref-45\" class=\"usa-link\" aria-describedby=\"ref-45\" aria-expanded=\"false\">Schierholt &amp; Dagli (1996)</a> employed MLP and a probabilistic neural network to forecast the S&amp;P 500 index. From February 1994 to September 1995, the data set included the daily closing S&amp;P 500 index as well as foreign exchange rates for the Yen, Pound, and Mark. The results indicated that the probabilistic neural network outperformed the MLP.</p>\n<p>Another study that achieves something similar is <a href=\"#ref-4\" class=\"usa-link\" aria-describedby=\"ref-4\" aria-expanded=\"false\">Charkha (2008)</a>, where they forecast both the pattern and validity of stock prices using a feed-forward neural network and a radial basis neural network with backpropagation. They retrieved data from the NSE since November 2005 and the study revealed that the feed-forward neural network with backpropagation is advantageous for trend prediction, with almost 100% accuracy as compared to the radial basis neural network’s 80% accuracy. Furthermore, the radial basis neural network outperformed the feed-forward neural network in stock price prediction, gaining a greater percentage of accuracy. In their stock market prediction in the trading study, <a href=\"#ref-30\" class=\"usa-link\" aria-describedby=\"ref-30\" aria-expanded=\"false\">Mizuno et al. (1998)</a> used neural networks. They forecast the Tokyo stock market, and the approach for doing so is more accurate than 63% of genetic algorithms. The forecasting procedure for changes in the index stock market is handled by a combination of neural networks and genetic algorithms.</p>\n<p>A performance comparison of ANN and SVM was carried out in 2011 by <a href=\"#ref-15\" class=\"usa-link\" aria-describedby=\"ref-15\" aria-expanded=\"false\">Kara, Acar Boyacioglu &amp; Baykan (2011)</a>. These two classifiers received ten technical indications in order to forecast the movements of the National 100 Index of the ISE. Researchers discovered that ANNs’ predictive power is noticeably superior to SVM. Popular ANNs that can forecast both price movement direction and price value include feed-forward ANNs. The studies on stock market prediction utilizing neural networks are shown in <a href=\"#table-2\" class=\"usa-link\">Table 2</a>.</p>\n<section class=\"tw xbox font-sm\" id=\"table-2\"><h4 class=\"obj_head\">Table 2. Stock market prediction using neural networks.</h4>\n<div class=\"tbl-box p\" tabindex=\"0\"><table class=\"content\" frame=\"hsides\" rules=\"groups\">\n<colgroup span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n</colgroup>\n<thead><tr>\n<th rowspan=\"1\" colspan=\"1\">Authors</th>\n<th rowspan=\"1\" colspan=\"1\">Scope</th>\n<th rowspan=\"1\" colspan=\"1\">Input features</th>\n<th rowspan=\"1\" colspan=\"1\">Feature extraction</th>\n<th rowspan=\"1\" colspan=\"1\">Prediction algorithm</th>\n</tr></thead>\n<tbody>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-33\" class=\"usa-link\" aria-describedby=\"ref-33\" aria-expanded=\"false\">Olivier (2007)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">ANN</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-12\" class=\"usa-link\" aria-describedby=\"ref-12\" aria-expanded=\"false\">Honghai &amp; Haifei (2012)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Decomposed intrinsic mode functions (IMFs)</td>\n<td rowspan=\"1\" colspan=\"1\">Combined SVM and EMD</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-59\" class=\"usa-link\" aria-describedby=\"ref-59\" aria-expanded=\"false\">White (1988)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">IBM common stock</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">Neural network modelling</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-40\" class=\"usa-link\" aria-describedby=\"ref-40\" aria-expanded=\"false\">Quah (2007)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NYSE &amp; NASDAQ</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">MLP/ANFIS &amp; GGAP-RBF</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-25\" class=\"usa-link\" aria-describedby=\"ref-25\" aria-expanded=\"false\">Mandziuk &amp; Jaruszewicz (2007)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">GSE, NYSE &amp; TSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">Neural networks and GA</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-45\" class=\"usa-link\" aria-describedby=\"ref-45\" aria-expanded=\"false\">Schierholt &amp; Dagli (1996)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">Probabilistic neural network (Custom Model)</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-4\" class=\"usa-link\" aria-describedby=\"ref-4\" aria-expanded=\"false\">Charkha (2008)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">Feed forward network with back propagation/radial basis network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-30\" class=\"usa-link\" aria-describedby=\"ref-30\" aria-expanded=\"false\">Mizuno et al. (1998)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">TSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">Custom neural network model</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-15\" class=\"usa-link\" aria-describedby=\"ref-15\" aria-expanded=\"false\">Kara, Acar Boyacioglu &amp; Baykan (2011)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">ISE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">ANN/SVM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-65\" class=\"usa-link\" aria-describedby=\"ref-65\" aria-expanded=\"false\">Zhi et al. (2017)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">CSWNN/WNN</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"p text-right font-secondary\"><a href=\"table/table-2/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></section><p><a href=\"#ref-65\" class=\"usa-link\" aria-describedby=\"ref-65\" aria-expanded=\"false\">Zhi et al. (2017)</a> developed a network to foresee stock market activities and predict stock prices by optimizing the basic WNN parameters. To improve the initial parameters, this work uses a brand-new meta-heuristic technique called Cuckoo Search. The outcome of the trials demonstrates that in terms of the degree of fitting and prediction accuracy, CS-WNN is superior to WNN.</p></section><section id=\"sec5\"><h3 class=\"pmc_sec_title\">Stock market prediction using sentiment analysis</h3>\n<p><a href=\"#ref-9\" class=\"usa-link\" aria-describedby=\"ref-9\" aria-expanded=\"false\">Gao et al. (2022)</a>, looked into the sentiment analysis-based prediction and proposed a framework with varying weights to boost prediction performance of the model. In sentiment analysis, news articles’ sentiment scores were determined using the popular Loughran-McDonald sentiment dictionary. The sentiment index for each news source was then calculated by integrating those sentiment scores. The RNN was then used to establish a series of basis classifiers depending on the market data and sentiment indices from various news publishers, and the evidential reasoning rule was used to integrate these base classifiers to predict the movement of the stock market index.</p>\n<p><a href=\"#ref-8\" class=\"usa-link\" aria-describedby=\"ref-8\" aria-expanded=\"false\">Duan, Liu &amp; Wang (2021)</a> quantifies the Chinese stock industry’s reactivity related to novel coronavirus 2019 (COVID-19). Using 6.3 million textual data pieces acquired from the government publications media and Chinese Social media blog sites, they built two COVID-19 sentiment indexes that reflect the feelings associated with COVID-19. Their stock market sentiment indicators are real-time, forward-looking indexes. They discovered that COVID-19 attitudes predicted stock market volatility and pay scales accurately.</p>\n<p>Based on the partial least squares technique, <a href=\"#ref-10\" class=\"usa-link\" aria-describedby=\"ref-10\" aria-expanded=\"false\">Gong et al. (2022)</a> suggest a new investor sentiment index (NISI). In three different methods, their sentimental analysis performs better than many other sentiment indicators now in use. First, the in-sample results demonstrate that the NISI has a higher level of predictive ability than the others. While the NISI is also beneficial during times of crisis, most mood indicators only demonstrate predictability during non-crisis periods. Additionally, the NISI shows a more obvious advantage in predicting over longer time horizons. Second, additional research reveals that, in contrast to the others, the NISI shows strong predictability before and during moments of market turmoil in China. In contrast to most of the others, the NISI is still considered effective despite taking leverage impact into account. Lastly, out-of-sample research shows that the NISI outperforms other sentiment metrics.</p>\n<p><a href=\"#ref-24\" class=\"usa-link\" aria-describedby=\"ref-24\" aria-expanded=\"false\">Malawana &amp; Rathnayaka (2020)</a> used a machine learning approach to do analysis and data processing utilizing the Spark model on the Google cloud platform. Logistic regression and Naive Bayes were effective in categorizing emotional reactions. The study’s key finding was that public perception has a significant influence on how economic forces and economic variables such as rate of interest, public trust, and faith in the bond market operate. Monetarism, political changes, unanticipated pandemics, and interest rates are some of these influences. In <a href=\"#ref-17\" class=\"usa-link\" aria-describedby=\"ref-17\" aria-expanded=\"false\">Khatri &amp; Srivastava (2016)</a>, sentimental analysis was done on the data that was taken from Stock Twits and Twitter. To determine the user’s comment’s mood, the data was evaluated. Four categories were used to group these comments: joyful, up, down, and rejected. An artificial neural network was given the polarity index and market data to forecast the outcomes.</p>\n<p><a href=\"#ref-28\" class=\"usa-link\" aria-describedby=\"ref-28\" aria-expanded=\"false\">Mehta, Malhar &amp; Shankarmani (2021)</a> concentrate on many approaches to studying the stock market’s patterns in real time. The approach with the highest reliability is the best and even more recommended method of projection. The authors used three distinct models for their work, as well as sentiment classification on tweets concerning the firm or commodity. The classification’s findings have provided clear and incisive insight into the market’s volatile movements as well as a fresh strategy for investors to use when deciding where to stake their capital. For every stock, the ARIMA model had the best accuracy.</p>\n<p><a href=\"#ref-14\" class=\"usa-link\" aria-describedby=\"ref-14\" aria-expanded=\"false\">Jing, Wu &amp; Wang (2021)</a> suggest a hybrid model for stock price prediction that combines a deep learning approach with a sentiment analysis model. They used CNN model to categorize the hidden sentiments of investors that they extract from a significant stock forum. Then, using the LSTM neural network technique to analyze the stock market’s technical indicators and the sentiment analysis findings from the first stage, they suggest a hybrid research model. In order to confirm the efficacy and applicability of the suggested model, this study has also carried out real-world tests from six important sectors across three-time intervals on the Shanghai Stock Exchange (SSE).</p>\n<p>The current digital world has altered the way we conduct our business, owing primarily to web technologies such as big data analytics, cloud computing, and sentiment classification. Sentiment analysis, also known as opinion mining, uses text mining and natural language processing (NLP) to analyze user opinions, assessments, sentiments, and attitudes, and to discover and extract sensory knowledge through emotions. This (<a href=\"#ref-2\" class=\"usa-link\" aria-describedby=\"ref-2\" aria-expanded=\"false\">Bhardwaj et al., 2015</a>) study investigated the importance of sentiment classification for stock market indices such as the Sensex and Nifty in forecasting stock prices on the Indian stock exchange.</p>\n<p>Sentiment analysis was utilized by <a href=\"#ref-41\" class=\"usa-link\" aria-describedby=\"ref-41\" aria-expanded=\"false\">Rajendiran &amp; Priyadarsini (2021)</a> to compare several conventional stock market forecast algorithms. As a result, the reliability of the revised stock market categorization model was not increased. The survival analysis demonstrated that the prediction outcome was not enhanced by the emotional analysis approach. The performance of stock market forecasts was computed using a wide variety of validation using traditional methodologies. Finally, a variety of machine learning and classification techniques were to enhance stock market predictive ability with the highest level of accuracy in the shortest amount of time.</p>\n<p><a href=\"#ref-34\" class=\"usa-link\" aria-describedby=\"ref-34\" aria-expanded=\"false\">Owen &amp; Oktariani (2020)</a> assess the idea of utilizing past data and sentiment evaluations derived from microblog language data to improve stock market prediction accuracy. The sentiment score is extracted using an ensemble-based approach that leverages the capability of CNN, MLP, and LSTM. They provide the SENN model, which is trained by analyzing sentiment in text data from StockTwits microblogs and historical Boeing stock data. Furthermore, they offer a one-of-a-kind approach for assessing the performance of stock market forecasting, namely adjusted MAPE (AMAPE), a variant of the traditional mean absolute percentage error (MAPE) metric. Further, <a href=\"#table-3\" class=\"usa-link\">Table 3</a> summarizes the findings on sentiment analysis-based stock market forecasting.</p>\n<section class=\"tw xbox font-sm\" id=\"table-3\"><h4 class=\"obj_head\">Table 3. Stock market prediction using sentiment analysis.</h4>\n<div class=\"tbl-box p\" tabindex=\"0\"><table class=\"content\" frame=\"hsides\" rules=\"groups\">\n<colgroup span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n</colgroup>\n<thead><tr>\n<th rowspan=\"1\" colspan=\"1\">Authors</th>\n<th rowspan=\"1\" colspan=\"1\">Scope</th>\n<th rowspan=\"1\" colspan=\"1\">Input features</th>\n<th rowspan=\"1\" colspan=\"1\">Feature extraction</th>\n<th rowspan=\"1\" colspan=\"1\">Prediction algorithm</th>\n</tr></thead>\n<tbody>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-9\" class=\"usa-link\" aria-describedby=\"ref-9\" aria-expanded=\"false\">Gao et al. (2022)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data &amp; Financial news</td>\n<td rowspan=\"1\" colspan=\"1\">Sentiment polarity using LMD dictionary</td>\n<td rowspan=\"1\" colspan=\"1\">RNN-ER-GA</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-8\" class=\"usa-link\" aria-describedby=\"ref-8\" aria-expanded=\"false\">Duan, Liu &amp; Wang (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Financial News &amp; text</td>\n<td rowspan=\"1\" colspan=\"1\">Scaling raw data</td>\n<td rowspan=\"1\" colspan=\"1\">Naive Bayes/SVM/Xgboost</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-10\" class=\"usa-link\" aria-describedby=\"ref-10\" aria-expanded=\"false\">Gong et al. (2022)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data &amp; Financial news</td>\n<td rowspan=\"1\" colspan=\"1\">Technical Indicators</td>\n<td rowspan=\"1\" colspan=\"1\">NISI</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-24\" class=\"usa-link\" aria-describedby=\"ref-24\" aria-expanded=\"false\">Malawana &amp; Rathnayaka (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">CSE</td>\n<td rowspan=\"1\" colspan=\"1\">Twitter Tweets</td>\n<td rowspan=\"1\" colspan=\"1\">Tokenization/Removing Stop words/Symbols</td>\n<td rowspan=\"1\" colspan=\"1\">Custom visual representation of sentiment time series</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-17\" class=\"usa-link\" aria-describedby=\"ref-17\" aria-expanded=\"false\">Khatri &amp; Srivastava (2016)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Twitter &amp; Stock Twits</td>\n<td rowspan=\"1\" colspan=\"1\">Polarity index</td>\n<td rowspan=\"1\" colspan=\"1\">ANN</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-28\" class=\"usa-link\" aria-describedby=\"ref-28\" aria-expanded=\"false\">Mehta, Malhar &amp; Shankarmani (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Twitter Tweet &amp; Stock ticker</td>\n<td rowspan=\"1\" colspan=\"1\">Regual expression extraction</td>\n<td rowspan=\"1\" colspan=\"1\">LSTM/ARIMA/LR</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-14\" class=\"usa-link\" aria-describedby=\"ref-14\" aria-expanded=\"false\">Jing, Wu &amp; Wang (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Textual data &amp; Technical Indicators</td>\n<td rowspan=\"1\" colspan=\"1\">Optimal classification accuracy</td>\n<td rowspan=\"1\" colspan=\"1\">LSTM/CNN</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-2\" class=\"usa-link\" aria-describedby=\"ref-2\" aria-expanded=\"false\">Bhardwaj et al. (2015)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data &amp; Financial news</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">Sentiment analysis for Stock Market prediction on the basis of variation in predicted values</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-34\" class=\"usa-link\" aria-describedby=\"ref-34\" aria-expanded=\"false\">Owen &amp; Oktariani (2020)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Financial news &amp; microblog text data</td>\n<td rowspan=\"1\" colspan=\"1\">Ensemble based model</td>\n<td rowspan=\"1\" colspan=\"1\">CNN/LSTM/MLP/SENN</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"p text-right font-secondary\"><a href=\"table/table-3/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></section></section><section id=\"sec6\"><h3 class=\"pmc_sec_title\">Stock market prediction using other techniques</h3>\n<p>The MG-Conv, a multi-graph CNN-based index pattern forecast model, was proposed by <a href=\"#ref-58\" class=\"usa-link\" aria-describedby=\"ref-58\" aria-expanded=\"false\">Wang et al. (2022b)</a>. A one-dimensional convolutional neural network and data normalization were originally proposed to extract fundamental properties from previous transaction data. The terminology known as static and dynamic graphs were then coined for two types of correlation graphs. The results of multigraph convolution on these two graphs were then converted into predicted values using fully connected networks. A total of 42 Chinese stock market indices were utilized as experimental data. Traditional approaches such as LSTM, 3D-CNN, GC-CNN, and AD-GAT were utilized as comparison benchmarks. The results indicated the method's tenacity and capacity to reduce the average model complexity by 5.11%.</p>\n<p>A nature-inspired algorithm that mimics the human ear’s auditory system by following its natural pathways called AA was proposed by <a href=\"#ref-35\" class=\"usa-link\" aria-describedby=\"ref-35\" aria-expanded=\"false\">Oyewola et al. (2021)</a>. To compare the performance of AA, high-performance machine evolutionary computation and prolonged stochastic processes are utilized. Machine learning approaches such as LR, SVM, feed forward neural network, and RNN were used in addition to continuous-time models such as the stochastic differential equation (SDE) and geometric brownian motion (GBM). The results indicate that AA beat the other algorithms evaluated in this study in terms of overall effectiveness since it significantly decreased prediction error to the bare minimum.</p>\n<p>In order to forecast long-term stock trend behavior, <a href=\"#ref-64\" class=\"usa-link\" aria-describedby=\"ref-64\" aria-expanded=\"false\">Zhao &amp; Wang (2015)</a> introduced a unique data mining technique in their study. They proposed a novel outlier mining technique to discover abnormalities in the market index based on volume sequences of high-frequency data. Such anomalous deals deduce always from the stock price on the stock exchange. By utilizing the clustered structure of such deviations, their system correctly anticipates stock market volatility in the actual worldwide market. The results of their experiment demonstrated that, when used over a long period of time, their suggested strategy generates profits on the Chinese stock exchange.</p>\n<p>In this study, <a href=\"#ref-56\" class=\"usa-link\" aria-describedby=\"ref-56\" aria-expanded=\"false\">Vlasenko et al. (2018)</a> suggest a hybrid five-layer neuro-fuzzy model and an associated learning algorithm for time-series prediction tasks in the stock market. In order to improve computational speed and representational capabilities in processing highly non-linear volatile data, multidimensional Gaussian functions were utilized in place of polynomials in the fourth layer of the suggested model as opposed to the traditional ANFIS design.</p>\n<p>By taking the parameters linked to COVID-19 into consideration, <a href=\"#ref-13\" class=\"usa-link\" aria-describedby=\"ref-13\" aria-expanded=\"false\">Jindal et al. (2021)</a>, proposed a study that seeks to improve the stock market forecast capacity of several popular prediction models. DT Regressor, RF Regressor, and SVR are the forecasting methods considered for their study. The United States, Russia, and India are the nations that are now most impacted by COVID-19. Therefore, they used mean absolute percentage error (MAPE) and root mean square error (RMSE) to analyze the performance of various prediction algorithms on the S&amp;P 500, Nifty50, and RTS Index. The results reveal that when the COVID-19 characteristics are employed, all of the strategies tested performed better.</p>\n<p>In this work, <a href=\"#ref-52\" class=\"usa-link\" aria-describedby=\"ref-52\" aria-expanded=\"false\">Umadevi et al. (2018)</a> have made an effort to develop a stock exchange forecasting system that takes into account various equity-specific factors. The equity ratings were obtained, and then the analysis was performed. Throughout this study, the equity ratings are shown using a variety of graphs, and the time series model ARIMA (autoregressive moving average) is used to forecast the scores. The findings demonstrate that the time series model successfully predicted market ratings with a significant degree of accuracy. To determine how each element related to market performance, separate studies of each factor were conducted. Additionally, the findings suggest that machine learning techniques might be used to forecast market behavior.</p>\n<p>Three fuzzy logic controllers are utilized in the algorithm by <a href=\"#ref-20\" class=\"usa-link\" aria-describedby=\"ref-20\" aria-expanded=\"false\">Lauguico et al. (2019)</a> designed to implement a certain trading strategy. Trigger functions such as candlestick characteristics and Bollinger Bands (BB) were used to assess the effectiveness of the purchase, hold, and sell signals. A specific stock organization provided information on equity markets. The opening and closing prices utilized to help compute the BB are included in these figures. The raw and generated values are the crisp input parameters of the fuzzy inference system (FIS). The classifiers were classified into very low, low, high, and very high levels based on the entered default parameters used by traders. Fuzzy logic was utilized to create association rules that would offer signals indicating the effectiveness of an execution recommendation.</p>\n<p>Even though there is a variety of data structures that can assist in identifying the proper clusters from a fuzzy model’s divine of reasoning, <a href=\"#ref-7\" class=\"usa-link\" aria-describedby=\"ref-7\" aria-expanded=\"false\">de Carvalho Tavares, Ferreira &amp; Mendes (2022)</a> proposed a unique fuzzy model based on a red-black tree (RBT) data structure in order to enhance the prospects of achieving improved projections. The RBT data structure, supports more balance, enabling more certainty. The suggested model performs superior predicting when compared to well-known fuzzy models in the literature.</p>\n<p>In this study, <a href=\"#ref-57\" class=\"usa-link\" aria-describedby=\"ref-57\" aria-expanded=\"false\">Wang et al. (2022a)</a> forecast the stock market index using Transformer, the most recent deep learning framework. The transformer was created to address the issue of natural language processing and is now used for time series forecasting. The transformer can more accurately represent the fundamental principles governing stock market movements because of its encoder-decoder design and multi-head attention mechanism. They perform a variety of side studies on the world’s leading equities, including the CSI 300, S&amp;P 500, Hang Seng, and Nikkei 225 Index. Each of the research demonstrates that transformer outclasses other traditional strategies and can deliver financial support to investors. The studies on different methods of stock market forecasting are summarized in <a href=\"#table-4\" class=\"usa-link\">Table 4</a>.</p>\n<section class=\"tw xbox font-sm\" id=\"table-4\"><h4 class=\"obj_head\">Table 4. Stock market prediction using other techniques.</h4>\n<div class=\"tbl-box p\" tabindex=\"0\"><table class=\"content\" frame=\"hsides\" rules=\"groups\">\n<colgroup span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n</colgroup>\n<thead><tr>\n<th rowspan=\"1\" colspan=\"1\">Authors</th>\n<th rowspan=\"1\" colspan=\"1\">Scope</th>\n<th rowspan=\"1\" colspan=\"1\">Input features</th>\n<th rowspan=\"1\" colspan=\"1\">Feature extraction</th>\n<th rowspan=\"1\" colspan=\"1\">Prediction algorithm</th>\n</tr></thead>\n<tbody>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-58\" class=\"usa-link\" aria-describedby=\"ref-58\" aria-expanded=\"false\">Wang et al. (2022b)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">LSTM/3D-CNN/GC-CNN &amp; AD-GAT</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-35\" class=\"usa-link\" aria-describedby=\"ref-35\" aria-expanded=\"false\">Oyewola et al. (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NGX</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">AA/LR/SVM/FFN/RNN/SDE &amp; GBM</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-64\" class=\"usa-link\" aria-describedby=\"ref-64\" aria-expanded=\"false\">Zhao &amp; Wang (2015)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Tick-by-tick data</td>\n<td rowspan=\"1\" colspan=\"1\">Outlier mining algorithm/Cluster algorithm</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-56\" class=\"usa-link\" aria-describedby=\"ref-56\" aria-expanded=\"false\">Vlasenko et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">Global</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">MIMO neuro-fuzzy model with multidimensional Gaussian functions</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-13\" class=\"usa-link\" aria-describedby=\"ref-13\" aria-expanded=\"false\">Jindal et al. (2021)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE/NYSE/MOEX</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Scaling raw data</td>\n<td rowspan=\"1\" colspan=\"1\">DT/RF &amp; SVR</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-52\" class=\"usa-link\" aria-describedby=\"ref-52\" aria-expanded=\"false\">Umadevi et al. (2018)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Stock score</td>\n<td rowspan=\"1\" colspan=\"1\">NA</td>\n<td rowspan=\"1\" colspan=\"1\">ARIMA</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-7\" class=\"usa-link\" aria-describedby=\"ref-7\" aria-expanded=\"false\">de Carvalho Tavares, Ferreira &amp; Mendes (2022)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">B3/NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Scaling raw data</td>\n<td rowspan=\"1\" colspan=\"1\">Hybrid fuzzy time series model with red–black tree data structure</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-57\" class=\"usa-link\" aria-describedby=\"ref-57\" aria-expanded=\"false\">Wang et al. (2022a)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">SSE/NYSE/HKEX/TSE</td>\n<td rowspan=\"1\" colspan=\"1\">Price data</td>\n<td rowspan=\"1\" colspan=\"1\">Normalization</td>\n<td rowspan=\"1\" colspan=\"1\">RNN/CNN/LSTM/Transformer</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">\n<a href=\"#ref-47\" class=\"usa-link\" aria-describedby=\"ref-47\" aria-expanded=\"false\">Sharma &amp; Juneja (2017)</a>\n</td>\n<td rowspan=\"1\" colspan=\"1\">NSE/BSE</td>\n<td rowspan=\"1\" colspan=\"1\">Technical indicators</td>\n<td rowspan=\"1\" colspan=\"1\">Exponential smoothing</td>\n<td rowspan=\"1\" colspan=\"1\">LS-RF</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"p text-right font-secondary\"><a href=\"table/table-4/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></section><p><a href=\"#ref-47\" class=\"usa-link\" aria-describedby=\"ref-47\" aria-expanded=\"false\">Sharma &amp; Juneja (2017)</a> focus on making future stock market index value predictions using past data. The exploratory appraisal is based on 10-year historical data for two Indian equity markets, the CNX Nifty and the S&amp;P BSE Sensex. Predictions are given for days 1 through 10, 15, 30, and 40 in the future. This study indicates utilizing LSboost to integrate the estimations and predictions from the ensemble of trees in a random forest. The suggested model’s prediction performance is contrasted with that of the well-known support vector regression. Each of the prediction models uses a different set of technical indicators as inputs.</p></section></section><section id=\"sec7\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"sec7\"><a id=\"sec7-anchor\" data-anchor-id=\"sec7\" class=\"usa-anchor\"></a>Challenges and discussion</h2>\n<p>Analysis and forecasting of the financial markets remain an intriguing and complex topic. Data is increasingly accessible, but it is becoming more challenging to gather and interpret in order to gain relevant insights and determine how it influences the investors in making their financial decisions wisely (<a href=\"#ref-36\" class=\"usa-link\" aria-describedby=\"ref-36\" aria-expanded=\"false\">Ozbayoglu, Gudelek &amp; Sezer, 2020</a>). It is challenging to extract characteristics from financial information as it is essential to address the many components used to generate forecasts. Financial datasets are frequently erratic and the data’s quality has a big impact on performing analysis and predictions.</p>\n<p>Economic uncertainty is the degree of swings in an investment’s market price. Uncertainty and inflation are the key causes of volatility, and a volatile market increases the risk factor to greater extent. Also, volatility in trading constantly affects our emotions in negative ways that in turn makes the investors to take wrong decisions. When the marketplace is highly dynamic, predicting stock values becomes more complex. One such instance is the flash collapse, which erased $860 billion from US financial markets in the space of 30 min. The volatility of the stock market is also significantly influenced by international politics. So, it is difficult to ascertain the adequacy and effectiveness of these methods as new versions are constantly introduced into the market. This branch of study is exceptional in its self-defeating tendency. Simply speaking, sharing extremely successful techniques with other organizations will render such strategies redundant. Smartest algorithm trading is extremely restricted and confidential in the market segments. The technique or framework of such algorithms is never revealed.</p>\n<p>Sentiment analysis using data derived from social media is receiving greater attention as a result of the growing influence of digital networking on many facets of our life (<a href=\"#ref-61\" class=\"usa-link\" aria-describedby=\"ref-61\" aria-expanded=\"false\">Yang et al., 2023</a>). Due to multiple reasons, including misleading information and the bot data released on the web by different sources, this data can be volatile and challenging to interpret. Finding high-quality data and getting useful insights from it is difficult. Organizational quarterly or annual reports that are employed for stock prediction are a respectable choice or additional resource. These data, when correctly deciphered, provide important knowledge of an organization’s state, aiding in the comprehension of the stock’s future development. But in order to understand the fundamental details of the company requires professional qualification or knowledge. Since historical Twitter information cannot be obtained without someone saving it, data must be collected over a given time period starting with the given date and time, required data must be filtered out of the stream of irrelevant tweets, and validation is needed to access real-time Twitter data. The live testing of the forecast will again be a significant problem.</p>\n<p>In order to understand the present literature status in the field of stock market analysis, an in-depth statistical survey was conducted, and the following section is focused on reviewing the research questions mentioned in the earlier section.</p>\n<section id=\"sec8\"><h3 class=\"pmc_sec_title\">RQ1: what are the research techniques or methodologies employed in recent times to predict the stock market movements?</h3>\n<p>Based on the survey analysis, it is evident that LSTM is the most preferred model among researchers for predicting stock price movements. Other machine learning models like SVM, KNN, ANN, and CNN are widely used in many studies next to LSTM. Among researchers, CNN is mostly preferred algorithm for feature selection, and LSTM for stock predictions. Logistic regression and the adaptive system is the least preferred model for the chosen field of study. The graph in <a href=\"#fig-1\" class=\"usa-link\">Fig. 1</a> shows the recent techniques used in the domain of stock market prediction.</p>\n<figure class=\"fig xbox font-sm\" id=\"fig-1\"><h4 class=\"obj_head\">Figure 1. Recent techniques used in stock market prediction.</h4>\n<p class=\"img-box line-height-none margin-x-neg-2 tablet:margin-x-0 text-center\"><a class=\"tileshop\" target=\"_blank\" href=\"https://www.ncbi.nlm.nih.gov/core/lw/2.0/html/tileshop_pmc/tileshop_pmc_inline.html?title=Click%20on%20image%20to%20zoom&amp;p=PMC3&amp;id=10909160_peerj-cs-10-1700-g001.jpg\"><img class=\"graphic zoom-in\" src=\"https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/7665620d8751/peerj-cs-10-1700-g001.jpg\" loading=\"lazy\" height=\"567\" width=\"663\" alt=\"Figure 1\"></a></p>\n<div class=\"p text-right font-secondary\"><a href=\"figure/fig-1/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></figure></section><section id=\"sec9\"><h3 class=\"pmc_sec_title\">RQ2: what are the different sources of datasets considered for the stock market predictions?</h3>\n<p>As depicted in <a href=\"#fig-2\" class=\"usa-link\">Fig. 2</a>, some of the selected studies were considered generic datasets that are suitable for the global market. In general, the majority of analysts preferred US-based indices such as NASDAQ, Dow Jones, NYSE, and S&amp;P 500 for their research analysis. Over 15% of studies were focused on Indian stock indices (NSE) followed by FTSE 100 (United Kingdom), Nikkei (Japan), Hang Sang (Hong Kong), Shangai (China), and others. For analysis, majority of studies used the dataset readily available in online platform like Kaggle, whereas few studies utilize the data directly from Yahoo finance.</p>\n<figure class=\"fig xbox font-sm\" id=\"fig-2\"><h4 class=\"obj_head\">Figure 2. Sources of datasets used in stock market predictions.</h4>\n<p class=\"img-box line-height-none margin-x-neg-2 tablet:margin-x-0 text-center\"><a class=\"tileshop\" target=\"_blank\" href=\"https://www.ncbi.nlm.nih.gov/core/lw/2.0/html/tileshop_pmc/tileshop_pmc_inline.html?title=Click%20on%20image%20to%20zoom&amp;p=PMC3&amp;id=10909160_peerj-cs-10-1700-g002.jpg\"><img class=\"graphic zoom-in\" src=\"https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/9ee913551d33/peerj-cs-10-1700-g002.jpg\" loading=\"lazy\" height=\"330\" width=\"738\" alt=\"Figure 2\"></a></p>\n<div class=\"p text-right font-secondary\"><a href=\"figure/fig-2/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></figure></section><section id=\"sec10\"><h3 class=\"pmc_sec_title\">RQ3: what are the most popular journals available in the domain of stock market investments?</h3>\n<p>For this study, we have considered the research articles pertaining to stock market predictions from widely popular repositories like Springer, Elsevier SCOPUS, IEEE, and Science Direct. The data as represented in <a href=\"#fig-3\" class=\"usa-link\">Fig. 3</a> shows sufficient research articles are available in the repositories to help the researchers and analysts with their research directions.</p>\n<figure class=\"fig xbox font-sm\" id=\"fig-3\"><h4 class=\"obj_head\">Figure 3. Publications of stock market analysis.</h4>\n<p class=\"img-box line-height-none margin-x-neg-2 tablet:margin-x-0 text-center\"><a class=\"tileshop\" target=\"_blank\" href=\"https://www.ncbi.nlm.nih.gov/core/lw/2.0/html/tileshop_pmc/tileshop_pmc_inline.html?title=Click%20on%20image%20to%20zoom&amp;p=PMC3&amp;id=10909160_peerj-cs-10-1700-g003.jpg\"><img class=\"graphic zoom-in\" src=\"https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/82bc36c875a7/peerj-cs-10-1700-g003.jpg\" loading=\"lazy\" height=\"426\" width=\"738\" alt=\"Figure 3\"></a></p>\n<div class=\"p text-right font-secondary\"><a href=\"figure/fig-3/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></figure></section><section id=\"sec11\"><h3 class=\"pmc_sec_title\">RQ4: what are the countries that show research interests in equity/capital market investments?</h3>\n<p>According to this survey as represented in <a href=\"#fig-4\" class=\"usa-link\">Fig. 4</a>, the research works in the field of stock market analysis predominantly popular in four countries namely USA, UK, India, and China. This shows that the interests among participants are directly proportional to the growth potential of the country. The least amount of research work in this field was carried out by Japan, Canada, Australia, and other countries.</p>\n<figure class=\"fig xbox font-sm\" id=\"fig-4\"><h4 class=\"obj_head\">Figure 4. Countries interested in equity market.</h4>\n<p class=\"img-box line-height-none margin-x-neg-2 tablet:margin-x-0 text-center\"><a class=\"tileshop\" target=\"_blank\" href=\"https://www.ncbi.nlm.nih.gov/core/lw/2.0/html/tileshop_pmc/tileshop_pmc_inline.html?title=Click%20on%20image%20to%20zoom&amp;p=PMC3&amp;id=10909160_peerj-cs-10-1700-g004.jpg\"><img class=\"graphic zoom-in\" src=\"https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/6372c35a1c6d/peerj-cs-10-1700-g004.jpg\" loading=\"lazy\" height=\"426\" width=\"738\" alt=\"Figure 4\"></a></p>\n<div class=\"p text-right font-secondary\"><a href=\"figure/fig-4/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></figure></section><section id=\"sec12\"><h3 class=\"pmc_sec_title\">RQ5: what are the most popular evaluation metrics used in a stock market analysis?</h3>\n<p>According to this study, the finding shows MSE and RMSE considered as most popular evaluation metrics followed by accuracy, precession, recall, and F-Score. The majority of machine learning models used error metrics in combination with accuracy as the preferred evaluation metric, whereas deep learning techniques employed precision, recall, and F-score as the preferred choice. The details pertaining to the evaluation metrics in the domain of stock market analysis are denoted in <a href=\"#fig-5\" class=\"usa-link\">Fig. 5</a>.</p>\n<figure class=\"fig xbox font-sm\" id=\"fig-5\"><h4 class=\"obj_head\">Figure 5. Evaluation metrics in stock market predictions.</h4>\n<p class=\"img-box line-height-none margin-x-neg-2 tablet:margin-x-0 text-center\"><a class=\"tileshop\" target=\"_blank\" href=\"https://www.ncbi.nlm.nih.gov/core/lw/2.0/html/tileshop_pmc/tileshop_pmc_inline.html?title=Click%20on%20image%20to%20zoom&amp;p=PMC3&amp;id=10909160_peerj-cs-10-1700-g005.jpg\"><img class=\"graphic zoom-in\" src=\"https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/77061d2f5335/peerj-cs-10-1700-g005.jpg\" loading=\"lazy\" height=\"436\" width=\"738\" alt=\"Figure 5\"></a></p>\n<div class=\"p text-right font-secondary\"><a href=\"figure/fig-5/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></figure></section></section><section id=\"sec13\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"sec13\"><a id=\"sec13-anchor\" data-anchor-id=\"sec13\" class=\"usa-anchor\"></a>Conclusions</h2>\n<p>In this study, we have conducted a survey of over 100 research articles in the domain of stock market prediction utilizing recent machine learning approaches, neural networks, text analytics, and other approaches on various stock exchanges available globally. Due to the volatile nature of the financial markets, prediction plays a crucial part in the stock market company, which is a highly difficult and complex procedure. Based on the survey conducted, this study attempted to address the five key research questions about equity market investment areas. The main objective of this study is to support researchers, analysts, investors, and individual participants to take informed decisions in equity market financing.</p></section><section id=\"sec14\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"sec14\"><a id=\"sec14-anchor\" data-anchor-id=\"sec14\" class=\"usa-anchor\"></a>Future scope</h2>\n<p>Other influences and aspects, such as financial ratios, numerous cases, <em>etc</em>., should be included in the future scope. As additional variables are incorporated, the performance will improve. The procedures could also be used to analyze the substance of comments on social media in order to find trends or links between clients and firm representatives. The overall performance structure of the firm may also be predicted with the use of conventional algorithms and data mining approaches. Future studies might focus on combining data from stock sentiment categorization with quantitative numbers pertaining to prior stock values to anticipate financial markets. More effective stock assessment systems may be constructed by combining both types of information. Deep learning-based strategies may be used to improve the effectiveness of feature extraction techniques. Graph knowledge approaches are a potential technology for developing the performance of the proposed engines; nevertheless, future studies ought to concentrate on the complexity and gradient of networking with plenty of nodes. There is potential for predicting stock market patterns during pandemics utilizing neural network algorithms, such as the LSTM and GRU (Gated Recurrent Units) techniques, which have been proven to be significantly successful in time series data prediction applications.</p>\n<p>The complete versions of all the acronyms discussed in our study are presented in <a href=\"#table-5\" class=\"usa-link\">Table 5</a>.</p>\n<section class=\"tw xbox font-sm\" id=\"table-5\"><h3 class=\"obj_head\">Table 5. Acronyms used in this article.</h3>\n<div class=\"tbl-box p\" tabindex=\"0\"><table class=\"content\" frame=\"hsides\" rules=\"groups\">\n<colgroup span=\"1\">\n<col span=\"1\">\n<col span=\"1\">\n</colgroup>\n<thead><tr>\n<th rowspan=\"1\" colspan=\"1\">Abbreviation</th>\n<th rowspan=\"1\" colspan=\"1\">Full form</th>\n</tr></thead>\n<tbody>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">KSE</td>\n<td rowspan=\"1\" colspan=\"1\">Karachi stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">NSE</td>\n<td rowspan=\"1\" colspan=\"1\">National stock exchange of India</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">NYSE</td>\n<td rowspan=\"1\" colspan=\"1\">New York stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">LSE</td>\n<td rowspan=\"1\" colspan=\"1\">London stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">NASDAQ</td>\n<td rowspan=\"1\" colspan=\"1\">National association of securities dealers automated quotations</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SSE</td>\n<td rowspan=\"1\" colspan=\"1\">Shanghai stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GSE</td>\n<td rowspan=\"1\" colspan=\"1\">German stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">TSE</td>\n<td rowspan=\"1\" colspan=\"1\">Tokyo stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ISE</td>\n<td rowspan=\"1\" colspan=\"1\">Istanbul stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">CSE</td>\n<td rowspan=\"1\" colspan=\"1\">Colombo stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">NGX</td>\n<td rowspan=\"1\" colspan=\"1\">Nigerian exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">MOEX</td>\n<td rowspan=\"1\" colspan=\"1\">Moscow exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">B3</td>\n<td rowspan=\"1\" colspan=\"1\">Brazil stock exchange</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">HKEX</td>\n<td rowspan=\"1\" colspan=\"1\">Hong kong exchanges</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SLP</td>\n<td rowspan=\"1\" colspan=\"1\">Single layer perceptron</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">MLP</td>\n<td rowspan=\"1\" colspan=\"1\">Multi layer perceptron</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">RBF</td>\n<td rowspan=\"1\" colspan=\"1\">Radial basis function</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SVM</td>\n<td rowspan=\"1\" colspan=\"1\">Support vector machine</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">PR</td>\n<td rowspan=\"1\" colspan=\"1\">Polynomial regression</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">LR</td>\n<td rowspan=\"1\" colspan=\"1\">Linear regression</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">RF</td>\n<td rowspan=\"1\" colspan=\"1\">Random forest</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">KNN</td>\n<td rowspan=\"1\" colspan=\"1\">K-nearest neighbor</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">RNN</td>\n<td rowspan=\"1\" colspan=\"1\">Recurrent neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">LSTM</td>\n<td rowspan=\"1\" colspan=\"1\">Long short-term memory</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">CNN</td>\n<td rowspan=\"1\" colspan=\"1\">Convolutional neural netwotk</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ANN</td>\n<td rowspan=\"1\" colspan=\"1\">Artificial neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ET</td>\n<td rowspan=\"1\" colspan=\"1\">Extra tree</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GBM</td>\n<td rowspan=\"1\" colspan=\"1\">Gradient boosting classifier</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ARIMA</td>\n<td rowspan=\"1\" colspan=\"1\">Auto-regressive integrated moving average</td>\n</tr>\n<tr>\n<td align=\"center\" rowspan=\"1\" colspan=\"1\">3MMA</td>\n<td rowspan=\"1\" colspan=\"1\">Three month moving average</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SGD</td>\n<td rowspan=\"1\" colspan=\"1\">Stochastic gradient descent</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">DT</td>\n<td rowspan=\"1\" colspan=\"1\">Decision tree</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ES</td>\n<td rowspan=\"1\" colspan=\"1\">Exponential smoothing</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ExtRa</td>\n<td rowspan=\"1\" colspan=\"1\">Extremely randomized trees</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">PLS</td>\n<td rowspan=\"1\" colspan=\"1\">Partial least square</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">NB</td>\n<td rowspan=\"1\" colspan=\"1\">Naïve Bayes</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SMO</td>\n<td rowspan=\"1\" colspan=\"1\">Sequential minimal optimization</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GAM</td>\n<td rowspan=\"1\" colspan=\"1\">Generalized additive model</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GA</td>\n<td rowspan=\"1\" colspan=\"1\">Generic algorithm</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">FA</td>\n<td rowspan=\"1\" colspan=\"1\">Fuzzy algorithms</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">DNN</td>\n<td rowspan=\"1\" colspan=\"1\">Dense neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">RA</td>\n<td rowspan=\"1\" colspan=\"1\">Regression algorithms</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">HA</td>\n<td rowspan=\"1\" colspan=\"1\">Hybrid approaches</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SOM</td>\n<td rowspan=\"1\" colspan=\"1\">Self-organising maps</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SVR</td>\n<td rowspan=\"1\" colspan=\"1\">Support vector regression</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">MCS</td>\n<td rowspan=\"1\" colspan=\"1\">Monte Carlo simulation</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">CART</td>\n<td rowspan=\"1\" colspan=\"1\">Classification and regression trees</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GP</td>\n<td rowspan=\"1\" colspan=\"1\">Gaussian processes</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">BSM</td>\n<td rowspan=\"1\" colspan=\"1\">Black Scholes model</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GRNN</td>\n<td rowspan=\"1\" colspan=\"1\">Generalized regression neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">BPNN</td>\n<td rowspan=\"1\" colspan=\"1\">Back propagation neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">HMM</td>\n<td rowspan=\"1\" colspan=\"1\">Hidden Markov model</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">EMD</td>\n<td rowspan=\"1\" colspan=\"1\">Empirical mode decomposition</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">ANFIS</td>\n<td rowspan=\"1\" colspan=\"1\">Adaptive neuro-fuzzy inference system</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GGAP</td>\n<td rowspan=\"1\" colspan=\"1\">General growing and pruning</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">CSWNN</td>\n<td rowspan=\"1\" colspan=\"1\">Cuckoo search wavelet neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">WNN</td>\n<td rowspan=\"1\" colspan=\"1\">Wavelet neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">NISI</td>\n<td rowspan=\"1\" colspan=\"1\">New investor sentiment index</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SENN</td>\n<td rowspan=\"1\" colspan=\"1\">Stockensemble-based neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GC-CNN</td>\n<td rowspan=\"1\" colspan=\"1\">Graph convolutional neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">AD-GAT</td>\n<td rowspan=\"1\" colspan=\"1\">Attribute-driven graph attention network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">AA</td>\n<td rowspan=\"1\" colspan=\"1\">Auditory algorithm</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">FNN</td>\n<td rowspan=\"1\" colspan=\"1\">Feed forward neural network</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">SDE</td>\n<td rowspan=\"1\" colspan=\"1\">Stochastic differential equations</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">GBM</td>\n<td rowspan=\"1\" colspan=\"1\">Geometric Brownian motion</td>\n</tr>\n<tr>\n<td rowspan=\"1\" colspan=\"1\">LS-FR</td>\n<td rowspan=\"1\" colspan=\"1\">Random forest using LSBoost</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"p text-right font-secondary\"><a href=\"table/table-5/\" class=\"usa-link\" target=\"_blank\" rel=\"noopener noreferrer\">Open in a new tab</a></div></section></section><section id=\"funding-statement1\" lang=\"en\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"funding-statement1\"><a id=\"funding-statement1-anchor\" data-anchor-id=\"funding-statement1\" class=\"usa-anchor\"></a>Funding Statement</h2>\n<p>The authors received no funding for this work.</p></section><section id=\"sec15\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"sec15\"><a id=\"sec15-anchor\" data-anchor-id=\"sec15\" class=\"usa-anchor\"></a>Additional Information and Declarations</h2>\n<section id=\"fn-group1\" class=\"fn-group\"><h3 class=\"pmc_sec_title\">Competing Interests</h3>\n<div class=\"fn-group p font-secondary-light font-sm\"><div class=\"fn p\" id=\"conflict-1\"><p>The authors declare that they have no competing interests.</p></div></div></section><section id=\"fn-group2\" class=\"fn-group\"><h3 class=\"pmc_sec_title\">Author Contributions</h3>\n<div class=\"fn-group p font-secondary-light font-sm\">\n<div class=\"fn p\" id=\"contribution-1\"><p>Prakash Balasubramanian conceived and designed the experiments, analyzed the data, authored or reviewed drafts of the article, and approved the final draft.</p></div>\n<div class=\"fn p\" id=\"contribution-2\"><p>Chinthan P. conceived and designed the experiments, performed the experiments, analyzed the data, performed the computation work, prepared figures and/or tables, and approved the final draft.</p></div>\n<div class=\"fn p\" id=\"contribution-3\"><p>Saleena Badarudeen performed the experiments, analyzed the data, prepared figures and/or tables, authored or reviewed drafts of the article, and approved the final draft.</p></div>\n<div class=\"fn p\" id=\"contribution-4\"><p>Harini Sriraman analyzed the data, performed the computation work, authored or reviewed drafts of the article, and approved the final draft.</p></div>\n</div></section><section id=\"fn-group3\" class=\"fn-group\"><h3 class=\"pmc_sec_title\">Data Availability</h3>\n<div class=\"fn-group p font-secondary-light font-sm\"><div class=\"fn p\" id=\"addinfo-1\">\n<p>The following information was supplied regarding data availability:</p>\n<p>This is a survey article and hence did not utilize code/data.</p>\n</div></div></section></section><section id=\"ref-list1\" class=\"ref-list\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"ref-list1\"><a id=\"ref-list1-anchor\" data-anchor-id=\"ref-list1\" class=\"usa-anchor\"></a>References</h2>\n<section id=\"ref-list1_sec2\"><ul class=\"ref-list font-sm\" style=\"list-style-type:none\">\n<li id=\"ref-1\">\n<span class=\"label\">Althelaya, El-Alfy &amp; Mohammed (2018).</span><cite>Althelaya KA, El-Alfy ESM, Mohammed S. Evaluation of bidirectional LSTM for short- and long-term stock market prediction. 2018 9th International Conference on Information and Communication Systems (ICICS); Piscataway: IEEE; 2018. pp. 151–156.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Althelaya%20KA,%20El-Alfy%20ESM,%20Mohammed%20S.%20Evaluation%20of%20bidirectional%20LSTM%20for%20short-%20and%20long-term%20stock%20market%20prediction.%202018%209th%20International%20Conference%20on%20Information%20and%20Communication%20Systems%20(ICICS);%20Piscataway:%20IEEE;%202018.%20pp.%20151%E2%80%93156.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-2\">\n<span class=\"label\">Bhardwaj et al. (2015).</span><cite>Bhardwaj A, Narayan Y, Vanraj P, Dutta M. Sentiment analysis for Indian stock market prediction using sensex and nifty. Procedia Computer Science. 2015;70:85–91. doi: 10.1016/j.procs.2015.10.043.</cite> [<a href=\"https://doi.org/10.1016/j.procs.2015.10.043\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Procedia%20Computer%20Science&amp;title=Sentiment%20analysis%20for%20Indian%20stock%20market%20prediction%20using%20sensex%20and%20nifty&amp;author=A%20Bhardwaj&amp;author=Y%20Narayan&amp;author=P%20Vanraj&amp;author=M%20Dutta&amp;volume=70&amp;publication_year=2015&amp;pages=85-91&amp;doi=10.1016/j.procs.2015.10.043&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-3\">\n<span class=\"label\">Budhani, Jha &amp; Budhani (2012).</span><cite>Budhani N, Jha CK, Budhani SK. Application of neural network in analysis of stock market prediction. International Journal of Computer Science &amp; Engineering Technology (IJCSET) 2012;3(4):61–68.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Journal%20of%20Computer%20Science%20&amp;%20Engineering%20Technology%20(IJCSET)&amp;title=Application%20of%20neural%20network%20in%20analysis%20of%20stock%20market%20prediction&amp;author=N%20Budhani&amp;author=CK%20Jha&amp;author=SK%20Budhani&amp;volume=3&amp;issue=4&amp;publication_year=2012&amp;pages=61-68&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-4\">\n<span class=\"label\">Charkha (2008).</span><cite>Charkha PR. Stock price prediction and trend prediction using neural networks. First International Conference on Emerging Trends in Engineering and Technology; 2008. pp. 592–594.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Charkha%20PR.%20Stock%20price%20prediction%20and%20trend%20prediction%20using%20neural%20networks.%20First%20International%20Conference%20on%20Emerging%20Trends%20in%20Engineering%20and%20Technology;%202008.%20pp.%20592%E2%80%93594.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-5\">\n<span class=\"label\">Chen &amp; Chang (2010).</span><cite>Chen S-M, Chang Y-C. Multi-variable fuzzy forecasting based on fuzzy clustering and fuzzy rule interpolation techniques. Information Sciences. 2010;180(24):4772–4783. doi: 10.1016/j.ins.2010.08.026.</cite> [<a href=\"https://doi.org/10.1016/j.ins.2010.08.026\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Information%20Sciences&amp;title=Multi-variable%20fuzzy%20forecasting%20based%20on%20fuzzy%20clustering%20and%20fuzzy%20rule%20interpolation%20techniques&amp;author=S-M%20Chen&amp;author=Y-C%20Chang&amp;volume=180&amp;issue=24&amp;publication_year=2010&amp;pages=4772-4783&amp;doi=10.1016/j.ins.2010.08.026&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-6\">\n<span class=\"label\">Chen &amp; Tanuwijaya (2011).</span><cite>Chen S-M, Tanuwijaya K. Multivariate fuzzy forecasting based on fuzzy time series and automatic clustering techniques. Expert Systems with Applications. 2011;38(8):10594–10605. doi: 10.1016/j.eswa.2011.02.098.</cite> [<a href=\"https://doi.org/10.1016/j.eswa.2011.02.098\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Expert%20Systems%20with%20Applications&amp;title=Multivariate%20fuzzy%20forecasting%20based%20on%20fuzzy%20time%20series%20and%20automatic%20clustering%20techniques&amp;author=S-M%20Chen&amp;author=K%20Tanuwijaya&amp;volume=38&amp;issue=8&amp;publication_year=2011&amp;pages=10594-10605&amp;doi=10.1016/j.eswa.2011.02.098&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-7\">\n<span class=\"label\">de Carvalho Tavares, Ferreira &amp; Mendes (2022).</span><cite>de Carvalho Tavares THB, Ferreira BP, Mendes EMAM. Fuzzy time series model based on red-black trees for stock index forecasting. Applied Soft Computing. 2022;127(10):109323. doi: 10.1016/j.asoc.2022.109323.</cite> [<a href=\"https://doi.org/10.1016/j.asoc.2022.109323\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Applied%20Soft%20Computing&amp;title=Fuzzy%20time%20series%20model%20based%20on%20red-black%20trees%20for%20stock%20index%20forecasting&amp;author=THB%20de%20Carvalho%20Tavares&amp;author=BP%20Ferreira&amp;author=EMAM%20Mendes&amp;volume=127&amp;issue=10&amp;publication_year=2022&amp;pages=109323&amp;doi=10.1016/j.asoc.2022.109323&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-8\">\n<span class=\"label\">Duan, Liu &amp; Wang (2021).</span><cite>Duan Y, Liu L, Wang Z. COVID-19 sentiment and the Chinese stock market: evidence from the official news media and Sina Weibo. Research in International Business and Finance. 2021;58(4):101432. doi: 10.1016/j.ribaf.2021.101432.</cite> [<a href=\"https://doi.org/10.1016/j.ribaf.2021.101432\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"/articles/PMC9756003/\" class=\"usa-link\">PMC free article</a>] [<a href=\"https://pubmed.ncbi.nlm.nih.gov/36540342/\" class=\"usa-link\">PubMed</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Research%20in%20International%20Business%20and%20Finance&amp;title=COVID-19%20sentiment%20and%20the%20Chinese%20stock%20market:%20evidence%20from%20the%20official%20news%20media%20and%20Sina%20Weibo&amp;author=Y%20Duan&amp;author=L%20Liu&amp;author=Z%20Wang&amp;volume=58&amp;issue=4&amp;publication_year=2021&amp;pages=101432&amp;pmid=36540342&amp;doi=10.1016/j.ribaf.2021.101432&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-9\">\n<span class=\"label\">Gao et al. (2022).</span><cite>Gao R, Cui S, Xiao H, Fan W, Zhang H, Wang Y. Integrating the sentiments of multiple news providers for stock market index movement prediction: a deep learning approach based on evidential reasoning rule. Information Sciences. 2022;615(4):529–556. doi: 10.1016/j.ins.2022.10.029.</cite> [<a href=\"https://doi.org/10.1016/j.ins.2022.10.029\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Information%20Sciences&amp;title=Integrating%20the%20sentiments%20of%20multiple%20news%20providers%20for%20stock%20market%20index%20movement%20prediction:%20a%20deep%20learning%20approach%20based%20on%20evidential%20reasoning%20rule&amp;author=R%20Gao&amp;author=S%20Cui&amp;author=H%20Xiao&amp;author=W%20Fan&amp;author=H%20Zhang&amp;volume=615&amp;issue=4&amp;publication_year=2022&amp;pages=529-556&amp;doi=10.1016/j.ins.2022.10.029&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-10\">\n<span class=\"label\">Gong et al. (2022).</span><cite>Gong X, Zhang W, Wang J, Wang C. Investor sentiment and stock volatility: new evidence. International Review of Financial Analysis. 2022;80(2):102028. doi: 10.1016/j.irfa.2022.102028.</cite> [<a href=\"https://doi.org/10.1016/j.irfa.2022.102028\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Review%20of%20Financial%20Analysis&amp;title=Investor%20sentiment%20and%20stock%20volatility:%20new%20evidence&amp;author=X%20Gong&amp;author=W%20Zhang&amp;author=J%20Wang&amp;author=C%20Wang&amp;volume=80&amp;issue=2&amp;publication_year=2022&amp;pages=102028&amp;doi=10.1016/j.irfa.2022.102028&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-11\">\n<span class=\"label\">Hiransha et al. (2018).</span><cite>Hiransha M, Gopalakrishnan EA, Menon VK, Soman KP. NSE stock market prediction using deep-learning models. Procedia Computer Science. 2018;132:1351–1362. doi: 10.1016/j.procs.2018.05.050.</cite> [<a href=\"https://doi.org/10.1016/j.procs.2018.05.050\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Procedia%20Computer%20Science&amp;title=NSE%20stock%20market%20prediction%20using%20deep-learning%20models&amp;author=M%20Hiransha&amp;author=EA%20Gopalakrishnan&amp;author=VK%20Menon&amp;author=KP%20Soman&amp;volume=132&amp;publication_year=2018&amp;pages=1351-1362&amp;doi=10.1016/j.procs.2018.05.050&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-12\">\n<span class=\"label\">Honghai &amp; Haifei (2012).</span><cite>Honghai Y, Haifei L. Improved stock market prediction by combining support vector machine and empirical mode decomposition. Fifth International Symposium on Computational Intelligence and Design (ISCID); Piscataway: IEEE; 2012. </cite> [<a href=\"https://scholar.google.com/scholar_lookup?Honghai%20Y,%20Haifei%20L.%20Improved%20stock%20market%20prediction%20by%20combining%20support%20vector%20machine%20and%20empirical%20mode%20decomposition.%20Fifth%20International%20Symposium%20on%20Computational%20Intelligence%20and%20Design%20(ISCID);%20Piscataway:%20IEEE;%202012.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-13\">\n<span class=\"label\">Jindal et al. (2021).</span><cite>Jindal R, Bansal N, Chawla N, Singhal S. Improving traditional stock market prediction algorithms using COVID-19 analysis. 2021 International Conference on Emerging Smart Computing and Informatics (ESCI); Piscataway: IEEE; 2021. pp. 374–379.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Jindal%20R,%20Bansal%20N,%20Chawla%20N,%20Singhal%20S.%20Improving%20traditional%20stock%20market%20prediction%20algorithms%20using%20COVID-19%20analysis.%202021%20International%20Conference%20on%20Emerging%20Smart%20Computing%20and%20Informatics%20(ESCI);%20Piscataway:%20IEEE;%202021.%20pp.%20374%E2%80%93379.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-14\">\n<span class=\"label\">Jing, Wu &amp; Wang (2021).</span><cite>Jing N, Wu Z, Wang H. A hybrid model integrating deep learning with investor sentiment analysis for stock price prediction. Expert Systems with Applications. 2021;178(3):115019. doi: 10.1016/j.eswa.2021.115019.</cite> [<a href=\"https://doi.org/10.1016/j.eswa.2021.115019\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Expert%20Systems%20with%20Applications&amp;title=A%20hybrid%20model%20integrating%20deep%20learning%20with%20investor%20sentiment%20analysis%20for%20stock%20price%20prediction&amp;author=N%20Jing&amp;author=Z%20Wu&amp;author=H%20Wang&amp;volume=178&amp;issue=3&amp;publication_year=2021&amp;pages=115019&amp;doi=10.1016/j.eswa.2021.115019&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-15\">\n<span class=\"label\">Kara, Acar Boyacioglu &amp; Baykan (2011).</span><cite>Kara Y, Acar Boyacioglu M, Baykan ÖK. Predicting direction of stock price index movement using artificial neural networks and support vector machines: the sample of the Istanbul stock exchange. Expert Systems with Applications. 2011;38(5):5311–5319. doi: 10.1016/j.eswa.2010.10.027.</cite> [<a href=\"https://doi.org/10.1016/j.eswa.2010.10.027\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Expert%20Systems%20with%20Applications&amp;title=Predicting%20direction%20of%20stock%20price%20index%20movement%20using%20artificial%20neural%20networks%20and%20support%20vector%20machines:%20the%20sample%20of%20the%20Istanbul%20stock%20exchange&amp;author=Y%20Kara&amp;author=M%20Acar%20Boyacioglu&amp;author=%C3%96K%20Baykan&amp;volume=38&amp;issue=5&amp;publication_year=2011&amp;pages=5311-5319&amp;doi=10.1016/j.eswa.2010.10.027&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-16\">\n<span class=\"label\">Khan et al. (2020).</span><cite>Khan W, Ghazanfar MA, Azam MA, Karami A, Alyoubi KH, Alfakeeh AS. Stock market prediction using machine learning classifiers and social media, news. Journal of Ambient Intelligence and Humanized Computing. 2020;13(7):3433–3456. doi: 10.1007/s12652-020-01839-w.</cite> [<a href=\"https://doi.org/10.1007/s12652-020-01839-w\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Journal%20of%20Ambient%20Intelligence%20and%20Humanized%20Computing&amp;title=Stock%20market%20prediction%20using%20machine%20learning%20classifiers%20and%20social%20media,%20news&amp;author=W%20Khan&amp;author=MA%20Ghazanfar&amp;author=MA%20Azam&amp;author=A%20Karami&amp;author=KH%20Alyoubi&amp;volume=13&amp;issue=7&amp;publication_year=2020&amp;pages=3433-3456&amp;doi=10.1007/s12652-020-01839-w&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-17\">\n<span class=\"label\">Khatri &amp; Srivastava (2016).</span><cite>Khatri SK, Srivastava A. Using sentimental analysis in prediction of stock market investment. 2016 5th International Conference on Reliability, Infocom Technologies and Optimization (Trends and Future Directions) (ICRITO); Piscataway: IEEE; 2016. pp. 566–569.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Khatri%20SK,%20Srivastava%20A.%20Using%20sentimental%20analysis%20in%20prediction%20of%20stock%20market%20investment.%202016%205th%20International%20Conference%20on%20Reliability,%20Infocom%20Technologies%20and%20Optimization%20(Trends%20and%20Future%20Directions)%20(ICRITO);%20Piscataway:%20IEEE;%202016.%20pp.%20566%E2%80%93569.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-18\">\n<span class=\"label\">Kompella, Chilukuri &amp; Kalyana (2019).</span><cite>Kompella S, Chilukuri C, Kalyana C. Stock market prediction using machine learning methods. International Journal of Computer Engineering and Technology. 2019;10(3):20–30.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Journal%20of%20Computer%20Engineering%20and%20Technology&amp;title=Stock%20market%20prediction%20using%20machine%20learning%20methods&amp;author=S%20Kompella&amp;author=C%20Chilukuri&amp;author=C%20Kalyana&amp;volume=10&amp;issue=3&amp;publication_year=2019&amp;pages=20-30&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-19\">\n<span class=\"label\">Kumar et al. (2018).</span><cite>Kumar I, Dogra K, Utreja C, Yadav P. A comparative study of supervised machine learning algorithms for stock market trend prediction. 2018 Second International Conference on Inventive Communication and Computational Technologies (ICICCT); Piscataway: IEEE; 2018. pp. 1003–1007.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Kumar%20I,%20Dogra%20K,%20Utreja%20C,%20Yadav%20P.%20A%20comparative%20study%20of%20supervised%20machine%20learning%20algorithms%20for%20stock%20market%20trend%20prediction.%202018%20Second%20International%20Conference%20on%20Inventive%20Communication%20and%20Computational%20Technologies%20(ICICCT);%20Piscataway:%20IEEE;%202018.%20pp.%201003%E2%80%931007.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-20\">\n<span class=\"label\">Lauguico et al. (2019).</span><cite>Lauguico S, Concepcion R, Alejandrino J, Macasaet D, Tobias R, Bandala A, Dadios E. A fuzzy logic-based stock market trading algorithm using Bollinger bands. 2019 IEEE 11th International Conference on Humanoid, Nanotechnology, Information Technology, Communication and Control, Environment, and Management (HNICEM); Piscataway: IEEE; 2019. pp. 1–6.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Lauguico%20S,%20Concepcion%20R,%20Alejandrino%20J,%20Macasaet%20D,%20Tobias%20R,%20Bandala%20A,%20Dadios%20E.%20A%20fuzzy%20logic-based%20stock%20market%20trading%20algorithm%20using%20Bollinger%20bands.%202019%20IEEE%2011th%20International%20Conference%20on%20Humanoid,%20Nanotechnology,%20Information%20Technology,%20Communication%20and%20Control,%20Environment,%20and%20Management%20(HNICEM);%20Piscataway:%20IEEE;%202019.%20pp.%201%E2%80%936.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-21\">\n<span class=\"label\">Lokesh et al. (2018).</span><cite>Lokesh S, Mitta S, Sethia S, Kalli SR, Sudhir M. Risk analysis and prediction of the stock market using machine learning and NLP. International Journal of Applied Engineering Research. 2018;13(22):16036–16041.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Journal%20of%20Applied%20Engineering%20Research&amp;title=Risk%20analysis%20and%20prediction%20of%20the%20stock%20market%20using%20machine%20learning%20and%20NLP&amp;author=S%20Lokesh&amp;author=S%20Mitta&amp;author=S%20Sethia&amp;author=SR%20Kalli&amp;author=M%20Sudhir&amp;volume=13&amp;issue=22&amp;publication_year=2018&amp;pages=16036-16041&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-22\">\n<span class=\"label\">Maini &amp; Govinda (2017).</span><cite>Maini SS, Govinda K. Stock market prediction using data mining techniques. 2017 International Conference on Intelligent Sustainable Systems (ICISS); Piscataway: IEEE; 2017. pp. 654–661.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Maini%20SS,%20Govinda%20K.%20Stock%20market%20prediction%20using%20data%20mining%20techniques.%202017%20International%20Conference%20on%20Intelligent%20Sustainable%20Systems%20(ICISS);%20Piscataway:%20IEEE;%202017.%20pp.%20654%E2%80%93661.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-23\">\n<span class=\"label\">Majumder &amp; Hussian (2008).</span><cite>Majumder M, Hussian A. Forecasting of Indian stock market index using artificial neural network. 2008. <a href=\"https://archives.nseindia.com/content/research/FinalPaper206.pdf\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">https://archives.nseindia.com/content/research/FinalPaper206.pdf</a> <a href=\"https://archives.nseindia.com/content/research/FinalPaper206.pdf\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">https://archives.nseindia.com/content/research/FinalPaper206.pdf</a></cite>\n</li>\n<li id=\"ref-24\">\n<span class=\"label\">Malawana &amp; Rathnayaka (2020).</span><cite>Malawana MVDHP, Rathnayaka RMKT. The public sentiment analysis within big data distributed system for stock market prediction—a case study on Colombo stock exchange. 2020 5th International Conference on Information Technology Research (ICITR); Piscataway: IEEE; 2020. pp. 1–6.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Malawana%20MVDHP,%20Rathnayaka%20RMKT.%20The%20public%20sentiment%20analysis%20within%20big%20data%20distributed%20system%20for%20stock%20market%20prediction%E2%80%94a%20case%20study%20on%20Colombo%20stock%20exchange.%202020%205th%20International%20Conference%20on%20Information%20Technology%20Research%20(ICITR);%20Piscataway:%20IEEE;%202020.%20pp.%201%E2%80%936.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-25\">\n<span class=\"label\">Mandziuk &amp; Jaruszewicz (2007).</span><cite>Mandziuk J, Jaruszewicz M. Neuro-evolutionary approach to stock market prediction. International Joint Conference on Neural Networks; Piscataway: IEEE; 2007. pp. 2515–2520.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Mandziuk%20J,%20Jaruszewicz%20M.%20Neuro-evolutionary%20approach%20to%20stock%20market%20prediction.%20International%20Joint%20Conference%20on%20Neural%20Networks;%20Piscataway:%20IEEE;%202007.%20pp.%202515%E2%80%932520.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-26\">\n<span class=\"label\">Mankar et al. (2018).</span><cite>Mankar T, Hotchandani T, Madhwani M, Chidrawar A, Lifna CS. Stock market prediction based on social sentiments using machine learning. 2018 International Conference on Smart City and Emerging Technology (ICSCET); Piscataway: IEEE; 2018. pp. 1–3.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Mankar%20T,%20Hotchandani%20T,%20Madhwani%20M,%20Chidrawar%20A,%20Lifna%20CS.%20Stock%20market%20prediction%20based%20on%20social%20sentiments%20using%20machine%20learning.%202018%20International%20Conference%20on%20Smart%20City%20and%20Emerging%20Technology%20(ICSCET);%20Piscataway:%20IEEE;%202018.%20pp.%201%E2%80%933.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-27\">\n<span class=\"label\">Mehrara et al. (2010).</span><cite>Mehrara M, Moeini A, Ahrari M, Ghafari A. Using technical analysis with neural network for prediction stock price index in Tehran stock exchange. Middle Eastern Finance and Economics. 2010;6(6):50–61. doi: 10.1109/INCET51464.2021.9456376.</cite> [<a href=\"https://doi.org/10.1109/INCET51464.2021.9456376\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Middle%20Eastern%20Finance%20and%20Economics&amp;title=Using%20technical%20analysis%20with%20neural%20network%20for%20prediction%20stock%20price%20index%20in%20Tehran%20stock%20exchange&amp;author=M%20Mehrara&amp;author=A%20Moeini&amp;author=M%20Ahrari&amp;author=A%20Ghafari&amp;volume=6&amp;issue=6&amp;publication_year=2010&amp;pages=50-61&amp;doi=10.1109/INCET51464.2021.9456376&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-28\">\n<span class=\"label\">Mehta, Malhar &amp; Shankarmani (2021).</span><cite>Mehta Y, Malhar A, Shankarmani R. Stock price prediction using machine learning and sentiment analysis. 2021 2nd International Conference for Emerging Technology (INCET); Piscataway: IEEE; 2021. pp. 1–4.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Mehta%20Y,%20Malhar%20A,%20Shankarmani%20R.%20Stock%20price%20prediction%20using%20machine%20learning%20and%20sentiment%20analysis.%202021%202nd%20International%20Conference%20for%20Emerging%20Technology%20(INCET);%20Piscataway:%20IEEE;%202021.%20pp.%201%E2%80%934.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-29\">\n<span class=\"label\">Mehta, Pandya &amp; Kotecha (2021).</span><cite>Mehta P, Pandya S, Kotecha K. Harvesting social media sentiment analysis to enhance stock market prediction using deep learning. PeerJ Computer Science. 2021;7(20):e476.  doi: 10.7717/peerj-cs.476.</cite> [<a href=\"https://doi.org/10.7717/peerj-cs.476\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"/articles/PMC8053016/\" class=\"usa-link\">PMC free article</a>] [<a href=\"https://pubmed.ncbi.nlm.nih.gov/33954250/\" class=\"usa-link\">PubMed</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=PeerJ%20Computer%20Science&amp;title=Harvesting%20social%20media%20sentiment%20analysis%20to%20enhance%20stock%20market%20prediction%20using%20deep%20learning&amp;author=P%20Mehta&amp;author=S%20Pandya&amp;author=K%20Kotecha&amp;volume=7&amp;issue=20&amp;publication_year=2021&amp;pages=e476&amp;pmid=33954250&amp;doi=10.7717/peerj-cs.476&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-30\">\n<span class=\"label\">Mizuno et al. (1998).</span><cite>Mizuno H, Kosaka M, Yajima H, Komoda N. Application of neural network to technical analysis of stock market prediction. Studies in Informatic and Control. 1998;7(3):111–120.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?journal=Studies%20in%20Informatic%20and%20Control&amp;title=Application%20of%20neural%20network%20to%20technical%20analysis%20of%20stock%20market%20prediction&amp;author=H%20Mizuno&amp;author=M%20Kosaka&amp;author=H%20Yajima&amp;author=N%20Komoda&amp;volume=7&amp;issue=3&amp;publication_year=1998&amp;pages=111-120&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-31\">\n<span class=\"label\">Mohan et al. (2019).</span><cite>Mohan S, Mullapudi S, Sammeta S, Vijayvergia P, Anastasiu DC. Stock price prediction using news sentiment analysis. 2019 IEEE Fifth International Conference on Big Data Computing Service and Applications (BigDataService); Piscataway: IEEE; 2019. pp. 205–208.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Mohan%20S,%20Mullapudi%20S,%20Sammeta%20S,%20Vijayvergia%20P,%20Anastasiu%20DC.%20Stock%20price%20prediction%20using%20news%20sentiment%20analysis.%202019%20IEEE%20Fifth%20International%20Conference%20on%20Big%20Data%20Computing%20Service%20and%20Applications%20(BigDataService);%20Piscataway:%20IEEE;%202019.%20pp.%20205%E2%80%93208.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-32\">\n<span class=\"label\">Obthong et al. (2020).</span><cite>Obthong M, Tantisantiwong N, Jeamwatthanachai W, Wills G. A survey on machine learning for stock price prediction: algorithms and techniques. 2nd International Conference on Finance, Economics, Management and IT Business.2020. </cite> [<a href=\"https://scholar.google.com/scholar_lookup?Obthong%20M,%20Tantisantiwong%20N,%20Jeamwatthanachai%20W,%20Wills%20G.%20A%20survey%20on%20machine%20learning%20for%20stock%20price%20prediction:%20algorithms%20and%20techniques.%202nd%20International%20Conference%20on%20Finance,%20Economics,%20Management%20and%20IT%20Business.2020.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-33\">\n<span class=\"label\">Olivier (2007).</span><cite>Olivier C.  Neural network modeling for stock movement prediction, state of art. Clermont-Ferrand: Blaise Pascal University; 2007. </cite> [<a href=\"https://scholar.google.com/scholar_lookup?title=Neural%20network%20modeling%20for%20stock%20movement%20prediction,%20state%20of%20art&amp;author=C%20Olivier&amp;publication_year=2007&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-34\">\n<span class=\"label\">Owen &amp; Oktariani (2020).</span><cite>Owen L, Oktariani F. SENN: stock ensemble-based neural network for stock market prediction using historical stock data and sentiment analysis. 2020 International Conference on Data Science and its Applications (ICoDSA); Piscataway: IEEE; 2020. pp. 1–7.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Owen%20L,%20Oktariani%20F.%20SENN:%20stock%20ensemble-based%20neural%20network%20for%20stock%20market%20prediction%20using%20historical%20stock%20data%20and%20sentiment%20analysis.%202020%20International%20Conference%20on%20Data%20Science%20and%20its%20Applications%20(ICoDSA);%20Piscataway:%20IEEE;%202020.%20pp.%201%E2%80%937.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-35\">\n<span class=\"label\">Oyewola et al. (2021).</span><cite>Oyewola DO, Ibrahim A, Kwanamu JA, Dada EG. A new auditory algorithm in stock market prediction on oil and gas sector in Nigerian stock exchange. Soft Computing Letters. 2021;3(14):100013. doi: 10.1016/j.socl.2021.100013.</cite> [<a href=\"https://doi.org/10.1016/j.socl.2021.100013\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Soft%20Computing%20Letters&amp;title=A%20new%20auditory%20algorithm%20in%20stock%20market%20prediction%20on%20oil%20and%20gas%20sector%20in%20Nigerian%20stock%20exchange&amp;author=DO%20Oyewola&amp;author=A%20Ibrahim&amp;author=JA%20Kwanamu&amp;author=EG%20Dada&amp;volume=3&amp;issue=14&amp;publication_year=2021&amp;pages=100013&amp;doi=10.1016/j.socl.2021.100013&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-36\">\n<span class=\"label\">Ozbayoglu, Gudelek &amp; Sezer (2020).</span><cite>Ozbayoglu AM, Gudelek MU, Sezer OB. Deep learning for financial applications : a survey. Applied Soft Computing. 2020;93(8):106384. doi: 10.1016/j.asoc.2020.106384.</cite> [<a href=\"https://doi.org/10.1016/j.asoc.2020.106384\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Applied%20Soft%20Computing&amp;title=Deep%20learning%20for%20financial%20applications%20:%20a%20survey&amp;author=AM%20Ozbayoglu&amp;author=MU%20Gudelek&amp;author=OB%20Sezer&amp;volume=93&amp;issue=8&amp;publication_year=2020&amp;pages=106384&amp;doi=10.1016/j.asoc.2020.106384&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-37\">\n<span class=\"label\">Parmar et al. (2018).</span><cite>Parmar I, Agarwal N, Saxena S, Arora R, Gupta S, Dhiman H, Chouhan L. Stock market prediction using machine learning. 2018 First International Conference on Secure Cyber Computing and Communication (ICSCCC); 2018. pp. 574–576.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Parmar%20I,%20Agarwal%20N,%20Saxena%20S,%20Arora%20R,%20Gupta%20S,%20Dhiman%20H,%20Chouhan%20L.%20Stock%20market%20prediction%20using%20machine%20learning.%202018%20First%20International%20Conference%20on%20Secure%20Cyber%20Computing%20and%20Communication%20(ICSCCC);%202018.%20pp.%20574%E2%80%93576.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-38\">\n<span class=\"label\">Pathak &amp; Pathak (2020).</span><cite>Pathak A, Pathak S. Study of machine learning algorithms for stock market prediction. International Journal of Engineering Research and Technology. 2020;9(6):295–300. doi: 10.17577/IJERTV9IS060064.</cite> [<a href=\"https://doi.org/10.17577/IJERTV9IS060064\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Journal%20of%20Engineering%20Research%20and%20Technology&amp;title=Study%20of%20machine%20learning%20algorithms%20for%20stock%20market%20prediction&amp;author=A%20Pathak&amp;author=S%20Pathak&amp;volume=9&amp;issue=6&amp;publication_year=2020&amp;pages=295-300&amp;doi=10.17577/IJERTV9IS060064&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-39\">\n<span class=\"label\">Pillai &amp; Al-Malkawi (2018).</span><cite>Pillai R, Al-Malkawi H-AN. On the relationship between corporate governance and firm performance: evidence from GCC countries. Research in International Business and Finance. 2018;44(3):394–410. doi: 10.1016/j.ribaf.2017.07.110.</cite> [<a href=\"https://doi.org/10.1016/j.ribaf.2017.07.110\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Research%20in%20International%20Business%20and%20Finance&amp;title=On%20the%20relationship%20between%20corporate%20governance%20and%20firm%20performance:%20evidence%20from%20GCC%20countries&amp;author=R%20Pillai&amp;author=H-AN%20Al-Malkawi&amp;volume=44&amp;issue=3&amp;publication_year=2018&amp;pages=394-410&amp;doi=10.1016/j.ribaf.2017.07.110&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-40\">\n<span class=\"label\">Quah (2007).</span><cite>Quah T-S. Using neural network for DJIA stock selection. Engineering Letters. 2007;15(1):126–133.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?journal=Engineering%20Letters&amp;title=Using%20neural%20network%20for%20DJIA%20stock%20selection&amp;author=T-S%20Quah&amp;volume=15&amp;issue=1&amp;publication_year=2007&amp;pages=126-133&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-41\">\n<span class=\"label\">Rajendiran &amp; Priyadarsini (2021).</span><cite>Rajendiran P, Priyadarsini PLK. Survival study on stock market prediction techniques using sentimental analysis. Materials Today: Proceedings. 2021;80(1):3229–3234. doi: 10.1016/j.matpr.2021.07.217.</cite> [<a href=\"https://doi.org/10.1016/j.matpr.2021.07.217\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Materials%20Today:%20Proceedings&amp;title=Survival%20study%20on%20stock%20market%20prediction%20techniques%20using%20sentimental%20analysis&amp;author=P%20Rajendiran&amp;author=PLK%20Priyadarsini&amp;volume=80&amp;issue=1&amp;publication_year=2021&amp;pages=3229-3234&amp;doi=10.1016/j.matpr.2021.07.217&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-42\">\n<span class=\"label\">Rao, Srinivas &amp; Mohan (2020).</span><cite>Rao PS, Srinivas K, Mohan AK. A survey on stock market prediction using machine learning techniques. ICDSMLA 2019; Cham: Springer; 2020. </cite> [<a href=\"https://scholar.google.com/scholar_lookup?Rao%20PS,%20Srinivas%20K,%20Mohan%20AK.%20A%20survey%20on%20stock%20market%20prediction%20using%20machine%20learning%20techniques.%20ICDSMLA%202019;%20Cham:%20Springer;%202020.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-43\">\n<span class=\"label\">Rouf et al. (2021).</span><cite>Rouf N, Malik MB, Arif T, Sharma S, Singh S, Aich S, Kim H-C. Stock market prediction using machine learning techniques: a decade survey on methodologies, recent developments, and future directions. Electronics. 2021;10(21):2717. doi: 10.3390/electronics10212717.</cite> [<a href=\"https://doi.org/10.3390/electronics10212717\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Electronics&amp;title=Stock%20market%20prediction%20using%20machine%20learning%20techniques:%20a%20decade%20survey%20on%20methodologies,%20recent%20developments,%20and%20future%20directions&amp;author=N%20Rouf&amp;author=MB%20Malik&amp;author=T%20Arif&amp;author=S%20Sharma&amp;author=S%20Singh&amp;volume=10&amp;issue=21&amp;publication_year=2021&amp;pages=2717&amp;doi=10.3390/electronics10212717&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-44\">\n<span class=\"label\">Sadia et al. (2019).</span><cite>Sadia KH, Sharma A, Paul A, Padhi S, Sanyal S. Stock market prediction using machine learning algorithms. International Journal of Engineering and Advanced Technology (IJEAT) 2019;8(4):25–31.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Journal%20of%20Engineering%20and%20Advanced%20Technology%20(IJEAT)&amp;title=Stock%20market%20prediction%20using%20machine%20learning%20algorithms&amp;author=KH%20Sadia&amp;author=A%20Sharma&amp;author=A%20Paul&amp;author=S%20Padhi&amp;author=S%20Sanyal&amp;volume=8&amp;issue=4&amp;publication_year=2019&amp;pages=25-31&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-45\">\n<span class=\"label\">Schierholt &amp; Dagli (1996).</span><cite>Schierholt K, Dagli CH. Stock market prediction using different neural network classification architectures. Proceedings of the IEEE/IAFE 1996 Conference on Computational Intelligence for Financial Engineering; Piscataway: IEEE; 1996. pp. 72–78.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Schierholt%20K,%20Dagli%20CH.%20Stock%20market%20prediction%20using%20different%20neural%20network%20classification%20architectures.%20Proceedings%20of%20the%20IEEE/IAFE%201996%20Conference%20on%20Computational%20Intelligence%20for%20Financial%20Engineering;%20Piscataway:%20IEEE;%201996.%20pp.%2072%E2%80%9378.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-46\">\n<span class=\"label\">Sharma, Bhuriya &amp; Singh (2017).</span><cite>Sharma A, Bhuriya D, Singh U. Survey of stock market prediction using machine learning approach. 2017 International Conference of Electronics, Communication and Aerospace Technology (ICECA); Piscataway: IEEE; 2017. pp. 506–509.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Sharma%20A,%20Bhuriya%20D,%20Singh%20U.%20Survey%20of%20stock%20market%20prediction%20using%20machine%20learning%20approach.%202017%20International%20Conference%20of%20Electronics,%20Communication%20and%20Aerospace%20Technology%20(ICECA);%20Piscataway:%20IEEE;%202017.%20pp.%20506%E2%80%93509.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-47\">\n<span class=\"label\">Sharma &amp; Juneja (2017).</span><cite>Sharma N, Juneja A. Combining of random forest estimates using LSboost for stock market index prediction. 2017 2nd International Conference for Convergence in Technology (I2CT); Piscataway: IEEE; 2017. pp. 1199–1202.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Sharma%20N,%20Juneja%20A.%20Combining%20of%20random%20forest%20estimates%20using%20LSboost%20for%20stock%20market%20index%20prediction.%202017%202nd%20International%20Conference%20for%20Convergence%20in%20Technology%20(I2CT);%20Piscataway:%20IEEE;%202017.%20pp.%201199%E2%80%931202.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-48\">\n<span class=\"label\">Singh (2022).</span><cite>Singh G. Machine learning models in stock market prediction. International Journal of Innovative Technology and Exploring Engineering. 2022;11(3):18–28. doi: 10.35940/ijitee.C9733.0111322.</cite> [<a href=\"https://doi.org/10.35940/ijitee.C9733.0111322\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=International%20Journal%20of%20Innovative%20Technology%20and%20Exploring%20Engineering&amp;title=Machine%20learning%20models%20in%20stock%20market%20prediction&amp;author=G%20Singh&amp;volume=11&amp;issue=3&amp;publication_year=2022&amp;pages=18-28&amp;doi=10.35940/ijitee.C9733.0111322&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-49\">\n<span class=\"label\">Soni, Tewari &amp; Krishnan (2022).</span><cite>Soni P, Tewari Y, Krishnan D. Machine learning approaches in stock price prediction: a systematic review. Journal of Physics: Conference Series. 2022;2161(1):12065. doi: 10.1088/1742-6596/2161/1/012065.</cite> [<a href=\"https://doi.org/10.1088/1742-6596/2161/1/012065\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Journal%20of%20Physics:%20Conference%20Series&amp;title=Machine%20learning%20approaches%20in%20stock%20price%20prediction:%20a%20systematic%20review&amp;author=P%20Soni&amp;author=Y%20Tewari&amp;author=D%20Krishnan&amp;volume=2161&amp;issue=1&amp;publication_year=2022&amp;pages=12065&amp;doi=10.1088/1742-6596/2161/1/012065&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-50\">\n<span class=\"label\">Strader et al. (2020).</span><cite>Strader TJ, Rozycki JJ, Root TH, Huang Y-HJ. Machine learning stock market prediction studies: review and research directions. Journal of International Technology and Information Management. 2020;28(4):63–83. doi: 10.58729/1941-6679.1435.</cite> [<a href=\"https://doi.org/10.58729/1941-6679.1435\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Journal%20of%20International%20Technology%20and%20Information%20Management&amp;title=Machine%20learning%20stock%20market%20prediction%20studies:%20review%20and%20research%20directions&amp;author=TJ%20Strader&amp;author=JJ%20Rozycki&amp;author=TH%20Root&amp;author=Y-HJ%20Huang&amp;volume=28&amp;issue=4&amp;publication_year=2020&amp;pages=63-83&amp;doi=10.58729/1941-6679.1435&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-52\">\n<span class=\"label\">Umadevi et al. (2018).</span><cite>Umadevi KS, Gaonka A, Kulkarni R, Kannan RJ. Analysis of stock market using streaming data framework. 2018 International Conference on Advances in Computing, Communications and Informatics (ICACCI); Piscataway: IEEE; 2018. pp. 1388–1390.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Umadevi%20KS,%20Gaonka%20A,%20Kulkarni%20R,%20Kannan%20RJ.%20Analysis%20of%20stock%20market%20using%20streaming%20data%20framework.%202018%20International%20Conference%20on%20Advances%20in%20Computing,%20Communications%20and%20Informatics%20(ICACCI);%20Piscataway:%20IEEE;%202018.%20pp.%201388%E2%80%931390.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-53\">\n<span class=\"label\">Umer, Awais &amp; Muzammul (2019).</span><cite>Umer M, Awais M, Muzammul M. Stock market prediction using machine learning (ML) algorithms. ADCAIJ: Advances in Distributed Computing and Artificial Intelligence Journal. 2019;8(4):97–116. doi: 10.14201/ADCAIJ20198497116.</cite> [<a href=\"https://doi.org/10.14201/ADCAIJ20198497116\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=ADCAIJ:%20Advances%20in%20Distributed%20Computing%20and%20Artificial%20Intelligence%20Journal&amp;title=Stock%20market%20prediction%20using%20machine%20learning%20(ML)%20algorithms&amp;author=M%20Umer&amp;author=M%20Awais&amp;author=M%20Muzammul&amp;volume=8&amp;issue=4&amp;publication_year=2019&amp;pages=97-116&amp;doi=10.14201/ADCAIJ20198497116&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-54\">\n<span class=\"label\">Usmani et al. (2016).</span><cite>Usmani M, Adil SH, Raza K, Ali SSA.  2016 IEEE 3rd International Conference on Computer and Information Sciences (ICCOINS) Pistacatay: IEEE; 2016. Stock market prediction using machine learning techniques; pp. 322–327.</cite> [<a href=\"https://doi.org/10.1109/ICCOINS.2016.7783235\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?title=2016%20IEEE%203rd%20International%20Conference%20on%20Computer%20and%20Information%20Sciences%20(ICCOINS)&amp;author=M%20Usmani&amp;author=SH%20Adil&amp;author=K%20Raza&amp;author=SSA%20Ali&amp;publication_year=2016&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-55\">\n<span class=\"label\">Vijh et al. (2020).</span><cite>Vijh M, Chandola D, Tikkiwal VA, Kumar A. Stock closing price prediction using machine learning techniques. Procedia Computer Science. 2020;167:599–606. doi: 10.1016/j.procs.2020.03.326.</cite> [<a href=\"https://doi.org/10.1016/j.procs.2020.03.326\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Procedia%20Computer%20Science&amp;title=Stock%20closing%20price%20prediction%20using%20machine%20learning%20techniques&amp;author=M%20Vijh&amp;author=D%20Chandola&amp;author=VA%20Tikkiwal&amp;author=A%20Kumar&amp;volume=167&amp;publication_year=2020&amp;pages=599-606&amp;doi=10.1016/j.procs.2020.03.326&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-56\">\n<span class=\"label\">Vlasenko et al. (2018).</span><cite>Vlasenko A, Vynokurova O, Vlasenko N, Peleshko M. A hybrid neuro-fuzzy model for stock market time-series prediction. 2018 IEEE Second International Conference on Data Stream Mining &amp; Processing (DSMP); Piscataway: IEEE; 2018. pp. 352–355.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Vlasenko%20A,%20Vynokurova%20O,%20Vlasenko%20N,%20Peleshko%20M.%20A%20hybrid%20neuro-fuzzy%20model%20for%20stock%20market%20time-series%20prediction.%202018%20IEEE%20Second%20International%20Conference%20on%20Data%20Stream%20Mining%20&amp;%20Processing%20(DSMP);%20Piscataway:%20IEEE;%202018.%20pp.%20352%E2%80%93355.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-57\">\n<span class=\"label\">Wang et al. (2022a).</span><cite>Wang C, Chen Y, Zhang S, Zhang Q. Stock market index prediction using deep transformer model. Expert Systems with Applications. 2022a;208:118128. doi: 10.1016/j.eswa.2022.118128.</cite> [<a href=\"https://doi.org/10.1016/j.eswa.2022.118128\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Expert%20Systems%20with%20Applications&amp;title=Stock%20market%20index%20prediction%20using%20deep%20transformer%20model&amp;author=C%20Wang&amp;author=Y%20Chen&amp;author=S%20Zhang&amp;author=Q%20Zhang&amp;volume=208&amp;publication_year=2022a&amp;pages=118128&amp;doi=10.1016/j.eswa.2022.118128&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-58\">\n<span class=\"label\">Wang et al. (2022b).</span><cite>Wang C, Liang H, Wang B, Cui X, Xu Y. MG-Conv: a spatiotemporal multi-graph convolutional neural network for stock market index trend prediction. Computers and Electrical Engineering. 2022b;103:108285. doi: 10.1016/j.compeleceng.2022.108285.</cite> [<a href=\"https://doi.org/10.1016/j.compeleceng.2022.108285\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Computers%20and%20Electrical%20Engineering&amp;title=MG-Conv:%20a%20spatiotemporal%20multi-graph%20convolutional%20neural%20network%20for%20stock%20market%20index%20trend%20prediction&amp;author=C%20Wang&amp;author=H%20Liang&amp;author=B%20Wang&amp;author=X%20Cui&amp;author=Y%20Xu&amp;volume=103&amp;publication_year=2022b&amp;pages=108285&amp;doi=10.1016/j.compeleceng.2022.108285&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-59\">\n<span class=\"label\">White (1988).</span><cite>White H. Economic prediction using neural networks: the case of IBM daily stock returns. IEEE International Conference on Neural Networks. 1988;2:451–458. doi: 10.1109/ICNN.1988.23959.</cite> [<a href=\"https://doi.org/10.1109/ICNN.1988.23959\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=IEEE%20International%20Conference%20on%20Neural%20Networks&amp;title=Economic%20prediction%20using%20neural%20networks:%20the%20case%20of%20IBM%20daily%20stock%20returns&amp;author=H%20White&amp;volume=2&amp;publication_year=1988&amp;pages=451-458&amp;doi=10.1109/ICNN.1988.23959&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-60\">\n<span class=\"label\">Yang &amp; Suash (2009).</span><cite>Yang X-S, Suash D. Cuckoo search via Lévy flights. 2009 World Congress on Nature &amp; Biologically Inspired Computing (NaBIC); Piscataway: IEEE; 2009. pp. 210–214.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Yang%20X-S,%20Suash%20D.%20Cuckoo%20search%20via%20L%C3%A9vy%20flights.%202009%20World%20Congress%20on%20Nature%20&amp;%20Biologically%20Inspired%20Computing%20(NaBIC);%20Piscataway:%20IEEE;%202009.%20pp.%20210%E2%80%93214.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-61\">\n<span class=\"label\">Yang et al. (2023).</span><cite>Yang J, Zhang W, Zhang X, Zhou J, Zhang P. Enhancing stock movement prediction with market index and curriculum learning. Expert Systems with Applications. 2023;213(Part A):118800. doi: 10.1016/j.eswa.2022.118800.</cite> [<a href=\"https://doi.org/10.1016/j.eswa.2022.118800\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Expert%20Systems%20with%20Applications&amp;title=Enhancing%20stock%20movement%20prediction%20with%20market%20index%20and%20curriculum%20learning&amp;author=J%20Yang&amp;author=W%20Zhang&amp;author=X%20Zhang&amp;author=J%20Zhou&amp;author=P%20Zhang&amp;volume=213&amp;issue=Part%20A&amp;publication_year=2023&amp;pages=118800&amp;doi=10.1016/j.eswa.2022.118800&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-62\">\n<span class=\"label\">Yun, Yoon &amp; Won (2021).</span><cite>Yun KK, Yoon SW, Won D. Prediction of stock price direction using hybrid GA-XGBoost algorithm with a three stage feature engineering process. Expert Systems with Applications. 2021;186:115716. doi: 10.1016/j.eswa.2021.115716.</cite> [<a href=\"https://doi.org/10.1016/j.eswa.2021.115716\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=Expert%20Systems%20with%20Applications&amp;title=Prediction%20of%20stock%20price%20direction%20using%20hybrid%20GA-XGBoost%20algorithm%20with%20a%20three%20stage%20feature%20engineering%20process&amp;author=KK%20Yun&amp;author=SW%20Yoon&amp;author=D%20Won&amp;volume=186&amp;publication_year=2021&amp;pages=115716&amp;doi=10.1016/j.eswa.2021.115716&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-63\">\n<span class=\"label\">Zhao et al. (2018).</span><cite>Zhao W, Guan Z, Chen L, He X, Cai D, Wang B, Wang Q. Weakly-supervised deep embedding for product review sentiment analysis. IEEE Transactions on Knowledge and Data Engineering. 2018;30(1):185–197. doi: 10.1109/TKDE.2017.2756658.</cite> [<a href=\"https://doi.org/10.1109/TKDE.2017.2756658\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">DOI</a>] [<a href=\"https://scholar.google.com/scholar_lookup?journal=IEEE%20Transactions%20on%20Knowledge%20and%20Data%20Engineering&amp;title=Weakly-supervised%20deep%20embedding%20for%20product%20review%20sentiment%20analysis&amp;author=W%20Zhao&amp;author=Z%20Guan&amp;author=L%20Chen&amp;author=X%20He&amp;author=D%20Cai&amp;volume=30&amp;issue=1&amp;publication_year=2018&amp;pages=185-197&amp;doi=10.1109/TKDE.2017.2756658&amp;\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-64\">\n<span class=\"label\">Zhao &amp; Wang (2015).</span><cite>Zhao L, Wang L. Price trend prediction of stock market using outlier data mining algorithm. 2015 IEEE Fifth International Conference on Big Data and Cloud Computing; Piscataway: IEEE; 2015. pp. 93–98.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Zhao%20L,%20Wang%20L.%20Price%20trend%20prediction%20of%20stock%20market%20using%20outlier%20data%20mining%20algorithm.%202015%20IEEE%20Fifth%20International%20Conference%20on%20Big%20Data%20and%20Cloud%20Computing;%20Piscataway:%20IEEE;%202015.%20pp.%2093%E2%80%9398.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n<li id=\"ref-65\">\n<span class=\"label\">Zhi et al. (2017).</span><cite>Zhi H, Zhang J, Xue Z, Zhang Y. Stock market forecast based on wavelet neural network optimized by Cuckoo search. 2017 8th IEEE International Conference on Software Engineering and Service Science (ICSESS); Piscataway: IEEE; 2017. pp. 560–562.</cite> [<a href=\"https://scholar.google.com/scholar_lookup?Zhi%20H,%20Zhang%20J,%20Xue%20Z,%20Zhang%20Y.%20Stock%20market%20forecast%20based%20on%20wavelet%20neural%20network%20optimized%20by%20Cuckoo%20search.%202017%208th%20IEEE%20International%20Conference%20on%20Software%20Engineering%20and%20Service%20Science%20(ICSESS);%20Piscataway:%20IEEE;%202017.%20pp.%20560%E2%80%93562.\" class=\"usa-link usa-link--external\" data-ga-action=\"click_feat_suppl\" target=\"_blank\" rel=\"noopener noreferrer\">Google Scholar</a>]</li>\n</ul></section></section><section id=\"_ad93_\" lang=\"en\" class=\"associated-data\"><h2 class=\"pmc_sec_title\" data-anchor-id=\"_ad93_\"><a id=\"_ad93_-anchor\" data-anchor-id=\"_ad93_\" class=\"usa-anchor\"></a>Associated Data</h2>\n<p class=\"font-secondary\"><em>This section collects any data citations, data availability statements, or supplementary materials included in this article.</em></p>\n<section id=\"_adda93_\" lang=\"en\" class=\"data-availability-statement\"><h3 class=\"pmc_sec_title\">Data Availability Statement</h3>\n<p>The following information was supplied regarding data availability:</p>\n<p>This is a survey article and hence did not utilize code/data.</p></section></section></section><footer class=\"p courtesy-note font-secondary font-sm text-center\"><hr class=\"headless\">\n<p>Articles from PeerJ Computer Science are provided here courtesy of <strong>PeerJ, Inc</strong></p></footer></section></article>\n\n                      \n\n                    </main>\n                </div>\n            </div>\n        </div>\n\n        \n\n\n\n<!-- Secondary navigation placeholder -->\n<div class=\"pmc-sidenav desktop:grid-col-4 display-flex\">\n    <section class=\"pmc-sidenav__container\" aria-label=\"Article resources and navigation\">\n        <button type=\"button\" class=\"usa-button pmc-sidenav__container__close usa-button--unstyled\">\n            <img src=\"/static/img/usa-icons/close.svg\" role=\"img\" alt=\"Close\">\n        </button>\n    <div class=\"display-none desktop:display-block\">\n       <section class=\"margin-top-4 desktop:margin-top-0\">\n              <h2 class=\"margin-top-0\">ACTIONS</h2>\n           <ul class=\"usa-list usa-list--unstyled usa-list--actions\">\n               \n               <li>\n                     <a href=\"https://doi.org/10.7717/peerj-cs.1700\" class=\"usa-button usa-button--outline width-24 font-xs display-inline-flex flex-align-center flex-justify-start padding-left-1\" target=\"_blank\" rel=\"noreferrer noopener\" data-ga-category=\"actions\" data-ga-action=\"click\" data-ga-label=\"publisher_link_desktop\">\n                         <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                            <use xlink:href=\"/static/img/sprite.svg#launch\"></use>\n                         </svg>\n                         <span class=\"display-inline-flex flex-justify-center flex-1 padding-right-2\">View on publisher site</span>\n                     </a>\n               </li>\n               \n               \n               <li>\n                    <a href=\"pdf/peerj-cs-10-1700.pdf\" class=\"usa-button usa-button--outline width-24 display-inline-flex flex-align-center flex-justify-start padding-left-1\" data-ga-category=\"actions\" data-ga-action=\"click\" data-ga-label=\"pdf_download_desktop\">\n                         <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                            <use xlink:href=\"/static/img/sprite.svg#file_download\"></use>\n                        </svg>\n                        <span class=\"display-inline-flex flex-justify-center flex-1\">PDF (2.9&nbsp;MB)</span>\n                    </a>\n               </li>\n               \n                \n               <li>\n                   <button role=\"button\" class=\"usa-button width-24 citation-dialog-trigger display-inline-flex flex-align-center flex-justify-start padding-left-1\" aria-label=\"Open dialog with citation text in different styles\" data-ga-category=\"actions\" data-ga-action=\"open\" data-ga-label=\"cite_desktop\" data-all-citations-url=\"/resources/citations/10909160/\" data-citation-style=\"nlm\" data-download-format-link=\"/resources/citations/10909160/export/\">\n                        <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                            <use xlink:href=\"/static/img/sprite.svg#format_quote\"></use>\n                        </svg>\n                       <span class=\"display-inline-flex flex-justify-center flex-1 button-label\">Cite</span>\n                    </button>\n               </li>\n                \n               <li>\n\n                        <button class=\"usa-button width-24 collections-dialog-trigger collections-button display-inline-flex flex-align-center flex-justify-start padding-left-1 collections-button-empty\" aria-label=\"Save article in MyNCBI collections.\" data-ga-category=\"actions\" data-ga-action=\"click\" data-ga-label=\"collections_button_desktop\" data-collections-open-dialog-enabled=\"false\" data-collections-open-dialog-url=\"https://account.ncbi.nlm.nih.gov/?back_url=https%3A%2F%2Fpmc.ncbi.nlm.nih.gov%2Farticles%2FPMC10909160%2F%23open-collections-dialog\" data-in-collections=\"false\">\n                            <svg class=\"usa-icon width-3 height-3 usa-icon--bookmark-full\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                                <use xlink:href=\"/static/img/action-bookmark-full.svg#icon\"></use>\n                            </svg>\n                            <svg class=\"usa-icon width-3 height-3 usa-icon--bookmark-empty\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                                <use xlink:href=\"/static/img/action-bookmark-empty.svg#icon\"></use>\n                            </svg>\n                            <span class=\"display-inline-flex flex-justify-center flex-1\">Collections</span>\n                       </button>\n               </li>\n               <li class=\"pmc-permalink\">\n                    <button type=\"button\" class=\"usa-button width-24 display-inline-flex flex-align-center flex-justify padding-left-1 shadow-none\" aria-label=\"Show article permalink\" aria-expanded=\"false\" aria-haspopup=\"true\" data-ga-category=\"actions\" data-ga-action=\"open\" data-ga-label=\"permalink_desktop\">\n                         <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\" hidden=\"\">\n                            <use xlink:href=\"/static/img/sprite.svg#share\"></use>\n                        </svg>\n                        <span class=\"display-inline-flex flex-justify-center flex-1 button-label\">Permalink</span>\n                    </button>\n                   \n\n<div class=\"pmc-permalink__dropdown\" hidden=\"\">\n    <div class=\"pmc-permalink__dropdown__container\">\n          <h2 class=\"usa-modal__heading margin-top-0 margin-bottom-2 text-uppercase font-sans-xs\">PERMALINK</h2>\n          <div class=\"pmc-permalink__dropdown__content\">\n              <input type=\"text\" class=\"usa-input\" value=\"https://pmc.ncbi.nlm.nih.gov/articles/PMC10909160/\" aria-label=\"Article permalink\">\n              <button class=\"usa-button display-inline-flex pmc-permalink__dropdown__copy__btn margin-right-0\" title=\"Copy article permalink\" data-ga-category=\"save_share\" data-ga-action=\"link\" data-ga-label=\"copy_link\" aria-expanded=\"false\">\n                  <svg class=\"usa-icon\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#content_copy\"></use>\n                  </svg>\n                  <span class=\"margin-left-1\">Copy</span>\n              </button>\n          </div>\n    </div>\n</div>\n               </li>\n           </ul>\n       </section>\n     </div>\n\n        <section class=\"pmc-resources margin-top-6 desktop:margin-top-4\" data-page-path=\"/articles/PMC10909160/\">\n            <h2 class=\"margin-top-0\">RESOURCES</h2>\n            \n                <div class=\"usa-accordion usa-accordion--multiselectable\" data-allow-multiple=\"\">\n                    <h3 class=\"usa-accordion__heading\">\n                        <button type=\"button\" class=\"usa-accordion__button\" aria-expanded=\"false\" aria-controls=\"resources-similar-articles\" data-ga-category=\"resources_accordion\" data-ga-action=\"open_similar_articles\" data-ga-label=\"/articles/PMC10909160/\" data-action-open=\"open_similar_articles\" data-action-close=\"close_similar_articles\">\n                            Similar articles\n                        </button>\n                    </h3>\n                    <div id=\"resources-similar-articles\" class=\"usa-accordion__content usa-prose\" data-source-url=\"/resources/similar-article-links/38435546/\" hidden=\"\">\n                        \n                    </div>\n                    <h3 class=\"usa-accordion__heading\">\n                        <button type=\"button\" class=\"usa-accordion__button\" aria-expanded=\"false\" aria-controls=\"resources-cited-by-other-articles\" data-ga-category=\"resources_accordion\" data-ga-action=\"open_cited_by\" data-ga-label=\"/articles/PMC10909160/\" data-action-open=\"open_cited_by\" data-action-close=\"close_cited_by\">\n                             Cited by other articles\n                        </button>\n                    </h3>\n                    <div id=\"resources-cited-by-other-articles\" class=\"usa-accordion__content usa-prose\" data-source-url=\"/resources/cited-by-links/38435546/\" hidden=\"\">\n                          \n                    </div>\n                    \n                        <h3 class=\"usa-accordion__heading\">\n                            <button type=\"button\" class=\"usa-accordion__button\" aria-expanded=\"false\" aria-controls=\"resources-links-to-ncbi-databases\" data-ga-category=\"resources_accordion\" data-ga-action=\"open_NCBI_links\" data-ga-label=\"/articles/PMC10909160/\" data-action-open=\"open_NCBI_links\" data-action-close=\"close_NCBI_link\">\n                                 Links to NCBI Databases\n                            </button>\n                        </h3>\n                        <div id=\"resources-links-to-ncbi-databases\" class=\"usa-accordion__content usa-prose\" data-source-url=\"/resources/db-links/10909160/\" hidden=\"\">\n                        </div>\n                    \n                    \n                </div>\n            \n        </section>\n\n\n        <section class=\"usa-in-page-nav usa-in-page-nav--wide margin-top-6 desktop:margin-top-4\" data-title-text=\"On this page\" data-title-heading-level=\"h2\" data-scroll-offset=\"0\" data-root-margin=\"-10% 0px -80% 0px\" data-main-content-selector=\"main\" data-threshold=\"1\"><nav aria-label=\"On this page\" class=\"usa-in-page-nav__nav\"><h2 class=\"usa-in-page-nav__heading\" tabindex=\"0\">On this page</h2><ul class=\"usa-in-page-nav__list\"><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"abstract1\" href=\"#abstract1\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Abstract\">Abstract</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"sec1\" href=\"#sec1\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Introduction\">Introduction</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"sec2\" href=\"#sec2\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Research method\">Research method</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"sec7\" href=\"#sec7\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Challenges and discussion\">Challenges and discussion</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"sec13\" href=\"#sec13\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Conclusions\">Conclusions</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"sec14\" href=\"#sec14\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Future scope\">Future scope</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"funding-statement1\" href=\"#funding-statement1\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Funding Statement\">Funding Statement</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"sec15\" href=\"#sec15\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Additional Information and Declarations\">Additional Information and Declarations</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"ref-list1\" href=\"#ref-list1\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"References\">References</a></li><li class=\"usa-in-page-nav__item\"><a data-anchor-id=\"_ad93_\" href=\"#_ad93_\" class=\"usa-in-page-nav__link\" data-ga-category=\"article_side_nav\" data-ga-action=\"click\" data-ga-label=\"Associated Data\">Associated Data</a></li></ul></nav></section>\n    </section>\n</div>\n\n\n        \n\n<div class=\"overlay citation-dialog-overlay\" role=\"dialog\" aria-label=\"Citation Dialog\">\n    <div class=\"dialog citation-dialog\" aria-hidden=\"true\" role=\"document\">\n        <div class=\"display-inline-flex flex-align-center flex-justify width-full margin-bottom-2\">\n            <h2 class=\"usa-modal__heading margin-0\">Cite</h2>\n             <button type=\"button\" class=\"usa-button usa-button--unstyled close-overlay text-black width-auto\" tabindex=\"1\" data-pinger-ignore=\"true\">\n                <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#close\"></use>\n                </svg>\n             </button>\n        </div>\n\n        \n\n<div class=\"citation-text-block\">\n  <div class=\"citation-text margin-bottom-2\"></div>\n  <ul class=\"usa-list usa-list--unstyled display-inline-flex flex-justify width-full flex-align-center\">\n      <li>\n        <button class=\"usa-button usa-button--unstyled text-no-underline display-flex flex-align-center copy-button dialog-focus\" data-ga-category=\"save_share\" data-ga-action=\"cite\" data-ga-label=\"copy\" tabindex=\"2\">\n            <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                <use xlink:href=\"/static/img/sprite.svg#content_copy\"></use>\n            </svg>\n            <span>Copy</span>\n        </button>\n      </li>\n      <li>\n          <a href=\"#\" role=\"button\" class=\"usa-button usa-button--unstyled text-no-underline display-flex flex-align-center export-button\" data-ga-category=\"save_share\" data-ga-action=\"cite\" data-ga-label=\"download\" title=\"Download a file for external citation management software\" tabindex=\"3\">\n                <svg class=\"usa-icon width-3 height-3\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n                    <use xlink:href=\"/static/img/sprite.svg#file_download\"></use>\n                </svg>\n                <span class=\"display-none mobile-lg:display-inline\">Download .nbib</span>\n                <span class=\"display-inline mobile-lg:display-none\">.nbib</span>\n            </a>\n      </li>\n      <li>\n          \n\n<div class=\"display-inline-flex flex-align-center\">\n  <label class=\"usa-label margin-top-0\">Format:</label>\n  <select aria-label=\"Format\" class=\"usa-select citation-style-selector padding-1 margin-top-0 border-0 padding-right-4\" tabindex=\"4\">\n    \n      <option data-style-url-name=\"ama\" value=\"AMA\">\n        AMA\n      </option>\n    \n      <option data-style-url-name=\"apa\" value=\"APA\">\n        APA\n      </option>\n    \n      <option data-style-url-name=\"mla\" value=\"MLA\">\n        MLA\n      </option>\n    \n      <option data-style-url-name=\"nlm\" value=\"NLM\" selected=\"selected\">\n        NLM\n      </option>\n    \n  </select>\n</div>\n      </li>\n  </ul>\n<div class=\"dots-loading-indicator citation-loading-indicator\">\n        <div class=\"dot dot-1\"></div>\n        <div class=\"dot dot-2\"></div>\n        <div class=\"dot dot-3\"></div>\n      </div></div>\n    </div>\n</div>\n\n        <div class=\"overlay collections-dialog-overlay\" role=\"dialog\">\n  <div id=\"collections-action-dialog\" class=\"dialog collections-dialog\" aria-hidden=\"true\" role=\"document\">\n   <div class=\"display-inline-flex flex-align-center flex-justify width-full margin-bottom-2\">\n        <h2 class=\"usa-modal__heading margin-0\">Add to Collections</h2>\n    </div>\n    <div class=\"collections-action-panel action-panel\">\n      \n\n\n<form id=\"collections-action-dialog-form\" class=\"usa-form maxw-full collections-action-panel-form action-panel-content action-form action-panel-smaller-selectors\" data-existing-collections-url=\"/list-existing-collections/\" data-add-to-existing-collection-url=\"/add-to-existing-collection/\" data-create-and-add-to-new-collection-url=\"/create-and-add-to-new-collection/\" data-myncbi-max-collection-name-length=\"100\" data-collections-root-url=\"https://www.ncbi.nlm.nih.gov/myncbi/collections/\">\n\n    <input type=\"hidden\" name=\"csrfmiddlewaretoken\" value=\"0WAEGA2Wk4oqea3ECvVOub57NHvzlyMh3GRe3MKeWeTO6TwpZziRH5DETnjm1E23\">\n\n    <fieldset class=\"usa-fieldset margin-bottom-2\">\n        <div class=\"usa-radio\">\n            <input type=\"radio\" id=\"collections-action-dialog-new\" class=\"usa-radio__input usa-radio__input--tile collections-new  margin-top-0\" name=\"collections\" value=\"new\" data-ga-category=\"collections_button\" data-ga-action=\"click\" data-ga-label=\"collections_radio_new\">\n            <label class=\"usa-radio__label\" for=\"collections-action-dialog-new\">Create a new collection</label>\n        </div>\n        <div class=\"usa-radio\">\n            <input type=\"radio\" id=\"collections-action-dialog-existing\" class=\"usa-radio__input usa-radio__input--tile collections-existing\" name=\"collections\" value=\"existing\" checked=\"true\" data-ga-category=\"collections_button\" data-ga-action=\"click\" data-ga-label=\"collections_radio_existing\">\n            <label class=\"usa-radio__label\" for=\"collections-action-dialog-existing\">Add to an existing collection</label>\n        </div>\n    </fieldset>\n\n    <fieldset class=\"usa-fieldset margin-bottom-2\">\n        <div class=\"action-panel-control-wrap new-collections-controls\">\n           <label for=\"collections-action-dialog-add-to-new\" class=\"usa-label margin-top-0\">\n                Name your collection\n               <abbr title=\"required\" class=\"usa-hint usa-hint--required text-no-underline\">*</abbr>\n          </label>\n          <input type=\"text\" name=\"add-to-new-collection\" id=\"collections-action-dialog-add-to-new\" class=\"usa-input collections-action-add-to-new\" pattern=\"[^&quot;&amp;=<>/]*\" title=\"The following characters are not allowed in the Name field: &quot;&amp;=<>/\" maxlength=\"\" data-ga-category=\"collections_button\" data-ga-action=\"create_collection\" data-ga-label=\"non_favorties_collection\" required=\"\">\n        </div>\n        <div class=\"action-panel-control-wrap existing-collections-controls\">\n             <label for=\"collections-action-dialog-add-to-existing\" class=\"usa-label margin-top-0\">\n                Choose a collection\n              </label>\n              <select id=\"collections-action-dialog-add-to-existing\" class=\"usa-select collections-action-add-to-existing\" data-ga-category=\"collections_button\" data-ga-action=\"select_collection\" data-ga-label=\"($('.collections-action-add-to-existing').val() === 'Favorites') ? 'Favorites' : 'non_favorites_collection'\">\n              </select>\n              <div class=\"collections-retry-load-on-error usa-input-error-message selection-validation-message\">\n                Unable to load your collection due to an error<br>\n                <a href=\"#\">Please try again</a>\n              </div>\n        </div>\n    </fieldset>\n\n    <div class=\"display-inline-flex\">\n        <button class=\"usa-button margin-top-0 action-panel-submit\" type=\"submit\" data-loading-label=\"Adding...\" data-pinger-ignore=\"\" data-ga-category=\"collections_button\" data-ga-action=\"click\" data-ga-label=\"add\">\n          Add\n        </button>\n        <button class=\"usa-button usa-button--outline margin-top-0 action-panel-cancel\" aria-label=\"Close 'Add to Collections' panel\" ref=\"linksrc=close_collections_panel\" data-ga-category=\"collections_button\" data-ga-action=\"click\" data-ga-label=\"cancel\">\n          Cancel\n        </button>\n    </div>\n</form>\n    </div>\n  </div>\n</div>\n\n        \n\n      </div>\n    </div>\n  </div>\n\n\n\n        \n    \n    \n\n<footer class=\"ncbi-footer ncbi-dark-background \">\n    \n        <div class=\"ncbi-footer__icon-section\">\n            <div class=\"ncbi-footer__social-header\">\n                Follow NCBI\n            </div>\n\n            <div class=\"grid-container ncbi-footer__ncbi-social-icons-container\">\n                \n                    <a href=\"https://twitter.com/ncbi\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--gray\" target=\"_blank\" rel=\"noreferrer noopener\">\n                        <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                            <path d=\"m6.067 8 10.81 13.9L6 33.2h4.2l8.4-9.1 7.068 9.1H34L22.8 18.5 31.9 8h-3.5l-7.7 8.4L14.401 8H6.067Zm3.6 1.734h3.266l16.8 21.732H26.57L9.668 9.734Z\">\n                            </path>\n                        </svg>\n                        <span class=\"usa-sr-only\">NCBI on X (formerly known as Twitter)</span>\n                    </a>\n                \n\n                \n                    <a href=\"https://www.facebook.com/ncbi.nlm\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--gray\" target=\"_blank\" rel=\"noreferrer noopener\">\n                        <svg width=\"16\" height=\"29\" focusable=\"false\" aria-hidden=\"true\" viewBox=\"0 0 16 29\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                            <path d=\"M3.8809 21.4002C3.8809 19.0932 3.8809 16.7876 3.8809 14.478C3.8809 14.2117 3.80103 14.1452 3.54278 14.1492C2.53372 14.1638 1.52334 14.1492 0.514288 14.1598C0.302626 14.1598 0.248047 14.0972 0.248047 13.8936C0.256034 12.4585 0.256034 11.0239 0.248047 9.58978C0.248047 9.37013 0.302626 9.30224 0.528931 9.3049C1.53798 9.31688 2.54837 9.3049 3.55742 9.31555C3.80103 9.31555 3.8809 9.26097 3.87957 9.00272C3.87158 8.00565 3.85428 7.00592 3.90753 6.00884C3.97142 4.83339 4.31487 3.73115 5.04437 2.78467C5.93095 1.63318 7.15699 1.09005 8.56141 0.967577C10.5582 0.79319 12.555 0.982221 14.5518 0.927641C14.7102 0.927641 14.7462 0.99287 14.7449 1.13664C14.7449 2.581 14.7449 4.02668 14.7449 5.47104C14.7449 5.67604 14.6517 5.68669 14.4946 5.68669C13.4523 5.68669 12.4113 5.68669 11.3703 5.68669C10.3506 5.68669 9.92057 6.10868 9.90593 7.13904C9.89661 7.7647 9.91525 8.39303 9.89794 9.01869C9.88995 9.26364 9.96583 9.31822 10.2015 9.31688C11.7204 9.30623 13.2393 9.31688 14.7595 9.3049C15.0257 9.3049 15.0723 9.3728 15.0444 9.62439C14.89 10.9849 14.7515 12.3467 14.6144 13.7085C14.5691 14.1571 14.5785 14.1585 14.1458 14.1585C12.8386 14.1585 11.5313 14.1665 10.2254 14.1518C9.95119 14.1518 9.89794 14.2317 9.89794 14.4899C9.90593 19.0799 9.89794 23.6752 9.91125 28.2612C9.91125 28.5674 9.8407 28.646 9.53186 28.6433C7.77866 28.6273 6.02414 28.6366 4.27094 28.634C3.82499 28.634 3.87158 28.6992 3.87158 28.22C3.87602 25.9472 3.87913 23.6739 3.8809 21.4002Z\">\n                            </path>\n                        </svg>\n                        <span class=\"usa-sr-only\">NCBI on Facebook</span>\n                    </a>\n                \n\n                \n                    <a href=\"https://www.linkedin.com/company/ncbinlm\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--gray\" target=\"_blank\" rel=\"noreferrer noopener\">\n                        <svg width=\"25\" height=\"23\" viewBox=\"0 0 26 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                            <path d=\"M14.6983 9.98423C15.6302 9.24808 16.5926 8.74754 17.6762 8.51991C19.673 8.09126 21.554 8.30824 23.1262 9.7526C24.2351 10.7723 24.7529 12.1115 25.0165 13.5612C25.1486 14.3363 25.2105 15.1218 25.2015 15.9081C25.2015 18.3043 25.2015 20.6898 25.2082 23.0806C25.2082 23.3468 25.1549 23.444 24.8621 23.4414C23.1297 23.4272 21.3992 23.4272 19.6704 23.4414C19.4041 23.4414 19.3429 23.3588 19.3442 23.1019C19.3535 20.5194 19.3442 17.9368 19.3442 15.3543C19.3442 14.0005 18.3258 12.9448 17.0266 12.9488C15.7273 12.9528 14.6983 14.0071 14.6983 15.361C14.6983 17.9328 14.6917 20.5047 14.6983 23.0753C14.6983 23.3708 14.6198 23.444 14.3296 23.4427C12.6185 23.4294 10.9079 23.4294 9.19779 23.4427C8.93155 23.4427 8.86099 23.3735 8.86232 23.1086C8.8783 19.7619 8.88628 16.4144 8.88628 13.066C8.88628 11.5688 8.87874 10.0708 8.86365 8.57182C8.86365 8.3575 8.90758 8.27896 9.14054 8.28029C10.9048 8.29094 12.6687 8.29094 14.4321 8.28029C14.6464 8.28029 14.6983 8.34818 14.6983 8.54653C14.6903 9.00047 14.6983 9.45441 14.6983 9.98423Z\">\n                            </path>\n                            <path d=\"M6.55316 15.8443C6.55316 18.2564 6.55316 20.6699 6.55316 23.082C6.55316 23.3629 6.48127 23.4388 6.19906 23.4374C4.47737 23.4241 2.75568 23.4241 1.03399 23.4374C0.767751 23.4374 0.69986 23.3629 0.701191 23.1006C0.709178 18.2648 0.709178 13.4281 0.701191 8.59053C0.701191 8.34026 0.765089 8.27237 1.01669 8.2737C2.74991 8.28435 4.48048 8.28435 6.20838 8.2737C6.47462 8.2737 6.5465 8.33627 6.54517 8.6065C6.54783 11.0186 6.55316 13.4308 6.55316 15.8443Z\">\n                            </path>\n                            <path d=\"M3.65878 0.243898C5.36804 0.243898 6.58743 1.45529 6.58743 3.1406C6.58743 4.75801 5.32145 5.95742 3.60819 5.96807C3.22177 5.97614 2.83768 5.90639 2.47877 5.76299C2.11985 5.61959 1.79344 5.40546 1.51897 5.13334C1.24449 4.86123 1.02755 4.53668 0.881058 4.17902C0.734563 3.82136 0.661505 3.43788 0.666231 3.05141C0.67555 1.42601 1.9362 0.242566 3.65878 0.243898Z\">\n                            </path>\n                        </svg>\n                        <span class=\"usa-sr-only\">NCBI on LinkedIn</span>\n                    </a>\n                \n\n                \n                    <a href=\"https://github.com/ncbi\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--gray\" target=\"_blank\" rel=\"noreferrer noopener\">\n                        <svg width=\"28\" height=\"27\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                            <path d=\"M16.7228 20.6334C17.5057 20.5527 18.2786 20.3944 19.0301 20.1608C21.3108 19.4193 22.5822 17.8259 22.963 15.4909C23.1228 14.5112 23.1814 13.5287 22.9883 12.5437C22.8106 11.6423 22.4013 10.8028 21.8007 10.1076C21.7526 10.0605 21.7197 10 21.7064 9.934C21.6931 9.86799 21.7 9.79952 21.7262 9.73748C22.0856 8.6206 21.9711 7.51969 21.601 6.42677C21.582 6.3497 21.5345 6.2827 21.468 6.23923C21.4016 6.19577 21.3211 6.17906 21.2429 6.19248C20.7329 6.21649 20.2313 6.33051 19.7611 6.52928C19.1103 6.7908 18.4899 7.12198 17.9104 7.51703C17.84 7.56996 17.7581 7.60551 17.6713 7.62078C17.5846 7.63605 17.4954 7.6306 17.4112 7.60489C15.2596 7.05882 13.0054 7.06203 10.8554 7.61421C10.7806 7.63586 10.7018 7.63967 10.6253 7.62534C10.5487 7.611 10.4766 7.57892 10.4148 7.53167C9.64788 7.03247 8.85171 6.58918 7.96368 6.33359C7.65781 6.24338 7.34123 6.19458 7.02239 6.18849C6.94879 6.17986 6.87462 6.19893 6.81432 6.242C6.75402 6.28507 6.71191 6.34904 6.69621 6.42145C6.32342 7.51437 6.2209 8.61527 6.56307 9.73348C6.59635 9.84264 6.64694 9.93316 6.54177 10.0516C5.47666 11.2604 5.09988 12.6834 5.19574 14.2676C5.2663 15.4244 5.46201 16.5466 6.01454 17.5769C6.84399 19.1171 8.21664 19.9119 9.85158 20.3352C10.3938 20.4706 10.9444 20.5698 11.4998 20.632C11.5384 20.7492 11.4506 20.7798 11.408 20.8291C11.1734 21.1179 10.9894 21.4441 10.8634 21.7942C10.7622 22.0458 10.8315 22.4039 10.6065 22.5516C10.263 22.7766 9.83827 22.8485 9.42421 22.8871C8.17936 23.0056 7.26471 22.4877 6.6283 21.4348C6.25552 20.8184 5.76956 20.3325 5.08523 20.0663C4.76981 19.9325 4.42139 19.8967 4.08537 19.9638C3.7898 20.029 3.73788 20.1901 3.93891 20.4111C4.03639 20.5234 4.14989 20.6207 4.27575 20.6999C4.9796 21.1318 5.51717 21.7884 5.80152 22.5636C6.37002 23.9973 7.48039 24.5697 8.93825 24.6323C9.43741 24.6575 9.93768 24.615 10.4254 24.5058C10.5892 24.4672 10.6531 24.4872 10.6517 24.6762C10.6451 25.4936 10.6637 26.3123 10.6517 27.131C10.6517 27.6635 10.1684 27.9297 9.58663 27.7393C8.17396 27.2671 6.84977 26.5631 5.66838 25.656C2.59555 23.2891 0.720966 20.1861 0.217704 16.3376C-0.357453 11.9127 0.911353 8.00824 3.98551 4.73881C6.11909 2.42656 8.99932 0.939975 12.1203 0.540191C16.5351 -0.0601815 20.4347 1.14323 23.7232 4.16373C26.2449 6.47869 27.724 9.37672 28.1048 12.7726C28.5828 17.0325 27.3686 20.7945 24.4768 23.9827C22.9762 25.6323 21.0956 26.8908 18.9982 27.6488C18.8783 27.6927 18.7585 27.738 18.636 27.7726C18.0356 27.9404 17.6189 27.6395 17.6189 27.0098C17.6189 25.7452 17.6308 24.4806 17.6295 23.2159C17.6329 22.9506 17.6128 22.6856 17.5696 22.4238C17.4325 21.6664 17.3419 21.484 16.7228 20.6334Z\">\n                            </path>\n                        </svg>\n                        <span class=\"usa-sr-only\">NCBI on GitHub</span>\n                    </a>\n                \n\n                \n                    <a href=\"https://ncbiinsights.ncbi.nlm.nih.gov/\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--gray\" target=\"_blank\" rel=\"noreferrer noopener\">\n                        <svg width=\"26\" height=\"26\" viewBox=\"0 0 27 27\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                            <path d=\"M23.7778 26.4574C23.1354 26.3913 22.0856 26.8024 21.636 26.3087C21.212 25.8444 21.4359 24.8111 21.324 24.0347C19.9933 14.8323 14.8727 8.80132 6.09057 5.85008C4.37689 5.28406 2.58381 4.99533 0.779072 4.99481C0.202773 4.99481 -0.0229751 4.83146 0.00455514 4.21479C0.0660406 3.08627 0.0660406 1.95525 0.00455514 0.826734C-0.0413285 0.0815827 0.259669 -0.0193618 0.896534 0.00266238C6.96236 0.222904 12.3693 2.24179 16.9889 6.16209C22.9794 11.2478 26.1271 17.7688 26.4372 25.648C26.4629 26.294 26.3179 26.5271 25.6609 26.4684C25.0827 26.417 24.4991 26.4574 23.7778 26.4574Z\">\n                            </path>\n                            <path d=\"M14.8265 26.441C14.0924 26.441 13.2371 26.6795 12.6626 26.3786C12.0092 26.0372 12.3781 25.0644 12.246 24.378C11.1154 18.5324 6.6849 14.5497 0.74755 14.1001C0.217135 14.0615 -0.0104482 13.9422 0.0134113 13.3659C0.0519536 12.1454 0.0482829 10.9213 0.0134113 9.69524C-0.00127145 9.14464 0.196946 9.03268 0.703502 9.04736C9.21217 9.27128 16.5994 16.2511 17.2804 24.7231C17.418 26.4446 17.418 26.4446 15.6579 26.4446H14.832L14.8265 26.441Z\">\n                            </path>\n                            <path d=\"M3.58928 26.4555C2.64447 26.4618 1.73584 26.0925 1.06329 25.4289C0.39073 24.7653 0.00933763 23.8617 0.0030097 22.9169C-0.00331824 21.9721 0.365937 21.0635 1.02954 20.3909C1.69315 19.7184 2.59675 19.337 3.54156 19.3306C4.48637 19.3243 5.39499 19.6936 6.06755 20.3572C6.7401 21.0208 7.1215 21.9244 7.12782 22.8692C7.13415 23.814 6.7649 24.7226 6.10129 25.3952C5.43768 26.0677 4.53409 26.4491 3.58928 26.4555Z\">\n                            </path>\n                        </svg>\n                        <span class=\"usa-sr-only\">NCBI RSS feed</span>\n                    </a>\n                \n            </div>\n        </div>\n    \n\n    <div data-testid=\"gridContainer\" class=\"grid-container ncbi-footer__container\">\n        <div class=\"grid-row ncbi-footer__main-content-container\" data-testid=\"grid\">\n            \n                <div class=\"ncbi-footer__column\">\n                    \n                        <p class=\"ncbi-footer__circled-icons-heading\">\n                            Connect with NLM\n                        </p>\n                    \n\n                    <div class=\"ncbi-footer__circled-icons-list\">\n                        \n                            <a href=\"https://twitter.com/nlm_nih\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--circled\" target=\"_blank\" rel=\"noreferrer noopener\">\n                                <svg width=\"32\" height=\"32\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                                    <path d=\"m6.067 8 10.81 13.9L6 33.2h4.2l8.4-9.1 7.068 9.1H34L22.8 18.5 31.9 8h-3.5l-7.7 8.4L14.401 8H6.067Zm3.6 1.734h3.266l16.8 21.732H26.57L9.668 9.734Z\">\n                                    </path>\n                                </svg>\n                                <span class=\"usa-sr-only\">NLM on X (formerly known as Twitter)</span>\n                            </a>\n                        \n\n                        \n                            <a href=\"https://www.facebook.com/nationallibraryofmedicine\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--circled\" target=\"_blank\" rel=\"noreferrer noopener\">\n                                <svg width=\"13\" height=\"24\" viewBox=\"0 0 13 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                                    <path d=\"M4.11371 23.1369C4.11371 23.082 4.11371 23.0294 4.11371 22.9745V12.9411H0.817305C0.6709 12.9411 0.670898 12.9411 0.670898 12.8016C0.670898 11.564 0.670898 10.3287 0.670898 9.09341C0.670898 8.97903 0.705213 8.95158 0.815017 8.95158C1.8673 8.95158 2.91959 8.95158 3.97417 8.95158H4.12057V8.83263C4.12057 7.8055 4.12057 6.7738 4.12057 5.74897C4.1264 4.92595 4.31387 4.11437 4.66959 3.37217C5.12916 2.38246 5.94651 1.60353 6.95717 1.1921C7.64827 0.905008 8.3913 0.764035 9.13953 0.778051C10.0019 0.791777 10.8644 0.830666 11.7268 0.860404C11.8869 0.860404 12.047 0.894717 12.2072 0.90158C12.2964 0.90158 12.3261 0.940469 12.3261 1.02968C12.3261 1.5421 12.3261 2.05452 12.3261 2.56465C12.3261 3.16857 12.3261 3.7725 12.3261 4.37642C12.3261 4.48165 12.2964 4.51367 12.1912 4.51138C11.5369 4.51138 10.8804 4.51138 10.2261 4.51138C9.92772 4.51814 9.63058 4.5526 9.33855 4.61433C9.08125 4.6617 8.84537 4.78881 8.66431 4.97766C8.48326 5.16652 8.3662 5.40755 8.32972 5.66661C8.28476 5.89271 8.26027 6.1224 8.25652 6.35289C8.25652 7.19014 8.25652 8.02969 8.25652 8.86923C8.25652 8.89439 8.25652 8.91955 8.25652 8.95615H12.0219C12.1797 8.95615 12.182 8.95616 12.1614 9.10714C12.0768 9.76596 11.9876 10.4248 11.9029 11.0813C11.8312 11.6319 11.7626 12.1824 11.697 12.733C11.6719 12.9434 11.6787 12.9434 11.4683 12.9434H8.26338V22.899C8.26338 22.979 8.26338 23.0591 8.26338 23.1392L4.11371 23.1369Z\">\n                                    </path>\n                                </svg>\n                                <span class=\"usa-sr-only\">NLM on Facebook</span>\n                            </a>\n                        \n\n                        \n                            <a href=\"https://www.youtube.com/user/NLMNIH\" class=\"ncbi-footer__social-icon ncbi-footer__social-icon--circled\" target=\"_blank\" rel=\"noreferrer noopener\">\n                                <svg width=\"21\" height=\"15\" viewBox=\"0 0 21 15\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\n                                    <path d=\"M19.2561 1.47914C18.9016 1.15888 18.5699 0.957569 17.2271 0.834039C15.5503 0.678484 13.2787 0.655608 11.563 0.65332H9.43556C7.71987 0.65332 5.4483 0.678484 3.77151 0.834039C2.43098 0.957569 2.097 1.15888 1.74242 1.47914C0.813665 2.32097 0.619221 4.62685 0.598633 6.89384C0.598633 7.31781 0.598633 7.74101 0.598633 8.16345C0.626084 10.4121 0.827391 12.686 1.74242 13.521C2.097 13.8412 2.4287 14.0425 3.77151 14.1661C5.4483 14.3216 7.71987 14.3445 9.43556 14.3468H11.563C13.2787 14.3468 15.5503 14.3216 17.2271 14.1661C18.5676 14.0425 18.9016 13.8412 19.2561 13.521C20.1712 12.6929 20.3725 10.451 20.3999 8.22064C20.3999 7.74025 20.3999 7.25986 20.3999 6.77946C20.3725 4.54907 20.1689 2.30724 19.2561 1.47914ZM8.55942 10.5311V4.65201L13.5601 7.50005L8.55942 10.5311Z\" fill=\"white\"></path>\n                                </svg>\n                                <span class=\"usa-sr-only\">NLM on YouTube</span>\n                            </a>\n                        \n                    </div>\n                </div>\n            \n\n            \n                <address class=\"ncbi-footer__address ncbi-footer__column\">\n                    \n        <p>\n            <a class=\"usa-link usa-link--external\" href=\"https://www.google.com/maps/place/8600+Rockville+Pike,+Bethesda,+MD+20894/%4038.9959508,\n            -77.101021,17z/data%3D!3m1!4b1!4m5!3m4!1s0x89b7c95e25765ddb%3A0x19156f88b27635b8!8m2!3d38.9959508!\n            4d-77.0988323\" rel=\"noopener noreferrer\" target=\"_blank\">National Library of Medicine\n            <br> 8600 Rockville Pike<br> Bethesda, MD 20894</a>\n        </p>\n    \n                </address>\n            \n\n            \n                <ul class=\"usa-list usa-list--unstyled ncbi-footer__vertical-list ncbi-footer__column\">\n                    \n                        <li class=\"ncbi-footer__vertical-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.nlm.nih.gov/web_policies.html\" class=\"usa-link  usa-link--alt ncbi-footer__link\">\n    \n\n    Web Policies\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__vertical-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.nih.gov/institutes-nih/nih-office-director/office-communications-public-liaison/freedom-information-act-office\" class=\"usa-link  usa-link--alt ncbi-footer__link\">\n    \n\n    FOIA\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__vertical-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.hhs.gov/vulnerability-disclosure-policy/index.html\" class=\"usa-link usa-link--external usa-link--alt ncbi-footer__link\" rel=\"noreferrer noopener\" target=\"_blank\">\n    \n\n    HHS Vulnerability Disclosure\n\n    \n</a>\n\n                        </li>\n                    \n                </ul>\n            \n\n            \n                <ul class=\"usa-list usa-list--unstyled ncbi-footer__vertical-list ncbi-footer__column\">\n                    \n                        <li class=\"ncbi-footer__vertical-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://support.nlm.nih.gov/?pagename=cloudpmc-viewer%3Apmc%3Aarticle%3Ajournal\" class=\"usa-link  usa-link--alt ncbi-footer__link\" data-pinger-pagename-param=\"true\">\n    \n\n    Help\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__vertical-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.nlm.nih.gov/accessibility.html\" class=\"usa-link  usa-link--alt ncbi-footer__link\">\n    \n\n    Accessibility\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__vertical-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.nlm.nih.gov/careers/careers.html\" class=\"usa-link  usa-link--alt ncbi-footer__link\">\n    \n\n    Careers\n\n    \n</a>\n\n                        </li>\n                    \n                </ul>\n            \n        </div>\n\n        \n            <div class=\"grid-row grid-col-12\" data-testid=\"grid\">\n                <ul class=\"ncbi-footer__bottom-links-list\">\n                    \n                        <li class=\"ncbi-footer__bottom-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.nlm.nih.gov/\" class=\"usa-link  usa-link--alt ncbi-footer__link\">\n    \n\n    NLM\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__bottom-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.nih.gov/\" class=\"usa-link  usa-link--alt ncbi-footer__link\">\n    \n\n    NIH\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__bottom-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.hhs.gov/\" class=\"usa-link usa-link--external usa-link--alt ncbi-footer__link\" rel=\"noreferrer noopener\" target=\"_blank\">\n    \n\n    HHS\n\n    \n</a>\n\n                        </li>\n                    \n                        <li class=\"ncbi-footer__bottom-list-item\">\n                            \n\n\n\n\n\n\n\n\n<a href=\"https://www.usa.gov/\" class=\"usa-link usa-link--external usa-link--alt ncbi-footer__link\" rel=\"noreferrer noopener\" target=\"_blank\">\n    \n\n    USA.gov\n\n    \n</a>\n\n                        </li>\n                    \n                </ul>\n            </div>\n        \n    </div>\n</footer>\n\n    \n\n\n        \n        \n    \n  <script type=\"text/javascript\" src=\"https://cdn.ncbi.nlm.nih.gov/core/pinger/pinger.js\"> </script><div id=\"ZN_dikYWqsjiUWN0Q5\"></div>\n\n\n    \n        \n\n<button class=\"back-to-top\" data-ga-category=\"pagination\" data-ga-action=\"back_to_top\">\n    <label>Back to Top</label>\n    <svg class=\"usa-icon order-0\" aria-hidden=\"true\" focusable=\"false\" role=\"img\">\n        <use xlink:href=\"/static/img/sprite.svg#arrow_upward\"></use>\n    </svg>\n</button>\n    \n\n\n        \n    \n    \n    \n        \n    <script type=\"application/javascript\">\n    window.ncbi = window.ncbi || {};\n    window.ncbi.pmc = window.ncbi.pmc || {};\n    window.ncbi.pmc.options = {\n        logLevel: 'INFO',\n        \n        staticEndpoint: '/static/',\n        \n        citeCookieName: 'pmc-cf',\n    };\n</script>\n    <script type=\"module\" crossorigin=\"\" src=\"/static/assets/base-370d5dd6.js\"></script>\n    \n    <script type=\"text/javascript\" src=\"https://cdn.ncbi.nlm.nih.gov/core/jquery/jquery-3.6.0.min.js\">&#xA0;</script>\n    <script type=\"text/javascript\">\n        jQuery.getScript(\"https://cdn.ncbi.nlm.nih.gov/core/alerts/alerts.js\", function () {\n            galert(['div.nav_and_browser', 'div.header', '#universal_header', '.usa-banner', 'body > *:nth-child(1)'])\n        });\n    </script>\n\n\n    <script type=\"text/javascript\">var exports = {};</script>\n     <script src=\"/static/CACHE/js/output.13b077bc3ffd.js\"></script>\n\n    <script type=\"module\" crossorigin=\"\" src=\"/static/assets/article-917ba005.js\"></script>\n    \n    \n\n    \n\n<div class=\"fake-body-scroll\"></div><div class=\"fake-body-scroll\"></div><script id=\"_fed_an_ua_tag\" text=\"\" charset=\"\" type=\"text/javascript\" src=\"https://dap.digitalgov.gov/Universal-Federated-Analytics-Min.js?agency=HHS&amp;subagency=NCBI%20-%20ncbi.nlm.nih.gov&amp;sitetopic=NCBI%20Pinger%200.39.3&amp;siteplatform=NCBI%20Pinger%200.39.3\"></script><script type=\"text/javascript\" src=\"https://zndikYWqsjiUWN0Q5-nlmenterprise.siteintercept.qualtrics.com/SIE/?Q_ZID=ZN_dikYWqsjiUWN0Q5\"></script><script src=\"https://siteintercept.qualtrics.com/dxjsmodule/CoreModule.js?Q_CLIENTVERSION=2.33.0&amp;Q_CLIENTTYPE=web&amp;Q_BRANDID=nlmenterprise\" defer=\"\"></script><script src=\"https://siteintercept.qualtrics.com/dxjsmodule/FeedbackButtonModule.js?Q_CLIENTVERSION=2.33.0&amp;Q_CLIENTTYPE=web&amp;Q_BRANDID=nlmenterprise\" defer=\"\"></script></body></html>", "images": [{"src": "https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/7665620d8751/peerj-cs-10-1700-g001.jpg", "alt": "Figure 1", "width": "663", "height": "567"}, {"src": "https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/77061d2f5335/peerj-cs-10-1700-g005.jpg", "alt": "Figure 5", "width": "738", "height": "436"}, {"src": "https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/82bc36c875a7/peerj-cs-10-1700-g003.jpg", "alt": "Figure 3", "width": "738", "height": "426"}, {"src": "https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/6372c35a1c6d/peerj-cs-10-1700-g004.jpg", "alt": "Figure 4", "width": "738", "height": "426"}, {"src": "https://cdn.ncbi.nlm.nih.gov/pmc/blobs/8834/10909160/9ee913551d33/peerj-cs-10-1700-g002.jpg", "alt": "Figure 2", "width": "738", "height": "330"}, {"src": "https://cdn.ncbi.nlm.nih.gov/pmc/banners/logo-peerjcs.png", "alt": "PeerJ Computer Science logo", "width": "500", "height": "75"}], "success": true, "screenshot": "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"}}