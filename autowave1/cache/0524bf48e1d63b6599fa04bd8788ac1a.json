{"timestamp": 1755263383.96458, "data": {"title": "Validating Connectivity and Policies Using the Access Tester", "content": "Validating Connectivity and Policies Using the Access Tester Skip Headers Oracle® Fusion Middleware Administrator's Guide for Oracle Access Manager 11 g Release 1 (11.1.1) Part Number E15478-02 Home Book List Contents Index Master Index Contact Us Previous Next View PDF 10 Validating Connectivity and Policies Using the Access Tester The Oracle Access Manager Access Tester enables IT professionals and administrators to simulate interactions between registered OAM Agents and OAM 11g Servers to help troubleshoot issues involving agent connections and to test policy definitions. This chapter introduces the Oracle Access Manager Access Tester and how to use it. The following topics are provided: Prerequisites Introduction to the OAM 11g Access Tester Installing and Starting the Access Tester Introduction to the Access Tester Console and Navigation Testing Connectivity and Policies from the Access Tester Console Creating and Managing Test Cases and Scripts Evaluating Scripts, Log File, and Statistics Prerequisites Before you can perform tasks in this chapter: Ensure that the OAM Administration Console, OAM run-time Server, and registered OAM Agent are running Confirm the application domain and policies for one or more resources, as described in Chapter 9 . Introduction to the OAM 11g Access Tester The Access Tester is a portable, stand-alone Java application that ships with Oracle Access Manager 11g. The Access Tester provides a functional interface between an individual IT professional or administrator and the OAM Server. IT professionals can use the Access Tester to verify connectivity and troubleshoot problems with the physical deployment. Application administrators can use the Access Tester to perform a quick validation of policies. In this chapter, the term \"administrator\" represents any individual who is using the Access Tester. The Access Tester can be used from any computer, either within or outside the WebLogic Server domain. Both a graphical user interface (known as the Console in this chapter) and a command-line interface are provided. Command line mode enables complete automation of test script execution in single or multi-client mode environments. By appearing to be a real agent, the Access Tester helps with policy configuration design and troubleshooting, and sometimes with troubleshooting OAM Server responsiveness. When using the Access Tester, you must appear to be the real end user; the Access Tester does not actually communicate with a real end user. To use the Access Tester, you must understand and administer authentication and authorization policies for an application or resource that is protected by Oracle Access Manager 11g. The Access Tester enables you to: Configure a request to be sent to the OAM Server that emulates what a real agent would send to the OAM Server in a real environment. Send your request to the OAM Server and receives a response that is the same as the response that would received by a real Agent. The Access Tester uses the OAM Access Protocol (OAP) API to send requests over the OAP channel to the OAM Proxy running as part of the OAM Server. The OAM Server processes the request and returns a response. Process and display the server response. Proceed in the manner a real agent would to handle the response. For example, if a WebGate determines that a resource is protected by a certificate authentication scheme, then it must obtain the end user's certificate from the http SSL connection. In the case of a certificate authentication scheme, you must point the Access Tester to a certificate to be used as the end user's credentials. In addition to simulating the Agent while performing functions in the previous list, the Access Tester enables you to: Review performance characteristics of intended policy changes Track the latency of authentication and authorization requests Stress test the OAM Server to establish low- and high-performance watermarks relative to desired user loads, and to size back-end hardware Establish performance metrics and measuring on an ongoing basis to prove desired outcomes During basic operations, the Access Tester does not make any determination about the Server response and whether it is a right or wrong response (for instance, whether or not resource X is protected, or user Y is authorized to access resource X). When operating the Access Tester, you must be aware of the policy configuration to determine if a specific response is appropriate. The Access Tester offers advanced functionality that enables you to group a number of individual requests into a test script that can be sent to the OAM Server for processing. The output of such a test run can be captured by the Access Tester and used to compare against a similar document containing \"known good\" responses. In this way, the Access Tester can be used for automated testing of policy configuration against errant changes. For more information, see the following topics in this chapter: About OAM Agent and...", "url": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/doc.1111/e15478/tester.htm", "html": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\"><html lang=\"en\" xml:lang=\"en\" xmlns=\"http://www.w3.org/1999/xhtml\"><head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=us-ascii\">\n<meta http-equiv=\"Content-Language\" content=\"en\">\n<meta http-equiv=\"Content-Style-Type\" content=\"text/css\">\n<meta http-equiv=\"Content-Script-Type\" content=\"text/javascript\">\n<meta name=\"robots\" content=\"all\" scheme=\"http://www.robotstxt.org/\">\n<meta name=\"generator\" content=\"Oracle DARB XHTML Converter (Mode = document) - Version 5.1.2 Build 009\">\n<meta name=\"Date\" content=\"2010-08-08T12:23:57Z\">\n<meta name=\"doctitle\" content=\"Oracle® Fusion Middleware Administrator's Guide for Oracle Access Manager 11g Release 1 (11.1.1)\">\n<meta name=\"partno\" content=\"E15478-02\">\n<meta name=\"docid\" content=\"AIAAG\">\n<link rel=\"Start\" href=\"../../index.htm\" title=\"Home\" type=\"text/html\">\n<link rel=\"Copyright\" href=\"../../dcommon/html/cpyr.htm\" title=\"Copyright\" type=\"text/html\">\n<link rel=\"Stylesheet\" href=\"../../dcommon/css/blafdoc.css\" title=\"Default\" type=\"text/css\">\n<script type=\"text/javascript\" src=\"http://www.oracle.com/pls/as111130/doccd_js?path=doc.1111/e15478/tester.htm\">\n</script>\n<link rel=\"Contents\" href=\"toc.htm\" title=\"Contents\" type=\"text/html\">\n<link rel=\"Index\" href=\"index.htm\" title=\"Index\" type=\"text/html\">\n<link rel=\"Prev\" href=\"app_domn.htm\" title=\"Previous\" type=\"text/html\">\n<link rel=\"Next\" href=\"logout.htm\" title=\"Next\" type=\"text/html\">\n<link rel=\"alternate\" href=\"../e15478.pdf\" title=\"PDF version\" type=\"application/pdf\">\n<title>Validating Connectivity and Policies Using the Access Tester</title>\n</head>\n<body>\n<div class=\"header\">\n<div class=\"zz-skip-header\"><a name=\"top\" id=\"top\" href=\"#BEGIN\">Skip Headers</a></div>\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n<tbody><tr>\n<td align=\"left\" valign=\"top\"><b>Oracle® Fusion Middleware Administrator's Guide for Oracle Access Manager<br>\n11<i>g</i> Release 1 (11.1.1)</b><br>\nPart Number E15478-02</td>\n<td valign=\"bottom\" align=\"right\">\n<table class=\"icons oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"294\">\n<tbody><tr>\n<td align=\"center\" valign=\"top\"><a href=\"../../index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/doclib.gif\" alt=\"Go to Documentation Home\"><br>\n<span class=\"icon\">Home</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../nav/portal_booklist.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/booklist.gif\" alt=\"Go to Book List\"><br>\n<span class=\"icon\">Book List</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"toc.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/toc.gif\" alt=\"Go to Table of Contents\"><br>\n<span class=\"icon\">Contents</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/index.gif\" alt=\"Go to Index\"><br>\n<span class=\"icon\">Index</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../mix.1111/b14005/toc.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/masterix.gif\" alt=\"Go to Master Index\"><br>\n<span class=\"icon\">Master Index</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../dcommon/html/feedback.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/feedbck2.gif\" alt=\"Go to Feedback page\"><br>\n<span class=\"icon\">Contact Us</span></a></td>\n</tr>\n</tbody></table>\n</td>\n</tr>\n</tbody></table>\n<hr>\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n<tbody><tr>\n<td align=\"left\" valign=\"top\">\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"98\">\n<tbody><tr>\n<td align=\"center\" valign=\"top\"><a href=\"app_domn.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/leftnav.gif\" alt=\"Go to previous page\"><br>\n<span class=\"icon\">Previous</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"logout.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/rightnav.gif\" alt=\"Go to next page\"><br>\n<span class=\"icon\">Next</span></a></td>\n</tr>\n</tbody></table>\n</td>\n<td align=\"right\" valign=\"top\" style=\"font-size: 90%\"><a href=\"../e15478.pdf\">View PDF</a></td>\n</tr>\n</tbody></table>\n<a name=\"BEGIN\" id=\"BEGIN\"></a></div>\n<div class=\"IND\"><!-- End Header --><a id=\"CACIAFDB\" name=\"CACIAFDB\"></a><a id=\"AIAAG1954\" name=\"AIAAG1954\"></a>\n<h1 class=\"chapter\"><span class=\"secnum\">10</span> Validating Connectivity and Policies Using the Access Tester</h1>\n<p>The Oracle Access Manager Access Tester enables IT professionals and administrators to simulate interactions between registered OAM Agents and OAM 11g Servers to help troubleshoot issues involving agent connections and to test policy definitions. This chapter introduces the Oracle Access Manager Access Tester and how to use it. The following topics are provided:</p>\n<ul>\n<li>\n<p><a href=\"#CACCAIHJ\">Prerequisites</a></p>\n</li>\n<li>\n<p><a href=\"#CACJCIHF\">Introduction to the OAM 11g Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACBEJDC\">Installing and Starting the Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACCHEFH\">Introduction to the Access Tester Console and Navigation</a></p>\n</li>\n<li>\n<p><a href=\"#CACEFCDD\">Testing Connectivity and Policies from the Access Tester Console</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBIEED\">Evaluating Scripts, Log File, and Statistics</a></p>\n</li>\n</ul>\n<a id=\"CACCAIHJ\" name=\"CACCAIHJ\"></a><a id=\"AIAAG1955\" name=\"AIAAG1955\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Prerequisites</h2>\n<p>Before you can perform tasks in this chapter:</p>\n<ul>\n<li>\n<p>Ensure that the OAM Administration Console, OAM run-time Server, and registered OAM Agent are running</p>\n</li>\n<li>\n<p>Confirm the application domain and policies for one or more resources, as described in <a href=\"app_domn.htm#BABBAEHB\">Chapter 9</a>.</p>\n</li>\n</ul>\n</div>\n<!-- class=\"sect1\" -->\n<a id=\"CACJCIHF\" name=\"CACJCIHF\"></a><a id=\"AIAAG1956\" name=\"AIAAG1956\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Introduction to the OAM 11g Access Tester</h2>\n<p>The Access Tester is a portable, stand-alone Java application that ships with Oracle Access Manager 11g. The Access Tester provides a functional interface between an individual IT professional or administrator and the OAM Server.</p>\n<p>IT professionals can use the Access Tester to verify connectivity and troubleshoot problems with the physical deployment. Application administrators can use the Access Tester to perform a quick validation of policies. In this chapter, the term \"administrator\" represents any individual who is using the Access Tester.</p>\n<p>The Access Tester can be used from any computer, either within or outside the WebLogic Server domain. Both a graphical user interface (known as the Console in this chapter) and a command-line interface are provided. Command line mode enables complete automation of test script execution in single or multi-client mode environments.</p>\n<p>By appearing to be a real agent, the Access Tester helps with policy configuration design and troubleshooting, and sometimes with troubleshooting OAM Server responsiveness. When using the Access Tester, you must appear to be the real end user; the Access Tester does not actually communicate with a real end user.</p>\n<p>To use the Access Tester, you must understand and administer authentication and authorization policies for an application or resource that is protected by Oracle Access Manager 11g.</p>\n<p>The Access Tester enables you to:</p>\n<ul>\n<li>\n<p>Configure a request to be sent to the OAM Server that emulates what a real agent would send to the OAM Server in a real environment.</p>\n</li>\n<li>\n<p>Send your request to the OAM Server and receives a response that is the same as the response that would received by a real Agent. The Access Tester uses the OAM Access Protocol (OAP) API to send requests over the OAP channel to the OAM Proxy running as part of the OAM Server. The OAM Server processes the request and returns a response.</p>\n</li>\n<li>\n<p>Process and display the server response.</p>\n</li>\n<li>\n<p>Proceed in the manner a real agent would to handle the response. For example, if a WebGate determines that a resource is protected by a certificate authentication scheme, then it must obtain the end user's certificate from the http SSL connection.</p>\n<p>In the case of a certificate authentication scheme, you must point the Access Tester to a certificate to be used as the end user's credentials.</p>\n</li>\n</ul>\n<p>In addition to simulating the Agent while performing functions in the previous list, the Access Tester enables you to:</p>\n<ul>\n<li>\n<p>Review performance characteristics of intended policy changes</p>\n</li>\n<li>\n<p>Track the latency of authentication and authorization requests</p>\n</li>\n<li>\n<p>Stress test the OAM Server to establish low- and high-performance watermarks relative to desired user loads, and to size back-end hardware</p>\n</li>\n<li>\n<p>Establish performance metrics and measuring on an ongoing basis to prove desired outcomes</p>\n</li>\n</ul>\n<p>During basic operations, the Access Tester does not make any determination about the Server response and whether it is a right or wrong response (for instance, whether or not resource X is protected, or user Y is authorized to access resource X). When operating the Access Tester, you must be aware of the policy configuration to determine if a specific response is appropriate.</p>\n<p>The Access Tester offers advanced functionality that enables you to group a number of individual requests into a test script that can be sent to the OAM Server for processing. The output of such a test run can be captured by the Access Tester and used to compare against a similar document containing \"known good\" responses. In this way, the Access Tester can be used for automated testing of policy configuration against errant changes.</p>\n<p>For more information, see the following topics in this chapter:</p>\n<ul>\n<li>\n<p><a href=\"#CACJBIAA\">About OAM Agent and Server Interoperability</a></p>\n</li>\n<li>\n<p><a href=\"#CACBICAC\">About Access Tester Security and Processing</a></p>\n</li>\n<li>\n<p><a href=\"#CACIICEH\">About Access Tester Modes and Administrator Interactions</a></p>\n</li>\n</ul>\n<a id=\"CACJBIAA\" name=\"CACJBIAA\"></a><a id=\"AIAAG1957\" name=\"AIAAG1957\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About OAM Agent and Server Interoperability</h3>\n<p>The two primary types of actors in the OAM architecture are the policy servers (OAM Servers) and OAM policy enforcement agents (WebGates or AccessGates). In the security world, Agents represent the policy enforcement point (PEP), while OAM Servers represent the policy decision point (PDP):</p>\n<ul>\n<li>\n<p>The Agent plays the role of a gatekeeper to secure resources such as http-based applications and manage all interactions with the user who is trying to access that resource. This is accomplished according to access control policies maintained on the policy server (OAM Server).</p>\n</li>\n<li>\n<p>The role of the OAM Server is to provide policy, identity, and session services to the Agent to properly secure application resources, authenticate and authorize users, and manage user sessions.</p>\n</li>\n</ul>\n<p>This core OAM product architecture revolves around the following exchanges, which drive the interaction between the Agent and OAM Server. To expose interoperability and the key decision points, <a href=\"#CACDAIFG\">Figure 10-1</a> illustrates a typical OAM Agent and OAM Server interaction during a user's request for a resource.</p>\n<div class=\"figure\"><a id=\"CACDAIFG\" name=\"CACDAIFG\"></a><a id=\"AIAAG1958\" name=\"AIAAG1958\"></a>\n<p class=\"titleinfigure\">Figure 10-1 OAM Agent (PEP) and OAM Server (PDP) Interoperability</p>\n<img width=\"569\" height=\"545\" src=\"img/aiaag_jd_109.gif\" alt=\"Surrounding text describes Figure 10-1 .\" title=\"Surrounding text describes Figure 10-1 .\"><br></div>\n<!-- class=\"figure\" -->\n<p>The following overview outlines the processing that occurs between OAM Agents and OAM Servers. During testing, the Access Tester emulates the Agent and communicates with the OAM Server while the administrator emulates the end user.</p>\n<a id=\"AIAAG1959\" name=\"AIAAG1959\"></a>\n<p class=\"subhead2\">Process overview: Interoperability between OAM Agents and OAM Servers</p>\n<ol>\n<li>\n<p>Establish server connectivity: The registered OAM Agent connects to the OAM Server.</p>\n</li>\n<li>\n<p>The user requests accesses to a resource.</p>\n</li>\n<li>\n<p>Validate resource protection: The Agent forwards the request to the OAM Server to determine if the resource is protected.</p>\n<p>Protected: The OAM Server responds with the type of credentials required.</p>\n</li>\n<li>\n<p>User credentials: Establishing the user identity enables tracking for Audit and SSO purposes, and conveyance to the application. For this, the Agent prompts the user for his credentials.</p>\n</li>\n<li>\n<p>Authenticate user credentials: The Agent forwards the supplied user credentials to the OAM Server for validation.</p>\n<p>Authentication Success: The Agent forwards the resource request to the OAM Server.</p>\n</li>\n<li>\n<p>Authorize user access to a resource: The Agents must first determine if the user is allowed to access the resource by forwarding the request for access to the OAM Server for authorization policy evaluation.</p>\n</li>\n<li>\n<p>The Agent grants or denies access based on the policy response.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACBICAC\" name=\"CACBICAC\"></a><a id=\"AIAAG1960\" name=\"AIAAG1960\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About Access Tester Security and Processing</h3>\n<p>The Access Tester supports only Open and Simple connection modes for communication with the OAM Server.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nThe Access Tester does not currently support OAM Servers and Agents configured for Cert mode transport security.</div>\n<p>The Access Tester encrypts all password-type values that it saves to configuration files and test cases. All network connectivity inherits the NetPoint Access Protocol (NAP) limit of a single connection pool (one primary or secondary connection pool).</p>\n<p><span class=\"bold\">Persistence</span>: The Access Tester manages a number of data structures that require persistent storage between Access Tester invocations. XML-file-based storage is provided for the following types of information:</p>\n<ul>\n<li>\n<p>Configuration data to minimize data entry between invocations of the application (OamTestConfiguration)</p>\n</li>\n<li>\n<p>Test scripts consisting of captured test cases (OamTestScriptCase)</p>\n</li>\n<li>\n<p>Statistical data representing execution metric from a test run (OamTestStats)</p>\n</li>\n</ul>\n<p><span class=\"bold\">XML Files for Input, Logging, and Analysis</span>: The following XML files are produced when you run the Access Tester to process test scripts:</p>\n<ul>\n<li>\n<p>Configuration Script: config.xml is the output file generated using the Save Configuration command within the Access Tester. The name of this document is used within the input script to provide proper connection information to the Access Tester running in command line mode. For details, see <a href=\"#CHDJFEGD\">\"About the Saved Connection Configuration File\"</a>.</p>\n</li>\n<li>\n<p>Input Script: script.xml represents a script that is generated by the Access Tester after capturing one or more test cases. For details, see <a href=\"#CHDBEHJC\">\"About the Generated Input Test Script\"</a>.</p>\n</li>\n<li>\n<p>Target Output Script: oamtest_target.xml is generated by running the Access Tester in command line mode and specifying the input script. For details, see <a href=\"#CHDJAIHC\">\"About the Target Output File Containing Test Run Results\"</a>. For example: <code><span class=\"codeinlinebold\">-Dscript.scriptfile=\"script.xml\" -jar oamtest.jar</span></code></p>\n</li>\n<li>\n<p>Statistics: oamtest_stats.xml is generated together with the output script. For details, see <a href=\"#CHDFGJGC\">\"About the Statistics Document\"</a>.</p>\n</li>\n<li>\n<p>Execution Log: lamtest_log.log is generated together with the output script. For details, see <a href=\"#CHDIEHJD\">\"About the Execution Log\"</a>.</p>\n</li>\n</ul>\n<p>For more information, see <a href=\"#CACIICEH\">\"About Access Tester Modes and Administrator Interactions\"</a>.</p>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACIICEH\" name=\"CACIICEH\"></a><a id=\"AIAAG1961\" name=\"AIAAG1961\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About Access Tester Modes and Administrator Interactions</h3>\n<p>In Console mode, the Access Tester provides a single window for interactions with the user. All Access Tester operations are available in the main window, which performs as a central dashboard where users can submit specific details for the test case and view responses.</p>\n<p><span class=\"bold\">Alternatively</span>, you can use the Access Tester in command line mode and develop test scripts, which you can run interactively or in batches for computerized execution to maximize productivity and minimize costs and resources.</p>\n<p><span class=\"bold\">Run-Time</span>: The Access Tester requires nap-api.jar in the same directory as the main jar oamtest.jar. Starting the application requires oamtest.jar.</p>\n<p>Regardless of the mode you choose for running the Access Tester, your primary interactions with the Access Tester include:</p>\n<ul>\n<li>\n<p>Issuing Requests and Reviewing Results</p>\n<p>You use the Access Tester to issue requests to the OAM Server to validate resource protection, policy configuration, user authentication, and user authorization. You can immediately analyze test case results and also retain the data for longer-term analysis, if needed.</p>\n</li>\n</ul>\n<ul>\n<li>\n<p>Managing Test Scripts</p>\n<p>You can build test scripts by capturing the data generated by test execution, which is available as stand-alone documents. You can run the test script for manual or automated analysis. The Access Tester provides for some automated analysis after each test run, while collecting full set of statistics to enable analysis after the fact.</p>\n</li>\n<li>\n<p>Managing OAM Server Connectivity</p>\n<p>You can manage application settings that include server connection information.</p>\n</li>\n</ul>\n<p><a href=\"#CACJGGIE\">Figure 10-2</a> depicts the flow of information during operations in both Console and command-line modes. Details follow the figure. Advanced operations include building and executing test scripts.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\n</div>\n<div class=\"figure\"><a id=\"CACJGGIE\" name=\"CACJGGIE\"></a><a id=\"AIAAG1962\" name=\"AIAAG1962\"></a>\n<p class=\"titleinfigure\">Figure 10-2 User Interactions with the Access Tester</p>\n<img width=\"657\" height=\"563\" src=\"img/aiaag_jd_110.gif\" alt=\"Surrounding text describes Figure 10-2 .\" title=\"Surrounding text describes Figure 10-2 .\"><br></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#CACEJGDH\">Table 10-1</a> describes the process flow of information during both Console mode operations and command-line mode operations.</p>\n<div class=\"tblhruleformalwidemax\"><a id=\"AIAAG1963\" name=\"AIAAG1963\"></a><a id=\"sthref250\" name=\"sthref250\"></a><a id=\"CACEJGDH\" name=\"CACEJGDH\"></a>\n<p class=\"titleintable\">Table 10-1 User Interactions Using Console Mode versus Command Line Mode Operations</p>\n<table class=\"HRuleFormalWideMax\" title=\"User Interactions Using Console Mode versus Command Line Mode Operations\" summary=\"User Interactions with the Tester\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"rows\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"*\">\n<col width=\"50%\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t4\">Console mode</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t4\">Command Line Mode</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t4\" headers=\"r1c1-t4\">\n<p>The user starts the Access Tester from the command line.</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t4 r1c2-t4\">\n<p>The user or a shell script starts the Access Tester in command line mode.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t4\" headers=\"r1c1-t4\">\n<p>The user opens a previously saved configuration file to populate the application fields and minimize data entry, including server connection fields. <span class=\"bold\">Alternatively</span>, the user can use the Console and enter data manually</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t4 r1c2-t4\">\n<p>The Access Tester starts processing test cases based on the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t4\" headers=\"r1c1-t4\">\n<p>The user clicks the Connect button to open the connection with the OAM Server.</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t4 r1c2-t4\">\n<p>The Access Tester opens a connection with the OAM Server based on details in the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t4\" headers=\"r1c1-t4\">\n<p>Resource Protection: The user performs steps in a sequence to validate resource protection, authenticate user credentials, and authorize user access.</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t4 r1c2-t4\">\n<p>Resource Protection: The Access Tester starts processing test cases based on the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t4\" headers=\"r1c1-t4\">\n<p>When the test completes, the Access Tester generates:</p>\n<ul>\n<li>\n<p>A script with results</p>\n</li>\n<li>\n<p>A file with execution statistics including information about mismatched responses</p>\n</li>\n<li>\n<p>A log file detailing processing flow</p>\n</li>\n</ul>\n</td>\n<td align=\"left\" headers=\"r6c1-t4 r1c2-t4\">\n<p>Once the script completes, the Access Tester generates:</p>\n<ul>\n<li>\n<p>A script with results</p>\n</li>\n<li>\n<p>A file with execution statistics including information about mismatched responses</p>\n</li>\n<li>\n<p>A log file detailing processing flow</p>\n</li>\n</ul>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t4\" headers=\"r1c1-t4\">\n<p>The user repeats steps as needed to complete validation</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t4 r1c2-t4\">\n<p>The user repeats steps as needed to complete validation.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblhruleformalwidemax\" -->\n<p>The following overview outlines the tasks involved with using the Access Tester, and the topics where more information can be found in this chapter.</p>\n<a id=\"AIAAG1964\" name=\"AIAAG1964\"></a>\n<p class=\"subhead2\">Task overview: Testing OAM 11g connections and policies includes</p>\n<ol>\n<li>\n<p>Review the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACBEJDC\">Installing and Starting the Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACCHEFH\">Introduction to the Access Tester Console and Navigation</a></p>\n</li>\n</ul>\n</li>\n<li>\n<p>Perform and capture tests using the Access Tester Console as described in <a href=\"#CACEFCDD\">\"Testing Connectivity and Policies from the Access Tester Console\"</a>:</p>\n</li>\n<li>\n<p>Proceed to <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a></p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACBEJDC\" name=\"CACBEJDC\"></a><a id=\"AIAAG1965\" name=\"AIAAG1965\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Installing and Starting the Access Tester</h2>\n<p>The Access Tester consists of two jar files that can be used from any computer, either within or outside the WebLogic Server domain. This section describes how to install the Access Tester, which involves copying the Access Tester jar files to a computer from which you want to run tests. The Access Tester must be started from a command line regardless of the mode you choose for test input: Console mode or command line mode. This section is divided into the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACEEIBJ\">Installing the Access Tester</a></p>\n</li>\n<li>\n<p><a href=\"#CACBHCIJ\">About Access Tester Supported System Properties</a></p>\n</li>\n<li>\n<p><a href=\"#CACHABCG\">Starting the Access Tester Without System Properties For Use in Console Mode</a></p>\n</li>\n<li>\n<p><a href=\"#CACGJBHA\">Starting the Access Tester with System Properties For Use in Command Line Mode</a></p>\n</li>\n</ul>\n<a id=\"CACEEIBJ\" name=\"CACEEIBJ\"></a><a id=\"AIAAG1966\" name=\"AIAAG1966\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Installing the Access Tester</h3>\n<p>This topic describes how to install the Access Tester for use on any computer. Following installation, the Access Tester is ready to use. No additional setup is required.</p>\n<a id=\"AIAAG1967\" name=\"AIAAG1967\"></a>\n<p class=\"subhead2\">To install the Access Tester</p>\n<ol>\n<li>\n<p>Ensure that the computer from which the tester will be run includes JDK/JRE 6. For example, you can test for Java as follows:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -version\n</pre>\n<p>The previous command returns the following information:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java version \"1.6.0_18\"\nJava(TM) SE Runtime Environment (build 1.6.0_18-b07)\nJava HotSpot(TM) Client VM (build 16.0-b13, mixed mode)\n</pre></li>\n<li>\n<p>On a computer hosting the OAM Server, locate and copy the Access Tester Jar files. For example:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">Oracle_HOME/oam/server/tester/oamtest.jar \nOracle_HOME/oam/server/tester/nap-api.jar \n\n</pre></li>\n<li>\n<p>Store the jar file copies together in the same directory on any computer from which you want to run the Access Tester.</p>\n</li>\n<li>\n<p>Proceed as follows, depending on your environment and requirements:</p>\n<ul>\n<li>\n<p><a href=\"#CACHABCG\">Starting the Access Tester Without System Properties For Use in Console Mode</a> enables you to manually drive requests.</p>\n</li>\n<li>\n<p><a href=\"#CACGJBHA\">Starting the Access Tester with System Properties For Use in Command Line Mode</a></p>\n</li>\n<li>\n<p><a href=\"#CACHGIHD\">Executing a Test Script</a> enables you to use a test script that has been created against a \"Known Good\" policy configuration and marked as \"Known Good\"</p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACBHCIJ\" name=\"CACBHCIJ\"></a><a id=\"AIAAG1968\" name=\"AIAAG1968\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">About Access Tester Supported System Properties</h3>\n<p>The Access Tester supports a number of configuration options that are used for presentation or during certain aspects of testing. These options are specified at startup using the Java-D mechanism, as shown in <a href=\"#CACFBJDC\">Table 10-2</a>, which describes all supported system properties.</p>\n<div class=\"tblformal\"><a id=\"AIAAG1969\" name=\"AIAAG1969\"></a><a id=\"sthref251\" name=\"sthref251\"></a><a id=\"CACFBJDC\" name=\"CACFBJDC\"></a>\n<p class=\"titleintable\">Table 10-2 Access Tester Supported System Properties</p>\n<table class=\"Formal\" title=\"Access Tester Supported System Properties\" summary=\"Access Tester System Properties\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"26%\">\n<col width=\"23%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t5\">Property</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t5\">Access Tester Mode</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c3-t5\">Description and Command Syntax</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t5\" headers=\"r1c1-t5\">\n<p>log.traceconnfile</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t5 r1c2-t5\">\n<p>Console and Command Line modes</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t5 r1c3-t5\">\n<p>Logs connection details to the specified file name.</p>\n<p>-Dlog.traceconnfile=\"&lt;file-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t5\" headers=\"r1c1-t5\">\n<p>display.fontname</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t5 r1c2-t5\">\n<p>Console mode</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t5 r1c3-t5\">\n<p>Starts the Access Tester with the specified font. This could be useful in compensating for differences in display resolution.</p>\n<p>- Ddisplay.fontname =\"&lt;font-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t5\" headers=\"r1c1-t5\">\n<p>display.fontsize</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t5 r1c2-t5\">\n<p>Console mode</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t5 r1c3-t5\">\n<p>Starts the Access Tester with the specified font size. This could be useful in compensating for differences in display resolution.</p>\n<p>- Ddisplay.fontsize =\"&lt;font-size&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t5\" headers=\"r1c1-t5\">\n<p>display.usesystem</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t5 r1c2-t5\">\n<p>Console mode</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t5 r1c3-t5\">\n<p>Starts the Access Tester with the default font name and size (Dialog font, size 10).</p>\n<p>- Ddisplay.usesystem</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t5\" headers=\"r1c1-t5\">\n<p>script.scriptfile</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t5 r1c3-t5\">\n<p>Runs the script &lt;file-name&gt; in command line mode.</p>\n<p>-Dscript.scriptfile=\"&lt;file-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t5\" headers=\"r1c1-t5\">\n<p>control.configfile</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t5 r1c3-t5\">\n<p>Overwrites script's \"configfile\" attribute containing the absolute path to the configuration XML file with the connection information. The Access Tester uses the configuration file to establish a connection to the Policy Server indicated by Connection element.</p>\n<p>-Dcontrol.config=\"&lt;file-name&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t5\" headers=\"r1c1-t5\">\n<p>control.testname</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t5 r1c3-t5\">\n<p>Overwrites script's \"testname\" attribute of the Control element containing a string representing a name of the test series to be used in naming output script, stats, and log files. Output log files begin with &lt;testname&gt;_&lt;testnumber&gt;.</p>\n<p>-Dcontrol.testname=\"&lt;String&gt;\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t5\" headers=\"r1c1-t5\">\n<p>control.testnumber</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t5 r1c3-t5\">\n<p>Specifies the control number to be used in naming output script, stats, and log files. Output log files begin with &lt;testname&gt;_&lt;testnumber&gt;.</p>\n<p>-Dcontrol.testnumber=\"&lt;String&gt;\".</p>\n<p>Although the auto generated string is a 7 digit number based on current local time (2 character minutes + 2 character seconds + 3 character hundredths), any string can be used to denote the control number as long as it can be used in a filename.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t5\" headers=\"r1c1-t5\">\n<p>control.ignorecontent</p>\n</td>\n<td align=\"left\" headers=\"r10c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r10c1-t5 r1c3-t5\">\n<p>Overwrites script's \"ignorecontent\" attribute of the Control element indicating the Access Tester should ignore differences in Content between the original test case and current results.</p>\n<p>-Dcontrol.testname=\"true|false\"</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t5\" headers=\"r1c1-t5\">\n<p>control.loopback</p>\n</td>\n<td align=\"left\" headers=\"r11c1-t5 r1c2-t5\">\n<p>Command Line mode</p>\n</td>\n<td align=\"left\" headers=\"r11c1-t5 r1c3-t5\">\n<p>Runs the Access Tester in loopback mode to test the Access Tester for internal regressions against a known good script. Used for unit testing the Access Tester.</p>\n<p>-Dcontrol.loopback=\"true\"</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformal\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHABCG\" name=\"CACHABCG\"></a><a id=\"AIAAG1970\" name=\"AIAAG1970\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Starting the Access Tester Without System Properties For Use in Console Mode</h3>\n<p>To manually drive (and capture) requests and view real-time response through the graphical user interface, start the tester in Console mode. This procedure omits all system properties, even though several can be used with Console mode.</p>\n<p>The jar file defines the class to be started by default; no class name need be specified. Ensure that the nap-api.jar is present in the same directory as oamtest.jar.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"#CACBHCIJ\">\"About Access Tester Supported System Properties\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACGJBHA\">\"Starting the Access Tester with System Properties For Use in Command Line Mode\"</a></p>\n</li>\n</ul>\n</div>\n<a id=\"AIAAG1971\" name=\"AIAAG1971\"></a>\n<p class=\"subhead2\">To start the Access Tester in console mode without system properties</p>\n<ol>\n<li>\n<p>From the directory containing the Access Tester jar files, enter the following command:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -jar oamtest.jar\n</pre></li>\n<li>\n<p>Proceed to one of the following topics for more information:</p>\n<ul>\n<li>\n<p><a href=\"#CACCHEFH\">Introduction to the Access Tester Console and Navigation</a></p>\n</li>\n<li>\n<p><a href=\"#CACEFCDD\">Testing Connectivity and Policies from the Access Tester Console</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACGJBHA\" name=\"CACGJBHA\"></a><a id=\"AIAAG1972\" name=\"AIAAG1972\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Starting the Access Tester with System Properties For Use in Command Line Mode</h3>\n<p>This section is divided into the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACDGHJE\">About the Access Tester Command Line Mode</a></p>\n</li>\n<li>\n<p><a href=\"#CACHABCG\">Starting the Access Tester Without System Properties For Use in Console Mode</a></p>\n</li>\n</ul>\n<a id=\"CACDGHJE\" name=\"CACDGHJE\"></a><a id=\"AIAAG1973\" name=\"AIAAG1973\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h4 class=\"sect3\">About the Access Tester Command Line Mode</h4>\n<p>To run a test script, or to customize Access Tester operations, you must start the tester in command line mode and include system properties using the Java -D option.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACBHCIJ\">\"About Access Tester Supported System Properties\"</a></div>\n<p>When running in command line mode, the Access Tester returns completion codes that can be used by shell scripts to manage test runs. When you run the Access Tester in Console mode, you do not need to act upon codes that might be returned by the Access Tester.</p>\n<p>Shell scripts that wrap the Access Tester to execute specific test cases must be able to recognize and act upon exit codes communicated by the Access Tester. In command line mode, the Access Tester exits using System.Exit (N), where N can be one of the following codes:</p>\n<ul>\n<li>\n<p>0 indicates successful completion of all test cases with no mismatches. This also includes a situation where no test cases are defined in the input script.</p>\n</li>\n<li>\n<p>3 indicates successful completion of all test cases with at least one mismatch.</p>\n</li>\n<li>\n<p>1 indicates that an error prevented the Access Tester from running or completing test cases. This includes conditions such as No input script specified, Unable to read the input script, Unable to establish server connection, Unable to generate the target script.</p>\n</li>\n</ul>\n<p>These exit codes can be picked up by shell scripts ($? In Bourne shell) designed to drive the Access Tester to execute specific test cases.</p>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"AIAAG1974\" name=\"AIAAG1974\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" --><a id=\"sthref252\" name=\"sthref252\"></a>\n<h4 class=\"sect3\">Starting the Access Tester with System Properties</h4>\n<p>Use the following procedure to start the Access Tester in command line mode and specify any number of configuration options using the Java-D mechanism.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACBHCIJ\">\"About Access Tester Supported System Properties\"</a></div>\n<a id=\"AIAAG1975\" name=\"AIAAG1975\"></a>\n<p class=\"subhead2\">To start the Access Tester with system properties or for use in command line mode</p>\n<ol>\n<li>\n<p>From the directory containing the Access Tester jar files, enter the command with the appropriate system properties for your environment. For example:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -Dscript.scriptfile=\"\\tests\\script.xml\" -Dcontrol.ignorecontent=\"true\" \n-jar oamtest.jar\n</pre></li>\n<li>\n<p>After startup, proceed to one of the following topics for more information:</p>\n<ul>\n<li>\n<p><a href=\"#CACEFCDD\">Testing Connectivity and Policies from the Access Tester Console</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACCHEFH\" name=\"CACCHEFH\"></a><a id=\"AIAAG1976\" name=\"AIAAG1976\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Introduction to the Access Tester Console and Navigation</h2>\n<p>This section introduces the Access Tester Console, navigation, and controls.</p>\n<p><a href=\"#CACBCBEF\">Figure 10-3</a> shows the fixed-size Access Tester Console. This is the window through which users can interact with the application if the Access Tester is started in Console mode. The window can not be resized. Details follow the screen.</p>\n<div class=\"figure\"><a id=\"CACBCBEF\" name=\"CACBCBEF\"></a><a id=\"AIAAG1977\" name=\"AIAAG1977\"></a>\n<p class=\"titleinfigure\">Figure 10-3 Access Tester Console</p>\n<img width=\"672\" height=\"601\" src=\"img/access_test_1.gif\" alt=\"Surrounding text describes Figure 10-3 .\" title=\"Surrounding text describes Figure 10-3 .\"><br></div>\n<!-- class=\"figure\" -->\n<p>At the top of the main window are the menu names within a menu bar. Under the menu bar is the tool bar. All of the commands represented by buttons in the tool bar are also available as menu commands.The Access Tester Console is divided into four panels, described in <a href=\"#CHDFJBHC\">Table 10-3</a>.</p>\n<div class=\"tblhruleformalmax\"><a id=\"AIAAG1978\" name=\"AIAAG1978\"></a><a id=\"sthref253\" name=\"sthref253\"></a><a id=\"CHDFJBHC\" name=\"CHDFJBHC\"></a>\n<p class=\"titleintable\">Table 10-3 Access Tester Console Panels</p>\n<table class=\"HRuleFormalMax\" title=\"Access Tester Console Panels\" summary=\"Access Tester Panels\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"rows\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"28%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t9\">Panel Name</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t9\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t9\" headers=\"r1c1-t9\">\n<p>Server Connection</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t9 r1c2-t9\">\n<p>Provides fields for the information required to establish a connection to the OAM Server (a single primary server and a single secondary server), and the Connect button:</p>\n<p>See also: <a href=\"#CACCEFED\">\"Establishing a Connection Between the Access Tester and the OAM Server\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t9\" headers=\"r1c1-t9\">\n<p>Protected Resource URI</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t9 r1c2-t9\">\n<p>Provides information about a resource whose protected status needs to be validated. The Validate button is used to submit the Validate Resource server request.</p>\n<p>See also: <a href=\"#CACEGDHA\">\"Validating Resource Protection from the Access Tester Console\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t9\" headers=\"r1c1-t9\">\n<p>User Identity</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t9 r1c2-t9\">\n<p>Provides information about a user whose credentials need to be authenticated. The Authenticate button is used to submit the Authenticate User server request.</p>\n<p>See also: <a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a>.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t9\" headers=\"r1c1-t9\">\n<p>Status Messages</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t9 r1c2-t9\">\n<p>Provides a scrollable status message area containing messages displayed by the application in response to user gestures. The Authorize button is used to submit the Authorize User server request.</p>\n<p>See also: <a href=\"#CACHGAEC\">\"Observing Request Latency\"</a>.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblhruleformalmax\" -->\n<p>Text fields support right-clicking to display the Edit menu and drag-and-drop operations using the mouse and cursor.</p>\n<p>There are four primary buttons through which you submit test requests to the OAM Server. Each button acts as a trigger to initiate the named action described in <a href=\"#CACEGHGE\">Table 10-4</a>.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1979\" name=\"AIAAG1979\"></a><a id=\"sthref254\" name=\"sthref254\"></a><a id=\"CACEGHGE\" name=\"CACEGHGE\"></a>\n<p class=\"titleintable\">Table 10-4 Command Buttons in Access Tester Panels</p>\n<table class=\"FormalMax\" title=\"Command Buttons in Access Tester Panels\" summary=\"Access Tester Buttons\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"22%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t10\">Panel Button</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t10\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t10\" headers=\"r1c1-t10\">\n<p>Connect</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t10 r1c2-t10\">\n<p>Submits connection information and initiates connecting.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t10\" headers=\"r1c1-t10\">\n<p>Validate</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t10 r1c2-t10\">\n<p>Submits information provided in the Protected Resource URI panel and initiates validation of protection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t10\" headers=\"r1c1-t10\">\n<p>Authenticate</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t10 r1c2-t10\">\n<p>Submits information provided in the User Identity panel and initiates authentication confirmation.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t10\" headers=\"r1c1-t10\">\n<p>Authorize</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t10 r1c2-t10\">\n<p>Submits information provided in the User Identity panel and initiates authorization confirmation.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" -->\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACIJEEB\">\"Access Tester Menus and Command Buttons\"</a></div>\n<a id=\"CACIJEEB\" name=\"CACIJEEB\"></a><a id=\"AIAAG1980\" name=\"AIAAG1980\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Access Tester Menus and Command Buttons</h3>\n<p><a href=\"#CACEBDBI\">Table 10-5</a> identifies additional Access Tester Console buttons and their use. All command buttons provide a tip when the cursor is on the button.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1981\" name=\"AIAAG1981\"></a><a id=\"sthref255\" name=\"sthref255\"></a><a id=\"CACEBDBI\" name=\"CACEBDBI\"></a>\n<p class=\"titleintable\">Table 10-5 Additional Access Tester Buttons</p>\n<table class=\"FormalMax\" title=\"Additional Access Tester Buttons\" summary=\"Access Tester Tool Bar Buttons\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t12\">Command Buttons</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t12\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/load_ac.gif\" alt=\"Load Configuration command button\" title=\"Load Configuration command button\"></td>\n<td align=\"left\" headers=\"r2c1-t12 r1c2-t12\">\n<p>Loads connection configuration details that were saved to an XML file (config.xml, by default).</p>\n<p>You can refresh the information in the Console by clicking this button.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"34\" src=\"img/ac_save_cfg.gif\" alt=\"Access Tester Save Configuration\" title=\"Access Tester Save Configuration\"></td>\n<td align=\"left\" headers=\"r3c1-t12 r1c2-t12\">\n<p>Saves connection configuration details to a file (default name, config.xml). You can add the name of this document to the input script to provide proper connection information to the Access Tester running in command line mode.</p>\n<p>The Save command button at the bottom of the Console saves the content of the Status Message panel to a log file.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"35\" src=\"img/ac_clear.gif\" alt=\"Access Tester Clear Field\" title=\"Access Tester Clear Field\"></td>\n<td align=\"left\" headers=\"r4c1-t12 r1c2-t12\">\n<p>Clears fields on a panel containing the icon. Tool bar action clears all fields except connection fields if the connection has already been established.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/ac_capture.gif\" alt=\"Surrounding text describes ac_capture.gif.\" title=\"Surrounding text describes ac_capture.gif.\"> <img width=\"34\" height=\"28\" src=\"img/ac_capture.gif\" alt=\"Access Tester Capture Last Request\" title=\"Access Tester Capture Last Request\"></td>\n<td align=\"left\" headers=\"r5c1-t12 r1c2-t12\">\n<p>Captures the last named request to the capture queue with the corresponding response received from the OAM Server. Together, the request and response create a test case.</p>\n<p>The capture queue status at the bottom of the Console is updated to reflect the number of test cases in the queue.</p>\n<p>You can save the contents of the capture queue to create a test script containing multiple test cases using the Generate Script command on the Test menu or a command button.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"30\" src=\"img/ac_gen_script.gif\" alt=\"Access Tester Generate Script\" title=\"Access Tester Generate Script\"></td>\n<td align=\"left\" headers=\"r6c1-t12 r1c2-t12\">\n<p>Generates a test script that includes every test case currently in the capture queue, and asks if the queue should be cleared. Do not clear the queue until all your test cases have been captured and saved to a test script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"34\" src=\"img/ac_run_script.gif\" alt=\"Access Tester Run Script\" title=\"Access Tester Run Script\"></td>\n<td align=\"left\" headers=\"r7c1-t12 r1c2-t12\">\n<p>Runs a test script against the current OAM Server. The Status message window is populated with the execution status as the script progresses through each test case.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/ac_import_uri.gif\" alt=\"Access Tester Import UIR\" title=\"Access Tester Import UIR\"></td>\n<td align=\"left\" headers=\"r8c1-t12 r1c2-t12\">\n<p>Imports a copied URI from the clipboard after parsing it to populate fields in the URI panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t12\" headers=\"r1c1-t12\"><img width=\"34\" height=\"28\" src=\"img/ac_show_pw.gif\" alt=\"Access Tester Display Password\" title=\"Access Tester Display Password\"></td>\n<td align=\"left\" headers=\"r9c1-t12 r1c2-t12\">\n<p>Displays a dialog showing the password in clear text</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" -->\n<p>The Access Tester provides the menus described in <a href=\"#BGBJEFBA\">Table 10-6</a>. All menu items have mnemonics that are exposed by holding down the ALT key (on Windows systems). There are also command accelerators (keyboard activation) available using the CTRL-&lt;KEY&gt; combination defined for each menu command.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1982\" name=\"AIAAG1982\"></a><a id=\"sthref256\" name=\"sthref256\"></a><a id=\"BGBJEFBA\" name=\"BGBJEFBA\"></a>\n<p class=\"titleintable\">Table 10-6 Access Tester Menus</p>\n<table class=\"FormalMax\" title=\"Access Tester Menus\" summary=\"Access Tester Menus\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"18%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t13\">Menu Title</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t13\">Menu Commands</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t13\" headers=\"r1c1-t13\">\n<p>File</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t13 r1c2-t13\">\n<ul>\n<li>\n<p>Open Configuration</p>\n</li>\n<li>\n<p>Save Configuration</p>\n</li>\n<li>\n<p>Exit</p>\n</li>\n</ul>\n<p><span class=\"bold\">Note</span>: To minimize the amount of data entry the Save Configuration and Open Configuration menu (and tool bar command buttons) allow for specific Connection, URI, and Identity information to be saved to (and read from) a file. Thus, it becomes fairly simple to manage multiple configurations. Also, the configuration file can be used as input to the Access Tester when you run it in command line mode and execute a test script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t13\" headers=\"r1c1-t13\">\n<p>Edit</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t13 r1c2-t13\">\n<p>Provides standard editing commands, which act on fields:</p>\n<ul>\n<li>\n<p>Cut</p>\n</li>\n<li>\n<p>Copy</p>\n</li>\n<li>\n<p>Paste</p>\n</li>\n<li>\n<p>Clear all fields</p>\n</li>\n<li>\n<p>Import URI fields from a saved URL</p>\n</li>\n</ul>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t13\" headers=\"r1c1-t13\">\n<p>Test</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t13 r1c2-t13\">\n<ul>\n<li>\n<p>Capture last \"...\" request (for example, Capture last \"authorize\" request)</p>\n</li>\n<li>\n<p>Save test script</p>\n</li>\n<li>\n<p>Run test script</p>\n</li>\n</ul>\n<p><span class=\"bold\">Note</span>: You can use functions here to capture the last request and response to create a test case that you can save to a test script to be run at a later time.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t13\" headers=\"r1c1-t13\">\n<p>Help</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t13 r1c2-t13\">&nbsp;</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACEFCDD\" name=\"CACEFCDD\"></a><a id=\"AIAAG1983\" name=\"AIAAG1983\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Testing Connectivity and Policies from the Access Tester Console</h2>\n<p>This section describes how to perform quick spot checks using the Access Tester in Console mode with OAM Servers.</p>\n<p>Spot checks or troubleshooting connections between the Agent and OAM Server can help you assess whether the Agent can communicate with the OAM Server, which is especially helpful after an upgrade or product migration. Spot checks or troubleshooting resource protection that can be exercised by Agents and OAM Servers can help you develop end-to-end tests of policy configuration during the application lifecycle.</p>\n<p>The following overview identifies the tasks and sequence to be performed and where to locate additional information about each task.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nYou can capture each request and response pair to create a test case, and save the test cases to a script file that can be run later. For details, see <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</div>\n<a id=\"AIAAG1984\" name=\"AIAAG1984\"></a>\n<p class=\"subhead2\">Task overview: Performing spot checks from the Access Tester Console</p>\n<ol>\n<li>\n<p>Start the Access Tester, as described in <a href=\"#CACBEJDC\">\"Installing and Starting the Access Tester\"</a>.</p>\n</li>\n<li>\n<p>Add relevant details to the Server Connection panel and click Connect, as described in <a href=\"#CACCEFED\">\"Establishing a Connection Between the Access Tester and the OAM Server\"</a>.</p>\n</li>\n<li>\n<p>Enter or import details into the Protected Resource URI pane and click Validate, as described in <a href=\"#CACEGDHA\">\"Validating Resource Protection from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>Add relevant details to the User Identity panel and click Authenticate, as described in <a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>After successful authentication, click Authorize in the User Identity panel, as described in <a href=\"#CACJIADH\">\"Testing User Authorization from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>Check the latency of requests, as described in <a href=\"#CACHGAEC\">\"Observing Request Latency\"</a>.</p>\n</li>\n</ol>\n<a id=\"CACCEFED\" name=\"CACCEFED\"></a><a id=\"AIAAG1985\" name=\"AIAAG1985\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Establishing a Connection Between the Access Tester and the OAM Server</h3>\n<p>Before you can send a request to the OAM Server you must establish a connection between the Access Tester and the server. This section describes how to establish that connectivity.</p>\n<ul>\n<li>\n<p><a href=\"#BGBFHDDJ\">About the Connection Panel</a></p>\n</li>\n<li>\n<p><a href=\"#BGBDEIFA\">Connecting the Access Tester with the OAM Server</a></p>\n</li>\n</ul>\n<a id=\"BGBFHDDJ\" name=\"BGBFHDDJ\"></a><a id=\"AIAAG1986\" name=\"AIAAG1986\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About the Connection Panel</h4>\n<p>You enter required information for the OAM Server and the Agent you are emulating in the Access Tester Connection panel and then click the Connect button. The Tester initiates the connection, and displays the status in the Status Messages panel. Once the connection is established, it is used for all further operations.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Caution:</p>\nOnce the connection is established, it cannot be changed until you restart the Access Tester Console.</div>\n<p><a href=\"#CACEDDJD\">Figure 10-4</a> illustrates the Server Connection panel and controls.</p>\n<div class=\"figure\"><a id=\"CACEDDJD\" name=\"CACEDDJD\"></a><a id=\"AIAAG1987\" name=\"AIAAG1987\"></a>\n<p class=\"titleinfigure\">Figure 10-4 Server Connection Panel in the Access Tester</p>\n<img width=\"663\" height=\"141\" src=\"img/ac_svr_connect.gif\" alt=\"Surrounding text describes Figure 10-4 .\" title=\"Surrounding text describes Figure 10-4 .\"><br></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#CACFIECI\">Table 10-7</a> describes the information needed to establish the connection. The source of your values is the OAM Administration Console, System Configuration tab.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG1988\" name=\"AIAAG1988\"></a><a id=\"sthref257\" name=\"sthref257\"></a><a id=\"CACFIECI\" name=\"CACFIECI\"></a>\n<p class=\"titleintable\">Table 10-7 Connection Panel Information</p>\n<table class=\"FormalMax\" title=\"Connection Panel Information\" summary=\"Access Tester Connection Details\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t16\">Fields</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t16\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t16\" headers=\"r1c1-t16\">\n<p>IP Address</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t16 r1c2-t16\">\n<p>The IP Address of the Primary and Secondary OAM Proxy listens on for this set of tests.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you enter values for only the Primary OAM Proxy. The Secondary OAM Proxy is needed only if you want to test failover between the primary and secondary OAM Server. However, a more practical use of the Secondary Server is reserved for later use, when the OAP API supports load balancing between Primary and Secondary OAM Server.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t16\" headers=\"r1c1-t16\">\n<p>Port</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t16 r1c2-t16\">\n<p>Enter the port number of the Primary and Secondary OAM Server.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t16\" headers=\"r1c1-t16\">\n<p>Max Conn</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t16 r1c2-t16\">\n<p>The maximum number of physical connection (TCP) sockets the Access Tester will use. Access Tester emulates a single threaded Agent.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you accept the default value, 1.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t16\" headers=\"r1c1-t16\">\n<p>Min Conn</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t16 r1c2-t16\">\n<p>The minimum number of physical connection (TCP) sockets the Access Tester will use. The Access Tester emulates a single threaded Agent.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you accept the default value, 1.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t16\" headers=\"r1c1-t16\">\n<p>Timeout</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t16 r1c2-t16\">\n<p>The number of milliseconds the Access Tester should wait for the connection to be established or to receive a response from the OAM Server.</p>\n<p><span class=\"bold\">Note</span>: Oracle recommends that you accept the default value.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t16\" headers=\"r1c1-t16\">\n<p>Mode</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t16 r1c2-t16\">\n<p>The level of communication security that is designated for the Agent to be emulated.</p>\n<ul>\n<li>\n<p><a id=\"sthref258\" name=\"sthref258\"></a>Open--No communication security set between the Agent and OAM Server.</p>\n</li>\n<li>\n<p><a id=\"sthref259\" name=\"sthref259\"></a>Simple--The physical connection is encrypted using built-in certificates. Choosing Simple presents a field for the global pass phrase set the OAM Server.</p>\n</li>\n<li>\n<p><a id=\"sthref260\" name=\"sthref260\"></a>Cert--The physical connection is encrypted using customer-supplied certificates. Choosing Cert presents a button that opens a dialog for the certificate information.</p>\n</li>\n</ul>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t16\" headers=\"r1c1-t16\">\n<p>Agent ID</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t16 r1c2-t16\">\n<p>Enter the identity of the OAM Agent the Tester is simulating.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t16\" headers=\"r1c1-t16\">\n<p>Agent Password</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t16 r1c2-t16\">\n<p>Enter the password for the OAM Agent the Tester is simulating, if there is one configured.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t16\" headers=\"r1c1-t16\"><img width=\"39\" height=\"38\" src=\"img/ac_yes.gif\" alt=\"Access Tester Yes\" title=\"Access Tester Yes\"></td>\n<td align=\"left\" headers=\"r10c1-t16 r1c2-t16\">\n<p>The green check mark beside the Connect button indicates a \"Yes\" response; the connection is made. The Status Messages panel also indicates a \"Yes\" response for the connection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t16\" headers=\"r1c1-t16\"><img width=\"39\" height=\"38\" src=\"img/ac_no.gif\" alt=\"Access Tester No\" title=\"Access Tester No\"></td>\n<td align=\"left\" headers=\"r11c1-t16 r1c2-t16\">\n<p>The red circle beside the Connect button indicates a \"No\" response; no connection exists. The Status Messages panel also indicates a \"No\" response for the connection.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" -->\n<p>After entering information and establishing a connection, you can save details to a configuration file that can be re-used later.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACCEFED\">\"Establishing a Connection Between the Access Tester and the OAM Server\"</a></div>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"BGBDEIFA\" name=\"BGBDEIFA\"></a><a id=\"AIAAG1989\" name=\"AIAAG1989\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Connecting the Access Tester with the OAM Server</h4>\n<p>Use the following procedure to submit your connection details for the OAM Server.</p>\n<a id=\"AIAAG2212\" name=\"AIAAG2212\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBEJDC\">Installing and Starting the Access Tester</a></p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#BGBFHDDJ\">\"About the Connection Panel\"</a></div>\n<a id=\"AIAAG1990\" name=\"AIAAG1990\"></a>\n<p class=\"subhead2\">To test connectivity between the Access Tester and the OAM Server</p>\n<ol>\n<li>\n<p>In the Server Connection Panel (<a href=\"#CACFIECI\">Table 10-7</a>), enter:</p>\n<ul>\n<li>\n<p>Primary and secondary OAM Proxy details</p>\n</li>\n<li>\n<p>Timeout period</p>\n</li>\n<li>\n<p>Communication encryption mode</p>\n</li>\n<li>\n<p>Agent details</p>\n</li>\n</ul>\n</li>\n<li>\n<p>Click the Connect button.</p>\n</li>\n<li>\n<p>Beside the Connect button, look for the green check mark indicating the connection is established.</p>\n</li>\n<li>\n<p>In the Status Messages panel, verify a Yes response.</p>\n<p>If the connection still cannot be made, start the Access Tester Console using the Trace Connection command mode and look for additional details in the connection log. Also, ask the OAM administrator of the OAM Server to review the policy server log.</p>\n</li>\n<li>\n<p>Save Good Connection Details: From the Test menu, click the Generate a Script command and enter a name for this configuration file (or use the default name, config.xml).</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACEGDHA\" name=\"CACEGDHA\"></a><a id=\"AIAAG1991\" name=\"AIAAG1991\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Validating Resource Protection from the Access Tester Console</h3>\n<p>Before a user can access a resource, the Agent must first validate that the resource is protected. Using the Access Tester, you can act as the Agent to have the OAM Server validate whether or not the given URI is protected and communicate the response to the Access Tester, as described here.</p>\n<ul>\n<li>\n<p><a href=\"#CACCJGHJ\">About the Protected Resource URI Panel</a></p>\n</li>\n<li>\n<p><a href=\"#CACBFEHJ\">Validating Resource Protection</a></p>\n</li>\n</ul>\n<a id=\"CACCJGHJ\" name=\"CACCJGHJ\"></a><a id=\"AIAAG1992\" name=\"AIAAG1992\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About the Protected Resource URI Panel</h4>\n<p>You must enter required information for the resource you want to validate in the Access Tester Protected Resource URI panel, and then click the Validate button.</p>\n<p>To minimize data entry, you can import long URIs that you have copied from a browser and then click the Import URI command button. The Tester parses the URI saved to the clipboard and populates the URI fields in the Access Tester.</p>\n<p><a href=\"#CACCABHB\">Figure 10-5</a> illustrates the panel where you enter the URI details to validate that the resource is protected. When combined, the URI fields follow RFC notation. For example: <code><span class=\"codeinlineitalic\">http://oam_server1:7777/index.html</span></code>.</p>\n<div class=\"figure\"><a id=\"CACCABHB\" name=\"CACCABHB\"></a><a id=\"AIAAG1993\" name=\"AIAAG1993\"></a>\n<p class=\"titleinfigure\">Figure 10-5 Protected Resource URI Panel in the Access Tester</p>\n<img width=\"665\" height=\"112\" src=\"img/ac_protected_res.gif\" alt=\"Surrounding text describes Figure 10-5 .\" title=\"Surrounding text describes Figure 10-5 .\"><br></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#CACFCHBA\">Table 10-8</a> describes the information needed to perform this validation.</p>\n<div class=\"tblformal\"><a id=\"AIAAG1994\" name=\"AIAAG1994\"></a><a id=\"sthref261\" name=\"sthref261\"></a><a id=\"CACFCHBA\" name=\"CACFCHBA\"></a>\n<p class=\"titleintable\">Table 10-8 Protected Resource URI Panel Fields and Controls</p>\n<table class=\"Formal\" title=\"Protected Resource URI Panel Fields and Controls \" summary=\"Access Tester Validate Resource Protection\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t19\">Field or Control</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t19\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t19\" headers=\"r1c1-t19\">\n<p>Scheme</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t19 r1c2-t19\">\n<p>Enter http or https, depending on the communication security specified for the resource.</p>\n<p><span class=\"bold\">Note</span>: The Access Tester supports only http or https resources. You cannot use the Access Tester to test policies that protect custom non-http resources.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t19\" headers=\"r1c1-t19\">\n<p>Host</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t19 r1c2-t19\">\n<p>Enter a valid host name for the resource.</p>\n<p><span class=\"bold\">Note</span>: Your &lt;<span class=\"italic\">host:port</span>&gt; combination specified in the Access Tester must match one of the Host Identifiers defined in the OAM Administration Console. If the host identifier is not recognized, OAM cannot validate resource protection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t19\" headers=\"r1c1-t19\">\n<p>Port</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t19 r1c2-t19\">\n<p>Enter a valid port for the URI.</p>\n<p><span class=\"bold\">Note</span>: The &lt;host:port&gt; combination specified in the Access Tester must match one of the Host Identifiers as defined in the OAM Server. If the host identifier is not recognized, OAM cannot validate resource protection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t19\" headers=\"r1c1-t19\">\n<p>Resource</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t19 r1c2-t19\">\n<p>Enter the Resource component of the URI (/index.htm in the example). This resource should match a resource defined for an authentication and authorization policy in the OAM Administration Console.</p>\n<p><span class=\"bold\">Note</span>: If protected, the resource identifier that you provide here must match the one specified in an authorization policy in the OAM Administration Console.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t19\" headers=\"r1c1-t19\"><img width=\"34\" height=\"28\" src=\"img/ac_import_uri.gif\" alt=\"Access Tester Import UIR\" title=\"Access Tester Import UIR\"></td>\n<td align=\"left\" headers=\"r6c1-t19 r1c2-t19\">\n<p>Click this button to parse and import a URI that is saved on a clipboard.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t19\" headers=\"r1c1-t19\">\n<p>Operation</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t19 r1c2-t19\">\n<p>Select the operational component of the URI from the list provided in the Access Tester. The OAM Server does not distinguish between different actions, however. Therefore, leaving this set to Get should suffice.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t19\" headers=\"r1c1-t19\">\n<p>Get Auth Scheme</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t19 r1c2-t19\">\n<p>Check this box to request the OAM Server to return details about the Authentication Scheme that is used to secure the protected resource. If the URI is protected, this information is displayed in the Status Messages panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t19\" headers=\"r1c1-t19\">\n<p>Validate</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t19 r1c2-t19\">\n<p>Click the Validate button to submit the request to the OAM Server. When the response is received, the Access Tester displays it in the Status Messages panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t19\" headers=\"r1c1-t19\"><img width=\"39\" height=\"38\" src=\"img/ac_yes.gif\" alt=\"Access Tester Yes\" title=\"Access Tester Yes\"></td>\n<td align=\"left\" headers=\"r10c1-t19 r1c2-t19\">\n<p>A green check mark appearing beside the Validate button indicates a \"Yes\" response; the resource is protected. The Status Messages panel provides the redirect URL for the resource and that credentials are expected.</p>\n<p><span class=\"bold\">Note</span>: If you checked the Get Auth Scheme box, the name and level of the Authentication Scheme that protects this resource are also provided in the Status Messages panel.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t19\" headers=\"r1c1-t19\"><img width=\"39\" height=\"38\" src=\"img/ac_no.gif\" alt=\"Access Tester No\" title=\"Access Tester No\"></td>\n<td align=\"left\" headers=\"r11c1-t19 r1c2-t19\">\n<p>A red circle appearing beside the Validate button indicates that the resource is not protected. A No response will also appear in the Status Messages.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformal\" -->\n<p>You can capture each request and response pair to create a test case, and save multiple test cases to a script file that can be run later.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"#CACEGDHA\">\"Validating Resource Protection from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a></p>\n</li>\n</ul>\n</div>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"CACBFEHJ\" name=\"CACBFEHJ\"></a><a id=\"AIAAG1995\" name=\"AIAAG1995\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Validating Resource Protection</h4>\n<p>Use the following procedure to submit your resource information to the OAM Server and verify responses in the Status Messages panel.</p>\n<a id=\"AIAAG2213\" name=\"AIAAG2213\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACCEFED\">Establishing a Connection Between the Access Tester and the OAM Server</a></p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#CACCJGHJ\">\"About the Protected Resource URI Panel\"</a></div>\n<a id=\"AIAAG1996\" name=\"AIAAG1996\"></a>\n<p class=\"subhead2\">To confirm that a resource is protected</p>\n<ol>\n<li>\n<p>In the Access Tester Protected Resource URI panel, enter or import your own resource information (<a href=\"#CACFCHBA\">Table 10-8</a>).</p>\n</li>\n<li>\n<p>Click the Validate button to submit the request.</p>\n</li>\n<li>\n<p>Review Access Tester output, including the relevant data about the resource such as how the resource is protected, level of protection, and so on.</p>\n</li>\n<li>\n<p>Beside the Validate button, look for the green check mark indicating the resource is protected.</p>\n</li>\n<li>\n<p>In the Status Messages panel, verify the redirect URL, authentication scheme, and that credentials are expected.</p>\n</li>\n<li>\n<p>Capture the request and response to create a test case for use later, as described in <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</p>\n</li>\n<li>\n<p>Retain the URI to minimize data entry and server processing using one of the following methods.</p>\n</li>\n<li>\n<p>Proceed to <a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a></p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHGIEC\" name=\"CACHGIEC\"></a><a id=\"AIAAG1997\" name=\"AIAAG1997\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Testing User Authentication from the Access Tester Console</h3>\n<p>This topic provides the following information:</p>\n<ul>\n<li>\n<p><a href=\"#BGBHJJJG\">About the User Identity Panel</a></p>\n</li>\n<li>\n<p><a href=\"#BGBDGJEF\">Testing User Credential Authentication</a></p>\n</li>\n</ul>\n<a id=\"BGBHJJJG\" name=\"BGBHJJJG\"></a><a id=\"AIAAG1998\" name=\"AIAAG1998\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h4 class=\"sect3\">About the User Identity Panel</h4>\n<p>Before a user can access a resource, the Agent must validate the user's identity based on the defined authentication policy on the OAM Server. Using the Access Tester, you can act as the Agent to have the OAM Server authenticate a specific userID for the protected resource. All relevant authentication responses are considered during this policy evaluation.</p>\n<p><a href=\"#BGBEGHAI\">Figure 10-6</a> illustrates the Access Tester panel where you enter the information needed to test authentication.</p>\n<div class=\"figure\"><a id=\"BGBEGHAI\" name=\"BGBEGHAI\"></a><a id=\"AIAAG1999\" name=\"AIAAG1999\"></a>\n<p class=\"titleinfigure\">Figure 10-6 Access Tester User Identity Panel</p>\n<img width=\"652\" height=\"115\" src=\"img/ac_user_id.gif\" alt=\"Surrounding text describes Figure 10-6 .\" title=\"Surrounding text describes Figure 10-6 .\"><br></div>\n<!-- class=\"figure\" -->\n<p><a href=\"#BGBEFAJB\">Table 10-9</a> describes the information you must provide.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2000\" name=\"AIAAG2000\"></a><a id=\"sthref262\" name=\"sthref262\"></a><a id=\"BGBEFAJB\" name=\"BGBEFAJB\"></a>\n<p class=\"titleintable\">Table 10-9 Access Tester User Identity Panel Fields and Controls</p>\n<table class=\"FormalMax\" title=\"Access Tester User Identity Panel Fields and Controls\" summary=\"Access Tester User Identity Panel\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"29%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t22\">Field or Control</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t22\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t22\" headers=\"r1c1-t22\">\n<p>IP Address</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t22 r1c2-t22\">\n<p>Enter the IP Address of the user whose credentials are being validated. All Agents communicating with the OAM Server send the IP address of the end user.</p>\n<p>Default: The IP address that is filled in belongs to the computer from which the Access Tester is run.</p>\n<p>To test a policy that requires a real user IP address, replace the default IP address with the real IP address.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t22\" headers=\"r1c1-t22\">\n<p>User Name</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t22 r1c2-t22\">\n<p>Enter the userID of the individual whose credentials are being validated.</p>\n<p>Note: The Access Tester enables (or disables) the user name and password fields if the resource is protected by an authentication scheme that requires those credentials. Otherwise, this field is disabled.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t22\" headers=\"r1c1-t22\">\n<p>Password</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t22 r1c2-t22\">\n<p>Enter the password of the individual whose credentials are being validated.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t22\" headers=\"r1c1-t22\">\n<p>?</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t22 r1c2-t22\">\n<p>Click this button to display the password in clear text within a popup window.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t22\" headers=\"r1c1-t22\">\n<p>User Certificate Store</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t22 r1c2-t22\">\n<p>The file (in PEM format) containing the X.509 certificate of the user whose credentials should be authenticated.</p>\n<p>If the URI is protected by the X.509 Authentication Scheme, the Access Tester uses the PEM-formatted X.509 certificate as a credential instead of (or in addition to) the username/password. If the the Authentication Scheme does not require an X.509 certificate, this field is disabled.</p>\n<p>Note: For certificate-based authentication to work, the OAM Server must be properly configured with root CA certificates and SSL keystore certificates. See <a href=\"keytool.htm#BHBGHIFC\">Appendix E</a> for details about securing communication between OAM 11g Servers and WebGates.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r7c1-t22\" headers=\"r1c1-t22\">\n<p>...</p>\n</td>\n<td align=\"left\" headers=\"r7c1-t22 r1c2-t22\">\n<p>Click this button to browse the file system for the user certificate store path.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r8c1-t22\" headers=\"r1c1-t22\">\n<p>Authenticate</p>\n</td>\n<td align=\"left\" headers=\"r8c1-t22 r1c2-t22\">\n<p>Click the Authenticate button to submit the request to the OAM Server and look for a response in the Status Messages panel.</p>\n<p>Note: The type of credentials supplied (username/password or X.509 certificate) must match the requirements of the authentication scheme that protects the URI.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r9c1-t22\" headers=\"r1c1-t22\">\n<p>Authorize</p>\n</td>\n<td align=\"left\" headers=\"r9c1-t22 r1c2-t22\">\n<p>After the user's credentials are validated, you can click the Authorize button to submit the request for the resource to the OAM Server. Check the Status Messages panel for a response.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r10c1-t22\" headers=\"r1c1-t22\"><img width=\"39\" height=\"38\" src=\"img/ac_yes.gif\" alt=\"Access Tester Yes\" title=\"Access Tester Yes\"></td>\n<td align=\"left\" headers=\"r10c1-t22 r1c2-t22\">\n<p>A green check mark appearing beside the Authenticate button indicates authentication success; The Status Messages panel also indicates \"yes\" authentication was successful, and provides the user DN and session id.</p>\n<p>A green check mark appearing beside the Authorize button indicates authorization success; The Status Messages panel also indicates \"yes\" authorization was successful, and provides application domain details.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r11c1-t22\" headers=\"r1c1-t22\"><img width=\"39\" height=\"38\" src=\"img/ac_no.gif\" alt=\"Access Tester No\" title=\"Access Tester No\"></td>\n<td align=\"left\" headers=\"r11c1-t22 r1c2-t22\">\n<p>A red circle appearing beside the Authenticate button indicates authentication failure; The Status Messages panel also indicates \"no\" authentication was not successful.</p>\n<p>A red circle appearing beside the Authorize button indicates authorization failure; The Status Messages panel also indicates \"no\" authorization was not successful.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" -->\n<p>You can capture each request and response pair to create a test case, and save multiple test cases to a script file that can be run later.</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<ul>\n<li>\n<p><a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a></p>\n</li>\n</ul>\n</div>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"BGBDGJEF\" name=\"BGBDGJEF\"></a><a id=\"AIAAG2001\" name=\"AIAAG2001\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Testing User Credential Authentication</h4>\n<p>Use the following procedure to submit the end user credentials to the OAM Server and verify authentication. All relevant authentication responses are considered during this policy evaluation.</p>\n<a id=\"AIAAG2214\" name=\"AIAAG2214\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACEGDHA\">Validating Resource Protection from the Access Tester Console</a> with URI information retained in the Console</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#BGBHJJJG\">\"About the User Identity Panel\"</a></div>\n<a id=\"AIAAG2002\" name=\"AIAAG2002\"></a>\n<p class=\"subhead2\">To test user credential authentication</p>\n<ol>\n<li>\n<p>In the Access Tester User Identity panel, enter information for the user to be authenticated (<a href=\"#BGBEFAJB\">Table 10-9</a>).</p>\n</li>\n<li>\n<p>Click the Authenticate button to submit the request.</p>\n</li>\n<li>\n<p>Beside the Authenticate button, look for the green check mark indicating the user is authenticated.</p>\n<p><span class=\"bold\">Not Successful</span>: Confirm that you entered the correct userID and password and try again. Also, check the OAM Administration Console for an active user session that you might need to end, as described in <a href=\"session.htm#CJHJHHAB\">Chapter 12</a>.</p>\n</li>\n<li>\n<p>Capture the request and response to create a test case for use later, as described in <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</p>\n</li>\n<li>\n<p>Retain the URI and user identity information and proceed to <a href=\"#CACJIADH\">\"Testing User Authorization from the Access Tester Console\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACJIADH\" name=\"CACJIADH\"></a><a id=\"AIAAG2003\" name=\"AIAAG2003\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Testing User Authorization from the Access Tester Console</h3>\n<p>Before a user can access a resource, the Agent must validate the user's permissions based on defined policies on the OAM Server. Using the Access Tester, you can act as the Agent to have the OAM Server validate whether or not the authenticated user identity can be authorized to access the resource.</p>\n<p>Use the following procedure to verify the authenticated end user's authorization for the resource. All relevant authorization constraints and responses are considered during this policy evaluation.</p>\n<a id=\"AIAAG2215\" name=\"AIAAG2215\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACHGIEC\">Testing User Authentication from the Access Tester Console</a> with all information retained in the Console</p>\n<div class=\"infoboxnotealso\">\n<p class=\"notep1\">See Also:</p>\n<a href=\"#BGBHJJJG\">\"About the User Identity Panel\"</a></div>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nOnce the protected resource URI is confirmed and the user's identity is authenticated from the Access Tester, no further information is needed. You simply click the Authorize button to submit the request. However, if the resource is changed to another you must start the sequence anew and validate, then authenticate, and then authorize.</div>\n<a id=\"AIAAG2004\" name=\"AIAAG2004\"></a>\n<p class=\"subhead2\">To test user authorization</p>\n<ol>\n<li>\n<p>In the Access Tester User Identity panel, confirm the user is authenticated (<a href=\"#BGBEFAJB\">Table 10-9</a>).</p>\n</li>\n<li>\n<p>In the Access Tester User Identity panel, click the Authorization button.</p>\n</li>\n<li>\n<p>Beside the Authorization button, look for the green check mark indicating the user is authorized.</p>\n<p><span class=\"bold\">Not Successful</span>: Confirm the authorization policy using the OAM Administration Console.</p>\n</li>\n<li>\n<p>In the Status Messages panel (or execution log file), verify details about the test run.</p>\n</li>\n<li>\n<p>Capture the request and response to create a test case for use later, as described in <a href=\"#CACJIAEF\">\"Creating and Managing Test Cases and Scripts\"</a>.</p>\n</li>\n<li>\n<p>Proceed to:</p>\n<ul>\n<li>\n<p><a href=\"#CACHGAEC\">Observing Request Latency</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBIEED\">Evaluating Scripts, Log File, and Statistics</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHGAEC\" name=\"CACHGAEC\"></a><a id=\"AIAAG2005\" name=\"AIAAG2005\"></a>\n<div class=\"sect2\">\n<h3 class=\"sect2\">Observing Request Latency</h3>\n<p>To understand OAM Server performance you must know how well the OAM Server handles requests passed by the Agent. While there are many ways to expose a server's metrics, it is sometimes useful to expose server performance from the standpoint of the Agent. Using the Access Tester, you can do just that as described here.</p>\n<a id=\"AIAAG2216\" name=\"AIAAG2216\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBEJDC\">\"Installing and Starting the Access Tester\"</a></p>\n<a id=\"AIAAG2006\" name=\"AIAAG2006\"></a>\n<p class=\"subhead2\">Task overview: Observing request latency includes</p>\n<ol>\n<li>\n<p><a href=\"#CACBFEHJ\">\"Validating Resource Protection\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACHGIEC\">\"Testing User Authentication from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p><a href=\"#CACJIADH\">\"Testing User Authorization from the Access Tester Console\"</a></p>\n</li>\n<li>\n<p>Check latency information in the execution logfile as shown here, as well as in other files generated during a test run. For example:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">...\n[2/3/10 11:03 PM][info] Summary statistics\n[2/3/10 11:03 PM][info] Matched 4 of 4, avg latency 232ms vs 238ms\n[2/3/10 11:03 PM][info] Validate: matched 2 of 2, avg latency 570ms vs 578ms\n[2/3/10 11:03 PM][info] Authenticate: matched 1 of 1, avg latency 187ms vs 187ms\n[2/3/10 11:03 PM][info] Authorize: matched 1 of 1, avg latency 172ms vs 188ms\n...\n</pre></li>\n<li>\n<p>Proceed to:</p>\n<ul>\n<li>\n<p><a href=\"#CACJIAEF\">Creating and Managing Test Cases and Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBIEED\">Evaluating Scripts, Log File, and Statistics</a></p>\n</li>\n</ul>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACJIAEF\" name=\"CACJIAEF\"></a><a id=\"AIAAG2007\" name=\"AIAAG2007\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Creating and Managing Test Cases and Scripts</h2>\n<p>Test management refers to the creation of repeatable tests that can be executed at any time by an individual administrator or system. Quick spot checks are very useful and effective in troubleshooting current issues. However, a more predictable and repeatable approach to validating server and policy configuration is often necessary. This approach can include testing OAM Server configuration for regressions after a product revision, or during a policy development and QA cycle.</p>\n<p>To be useful such tests must allow for multiple use cases to be executed as group. Once the test scripts have been designed and validated as correct, replaying the tests against the OAM Server helps identify regressions in a policy configuration.</p>\n<p>This section provides the information you need to perform test management in the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CACJIHFH\">About Test Cases and Test Scripts</a></p>\n</li>\n<li>\n<p><a href=\"#CACBICDG\">Generating an Input Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CHDIDJEB\">Personalizing an Input Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CACHGIHD\">Executing a Test Script</a></p>\n</li>\n</ul>\n<a id=\"CACJIHFH\" name=\"CACJIHFH\"></a><a id=\"AIAAG2008\" name=\"AIAAG2008\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About Test Cases and Test Scripts</h3>\n<p>A test case is created from the request sent to, and response data received from, the OAM Server using the Access Tester. Among other data elements, a test case includes request latency and other identifying information that enables analysis and comparison of old and new test cases.</p>\n<p>Once captured, the test case can be replayed without new input, and then new results can be compared with old results. If the old results are marked as \"known good\" then deviations from those results constitute failed test cases.</p>\n<p>The test case workflow is illustrated by <a href=\"#CHDECCJD\">Figure 10-7</a>.</p>\n<div class=\"figure\"><a id=\"CHDECCJD\" name=\"CHDECCJD\"></a><a id=\"AIAAG2009\" name=\"AIAAG2009\"></a>\n<p class=\"titleinfigure\">Figure 10-7 Test Case Workflow</p>\n<img width=\"605\" height=\"425\" src=\"img/aiaag_jd_108.gif\" alt=\"Surrounding text describes Figure 10-7 .\" title=\"Surrounding text describes Figure 10-7 .\"><br></div>\n<!-- class=\"figure\" -->\n<a id=\"AIAAG2010\" name=\"AIAAG2010\"></a>\n<p class=\"subhead2\">Task overview: Creating and managing a test case</p>\n<p>From the Access Tester Console, you can connect to the OAM Server and manually conduct individual tests. You can save the request to the capture queue after a request is sent and the response is received from the OAM Server. You can continue capturing additional test cases before generating a test script and clearing the capture queue. If you exit the Access Tester before saving the capture queue, you are asked if the test cases should be saved to a script before exiting. Oracle recommends that you do not clear the queue until all your test cases have been captured.</p>\n<p>Once you have the test script, you can run it from either the Access Tester Console or from the command line.</p>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"BGBDDBAC\" name=\"BGBDDBAC\"></a><a id=\"AIAAG2011\" name=\"AIAAG2011\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Capturing Test Cases</h3>\n<p>You can save each test case to a capture queue after sending the request from the Access Tester to the OAM Server and receiving the response. You can capture as many individual test cases as you need before generating a test script that will automate running the group of test cases. For instance, the following outlines three test cases that must be captured individually:</p>\n<ul>\n<li>\n<p>A validation request and response</p>\n</li>\n<li>\n<p>An authentication request and response</p>\n</li>\n<li>\n<p>An authorization request and response</p>\n</li>\n</ul>\n<p><a href=\"#BGBHEDIE\">Table 10-10</a> describes the location of the capture options.</p>\n<div class=\"tblformal\"><a id=\"AIAAG2012\" name=\"AIAAG2012\"></a><a id=\"sthref263\" name=\"sthref263\"></a><a id=\"BGBHEDIE\" name=\"BGBHEDIE\"></a>\n<p class=\"titleintable\">Table 10-10 Access Tester Capture Request Options</p>\n<table class=\"Formal\" title=\"Access Tester Capture Request Options\" summary=\"Access Tester Capture Request\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t27\">Location</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t27\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t27\" headers=\"r1c1-t27\">\n<p>Test menu</p>\n<p>Capture last \"...\" request</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t27 r1c2-t27\">\n<p>Select this command from the Test menu to add the last request issued and results received to the capture queue (for inclusion in a test script later).</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t27\" headers=\"r1c1-t27\"><img width=\"34\" height=\"28\" src=\"img/ac_capture.gif\" alt=\"Capture last request\" title=\"Capture last request\"></td>\n<td align=\"left\" headers=\"r3c1-t27 r1c2-t27\">\n<p>Select this command button from the tool bar to add the last request issued and results received to the capture queue (for inclusion in a test script later).</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformal\" -->\n<p>If you exit the Access Tester before saving the capture queue, you are asked if the test cases should be saved to a script before exiting. Do not clear the Access Tester capture queue until all your test cases have been captured.</p>\n<a id=\"AIAAG2013\" name=\"AIAAG2013\"></a>\n<p class=\"subhead2\">To capture one or more test cases</p>\n<ol>\n<li>\n<p>Initiate a request from the Access Tester Console, as described in <a href=\"#CACEFCDD\">\"Testing Connectivity and Policies from the Access Tester Console\"</a>.</p>\n</li>\n<li>\n<p>After receiving the response, click the Capture last \"...\" request command button in the tool bar (or choose it from the Test menu).</p>\n</li>\n<li>\n<p>Confirm the capture in the Status Messages panel and note the Capture Queue test case count at the bottom of the Console, as shown here.</p>\n<img width=\"711\" height=\"239\" src=\"img/ac_capt_queue.gif\" alt=\"Capture Queue Message\" title=\"Capture Queue Message\"><br></li>\n<li>\n<p>Repeat steps 1, 2, and 3 to capture in the queue each test case that you need for your test script.</p>\n</li>\n<li>\n<p>Proceed to <a href=\"#CACBICDG\">\"Generating an Input Test Script\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect2\" -->\n<a id=\"CACBICDG\" name=\"CACBICDG\"></a><a id=\"AIAAG2014\" name=\"AIAAG2014\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Generating an Input Test Script</h3>\n<p>A test script is a collection of individual test cases that were captured using the Access Tester Console. When individual test cases are grouped together, it becomes possible to automate test coverage to validate policy configuration for a specific application or site.</p>\n<p>You can create a test script to be used as input to the Access Tester and drive automated processing of multiple test cases. The Generate Script option enables you to create an XML file test script and clear the capture queue. If you exit the Access Tester before saving the capture queue, you are asked if the test cases should be saved to a script before exiting.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nDo not clear the capture queue until you have captured all the test cases you want to include in the script.</div>\n<a id=\"AIAAG2015\" name=\"AIAAG2015\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" --><a id=\"sthref264\" name=\"sthref264\"></a>\n<h4 class=\"sect3\">About Generating an Input Test Script</h4>\n<p>You can create a test script to be used as input to the Access Tester and drive automated processing of multiple test cases. Such a script must follow these rules:</p>\n<ul>\n<li>\n<p>Allows possible replay by a person or system</p>\n</li>\n<li>\n<p>Allows possible replay against different policy servers w/o changing the script, to enable sharing of test scripts to drive different Policy Servers</p>\n</li>\n<li>\n<p>Allows comparison of test execution results against \"Known Good\" results</p>\n</li>\n</ul>\n<p>Following are the locations of the Generate Script command.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2016\" name=\"AIAAG2016\"></a><a id=\"sthref265\" name=\"sthref265\"></a><a id=\"sthref266\" name=\"sthref266\"></a>\n<p class=\"titleintable\">Table 10-11 Generate Script Command</p>\n<table class=\"FormalMax\" title=\"Generate Script Command\" summary=\"Generate Script Options\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t29\">Location of the Command</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t29\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t29\" headers=\"r1c1-t29\">\n<p>Test menu</p>\n<p>Generate Script</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t29 r1c2-t29\">\n<p>Select Generate Script from the Test menu to initiate creation of the script containing your captured test cases.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t29\" headers=\"r1c1-t29\"><img width=\"34\" height=\"30\" src=\"img/ac_gen_script.gif\" alt=\"Access Tester Generate Script\" title=\"Access Tester Generate Script\"></td>\n<td align=\"left\" headers=\"r3c1-t29 r1c2-t29\">\n<p>Select the Generate Script command button from the tool bar to initiate creation of the script containing your captured test cases. After you specify or select a name for your script, you are asked if the capture queue should be cleared. Do not clear the capture queue until all your test cases are saved to a script.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect3\" -->\n<a id=\"AIAAG2017\" name=\"AIAAG2017\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" --><a id=\"sthref267\" name=\"sthref267\"></a>\n<h4 class=\"sect3\">Generating an Input Test Script</h4>\n<a id=\"AIAAG2217\" name=\"AIAAG2217\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#BGBDDBAC\">Capturing Test Cases</a></p>\n<a id=\"AIAAG2018\" name=\"AIAAG2018\"></a>\n<p class=\"subhead2\">To record a test script containing captured test cases</p>\n<ol>\n<li>\n<p>Perform and capture each request that you want in the script, as described in <a href=\"#BGBDDBAC\">\"Capturing Test Cases\"</a>.</p>\n</li>\n<li>\n<p>Click the Generate Script command button in the tool bar (or choose it from the Test menu to include all captured test cases.</p>\n</li>\n<li>\n<p>In the new dialog box, select or enter the name of your new XML script file and then click Save.</p>\n</li>\n<li>\n<p>Click Yes to overwrite an existing file (or No to dismiss the window and give the file a new name).</p>\n</li>\n<li>\n<p>In the Save Waning dialog box, click No to retain the capture queue and continue adding test cases to your script (or click Yes to clear the queue of all test cases).</p>\n</li>\n<li>\n<p>Confirm the location of the test script before you exit the Access Tester.</p>\n</li>\n<li>\n<p>Personalize the test script to include details such as who, when, and why the script was developed, as described next.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDIDJEB\" name=\"CHDIDJEB\"></a><a id=\"AIAAG2019\" name=\"AIAAG2019\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Personalizing an Input Test Script</h3>\n<p>This section describes how to personalize and customize a test script.</p>\n<ul>\n<li>\n<p><a href=\"#CHDEEJAI\">About Customizing a Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CHDHAEAG\">Customizing a Test Script</a></p>\n</li>\n</ul>\n<a id=\"CHDEEJAI\" name=\"CHDEEJAI\"></a><a id=\"AIAAG2020\" name=\"AIAAG2020\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About Customizing a Test Script</h4>\n<p>The control block of a test script is used to tag the script and specify information to be used during the execution of a test. You might want to include details about who created the script and when and why the script was created. You might also want to customize the script using one or more control parameters.</p>\n<p>The Access Tester provides command line \"control\" parameters to change processing of the script without changing the script. (test name, test number, and so on). This enables you to configure test runs without having to change \"known good\" input test scripts. <a href=\"#CHDCHDHI\">Table 10-12</a> describes the control elements and how to customize these.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2021\" name=\"AIAAG2021\"></a><a id=\"sthref268\" name=\"sthref268\"></a><a id=\"CHDCHDHI\" name=\"CHDCHDHI\"></a>\n<p class=\"titleintable\">Table 10-12 Test Script Control Parameters</p>\n<table class=\"FormalMax\" title=\"Test Script Control Parameters\" summary=\"Controls Parameters to Customize a Test Script\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"31%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t30\">Control Parameter</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t30\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t30\" headers=\"r1c1-t30\">\n<p>i<code>gnorecontent=true</code></p>\n</td>\n<td align=\"left\" headers=\"r2c1-t30 r1c2-t30\">\n<p>Ignores differences in the Content section of the use case when comparing the original OAM Server response to the current response. The default is to compare the Content sections. This parameter can be overwritten by a command line property when running in the command line mode.</p>\n<p>Default: false (Compare Content sections).</p>\n<p>Values: true or false</p>\n<p>In command line mode, use Ignorecontent=true to over ride the specified value in the Control section of the input script.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t30\" headers=\"r1c1-t30\">\n<p>testname=\"oamtest\"</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t30 r1c2-t30\">\n<p>Specifies a prefix to add to file names in the \"results bundle\" as described in the previous section.</p>\n<p>In command line mode, use Testname=name to over ride the specified value in the Control section.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t30\" headers=\"r1c1-t30\">\n<p>Configfile=\"config.xml\"</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t30 r1c2-t30\">\n<p>Specifies the absolute path to a configuration XML file that was previously created by the Access Tester.</p>\n<p>In command line mode, this file is used by the Access Tester to locate connection details to establish a server connection.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r5c1-t30\" headers=\"r1c1-t30\">\n<p>Numthreads</p>\n<p>Reserved for future use</p>\n</td>\n<td align=\"left\" headers=\"r5c1-t30 r1c2-t30\">\n<p>indicates the number of threads to be started by the Access Tester to run multiple copies of the test script. This supports stress testing of the OAM Server.</p>\n<p>Default: 1</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r6c1-t30\" headers=\"r1c1-t30\">\n<p>Numiterations</p>\n<p>Reserved for future use</p>\n</td>\n<td align=\"left\" headers=\"r6c1-t30 r1c2-t30\">\n<p>indicates the number of iterations of the test should be performed by the Access Tester. This provides for longevity testing of the OAM Server.</p>\n<p>Default: 1</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect3\" -->\n<a id=\"CHDHAEAG\" name=\"CHDHAEAG\"></a><a id=\"AIAAG2022\" name=\"AIAAG2022\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">Customizing a Test Script</h4>\n<a id=\"AIAAG2218\" name=\"AIAAG2218\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBICDG\">Generating an Input Test Script</a></p>\n<a id=\"AIAAG2023\" name=\"AIAAG2023\"></a>\n<p class=\"subhead2\">To customize a test script</p>\n<ol>\n<li>\n<p>Locate and open the test script that was generated by the Access Tester.</p>\n</li>\n<li>\n<p>Add any details that you need to customize or personalize the script.</p>\n</li>\n<li>\n<p>Save the file and proceed to <a href=\"#CACHGIHD\">\"Executing a Test Script\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CACHGIHD\" name=\"CACHGIHD\"></a><a id=\"AIAAG2024\" name=\"AIAAG2024\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">Executing a Test Script</h3>\n<p>Once a test script has been created against a \"Known Good\" policy configuration and marked as \"Known Good\", it is important to drive the Access Tester using the script rather than specifying each test manually using the Console. This section provides the following topics:</p>\n<ul>\n<li>\n<p><a href=\"#CHDEDECG\">About Test Script Execution</a></p>\n</li>\n<li>\n<p><a href=\"#CHDJDHBA\">Running a Test Script</a></p>\n</li>\n</ul>\n<a id=\"CHDEDECG\" name=\"CHDEDECG\"></a><a id=\"AIAAG2025\" name=\"AIAAG2025\"></a>\n<div class=\"sect3\">\n<h4 class=\"sect3\">About Test Script Execution</h4>\n<p>You can interactively execute tests scripts from within the Access Tester Console, or use automated test runs performed by command scripts. Automated test runs can be scheduled by the operating system or a harness such as Apache JMeter, and executed without manual intervention. Other than lack of human input in command line mode, the two execution modes are identical.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nA script such as .bat (Windows) or .sh (Unix) executes a test script in command line mode. Once a test script is created, it can be executed using either the Run Script menu command or the Access Tester command line.</div>\n<p><a href=\"#CHDIJHEE\">Table 10-13</a> describes the commands to execute a test script.</p>\n<div class=\"tblformal\"><a id=\"AIAAG2026\" name=\"AIAAG2026\"></a><a id=\"sthref269\" name=\"sthref269\"></a><a id=\"CHDIJHEE\" name=\"CHDIJHEE\"></a>\n<p class=\"titleintable\">Table 10-13 Run Test Script Commands</p>\n<table class=\"Formal\" title=\"Run Test Script Commands\" summary=\"Run a Test Script\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t32\">Location</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t32\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t32\" headers=\"r1c1-t32\">\n<p>Test menu</p>\n<p>Run Script</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t32 r1c2-t32\">\n<p>Select the Run Script command from the Test menu to begin running a saved test script against the current policy server. The Status message panel is populated with the execution status as the script progresses.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t32\" headers=\"r1c1-t32\"><img width=\"34\" height=\"34\" src=\"img/ac_run_script.gif\" alt=\"Access Tester Run Script\" title=\"Access Tester Run Script\"></td>\n<td align=\"left\" headers=\"r3c1-t32 r1c2-t32\">\n<p>Select the Run Script command button from the tool bar to begin running a saved test script against the current policy server. The Status message panel is populated with the execution status as the script progresses.</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r4c1-t32\" headers=\"r1c1-t32\">\n<p>Command line mode</p>\n</td>\n<td align=\"left\" headers=\"r4c1-t32 r1c2-t32\">\n<p>A script such as .bat (Windows) or .sh (Unix) executes a test script in command line mode. Once a test script is created, it can be executed using either the Run Script menu command or the Access Tester command line.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformal\" -->\n<p>The following overview describes how the Access Tester operates when running a test. Other than lack of human input in command line mode, the two execution modes are identical.</p>\n<a id=\"AIAAG2027\" name=\"AIAAG2027\"></a>\n<p class=\"subhead2\">Process overview: Access Tester behavior when running a test script</p>\n<ol>\n<li>\n<p>The Access Tester loads the input xml file.</p>\n<p>In command line mode, the Access Tester opens the configuration XML file defined within the input test script's Control element.</p>\n</li>\n<li>\n<p>The Access Tester connects to the primary and secondary OAM Proxy using information in the Server Connection panel of the Console.</p>\n<p>In command line mode, the Access Tester uses information in the Connection element of the configuration XML file.</p>\n</li>\n<li>\n<p>In command line mode, the Access Tester checks the Control elements in the input script XML file to ensure none have been overwritten on the command line (command line values take precedence).</p>\n</li>\n<li>\n<p>For each original test case defined in the script, the Access Tester:</p>\n<ol>\n<li>\n<p>Creates a new target test case.</p>\n</li>\n<li>\n<p>Sends the original request to the OAM Server and collects the response.</p>\n</li>\n<li>\n<p>Makes the following comparisons:</p>\n<p>Compares the new response to the original response.</p>\n<p>Compares response codes and marks as \"mismatched\" any new target test case where response codes differ from the original test case. For instance, if the original Validate returned \"Yes\", and now returns \"No\", a mismatch is marked.</p>\n<p>When response codes are identical, and \"the ignorecontent\" control parameter is \"false\", the Access Tester compares Content (the name of the Authentication scheme or post authorization actions that are logged after each request). If Content sections differ, the new target test case is marked \"mismatched\".</p>\n</li>\n<li>\n<p>Collect new elapsed time and store it in the target use case.</p>\n</li>\n<li>\n<p>Build a new target test case containing the full state of the last server request and the same unique ID (UUID) as the original test case.</p>\n</li>\n<li>\n<p>Update the internal statistics table with statistics for the target test case (request type, elapsed time, mismatched, and so on).</p>\n</li>\n</ol>\n</li>\n<li>\n<p>After completing all the input test cases, the Access Tester:</p>\n<ol>\n<li>\n<p>Displays summary results.</p>\n</li>\n<li>\n<p>Obtains and combines the <span class=\"italic\">testname</span> and <span class=\"italic\">testnumber</span>, and generates a name for the \"results bundle\" (three files whose names start with &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>&gt;.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nShell scripts can automate generating the bundle by providing testname and testnumber command line parameters.</div>\n<p>Obtain <span class=\"italic\">testname</span> from the command line parameter. If not specified in the command line, use the <span class=\"italic\">testname</span> element of the input script's Control block.</p>\n<p>Obtain <span class=\"italic\">testnumber</span> from the command line parameter. If not specified, <span class=\"italic\">testnumber</span> defaults to a 7-character numeric string based on the current local time: 2 character minutes, 2 character seconds, 3 character hundredths.</p>\n</li>\n<li>\n<p>Generates the \"results bundle\": three files whose names start with &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>&gt;:</p>\n<p>The target XML script contains the new test cases: &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>_results.xml.</p>\n<p>The statistics XML file contains a summary and detailed statistics of the entire test run, plus those test cases marked as \"mismatched\": &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>_stats.xml</p>\n<p>The execution log file contains information from the Status Message panel: &lt;<span class=\"italic\">testname</span>&gt;_&lt;<span class=\"italic\">testnumber</span>_log.log.</p>\n</li>\n<li>\n<p>In command line mode, the Access Tester exits with the exit code as described in <a href=\"#CACDGHJE\">\"About the Access Tester Command Line Mode\"</a>.</p>\n</li>\n</ol>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" -->\n<a id=\"CHDJDHBA\" name=\"CHDJDHBA\"></a><a id=\"AIAAG2028\" name=\"AIAAG2028\"></a>\n<div class=\"sect3\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h4 class=\"sect3\">Running a Test Script</h4>\n<a id=\"AIAAG2219\" name=\"AIAAG2219\"></a>\n<p class=\"subhead2\">Prerequisites</p>\n<p><a href=\"#CACBICDG\">Generating an Input Test Script</a></p>\n<a id=\"AIAAG2029\" name=\"AIAAG2029\"></a>\n<p class=\"subhead2\">To run a test script</p>\n<ol>\n<li>\n<p>Confirm the location of the saved test script before exiting the Access Tester., as described in <a href=\"#CACBICDG\">\"Generating an Input Test Script\"</a>.</p>\n</li>\n<li>\n<p>Submit the test script for processing using one of the following methods:</p>\n<ul>\n<li>\n<p>From the Access Tester Console, click the Run Script command button in the tool bar (or select Run Script from the Test menu), then follow the prompts and observe messages in the Status Message panel as the script executes.</p>\n</li>\n<li>\n<p>From the command line, specify your test script with the desired system properties, as described in <a href=\"#CACGJBHA\">\"Starting the Access Tester with System Properties For Use in Command Line Mode\"</a>.</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">java -Dscript.scriptfile=\"\\tests\\script.xml\" -Dcontrol.ignorecontent=\"true\" \n-jar oamtest.jar\n</pre></li>\n</ul>\n</li>\n<li>\n<p>Review the log and output files and perform additional analysis after the Access Tester compares newly generated results with results captured in the input script, as described in <a href=\"#CACBIEED\">\"Evaluating Scripts, Log File, and Statistics\"</a>.</p>\n</li>\n</ol>\n</div>\n<!-- class=\"sect3\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" -->\n<a id=\"CACBIEED\" name=\"CACBIEED\"></a><a id=\"AIAAG2030\" name=\"AIAAG2030\"></a>\n<div class=\"sect1\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h2 class=\"sect1\">Evaluating Scripts, Log File, and Statistics</h2>\n<p>This section provides the following information:</p>\n<ul>\n<li>\n<p><a href=\"#CHDDDHCG\">About Evaluating Test Results</a></p>\n</li>\n<li>\n<p><a href=\"#CHDJFEGD\">About the Saved Connection Configuration File</a></p>\n</li>\n<li>\n<p><a href=\"#CHDBEHJC\">About the Generated Input Test Script</a></p>\n</li>\n<li>\n<p><a href=\"#CHDJAIHC\">About the Target Output File Containing Test Run Results</a></p>\n</li>\n<li>\n<p><a href=\"#CHDFGJGC\">About the Statistics Document</a></p>\n</li>\n<li>\n<p><a href=\"#CHDIEHJD\">About the Execution Log</a></p>\n</li>\n</ul>\n<a id=\"CHDDDHCG\" name=\"CHDDDHCG\"></a><a id=\"AIAAG2031\" name=\"AIAAG2031\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About Evaluating Test Results</h3>\n<p>At the end of a test run a \"results bundle\" gets generated containing three documents:</p>\n<ul>\n<li>\n<p>Target script: An XML document containing new test cases</p>\n</li>\n<li>\n<p>Execution log: A text file containing the messages displayed during script execution</p>\n</li>\n<li>\n<p>Execution statistics: An XML document containing test metrics and a list of mismatched elements</p>\n</li>\n</ul>\n<p>The matching pair of test cases in the original and target scripts shares the test case ID. This ID is represented by a UUID value, which makes it possible to compare individual test cases in the original script with those in the target script. For more information, see <a href=\"#CHDBEHJC\">\"About the Generated Input Test Script\"</a>.</p>\n<p>The statistics document contains the summary and detail statistics, as well as a list of test cases that did not match. The detailed statistics can be used for further analysis or to keep a historical trail of results. The summary statistics are the same statistics displayed at the end of the test run and can be used to quickly assess the state of a test run. The list of mismatched test cases as created in the statistics document contains test case IDs that have triggered mismatch and includes the reason for the mismatch, as seen in <a href=\"#CHDCCJID\">Table 10-14</a>.</p>\n<div class=\"tblformalmax\"><a id=\"AIAAG2032\" name=\"AIAAG2032\"></a><a id=\"sthref270\" name=\"sthref270\"></a><a id=\"CHDCCJID\" name=\"CHDCCJID\"></a>\n<p class=\"titleintable\">Table 10-14 Mismatched Results Reasons in the Statistics Document</p>\n<table class=\"FormalMax\" title=\"Mismatched Results Reasons in the Statistics Document\" summary=\"Test Results for Evaluation\" dir=\"ltr\" border=\"1\" width=\"100%\" frame=\"hsides\" rules=\"groups\" cellpadding=\"3\" cellspacing=\"0\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n</colgroup><thead>\n<tr align=\"left\" valign=\"top\">\n<th align=\"left\" valign=\"bottom\" id=\"r1c1-t34\">Reason for a MisMatch</th>\n<th align=\"left\" valign=\"bottom\" id=\"r1c2-t34\">Description</th>\n</tr>\n</thead>\n<tbody>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r2c1-t34\" headers=\"r1c1-t34\">\n<p>Result</p>\n</td>\n<td align=\"left\" headers=\"r2c1-t34 r1c2-t34\">\n<p>The test cases did not match because of the difference in OAM Server response codes (Yes versus No).</p>\n</td>\n</tr>\n<tr align=\"left\" valign=\"top\">\n<td align=\"left\" id=\"r3c1-t34\" headers=\"r1c1-t34\">\n<p>Content</p>\n</td>\n<td align=\"left\" headers=\"r3c1-t34 r1c2-t34\">\n<p>The test cases did not match because of the differences in the specific data values that were returned by the OAM Server. The specific values from the last test run that have triggered the mismatch are included.</p>\n</td>\n</tr>\n</tbody>\n</table>\n<br></div>\n<!-- class=\"tblformalmax\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDJFEGD\" name=\"CHDJFEGD\"></a><a id=\"AIAAG2033\" name=\"AIAAG2033\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Saved Connection Configuration File</h3>\n<p>This is the output files that is saved using the Save Configuration command on the File menu; the default file name is config.xml. This connection configuration file includes details that were specified in the Access Tester Console, Server Connection panel.</p>\n<div class=\"infoboxnote\">\n<p class=\"notep1\">Note:</p>\nAn input test script file is also generated as described in the following topic. The name of the configuration file is used in the input test script to ensure that running the Access Tester in command line mode picks up connection information defined in the connection file.</div>\n<div class=\"example\"><a id=\"AIAAG2034\" name=\"AIAAG2034\"></a><a id=\"sthref271\" name=\"sthref271\"></a>\n<p class=\"titleinexample\">Example 10-1 Connection Configuration File</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">&lt;?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?&gt;\n&lt;oamtestconfig xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n    &lt;connection timeout=\"30000\" minnconn=\"1\" mode=\"open\"&gt;\n        &lt;agent password=\"00030d05101b050c42\" name=\"<span class=\"italic\">agent1</span>\"/&gt;\n        &lt;keystore rootstore=\"\" keystore_password=\"\" keystore=\"\" \nglobal_passphrase=\"\"/&gt;\n        &lt;primary&gt;\n            &lt;server maxconn=\"1\" port=\"<span class=\"italic\">2100</span>\" addr=\"<span class=\"italic\">oam_server1</span>\"/&gt;\n        &lt;/primary&gt;\n        &lt;secondary&gt;\n            &lt;server maxconn=\"1\" port=\"0\" addr=\"\"/&gt;\n        &lt;/secondary&gt;\n    &lt;/connection&gt;\n    &lt;uri getauthscheme=\"true\"&gt;\n        &lt;scheme&gt;http&lt;/scheme&gt;\n        &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n        &lt;port&gt;7777&lt;/port&gt;\n        &lt;resource&gt;/index.html&lt;/resource&gt;\n        &lt;operation&gt;Get&lt;/operation&gt;\n    &lt;/uri&gt;\n    &lt;identity&gt;\n        &lt;id&gt;<span class=\"italic\">admin1</span>&lt;/id&gt;\n        &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n        &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n    &lt;/identity&gt;\n&lt;/oamtestconfig&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDBEHJC\" name=\"CHDBEHJC\"></a><a id=\"AIAAG2035\" name=\"AIAAG2035\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Generated Input Test Script</h3>\n<p>The input test script is generated by using the Access Tester and capturing your own test cases. The \"configfile\" attribute of the \"Control\" element is updated after creation to specify the connection configuration file to be used in command line mode for establishing a connection to the OAM Server.</p>\n<div class=\"example\"><a id=\"AIAAG2036\" name=\"AIAAG2036\"></a><a id=\"sthref272\" name=\"sthref272\"></a>\n<p class=\"titleinexample\">Example 10-2 Generated Input Test Script</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">&lt;?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?&gt;\n&lt;oamtestscript xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n    &lt;history description=\"Manually generated using agent 'agent1'\" \ncreatedon=\"2010-02-03T22:28:00.468-05:00\" createdby=\"test_user\"/&gt;\n    &lt;control numthreads=\"1\" numiterations=\"1\" ignorecontent=\"false\" \ntestname=\"samplerun1\" configfile=\"config.xml\"/&gt;\n    &lt;cases numcases=\"4\"&gt;\n        &lt;case uuid=\"465a4fda-d814-4ab7-b81b-f3f1cd72bbc0\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"984\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;\n                    &lt;line type=\"auth.scheme.id\"&gt;LDAPScheme&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.level\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.required.creds\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.redirect.url\"&gt;http://dadvmh0172.us.oracle.com:14100/oam/server/&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"009b44e3-1a94-4bfc-a0c3-84a38a9e0f2a\"&gt;\n            &lt;request code=\"Authenticate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"187\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 10(CredentialsAccepted) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;\n                    &lt;line type=\"user.dn\"&gt;cn=weblogic,dc=us,dc=oracle,dc=com&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"84fe9b06-86d1-47df-a399-6311990743c3\"&gt;\n            &lt;request code=\"Authorize\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"188\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 8(Allow) Minor code: 2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"61579e47-5532-42c3-bbc7-a00828256bf4\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"false\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;<span class=\"italic\">oam_server1</span>&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"172\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n    &lt;/cases&gt;\n&lt;/oamtestscript&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDJAIHC\" name=\"CHDJAIHC\"></a><a id=\"AIAAG2037\" name=\"AIAAG2037\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Target Output File Containing Test Run Results</h3>\n<p>This example was generated by running the Access Tester in command line mode and specifying the script.xml file as input to execute the 4 captured test cases:</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">Dscript.scriptfile=\"script.xml\" -jar oamtest.jar\n</pre>\n<p>Notice the various sections in <a href=\"#CHDFBBIH\">Example 10-3</a>. As shown in the execution log, this test run found no mismatches, and shows that 4 out of 4 requests matched.</p>\n<div class=\"example\"><a id=\"CHDFBBIH\" name=\"CHDFBBIH\"></a><a id=\"AIAAG2038\" name=\"AIAAG2038\"></a>\n<p class=\"titleinexample\">Example 10-3 Output File Generated During a Test Run</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">&lt;?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?&gt;\n&lt;oamtestscript xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n    &lt;history description=\"Generated from script 'script.xml' using agent 'agent1'\" \ncreatedon=\"2010-02-03T23:03:02.171-05:00\" createdby=\"test_user\"/&gt;\n    &lt;control numthreads=\"1\" numiterations=\"1\" ignorecontent=\"false\" \ntestname=\"oamtest\" configfile=\"\"/&gt;\n    &lt;cases numcases=\"4\"&gt;\n        &lt;case uuid=\"465a4fda-d814-4ab7-b81b-f3f1cd72bbc0\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"969\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;\n                    &lt;line type=\"auth.scheme.id\"&gt;LDAPScheme&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.level\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.required.creds\"&gt;2&lt;/line&gt;\n                    &lt;line type=\"auth.scheme.redirect.url\"&gt;http://dadvmh0172.us.oracle.com:14100/oam/server/\n&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"009b44e3-1a94-4bfc-a0c3-84a38a9e0f2a\"&gt;\n            &lt;request code=\"Authenticate\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"187\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 10(CredentialsAccepted) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content&gt;    \n                    &lt;line type=\"user.dn\"&gt;cn=weblogic,dc=us,dc=oracle,dc=com&lt;/line&gt;\n                &lt;/content&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"84fe9b06-86d1-47df-a399-6311990743c3\"&gt;\n            &lt;request code=\"Authorize\"&gt;\n                &lt;uri getauthscheme=\"true\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n                &lt;identity&gt;\n                    &lt;id&gt;weblogic&lt;/id&gt;\n                    &lt;password&gt;00030d05101b050c42&lt;/password&gt;\n                    &lt;ipaddr&gt;***********&lt;/ipaddr&gt;\n                &lt;/identity&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"172\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 8(Allow) Minor code: 2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n        &lt;case uuid=\"61579e47-5532-42c3-bbc7-a00828256bf4\"&gt;\n            &lt;request code=\"Validate\"&gt;\n                &lt;uri getauthscheme=\"false\"&gt;\n                    &lt;scheme&gt;http&lt;/scheme&gt;\n                    &lt;host&gt;oam_server1&lt;/host&gt;\n                    &lt;port&gt;7777&lt;/port&gt;\n                    &lt;resource&gt;/index.html&lt;/resource&gt;\n                    &lt;operation&gt;Get&lt;/operation&gt;\n                &lt;/uri&gt;\n            &lt;/request&gt;\n            &lt;response elapsed=\"171\" code=\"Yes\"&gt;\n                &lt;comment&gt;&lt;/comment&gt;\n                &lt;status&gt;Major code: 4(ResrcOpProtected) Minor code: \n2(NoCode)&lt;/status&gt;\n                &lt;content/&gt;\n            &lt;/response&gt;\n        &lt;/case&gt;\n    &lt;/cases&gt;\n&lt;/oamtestscript&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDFGJGC\" name=\"CHDFGJGC\"></a><a id=\"AIAAG2039\" name=\"AIAAG2039\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Statistics Document</h3>\n<p>The statistics file (_stats.xml) is generated together with the target output script during the test run identified in the Execution log. The script.xml file was used as input to execute the 4 captured test cases. The test run found no mismatches, and shows that 4 out of 4 requests matched.</p>\n<p>A sample statistics document is shown in <a href=\"#CHDICAGE\">Example 10-4</a>. The various sections that provide statistics for this run, which you can compare against statistics for an earlier \"known good\" run.</p>\n<div class=\"example\"><a id=\"CHDICAGE\" name=\"CHDICAGE\"></a><a id=\"AIAAG2040\" name=\"AIAAG2040\"></a>\n<p class=\"titleinexample\">Example 10-4 Sample Statistics Document</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">A sample statistics document is shown here. Notice, \n&lt;oamteststats xmlns=\"http://xmlns.oracle.com/idm/oam/oamtest/schema\" \nversion=\"1.0\"&gt;\n     &lt;history description=\"Generated from script 'script.xml' using agent \n       'agent1'\" createdon=\"2010-02-03T23:03:02.171-05:00\" createdby=\"test_user\"/&gt;\n     &lt;summary&gt;\n          &lt;total&gt;\n              &lt;nummatched&gt;4&lt;/nummatched&gt;\n              &lt;numtotal&gt;4&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;238&lt;/avgelapsedsource\n              &lt;avgelapsedtarget&gt;232&lt;/avgelapsedtarget&gt;\n          &lt;/total&gt;\n          &lt;validate&gt;\n              &lt;nummatched&gt;2&lt;/nummatched&gt;\n              &lt;numtotal&gt;2&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;578&lt;/avgelapsedsource&gt;\n              &lt;avgelapsedtarget&gt;570&lt;/avgelapsedtarget&gt;\n          &lt;/validate&gt;\n          &lt;authenticate&gt;\n              &lt;nummatched&gt;1&lt;/nummatched&gt;\n              &lt;numtotal&gt;1&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;187&lt;/avgelapsedsource&gt;\n              &lt;avgelapsedtarget&gt;187&lt;/avgelapsedtarget&gt;\n          &lt;/authenticate&gt;\n          &lt;authorize&gt;\n              &lt;nummatched&gt;1&lt;/nummatched&gt;\n              &lt;numtotal&gt;1&lt;/numtotal&gt;\n              &lt;avgelapsedsource&gt;188&lt;/avgelapsedsource&gt;\n              &lt;avgelapsedtarget&gt;172&lt;/avgelapsedtarget&gt;\n          &lt;/authorize&gt;\n          &lt;summary&gt;\n          &lt;detail&gt;\n               &lt;source&gt;\n                    &lt;validate&gt;\n                       &lt;yes&gt;2&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;1156&lt;/elapsed&gt;\n                    &lt;/validate&gt;\n                &lt;authenticate&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;187&lt;/elapsed&gt;\n               &lt;/authenticate&gt;\n               &lt;authorize&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;188&lt;/elapsed&gt;\n               &lt;/authorize&gt;\n          &lt;/source&gt;\n          &lt;target&gt;\n               &lt;validate&gt;\n                       &lt;yes&gt;2&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;1140&lt;/elapsed&gt;\n               &lt;/validate&gt;\n          &lt;authenticate&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;187&lt;/elapsed&gt;\n          &lt;/authenticate&gt;\n          &lt;authorize&gt;\n                       &lt;yes&gt;1&lt;/yes&gt;\n                       &lt;no&gt;0&lt;/no&gt;\n                       &lt;error&gt;0&lt;/error&gt;\n                       &lt;mismatch&gt;0&lt;/mismatch&gt;\n                       &lt;elapsed&gt;172&lt;/elapsed&gt;\n          &lt;/authorize&gt;\n      &lt;target&gt;\n      &lt;/detail&gt;\n    &lt;mismatch numcases=\"0\"/&gt;\n&lt;/oamteststats&gt;\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" -->\n<a id=\"CHDIEHJD\" name=\"CHDIEHJD\"></a><a id=\"AIAAG2041\" name=\"AIAAG2041\"></a>\n<div class=\"sect2\"><!-- infolevel=\"all\" infotype=\"General\" -->\n<h3 class=\"sect2\">About the Execution Log</h3>\n<p>This sample execution log was generated together with the target output script during a test run using script.xml to execute 4 test cases. The test run found no mismatches, and shows that 4 out of 4 requests matched.</p>\n<p>As you review this example, notice the information provided which is the same as the information you see in the Status Messages panel of the Access Tester. Notice the test cases, test name, connection configuration file, agent name, connection status, request validation status, authentication scheme, redirect URL, credentials expected, authentication status and user DN, session ID, authorization status, validation status, and summary statistics. Also notice that the target script and statistics document were generated by this run.</p>\n<div class=\"example\"><a id=\"AIAAG2042\" name=\"AIAAG2042\"></a><a id=\"sthref273\" name=\"sthref273\"></a>\n<p class=\"titleinexample\">Example 10-5 Execution Log</p>\n<pre xml:space=\"preserve\" class=\"oac_no_warn\">[2/3/10 11:02 PM][info] Setting up to run script '<span class=\"italic\">script.xml</span>'\n[2/3/10 11:02 PM][info] Loading test cases and control parameters from script\n[2/3/10 11:02 PM][info] Loaded 4 cases\n[2/3/10 11:02 PM][info] Control data for this test run:\n[2/3/10 11:02 PM][info] Test name : '<span class=\"italic\">samplerun1</span>'\n[2/3/10 11:02 PM][info] Configuration file : '<span class=\"italic\">config.xml</span>'\n[2/3/10 11:02 PM][info] Ignore content : 'false'\n[2/3/10 11:02 PM][info] Loading server configuration from file\n[2/3/10 11:02 PM][info] Loaded server configuration\n[2/3/10 11:02 PM][info] Connecting to server as agent '<span class=\"italic\">oam_agent1</span>'\n[2/3/10 11:03 PM][info][request] Connect : Yes\n...\n[2/3/10 11:03 PM][info] Test '<span class=\"italic\">samplerun1</span>' will process 4 cases\n[2/3/10 11:03 PM][info][request] Validate : Yes\n[2/3/10 11:03 PM][info] Authentication scheme : <span class=\"italic\">LDAPScheme</span>, level : <span class=\"italic\">2</span>\n[2/3/10 11:03 PM][info] Redirect URL : \nhttp://<span class=\"italic\">oam_server1</span>.us.company.com:2100/server/\n[2/3/10 11:03 PM][info] Credentials expected: <span class=\"italic\">0x01 (password)</span>\n[2/3/10 11:03 PM][info][request] Authenticate : Yes\n[2/3/10 11:03 PM][info] User DN : cn=<span class=\"italic\">admin</span>1,dc=us,dc=<span class=\"italic\">company</span>,dc=com\n[2/3/10 11:03 PM][info] Session ID : -1\n[2/3/10 11:03 PM][info][request] Authorize : Yes\n[2/3/10 11:03 PM][info][request] Validate : Yes\n[2/3/10 11:03 PM][info] Summary statistics\n[2/3/10 11:03 PM][info] Matched 4 of 4, avg latency 232ms vs 238ms\n[2/3/10 11:03 PM][info] Validate: matched 2 of 2, avg latency 570ms vs 578ms\n[2/3/10 11:03 PM][info] Authenticate: matched 1 of 1, avg latency 187ms vs 187ms\n[2/3/10 11:03 PM][info] Authorize: matched 1 of 1, avg latency 172ms vs 188ms\n[2/3/10 11:03 PM][info] Generated target script 'samplerun1_0302171__target.xml'\n[2/3/10 11:03 PM][info] Generated statistics log 'samplerun1_0302171__stats.xml'\n</pre></div>\n<!-- class=\"example\" --></div>\n<!-- class=\"sect2\" --></div>\n<!-- class=\"sect1\" --></div>\n<!-- class=\"ind\" -->\n<div class=\"footer\">\n<hr>\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n<colgroup><col width=\"33%\">\n<col width=\"*\">\n<col width=\"33%\">\n</colgroup><tbody><tr>\n<td align=\"left\">\n<table class=\"simple oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"98\">\n<tbody><tr>\n<td align=\"center\" valign=\"top\"><a href=\"app_domn.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/leftnav.gif\" alt=\"Go to previous page\"><br>\n<span class=\"icon\">Previous</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"logout.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/rightnav.gif\" alt=\"Go to next page\"><br>\n<span class=\"icon\">Next</span></a></td>\n</tr>\n</tbody></table>\n</td>\n<td style=\"font-size: 90%\" align=\"center\" class=\"copyrightlogo\"><img width=\"144\" height=\"18\" src=\"../../dcommon/gifs/oracle.gif\" alt=\"Oracle\"><br>\nCopyright&nbsp;©&nbsp;2010,&nbsp;Oracle&nbsp;and/or&nbsp;its&nbsp;affiliates.&nbsp;All&nbsp;rights&nbsp;reserved.<br>\n<a href=\"../../dcommon/html/cpyr.htm\">Legal Notices</a></td>\n<td align=\"right\">\n<table class=\"icons oac_no_warn\" summary=\"\" cellspacing=\"0\" cellpadding=\"0\" width=\"294\">\n<tbody><tr>\n<td align=\"center\" valign=\"top\"><a href=\"../../index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/doclib.gif\" alt=\"Go to Documentation Home\"><br>\n<span class=\"icon\">Home</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../nav/portal_booklist.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/booklist.gif\" alt=\"Go to Book List\"><br>\n<span class=\"icon\">Book List</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"toc.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/toc.gif\" alt=\"Go to Table of Contents\"><br>\n<span class=\"icon\">Contents</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"index.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/index.gif\" alt=\"Go to Index\"><br>\n<span class=\"icon\">Index</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../mix.1111/b14005/toc.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/masterix.gif\" alt=\"Go to Master Index\"><br>\n<span class=\"icon\">Master Index</span></a></td>\n<td align=\"center\" valign=\"top\"><a href=\"../../dcommon/html/feedback.htm\"><img width=\"24\" height=\"24\" src=\"../../dcommon/gifs/feedbck2.gif\" alt=\"Go to Feedback page\"><br>\n<span class=\"icon\">Contact Us</span></a></td>\n</tr>\n</tbody></table>\n</td>\n</tr>\n</tbody></table>\n</div>\n<noscript>\n<p>Scripting on this page enhances content navigation, but does not change the content in any way.</p>\n</noscript>\n\n<!-- Start SiteCatalyst code -->\n<script type=\"text/javascript\" language=\"JavaScript\" src=\"http://www.oracle.com/ocom/groups/systemobject/@mktg_admin/documents/systemobject/s_code_download.js\"></script>\n<script type=\"text/javascript\" language=\"JavaScript\" src=\"http://www.oracle.com/ocom/groups/systemobject/@mktg_admin/documents/systemobject/s_code.js\"></script>\n\n<!-- ********** DO NOT ALTER ANYTHING BELOW THIS LINE ! *********** -->\n<!--  Below code will send the info to Omniture server -->\n<script type=\"text/javascript\" language=\"javascript\">var s_code=s.t();if(s_code)document.write(s_code)</script>\n\n<!-- End SiteCatalyst code -->\n\n\n</body></html>", "images": [{"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/doc.1111/e15478/img/aiaag_jd_110.gif", "alt": "Surrounding text describes Figure 10-2 .", "width": "657", "height": "563"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/doc.1111/e15478/img/aiaag_jd_109.gif", "alt": "Surrounding text describes Figure 10-1 .", "width": "569", "height": "545"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/doclib.gif", "alt": "Go to Documentation Home", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/booklist.gif", "alt": "Go to Book List", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/toc.gif", "alt": "Go to Table of Contents", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/index.gif", "alt": "Go to Index", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/masterix.gif", "alt": "Go to Master Index", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/feedbck2.gif", "alt": "Go to Feedback page", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/leftnav.gif", "alt": "Go to previous page", "width": "24", "height": "24"}, {"src": "https://www.appservgrid.com/documentation111/docs/fmw11g1113documentation/dcommon/gifs/rightnav.gif", "alt": "Go to next page", "width": "24", "height": "24"}], "success": true, "screenshot": "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***************************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"}}