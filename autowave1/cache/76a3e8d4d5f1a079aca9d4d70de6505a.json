{"timestamp": 1751723671.6382358, "data": {"title": "CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform", "content": "CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform Agree & Join LinkedIn By clicking Continue to join or sign in, you agree to LinkedIn’s User Agreement , Privacy Policy , and Cookie Policy . Skip to main content LinkedIn Articles People Learning Jobs Games Join now Sign in CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform Report this article <PERSON> President and CEO at SIACorp, Director and Board Member at Agrometrika, Board Member at Akrual Systems, over 45 years experience in Technology, Artificial Intelligence, Credit Automation and Risk Management Published May 6, 2024 + Follow The analysis and assessment of credit risk of legal entities plays a fundamental role in making decisions regarding the establishment of credit limits and durations values granted to these companies depending on their level of risk and the expected return depending on aspects such as reciprocity and relationship. The use of artificial intelligence techniques in credit automation is not something new, dating back to the late 1980s, when the first expert systems for credit analysis began to emerge, based on technologies such as decision trees, logistic regression and neural networks. Nevertheless, recent advances in Generative AI technology open up a wide range of new possibilities. Chatbots based on LLM (Large Language Models) models, such as ChatGPT, have been gaining great popularity due to their ability to carry out complex activities in an automated and personalized way, revolutionizing the way organizations operate. As a result, in the coming years, customizations of LLM models will enable a dramatic increase in the productivity of organizations through the automation of complex day-to-day tasks. Areas such as Finance, Marketing & Sales, Programming, and many others will benefit profoundly from this evolution. The Advent of Chat GPT Recently, the introduction of the ChatGPT Platform by OpenAI revolutionized the world of Generative AI. ChatGPT uses Deep Learning techniques, natural language processing (NLP), and LLM technology to generate human-like responses to user queries. As expected, the entire finance industry and particularly the areas of credit and risk management, analysis, approval and review will be profoundly impacted by the possibility of applying this technology to activities such as analysis reports generation, automated reviews of customer credit limits and dynamic approval workflows. ChatGPT Applications in the Credit Area There are numerous applications of ChatGPT in the Financial Area and particularly in the Credit Analysis Area. Among the various use cases in which Generative AI can be used, the following stand out: • Balance Sheet Analysis • Analysis of Credit Proposals • Review of Credit Lines and Limits • Credit Portfolio Analysis • Litigation Analysis • “Peer Analysis” • Preparation of Credit Reports and Presentations • Decision Support in Credit Committees The analysis and compilation of large amounts of financial data can be time-consuming and subject to human error, especially if carried out by professionals with less experience. ChatGPT is very efficient in analyzing large amounts of data, being able to simultaneously evaluate registration aspects, internal and external information, financial ratios and news about the Company, detecting trends and identifying potential credit risks. Additionally, carrying out up-to-date analyzes based on trends, news and market sentiments adds substantial value to effective credit risk management. By automating this process, Organizations will enable credit analysts to save valuable time used in preparing credit analysis reports and use it in activities such as visiting customers and improving the ability to detect credit default patterns to refine models. , in order to present accurate and contextually relevant forecasts. CreditChat Since its founding, 25 years ago, SIACorp has used Artificial Intelligence techniques such as decision forests, Bayesian networks and logistic regression in the rating calculation models and limit suggestions made by its CreditFlow Credit Automation Platform. Envisioning the potential for using Generative AI technology in the credit analysis and granting process, SIACorp created CreditChat, a Chatbot for Credit Analysis fully integrated with the CreditFlow Credit Automation Platform. CreditChat is a generative AI application that uses the ChatGPT integration features provided by Open.Ai to access information from various sources and generate richly elaborated credit analyses, which can be adapted to the specific needs of the business, including the incorporation of the company's exclusive knowledge bases and regulatory standards, which allows it to provide extremely accurate and relevant answers. This information includes customer's relevant data, their financial statements, punctuality information (dynamica...", "url": "https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf", "html": "<!DOCTYPE html><html class=\"cls-fix-enabled\" lang=\"en\"><head>\n        <meta name=\"pageKey\" content=\"d_flagship2_pulse_read\">\n          \n    <meta name=\"robots\" content=\"max-image-preview:large, noarchive\">\n    <meta name=\"bingbot\" content=\"nocache\">\n  \n<!----><!---->        <meta name=\"locale\" content=\"en_US\">\n        <meta id=\"config\" data-app-version=\"0.0.4464\" data-call-tree-id=\"AAY5LvK9t4JO7AhDT/dkSw==\" data-multiproduct-name=\"article-ssr-frontend\" data-service-name=\"article-ssr-frontend\" data-browser-id=\"934c66ce-701f-43a1-8dda-c0f89c0deaa9\" data-enable-page-view-heartbeat-tracking=\"\" data-page-instance=\"urn:li:page:d_flagship2_pulse_read;2zpAiixpRAauINO1Mmax/Q==\" data-theme=\"light\" data-disable-jsbeacon-pagekey-suffix=\"false\" data-member-id=\"0\" data-logout-url=\"https://www.linkedin.com/uas/logout?session_full_logout=true&amp;csrfToken=ajax%3A0162018755728344549\" data-should-use-full-url-in-pve-path=\"true\" data-dna-member-lix-treatment=\"enabled\" data-human-member-lix-treatment=\"enabled\" data-dfp-member-lix-treatment=\"control\" data-sync-apfc-headers-lix-treatment=\"control\" data-sync-apfc-cb-lix-treatment=\"enabled\" data-recaptcha-v3-integration-lix-value=\"control\" data-network-interceptor-lix-value=\"control\">\n\n        <link rel=\"canonical\" href=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\">\n<!----><!---->\n<!---->\n<!---->\n          <meta property=\"al:android:url\" content=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\">\n          <meta property=\"al:android:package\" content=\"com.linkedin.android\">\n          <meta property=\"al:android:app_name\" content=\"LinkedIn\">\n          <meta property=\"al:ios:url\" content=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\">\n          <meta property=\"al:ios:app_store_id\" content=\"288429040\">\n          <meta property=\"al:ios:app_name\" content=\"LinkedIn\">\n\n<!---->\n          <link rel=\"icon\" href=\"https://static.licdn.com/aero-v1/sc/h/al2o9zrvru7aqj8e1x2rzsrca\">\n\n\n        <script>\n          function getDfd() {let yFn,nFn;const p=new Promise(function(y, n){yFn=y;nFn=n;});p.resolve=yFn;p.reject=nFn;return p;}\n          window.lazyloader = getDfd();\n          window.tracking = getDfd();\n          window.impressionTracking = getDfd();\n          window.ingraphTracking = getDfd();\n          window.appDetection = getDfd();\n          window.pemTracking = getDfd();\n        </script>\n\n<!---->\n        \n        <title>CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform</title>\n        <link rel=\"stylesheet\" href=\"https://static.licdn.com/aero-v1/sc/h/4lvovrt1ua20s4dt8ym09qf1g\">\n        \n    \n    \n    \n\n    <meta name=\"description\" content=\"The analysis and assessment of credit risk of legal entities plays a fundamental role in making decisions regarding the establishment of credit limits and durations values granted to these companies depending on their level of risk and the expected return depending on aspects such as reciprocity and\">\n    <meta property=\"og:title\" content=\"CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform\">\n    <meta property=\"og:description\" content=\"The analysis and assessment of credit risk of legal entities plays a fundamental role in making decisions regarding the establishment of credit limits and durations values granted to these companies depending on their level of risk and the expected return depending on aspects such as reciprocity and\">\n    <meta property=\"og:image\" content=\"https://media.licdn.com/dms/image/v2/D4D12AQEalogBqcGJ7w/article-cover_image-shrink_720_1280/article-cover_image-shrink_720_1280/0/1714997667793?e=**********&amp;v=beta&amp;t=v7oWSHKg57tYrEPMWBhh826yN-nrLfMS4NCmLNJE1s0\">\n    <meta property=\"og:type\" content=\"article\">\n    <meta property=\"og:url\" content=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\">\n\n    <meta name=\"twitter:card\" content=\"summary_large_image\">\n    <meta name=\"twitter:site\" content=\"@LinkedInEditors\">\n    <meta name=\"twitter:title\" content=\"CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform\">\n    <meta name=\"twitter:description\" content=\"The analysis and assessment of credit risk of legal entities plays a fundamental role in making decisions regarding the establishment of credit limits and durations values granted to these companies depending on their level of risk and the expected return depending on aspects such as reciprocity and\">\n    <meta name=\"twitter:image\" content=\"https://media.licdn.com/dms/image/v2/D4D12AQEalogBqcGJ7w/article-cover_image-shrink_720_1280/article-cover_image-shrink_720_1280/0/1714997667793?e=**********&amp;v=beta&amp;t=v7oWSHKg57tYrEPMWBhh826yN-nrLfMS4NCmLNJE1s0\">\n    <meta name=\"appId\">\n      <meta name=\"twitter:label1\" content=\"Written by\">\n      <meta name=\"twitter:data1\" content=\"Alexandre Marinho\">\n      <meta name=\"twitter:label2\" content=\"Reading time\">\n      <meta name=\"twitter:data2\" content=\"11 min read\">\n\n    <meta name=\"clientSideIngraphs\" content=\"1\" data-gauge-metric-endpoint=\"/pulse/api/ingraphs/gauge\" data-counter-metric-endpoint=\"/pulse/api/ingraphs/counter\">\n  \n            \n    \n      <script type=\"application/ld+json\">\n        {\"@context\":\"http://schema.org\",\"@type\":\"Article\",\"headline\":\"The analysis and assessment of credit risk of legal entities plays a fundamental role in making decisions regarding the establishment of credit limits and durations values granted to these companies depending on their level of risk and the expected return depending on aspects such as reciprocity and\",\"url\":\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\",\"publisher\":null,\"name\":\"CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform\",\"commentCount\":2,\"interactionStatistic\":[{\"@type\":\"InteractionCounter\",\"interactionType\":\"http://schema.org/LikeAction\",\"userInteractionCount\":15},{\"@type\":\"InteractionCounter\",\"interactionType\":\"http://schema.org/CommentAction\",\"userInteractionCount\":2}],\"datePublished\":\"2024-05-06T12:19:18.000+00:00\",\"mainEntityOfPage\":\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\",\"isAccessibleForFree\":true,\"image\":{\"@type\":\"ImageObject\",\"url\":\"https://media.licdn.com/dms/image/v2/D4D12AQEalogBqcGJ7w/article-cover_image-shrink_720_1280/article-cover_image-shrink_720_1280/0/1714997667793?e=**********&v=beta&t=v7oWSHKg57tYrEPMWBhh826yN-nrLfMS4NCmLNJE1s0\"},\"dateModified\":\"2024-05-06T13:19:57.000+00:00\",\"author\":{\"@type\":\"Person\",\"url\":\"https://br.linkedin.com/in/alexandremarinho\",\"name\":\"Alexandre Marinho\",\"interactionStatistic\":{\"@type\":\"InteractionCounter\",\"interactionType\":\"https://schema.org/FollowAction\",\"name\":\"Followers\",\"userInteractionCount\":1276}}}\n      </script>\n  \n  \n      \n<!---->      </head>\n      <body dir=\"ltr\" class=\"overflow-hidden\">\n<!----><!----><!---->\n        \n          \n    <div class=\"guest-upsells\" aria-hidden=\"true\">\n        \n    \n    \n    \n    <form class=\"google-auth base-google-auth\" action=\"https://www.linkedin.com/uas/login-submit\" method=\"post\">\n      <input name=\"loginCsrfParam\" value=\"934c66ce-701f-43a1-8dda-c0f89c0deaa9\" type=\"hidden\">\n        <input name=\"session_redirect\" value=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\" type=\"hidden\">\n      <input name=\"trk\" value=\"pulse-article_google-one-tap-submit\" type=\"hidden\">\n        <div class=\"google-one-tap__module hidden fixed flex flex-col items-center top-[20px] right-[20px] z-[9999]\">\n          <div class=\"google-auth__tnc-container hidden relative top-2 bg-color-background-container-tint pl-2 pr-1 pt-2 pb-3 w-[375px] rounded-md shadow-2xl\">\n            <p class=\"text-md font-bold text-color-text\">\n              Agree &amp; Join LinkedIn\n            </p>\n            \n    \n    \n    <p class=\"linkedin-tc__text text-color-text-low-emphasis text-xs pb-2 !text-sm !text-color-text\" data-impression-id=\"pulse-article_one-tap-skip-tc-text\">\n      By clicking Continue to join or sign in, you agree to LinkedIn’s <a href=\"/legal/user-agreement?trk=linkedin-tc_auth-button_user-agreement\" target=\"_blank\" data-tracking-control-name=\"linkedin-tc_auth-button_user-agreement\" data-tracking-will-navigate=\"true\">User Agreement</a>, <a href=\"/legal/privacy-policy?trk=linkedin-tc_auth-button_privacy-policy\" target=\"_blank\" data-tracking-control-name=\"linkedin-tc_auth-button_privacy-policy\" data-tracking-will-navigate=\"true\">Privacy Policy</a>, and <a href=\"/legal/cookie-policy?trk=linkedin-tc_auth-button_cookie-policy\" target=\"_blank\" data-tracking-control-name=\"linkedin-tc_auth-button_cookie-policy\" data-tracking-will-navigate=\"true\">Cookie Policy</a>.\n    </p>\n  \n          </div>\n          <div data-tracking-control-name=\"pulse-article_google-one-tap\" id=\"google-one-tap__container\"></div>\n        </div>\n      \n    <div class=\"loader loader--full-screen\">\n      <div class=\"loader__container mb-2 overflow-hidden\">\n        <icon class=\"loader__icon inline-block loader__icon--default text-color-progress-loading lazy-loaded\" data-svg-class-name=\"loader__icon-svg--large fill-currentColor h-[60px] min-h-[60px] w-[60px] min-w-[60px]\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 60 60\" width=\"60\" height=\"60\" focusable=\"false\" class=\"loader__icon-svg--large fill-currentColor h-[60px] min-h-[60px] w-[60px] min-w-[60px] lazy-loaded\" aria-busy=\"false\">\n  <g>\n    <path opacity=\"1\" d=\"M30.1,16.1L30.1,16.1c-0.6,0-1-0.5-1-1V1c0-0.6,0.5-1,1-1l0,0c0.6,0,1,0.5,1,1v14.1C31.1,15.7,30.6,16.1,30.1,16.1z\"></path>\n    <path opacity=\"0.85\" d=\"M23.1,18.1L23.1,18.1c-0.5,0.3-1.1,0.1-1.4-0.4L14.5,5.6c-0.3-0.5-0.2-1.1,0.4-1.4l0,0C15.4,3.9,16,4,16.3,4.6l7.2,12.1C23.8,17.2,23.6,17.8,23.1,18.1z\"></path>\n    <path opacity=\"0.77\" d=\"M17.9,23.1L17.9,23.1c-0.3,0.5-0.9,0.7-1.4,0.4l-12.2-7c-0.5-0.3-0.7-0.9-0.4-1.4l0,0c0.3-0.5,0.9-0.7,1.4-0.4l12.2,7C18,22,18.2,22.7,17.9,23.1z\"></path>\n    <path opacity=\"0.69\" d=\"M16.1,30.1L16.1,30.1c0,0.6-0.5,1-1,1L1,31.2c-0.6,0-1-0.5-1-1l0,0c0-0.6,0.5-1,1-1l14.1-0.1C15.7,29.1,16.1,29.5,16.1,30.1z\"></path>\n    <path opacity=\"0.61\" d=\"M18,36.9L18,36.9c0.3,0.5,0.2,1.1-0.4,1.4L5.5,45.6c-0.5,0.3-1.1,0.2-1.4-0.4l0,0c-0.3-0.5-0.2-1.1,0.4-1.4l12.1-7.3C17.1,36.2,17.7,36.4,18,36.9z\"></path>\n    <path opacity=\"0.53\" d=\"M23.3,42.1L23.3,42.1c0.5,0.3,0.6,0.9,0.4,1.4l-7.3,12.1c-0.3,0.5-0.9,0.6-1.4,0.4l0,0c-0.5-0.3-0.6-0.9-0.4-1.4l7.3-12.1C22.1,41.9,22.8,41.8,23.3,42.1z\"></path>\n    <path opacity=\"0.45\" d=\"M30.1,43.9L30.1,43.9c0.6,0,1,0.5,1,1V59c0,0.6-0.5,1-1,1l0,0c-0.6,0-1-0.5-1-1V44.9C29,44.4,29.5,43.9,30.1,43.9z\"></path>\n    <path opacity=\"0.37\" d=\"M37,41.9L37,41.9c0.5-0.3,1.1-0.2,1.4,0.4l7.2,12.1c0.3,0.5,0.2,1.1-0.4,1.4l0,0c-0.5,0.3-1.1,0.2-1.4-0.4l-7.2-12.1C36.4,42.8,36.6,42.2,37,41.9z\"></path>\n    <path opacity=\"0.29\" d=\"M42.2,36.8L42.2,36.8c0.3-0.5,0.9-0.7,1.4-0.4l12.2,7c0.5,0.3,0.7,0.9,0.4,1.4l0,0c-0.3,0.5-0.9,0.7-1.4,0.4l-12.2-7C42.1,38,41.9,37.4,42.2,36.8z\"></path>\n    <path opacity=\"0.21 \" d=\"M44,29.9L44,29.9c0-0.6,0.5-1,1-1h14.1c0.6,0,1,0.5,1,1l0,0c0,0.6-0.5,1-1,1L45,31C44.4,31,44,30.5,44,29.9z\"></path>\n    <path opacity=\"0.13\" d=\"M42.1,23.1L42.1,23.1c-0.3-0.5-0.2-1.1,0.4-1.4l12.1-7.3c0.5-0.3,1.1-0.2,1.4,0.4l0,0c0.3,0.4,0.1,1.1-0.4,1.3l-12.1,7.3C43.1,23.7,42.4,23.6,42.1,23.1z\"></path>\n    <path opacity=\"0.05\" d=\"M36.9,17.9L36.9,17.9c-0.5-0.3-0.6-0.9-0.4-1.4l7.3-12.1c0.3-0.5,0.9-0.6,1.4-0.4l0,0c0.5,0.3,0.6,0.9,0.4,1.4l-7.4,12.2C38,18.1,37.3,18.2,36.9,17.9z\"></path>\n    <animateTransform attributeName=\"transform\" attributeType=\"XML\" type=\"rotate\" begin=\"0s\" dur=\"1s\" repeatCount=\"indefinite\" calcMode=\"discrete\" keyTimes=\"0;.0833;.166;.25;.3333;.4166;.5;.5833;.6666;.75;.8333;.9166;1\" values=\"0,30,30;30,30,30;60,30,30;90,30,30;120,30,30;150,30,30;180,30,30;210,30,30;240,30,30;270,30,30;300,30,30;330,30,30;360,30,30\"></animateTransform>\n  </g>\n</svg></icon>\n      </div>\n    </div>\n  \n    </form>\n    <script data-module-id=\"google-gsi-lib\" src=\"https://static.licdn.com/aero-v1/sc/h/29rdkxlvag0d3cpj96fiilbju\" class=\"lazy-loaded\"></script>\n    \n    \n    \n    \n      \n      \n  \n\n        \n    \n    \n    \n    \n    \n    \n\n    <div class=\"contextual-sign-in-modal base-contextual-sign-in-modal\" data-cool-off-enabled=\"\" data-show-on-page-load=\"\">\n<!---->\n        \n\n    \n    <div class=\"\">\n<!---->\n      \n    </div>\n  \n<!---->    </div>\n  \n\n      \n    \n    \n    \n\n    \n    \n\n<!---->  \n    </div>\n    \n  \n          \n  \n  \n  <code id=\"isMobile\" style=\"display: none\" aria-hidden=\"true\"><!--false--></code>\n  \n  <code id=\"articleUrn\" style=\"display: none\" aria-hidden=\"true\"><!--\"urn:li:linkedInArticle:7193221334208802816\"--></code>\n    <code id=\"legacyArticleUrn\" style=\"display: none\" aria-hidden=\"true\"><!--\"urn:li:article:8361100863320835500\"--></code>\n  \n              \n        \n<!---->\n    \n      \n    \n\n    <a href=\"#main-content\" class=\"skip-link btn-md btn-primary absolute z-11 -top-[100vh] focus:top-0\" aria-hidden=\"true\">\n      Skip to main content\n    </a>\n  \n    <header class=\"header base-detail-page__header px-mobile-container-padding bg-color-background-container global-alert-offset sticky-header\" aria-hidden=\"true\">\n      \n        \n\n    \n    \n    \n    \n\n    <nav class=\"nav pt-1.5 pb-2 flex items-center justify-between relative flex-nowrap babymamabear:py-1.5\n         nav--minified-mobile \n        \n         babybear:flex-wrap \" aria-label=\"Primary\">\n\n      <a href=\"/?trk=article-ssr-frontend-pulse_nav-header-logo\" class=\"nav__logo-link link-no-visited-state z-1 mr-auto min-h-[52px] flex items-center babybear:z-0 hover:no-underline focus:no-underline active:no-underline\n           babymamabear:mr-3\" data-tracking-control-name=\"article-ssr-frontend-pulse_nav-header-logo\" data-tracking-will-navigate=\"\">\n          \n              \n    \n    <span class=\"sr-only\">LinkedIn</span>\n      <icon class=\"nav-logo--inbug flex text-color-brand papabear:hidden mamabear:hidden lazy-loaded\" data-svg-class-name=\"h-[34px] w-[34px] babybear:h-[26px] babybear:w-[26px]\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" height=\"27\" width=\"27\" viewBox=\"0 0 27 27\" focusable=\"false\" class=\"h-[34px] w-[34px] babybear:h-[26px] babybear:w-[26px] lazy-loaded\" aria-busy=\"false\">\n  <g fill=\"currentColor\">\n    <path d=\"M1.91 0h22.363a1.91 1.91 0 011.909 1.91v22.363a1.91 1.91 0 01-1.91 1.909H1.91A1.91 1.91 0 010 24.272V1.91A1.91 1.91 0 011.91 0zm1.908 22.364h3.818V9.818H3.818zM8.182 5.727a2.455 2.455 0 10-4.91 0 2.455 2.455 0 004.91 0zm2.182 4.091v12.546h3.818v-6.077c0-2.037.75-3.332 2.553-3.332 1.3 0 1.81 1.201 1.81 3.332v6.077h3.819v-6.93c0-3.74-.895-5.78-4.667-5.78-1.967 0-3.277.921-3.788 1.946V9.818z\" fill=\"currentColor\" fill-rule=\"evenodd\"></path>\n  </g>\n</svg></icon>\n      <icon class=\"block text-color-brand w-[102px] h-[26px] babybear:hidden lazy-loaded\" data-test-id=\"nav-logo\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 84 21\" preserveAspectRatio=\"xMinYMin meet\" version=\"1.1\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <g class=\"inbug\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n    <path d=\"M19.479,0 L1.583,0 C0.727,0 0,0.677 0,1.511 L0,19.488 C0,20.323 0.477,21 1.333,21 L19.229,21 C20.086,21 21,20.323 21,19.488 L21,1.511 C21,0.677 20.336,0 19.479,0\" class=\"bug-text-color\" transform=\"translate(63.000000, 0.000000)\"></path>\n    <path d=\"M82.479,0 L64.583,0 C63.727,0 63,0.677 63,1.511 L63,19.488 C63,20.323 63.477,21 64.333,21 L82.229,21 C83.086,21 84,20.323 84,19.488 L84,1.511 C84,0.677 83.336,0 82.479,0 Z M71,8 L73.827,8 L73.827,9.441 L73.858,9.441 C74.289,8.664 75.562,7.875 77.136,7.875 C80.157,7.875 81,9.479 81,12.45 L81,18 L78,18 L78,12.997 C78,11.667 77.469,10.5 76.227,10.5 C74.719,10.5 74,11.521 74,13.197 L74,18 L71,18 L71,8 Z M66,18 L69,18 L69,8 L66,8 L66,18 Z M69.375,4.5 C69.375,5.536 68.536,6.375 67.5,6.375 C66.464,6.375 65.625,5.536 65.625,4.5 C65.625,3.464 66.464,2.625 67.5,2.625 C68.536,2.625 69.375,3.464 69.375,4.5 Z\" class=\"background\" fill=\"currentColor\"></path>\n  </g>\n  <g class=\"linkedin-text\">\n    <path d=\"M60,18 L57.2,18 L57.2,16.809 L57.17,16.809 C56.547,17.531 55.465,18.125 53.631,18.125 C51.131,18.125 48.978,16.244 48.978,13.011 C48.978,9.931 51.1,7.875 53.725,7.875 C55.35,7.875 56.359,8.453 56.97,9.191 L57,9.191 L57,3 L60,3 L60,18 Z M54.479,10.125 C52.764,10.125 51.8,11.348 51.8,12.974 C51.8,14.601 52.764,15.875 54.479,15.875 C56.196,15.875 57.2,14.634 57.2,12.974 C57.2,11.268 56.196,10.125 54.479,10.125 L54.479,10.125 Z\" fill=\"currentColor\"></path>\n    <path d=\"M47.6611,16.3889 C46.9531,17.3059 45.4951,18.1249 43.1411,18.1249 C40.0001,18.1249 38.0001,16.0459 38.0001,12.7779 C38.0001,9.8749 39.8121,7.8749 43.2291,7.8749 C46.1801,7.8749 48.0001,9.8129 48.0001,13.2219 C48.0001,13.5629 47.9451,13.8999 47.9451,13.8999 L40.8311,13.8999 L40.8481,14.2089 C41.0451,15.0709 41.6961,16.1249 43.1901,16.1249 C44.4941,16.1249 45.3881,15.4239 45.7921,14.8749 L47.6611,16.3889 Z M45.1131,11.9999 C45.1331,10.9449 44.3591,9.8749 43.1391,9.8749 C41.6871,9.8749 40.9121,11.0089 40.8311,11.9999 L45.1131,11.9999 Z\" fill=\"currentColor\"></path>\n    <polygon fill=\"currentColor\" points=\"38 8 34.5 8 31 12 31 3 28 3 28 18 31 18 31 13 34.699 18 38.241 18 34 12.533\"></polygon>\n    <path d=\"M16,8 L18.827,8 L18.827,9.441 L18.858,9.441 C19.289,8.664 20.562,7.875 22.136,7.875 C25.157,7.875 26,9.792 26,12.45 L26,18 L23,18 L23,12.997 C23,11.525 22.469,10.5 21.227,10.5 C19.719,10.5 19,11.694 19,13.197 L19,18 L16,18 L16,8 Z\" fill=\"currentColor\"></path>\n    <path d=\"M11,18 L14,18 L14,8 L11,8 L11,18 Z M12.501,6.3 C13.495,6.3 14.3,5.494 14.3,4.5 C14.3,3.506 13.495,2.7 12.501,2.7 C11.508,2.7 10.7,3.506 10.7,4.5 C10.7,5.494 11.508,6.3 12.501,6.3 Z\" fill=\"currentColor\"></path>\n    <polygon fill=\"currentColor\" points=\"3 3 0 3 0 18 9 18 9 15 3 15\"></polygon>\n  </g>\n</svg></icon>\n  \n          \n      </a>\n\n<!---->\n        \n    \n    \n    \n    \n    \n    \n    \n    \n    <ul class=\"top-nav-menu flex items-center babybear:w-full babybear:justify-between babybear:pt-1 justify-start w-max pt-0 overflow-x-auto\n        after:papamamabear:up-down-divider after:papamamabear:!h-[37px]\n         nav__menu babybear:order-last order-3 ml-auto\">\n        <li class=\"\">\n          \n    <a href=\"https://www.linkedin.com/pulse/topics/home/<USER>\" data-tracking-control-name=\"article-ssr-frontend-pulse_guest_nav_menu_articles\" data-tracking-will-navigate=\"\" class=\"top-nav-link flex justify-center items-center min-h-[52px] hover:text-color-text visited:hover:text-color-text hover:no-underline\n        w-8 \n        flex-col mx-1 babybear:mx-0\n        text-color-text-secondary visited:text-color-text-secondary\">\n      <icon class=\"top-nav-link__icon flex h-3 w-3 flex-shrink-0 justify-center lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 20 17\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"m11 9.5h5v1h-5v-1zm5-5h-12v3h12v-3zm-5 8h5v-1h-5v1zm9-12v13c0 1.657-1.343 3-3 3h-14c-1.657 0-3-1.343-3-3v-13h20zm-2 2h-16v11c0 0.552 0.449 1 1 1h14c0.551 0 1-0.448 1-1v-11zm-9 7h-5v3h5v-3z\" fill=\"currentColor\" fill-opacity=\".9\"></path>\n</svg></icon>\n      <span class=\"top-nav-link__label-text font-sans text-xs leading-regular text-center w-max\n          font-regular\">\n        Articles\n      </span>\n    </a>\n  \n        </li>\n        <li class=\"\">\n          \n    <a href=\"https://www.linkedin.com/pub/dir/+/+?trk=article-ssr-frontend-pulse_guest_nav_menu_people\" data-tracking-control-name=\"article-ssr-frontend-pulse_guest_nav_menu_people\" data-tracking-will-navigate=\"\" class=\"top-nav-link flex justify-center items-center min-h-[52px] hover:text-color-text visited:hover:text-color-text hover:no-underline\n        w-8 \n        flex-col mx-1 babybear:mx-0\n        text-color-text-secondary visited:text-color-text-secondary\">\n      <icon class=\"top-nav-link__icon flex h-3 w-3 flex-shrink-0 justify-center lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"20\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M9 14v6H0v-6c0-1.7 1.3-3 3-3h3c1.7 0 3 1.3 3 3Zm5.5-3c1.9 0 3.5-1.6 3.5-3.5S16.4 4 14.5 4 11 5.6 11 7.5s1.6 3.5 3.5 3.5Zm1 2h-2c-1.4 0-2.5 1.1-2.5 2.5V20h7v-4.5c0-1.4-1.1-2.5-2.5-2.5ZM4.5 0C2 0 0 2 0 4.5S2 9 4.5 9 9 7 9 4.5 7 0 4.5 0Z\" fill=\"currentColor\"></path>\n</svg></icon>\n      <span class=\"top-nav-link__label-text font-sans text-xs leading-regular text-center w-max\n          font-regular\">\n        People\n      </span>\n    </a>\n  \n        </li>\n        <li class=\"\">\n          \n    <a href=\"https://www.linkedin.com/learning/search?trk=article-ssr-frontend-pulse_guest_nav_menu_learning\" data-tracking-control-name=\"article-ssr-frontend-pulse_guest_nav_menu_learning\" data-tracking-will-navigate=\"\" class=\"top-nav-link flex justify-center items-center min-h-[52px] hover:text-color-text visited:hover:text-color-text hover:no-underline\n        w-8 \n        flex-col mx-1 babybear:mx-0\n        text-color-text-secondary visited:text-color-text-secondary\">\n      <icon class=\"top-nav-link__icon flex h-3 w-3 flex-shrink-0 justify-center lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M23 3H1a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h22a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1ZM2 19h20V5H2v14Z\" fill=\"currentColor\"></path>\n  <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4 9h6V7H4v2Zm0 4h6v-2H4v2Zm0 4h6v-2H4v2Zm-2 2h10V5H2v14Z\" fill=\"currentColor\" fill-opacity=\".25\"></path>\n  <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M14 9h6V7h-6v2Zm0 4h6v-2h-6v2Zm6 4h-6v-2h6v2Z\" fill=\"currentColor\" fill-opacity=\".6\"></path>\n  <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M10 7.534v8.933a.28.28 0 0 0 .439.23l6.433-4.436A.307.307 0 0 0 17 12a.305.305 0 0 0-.128-.26l-6.433-4.437a.28.28 0 0 0-.439.23Z\" fill=\"currentColor\"></path>\n</svg></icon>\n      <span class=\"top-nav-link__label-text font-sans text-xs leading-regular text-center w-max\n          font-regular\">\n        Learning\n      </span>\n    </a>\n  \n        </li>\n        <li class=\"\">\n          \n    <a href=\"https://www.linkedin.com/jobs/search?trk=article-ssr-frontend-pulse_guest_nav_menu_jobs\" data-tracking-control-name=\"article-ssr-frontend-pulse_guest_nav_menu_jobs\" data-tracking-will-navigate=\"\" class=\"top-nav-link flex justify-center items-center min-h-[52px] hover:text-color-text visited:hover:text-color-text hover:no-underline\n        w-8 \n        flex-col mx-1 babybear:mx-0\n        text-color-text-secondary visited:text-color-text-secondary\">\n      <icon class=\"top-nav-link__icon flex h-3 w-3 flex-shrink-0 justify-center lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"18\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M15 4V3c0-1.7-1.3-3-3-3H8C6.3 0 5 1.3 5 3v1H0v4c0 1.7 1.3 3 3 3h14c1.7 0 3-1.3 3-3V4h-5ZM7 3c0-.6.4-1 1-1h4c.6 0 1 .4 1 1v1H7V3Zm10 9c1.2 0 2.3-.5 3-1.4V15c0 1.7-1.3 3-3 3H3c-1.7 0-3-1.3-3-3v-4.4c.7.9 1.8 1.4 3 1.4h14Z\" fill=\"currentColor\"></path>\n</svg></icon>\n      <span class=\"top-nav-link__label-text font-sans text-xs leading-regular text-center w-max\n          font-regular\">\n        Jobs\n      </span>\n    </a>\n  \n        </li>\n        <li class=\"\">\n          \n    <a href=\"https://www.linkedin.com/games?trk=article-ssr-frontend-pulse_guest_nav_menu_games\" data-tracking-control-name=\"article-ssr-frontend-pulse_guest_nav_menu_games\" data-tracking-will-navigate=\"\" class=\"top-nav-link flex justify-center items-center min-h-[52px] hover:text-color-text visited:hover:text-color-text hover:no-underline\n        w-8 \n        flex-col mx-1 babybear:mx-0\n        text-color-text-secondary visited:text-color-text-secondary\">\n      <icon class=\"top-nav-link__icon flex h-3 w-3 flex-shrink-0 justify-center lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"21\" height=\"20\" viewBox=\"0 0 21 20\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M12.3446 11.8441C12.718 11.4706 13.2378 11.2385 13.8081 11.2385C14.7569 11.2385 15.5543 11.8744 15.8015 12.7424C15.9328 13.2117 16.2305 13.6155 16.6191 13.8829L20.5 10.002L16.6191 6.12114L16.6797 6.03534C16.9471 5.68712 17.3256 5.42469 17.7597 5.30357C18.6277 5.05629 19.2636 4.25891 19.2636 3.31013C19.2636 2.73986 19.0314 2.22005 18.658 1.8466C18.2845 1.47314 17.7647 1.241 17.1944 1.241C16.2456 1.241 15.4483 1.87688 15.201 2.74491C15.0799 3.17892 14.8174 3.55742 14.4692 3.8249L14.3834 3.88546L10.4975 -0.000488281L6.61658 3.88041C6.8891 4.27405 7.28779 4.56676 7.75713 4.70302C8.62516 4.95031 9.26104 5.74768 9.26104 6.69646C9.26104 7.26673 9.02889 7.78654 8.65544 8.16C8.28198 8.53345 7.76218 8.7656 7.1919 8.7656C6.24312 8.7656 5.44575 8.12971 5.19846 7.26169C5.06725 6.79234 4.76949 6.38861 4.3809 6.12114L0.5 9.99699L4.3809 13.8779L4.32034 13.9637C4.05286 14.3119 3.67436 14.5743 3.24035 14.6954C2.37232 14.9427 1.73644 15.7401 1.73644 16.6889C1.73644 17.2592 1.96858 17.779 2.34204 18.1524C2.71549 18.5259 3.2353 18.758 3.80558 18.758C4.75435 18.758 5.55173 18.1221 5.79902 17.2541C5.92014 16.8201 6.18256 16.4416 6.53078 16.1741L6.61658 16.1136L10.4975 19.9945L14.3784 16.1136C14.1059 15.7199 13.7072 15.4272 13.2378 15.291C12.3698 15.0437 11.7339 14.2463 11.7339 13.2975C11.7339 12.7272 11.9661 12.2074 12.3395 11.834L12.3446 11.8441Z\" fill=\"currentColor\" fill-opacity=\".9\"></path>\n</svg></icon>\n      <span class=\"top-nav-link__label-text font-sans text-xs leading-regular text-center w-max\n          font-regular\">\n        Games\n      </span>\n    </a>\n  \n        </li>\n    </ul>\n  \n\n      <div class=\"nav__cta-container order-3 flex gap-x-1 justify-end min-w-[100px] flex-nowrap flex-shrink-0 babybear:flex-wrap flex-2\n           babymamabear:min-w-[50px] \">\n<!---->\n        \n    \n    <a class=\"nav__button-tertiary btn-md btn-tertiary\" href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_nav-header-join\" data-tracking-control-name=\"article-ssr-frontend-pulse_nav-header-join\" data-test-live-nav-primary-cta=\"\" data-tracking-will-navigate=\"\" data-tracking-client-ingraph=\"\">\n      Join now\n    </a>\n\n\n          \n  \n  \n\n      \n      <a class=\"nav__button-secondary btn-secondary-emphasis btn-md\" href=\"https://www.linkedin.com/uas/login?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;fromSignIn=true&amp;trk=article-ssr-frontend-pulse_nav-header-signin\" data-tracking-control-name=\"article-ssr-frontend-pulse_nav-header-signin\" data-tracking-will-navigate=\"\" data-tracking-client-ingraph=\"\">\n          Sign in\n      </a>\n\n\n          <a aria-label=\"Sign in\" class=\"nav__link-person papabear:hidden mamabear:hidden\" data-tracking-control-name=\"article-ssr-frontend-pulse_nav-header-signin\" data-tracking-will-navigate=\"\" href=\"https://www.linkedin.com/uas/login?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;fromSignIn=true&amp;trk=article-ssr-frontend-pulse_nav-header-signin\">\n            \n      <img class=\"inline-block relative rounded-[50%] w-4 h-4 bg-color-entity-ghost-background lazy-loaded\" data-ghost-classes=\"bg-color-entity-ghost-background\" data-ghost-url=\"https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2\" alt=\"\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2\">\n      \n          </a>\n      </div>\n\n<!---->\n<!---->    </nav>\n  \n      \n    </header>\n\n    \n<!---->      \n\n<!---->\n    <main class=\"main papabear:flex papabear:w-content-max-w papabear:mx-auto papabear:pt-desktop-content-top-margin mamabear:pt-desktop-content-top-margin\n        \" id=\"main-content\" role=\"main\" aria-hidden=\"true\">\n      <section class=\"core-rail mx-auto papabear:w-core-rail-width mamabear:max-w-[790px] babybear:max-w-[790px]\">\n        \n        \n      \n\n        <div class=\"details mx-details-container-padding\">\n          \n        \n            \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n<!---->    \n    <code id=\"memberUrn\" style=\"display: none\"><!--\"urn:li:member:0\"--></code>\n    <code id=\"isNbaRecommendedArticlesDeeplinkEnabled\" style=\"display: none\"><!--\"true\"--></code>\n    <code id=\"isDynamicTextareaEnabled\" style=\"display: none\"><!--\"true\"--></code>\n    <article class=\"article-main relative flex-grow pulse\">\n<!---->            \n    <figure class=\"cover-img\">\n<!---->      <div class=\"cover-img__image-frame relative w-full overflow-hidden pb-[calc((134/782)*100%)]\">\n        <div class=\"cover-img__image-position absolute top-0 right-0 bottom-0 left-0\n            \">\n            <img class=\"cover-img__image relative w-full h-full object-cover\" src=\"https://media.licdn.com/dms/image/v2/D4D12AQEalogBqcGJ7w/article-cover_image-shrink_720_1280/article-cover_image-shrink_720_1280/0/1714997667793?e=**********&amp;v=beta&amp;t=v7oWSHKg57tYrEPMWBhh826yN-nrLfMS4NCmLNJE1s0\" fetchpriority=\"high\" data-embed-id=\"cover-image\" alt=\"CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform\" tabindex=\"0\">\n        </div>\n      </div>\n<!---->    </figure>\n  \n                  \n      <header>\n        <section class=\"core-section-container relative my-3\">\n            \n              \n  <h1 class=\"pulse-title text-display-md text-color-text lg:text-display-lg font-bold w-11/12\">CreditChat: The Benefits of Integrating Generative AI into a Credit Analysis and Automation Platform</h1>\n\n                \n\n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n      <div class=\"ellipsis-menu absolute right-0 top-0\">\n        \n\n    \n\n    <div class=\"collapsible-dropdown flex items-center relative hyphens-auto\">\n          \n            <button class=\"ellipsis-menu__trigger\n                collapsible-dropdown__button btn-md btn-tertiary cursor-pointer\n                !py-[6px] !px-1 flex items-center rounded-[50%]\n                \n                \" aria-expanded=\"false\" aria-label=\"Open menu\" data-tracking-control-name=\"article-ssr-frontend-pulse_ellipsis-menu-trigger\" tabindex=\"0\">\n              <icon class=\"ellipsis-menu__trigger-icon m-0 p-0 centered-icon lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" data-supported-dps=\"24x24\" fill=\"currentColor\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M2 10h4v4H2v-4zm8 4h4v-4h-4v4zm8-4v4h4v-4h-4z\"></path>\n</svg></icon>\n            </button>\n          \n\n        <ul class=\"collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]\" role=\"menu\" tabindex=\"-1\">\n          \n              \n\n                <li class=\"ellipsis-menu__item border-t-1 border-solid border-color-border-low-emphasis first-of-type:border-none flex\" role=\"presentation\">\n                  \n\n    \n    \n\n    \n\n    \n\n    <a href=\"/uas/login?session_redirect=https%3A%2F%2Fwww.linkedin.com%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_ellipsis-menu-semaphore-sign-in-redirect&amp;guestReportContentType=PONCHO_ARTICLE&amp;_f=guest-reporting\" data-tracking-control-name=\"article-ssr-frontend-pulse_ellipsis-menu-semaphore-sign-in-redirect\" data-tracking-will-navigate=\"\" data-item-type=\"semaphore\" data-semaphore-content-type=\"PONCHO_ARTICLE\" data-semaphore-content-urn=\"urn:li:linkedInArticle:7193221334208802816\" data-semaphore-tracking-prefix=\"article-ssr-frontend-pulse_ellipsis-menu-semaphore\" data-is-logged-in=\"false\" data-modal=\"semaphore__toggle\" class=\"semaphore__toggle visited:text-color-text-secondary ellipsis-menu__semaphore ellipsis-menu__item-button flex items-center w-full p-1 cursor-pointer font-sans text-sm font-bold link-styled focus:link-styled link:no-underline active:bg-color-background-container-tint focus:bg-color-background-container-tint hover:bg-color-background-container-tint outline-offset-[-2px]\" role=\"menuitem\">\n<!---->        \n                      <icon class=\"ellipsis-menu__item-icon text-color-text h-[24px] w-[24px] mr-1 lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" data-supported-dps=\"24x24\" fill=\"currentColor\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M13.82 5L14 4a1 1 0 00-1-1H5V2H3v20h2v-7h4.18L9 16a1 1 0 001 1h8.87L21 5h-7.18zM5 13V5h6.94l-1.41 8H5zm12.35 2h-6.3l1.42-8h6.29z\"></path>\n</svg></icon>\n                      Report this article\n                    \n    </a>\n\n<!---->  \n                </li>\n<!---->          \n        </ul>\n\n<!---->    </div>\n  \n\n      </div>\n  \n          \n\n          <div class=\"core-section-container__content break-words\">\n            \n              \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n         publisher-author-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://br.linkedin.com/in/alexandremarinho\" data-tracking-control-name=\"article-ssr-frontend-pulse_publisher-author-card\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        Alexandre Marinho\n      \n      \n          </span>\n        </a>\n\n      \n          \n      <img class=\"inline-block relative rounded-[50%] w-6 h-6 bg-color-entity-ghost-background lazy-loaded\" data-ghost-classes=\"bg-color-entity-ghost-background\" data-ghost-url=\"https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2\" alt=\"Alexandre Marinho\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2\">\n      \n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            \">\n<!---->          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              \n              base-main-card__title--link\">\n            \n        Alexandre Marinho\n      \n          </h3>\n          \n\n            <h4 class=\"base-main-card__subtitle body-text text-color-text overflow-hidden\n                \">\n              \n            President and CEO at SIACorp, Director and Board Member at Agrometrika, Board Member at Akrual Systems, over 45 years experience in Technology, Artificial Intelligence, Credit Automation and Risk Management\n                \n            </h4>\n\n<!---->\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata\">\n                \n          Published May 6, 2024\n              \n              </div>\n<!---->        </div>\n\n          <div class=\"base-main-card__ctas z-[3] self-center ml-3 babybear:ml-1 babybear:self-start\">\n            \n\n            <a class=\"base-main-card__cta btn-sm btn-secondary\" href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_publisher-author-card\" data-tracking-control-name=\"article-ssr-frontend-pulse_publisher-author-card\" data-tracking-will-navigate=\"\">\n              + Follow\n            </a>\n      \n          </div>\n      \n    \n      </div>\n  \n  \n  \n  \n          \n          </div>\n        </section>\n      </header>\n      \n              <div data-test-id=\"article-content-blocks\">\n<!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The analysis and assessment of credit risk of legal entities plays a fundamental role in making decisions regarding the establishment of credit limits and durations values granted to these companies depending on their level of risk and the expected return depending on aspects such as reciprocity and relationship.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The use of artificial intelligence techniques in credit automation is not something new, dating back to the late 1980s, when the first expert systems for credit analysis began to emerge, based on technologies such as decision trees, logistic regression and neural networks.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Nevertheless, recent advances in Generative AI technology open up a wide range of new possibilities. Chatbots based on LLM (Large Language Models) models, such as ChatGPT, have been gaining great popularity due to their ability to carry out complex activities in an automated and personalized way, revolutionizing the way organizations operate.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">As a result, in the coming years, customizations of LLM models will enable a dramatic increase in the productivity of organizations through the automation of complex day-to-day tasks. Areas such as Finance, Marketing &amp; Sales, Programming, and many others will benefit profoundly from this evolution.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQH7l938eUeJGA/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1715001501489?e=**********&amp;v=beta&amp;t=sSV4FbQFNJcp_aMHB1Nz1FcRbouEoRqapm4tRViF7HE\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">The Advent of Chat GPT</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Recently, the introduction of the ChatGPT Platform by OpenAI revolutionized the world of Generative AI. ChatGPT uses Deep Learning techniques, natural language processing (NLP), and LLM technology to generate human-like responses to user queries.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">As expected, the entire finance industry and particularly the areas of credit and risk management, analysis, approval and review will be profoundly impacted by the possibility of applying this technology to activities such as analysis reports generation, automated reviews of customer credit limits and dynamic approval workflows.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">ChatGPT Applications in the Credit Area</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">There are numerous applications of ChatGPT in the Financial Area and particularly in the Credit Analysis Area. Among the various use cases in which Generative AI can be used, the following stand out:</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Balance Sheet Analysis</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Analysis of Credit Proposals</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Review of Credit Lines and Limits</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Credit Portfolio Analysis</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Litigation Analysis</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• “Peer Analysis”</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Preparation of Credit Reports and Presentations</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Decision Support in Credit Committees</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The analysis and compilation of large amounts of financial data can be time-consuming and subject to human error, especially if carried out by professionals with less experience.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">ChatGPT is very efficient in analyzing large amounts of data, being able to simultaneously evaluate registration aspects, internal and external information, financial ratios and news about the Company, detecting trends and identifying potential credit risks.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Additionally, carrying out up-to-date analyzes based on trends, news and market sentiments adds substantial value to effective credit risk management.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">By automating this process, Organizations will enable credit analysts to save valuable time used in preparing credit analysis reports and use it in activities such as visiting customers and improving the ability to detect credit default patterns to refine models. , in order to present accurate and contextually relevant forecasts.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">CreditChat</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Since its founding, 25 years ago, SIACorp has used Artificial Intelligence techniques such as decision forests, Bayesian networks and logistic regression in the rating calculation models and limit suggestions made by its CreditFlow Credit Automation Platform.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Envisioning the potential for using Generative AI technology in the credit analysis and granting process, SIACorp created CreditChat, a Chatbot for Credit Analysis fully integrated with the CreditFlow Credit Automation Platform.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQFwh5o9j0UUuA/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1715001580890?e=**********&amp;v=beta&amp;t=ZDHaqrkTtzUeupsXF-qTnp48znWfFO0BrANLaLeZtjI\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">CreditChat is a generative AI application that uses the ChatGPT integration features provided by </span><span class=\"\"><a href=\"http://Open.Ai?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">Open.Ai<!----></a></span><span class=\"\"> to access information from various sources and generate richly elaborated credit analyses, which can be adapted to the specific needs of the business, including the incorporation of the company's exclusive knowledge bases and regulatory standards, which allows it to provide extremely accurate and relevant answers.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">This information includes customer's relevant data, their financial statements, punctuality information (dynamically accessed in the accounts receivable module of the ERP or Core Banking System), restrictions (accessed in credit bureaus) and behavioral information, which is automatically uploaded by the CreditFlow modules in ChatGPT dynamically building the prompt submitted to generate the Analysis.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">A future version of CredtChat will incorporate recent news. As the knowledge base of the current ChatGPT version is trained with news until July 2023, we decided not to incorporate news processing at this time.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQEGasxraEDNyA/article-inline_image-shrink_400_744/article-inline_image-shrink_400_744/0/1714998707806?e=**********&amp;v=beta&amp;t=MbP-FJ8qbmM20_IH4_Re0tQ-30T_3A4ISsAZT7PyVMQ\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">APIs, Prompt Engineering, and Model Training</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Through advanced integration and prompt engineering techniques, we use ChatGPT to generate context in JSON format from information provided by the CreditFlow Platform and then use it as input for the </span><span class=\"\"><a href=\"http://Open.AI?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">Open.AI<!----></a></span><span class=\"\"> API, ultimately exporting the information generated by ChatGPT in JSON/XML format and rendering dynamic analysis reports.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQE6dgeW1aE_1A/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1714998730204?e=**********&amp;v=beta&amp;t=oHm_8o0AyJ9JNVyTC-C9CsHFG8QAQ4qfTQ3PND6kpgI\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\"><a href=\"http://Open.Ai?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">Open.Ai<!----></a></span><span class=\"\">'s APIs and Tools can be used to customize ChatGPT regarding Text Generation, Prompt Engineering, Embeddings, Speech to Text and Text to Speech, Image Generation, Model Fine-Tuning and Vision. To implement CreditChat, SIACorp focused on the use of APIs in the context of Text Generation and Prompt Engineering.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Information Analyzed by CreditChat</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Among the information submitted by CreditChat to ChatGPT we can highlight aspects such as:</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Master Data and Registration Information</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Internal Financial Information (ERP or Core Banking)</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• External Financial Information (Credit Bureaus)</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Information and Debt (Bacen SCR)</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Collateral and Gurantees Information</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Scoring and Credit Rating Criteria</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Regulatory Compliance</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Furthermore, the system is capable of reading files and can be trained with large volumes of information contained in documents such as:</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Balance Sheets</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Industry Credit Standards</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Financial Health Indicators</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Previous Credit Reports</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQFi98DJP-5aVA/article-inline_image-shrink_400_744/article-inline_image-shrink_400_744/0/1714998781814?e=**********&amp;v=beta&amp;t=JYbf0vEtfqQodlo44jqiaYkRwNMg2VlxnBn_RGdHXBs\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Based on information uploaded from CreditFlow through the </span><span class=\"\"><a href=\"http://Open.AI?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">Open.AI<!----></a></span><span class=\"\"> Library and automatically submitted to ChatGPT, CreditChat prepares detailed and complex credit analyses, which consider all aspects analyzed, obtained from the submission of questions (or prompts) to ChatGPT.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Prompt Engineering</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Prompt Engineering is one of the most important activities within the scope of Generative AI.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">This topic is so relevant that SIACorp created a Prompt Engineering Area, dedicated to the study and elaboration of ideal questions (in JSON and natural language) to obtain the best possible answers regarding topics related to credit analysis.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Writing data to the ChatGPT prompt screen works well in situations where you want to get answers to queries made on a relatively small data set.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQF5TLLkUvlUHA/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1714998815353?e=**********&amp;v=beta&amp;t=cdGCreOWwNAKgEnZMAusGGV6qkAN_lku9-D40qippoY\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">However, it is not the best way to work with a large set of documents or web pages as input to LLM, due to the limitation on prompt size and the cost associated with passing large text to ChatGPT.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">To implement CreditChat, SIACorp created customized prompts, in addition to training the ChatGPT knowledge base with data and documents stored in CreditFlow, as shown in the following figure.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Among the various Prompt Engineering techniques available, we can mention Chain-of-Thought (COT) Prompting (used by SIACorp in the implementation of CreditChat), Zero-Shot Prompting, Few-Shot-Prompting, Tree of Thoughts Automatic Reasoning and many others .</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Through the use of the COT technique, ChatGPT can generate a series of intermediate steps to lead to the final answer, providing detailed information as well as reasons and justifications for your answers.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Embeddings</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Embedding is a technique for representing information such as text, image or audio, in numerical form. The diagram below conceptually explains how embeddings are used to retrieve information from documents using LLM.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In this technique, documents go through a model that creates small data structures and stores them in a vector database. When a user wants to query the LLM, the embeddings are retrieved from the vector database and passed to the LLM. </span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Embeddings are widely used in the CreditChat Solution, which incorporates information contained in regulatory documents, financial statements, shareholder reports and company portfolios into its analyses.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQF3ijSDFkt2eg/article-inline_image-shrink_400_744/article-inline_image-shrink_400_744/0/1714998894472?e=**********&amp;v=beta&amp;t=l5gp0w9bSSRf5dnXKI35Ejv_1OsHO7L6YwXZTP9oxiI\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">RAG</span><span class=\"\"> </span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The RAG (Retrieval Augmented Generation) technique is used to increase the accuracy and reliability of LLMs, expanding their knowledge base. Through this technique, it is possible to build solutions with private data and use the intelligence of an LLM without having to train it again, since they incorporate targeted information, which may be more up to date than the set used in its training or be specific to a context. particular.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQEHVifkUaSnww/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1714998911081?e=**********&amp;v=beta&amp;t=mpffUECCmRn9NUzS4L_i9LHKeoz-886cTy_cVbRN2KE\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Preparation of Presentations, Reports and Credit Assessments</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">CreditChat generates presentations for committees and richly designed credit reports, thanks to the integration between Generative AI and the entire collection of internal and external documents and information managed by CreditFlow Modules.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">CreditChat accesses information on the CreditFlow Platform and submits it as input to the ChatGPT APIs. Based on the information uploaded from CreditFlow submitted to ChatGPT, CreditChat prepares detailed and complex credit analyses, which consider thousands of pieces of information simultaneously, in a manner that fully adheres to the policies and parameters defined by the company.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In addition to the texts automatically generated by Generative AI, the reports generated by CreditChat incorporate KPIs, KRIs, graphs, maps, traffic lights and extensive visual resources to present the credit situation of the company under analysis.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The reports and opinions issued by CreditChat are generated through robust LLM techniques integrated with financial statements, punctuality information (dynamically accessed in the accounts receivable module of the ERP System or Core Banking), debt (Brazil´s Central Bank Risk System), restrictions (accessed in credit bureaus) and behavioral information.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQHNegDKLDQC9A/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/*************?e=**********&amp;v=beta&amp;t=p4HKAlmINP8o1e1S3FKYiIebr7ZMA31npl7ojJ-6kXM\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Technical Architecture of the Solution</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Generative AI technology demands high-performance computing systems, aiming for faster processing to deal with the large amounts of data generated by applications.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The entire CreditFlow Platform, including CreditChat, is implemented in a cloud-native architecture with support for containers and microservices.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">CreditChat was implemented in C#, on the Microsoft .NET 8.0 Platform. In its implementation, we adopted a wide variety of cutting-edge technologies to guarantee the Platform a very high level of performance, robustness and portability.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQEeaJara1mh1Q/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1714998962590?e=**********&amp;v=beta&amp;t=hY_Z86kK0zFNVsyy99DkDOBOnv1l4kldIzuqPiZTweg\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Among the technologies used, the following stand out.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• </span><span class=\"\"><a href=\"http://ASP.NET?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">ASP.NET<!----></a></span><span class=\"\"> WebApi Core with JWT Bearer Authentication and </span><span class=\"\"><a href=\"http://ASP.NET?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">ASP.NET<!----></a></span><span class=\"\"> Identity Core: We implement JWT Bearer authentication to ensure end-to-end security in our operations, simplifying identity management and enabling efficient authentication and authorization.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• .NET Core Native DI: We adopt native dependency injection to enable a more modular and easier to maintain architecture.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• AutoMapper and FluentValidator: We automate processes and validations, simplifying development and ensuring data consistency.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• MediatR: We facilitate communication between system components, promoting a decoupled and scalable architecture.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Domain Driven Design and CQRS: We adopt a domain-driven design, separating read and write operations for greater efficiency and scalability.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Event Sourcing and Unit of Work: Traceability and flexibility are guaranteed, allowing a historical view of data and modular maintenance.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• Application Insights: We use detailed metrics to quickly identify and resolve issues, ensuring a seamless experience for users.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">&nbsp;</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Testing Methodology</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The following figure presents the methodology used by SIACorp to test ChatGPT proposed by Xingzhi Wanga, Nabil Anwerb and Yun Daic Ang Liua in (1), in which we answered separate questions in six topics relating to the Credit Analysis and Review processes.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Topics relating to Credit Analysis cover aspects such as Registration Analysis (which involves the evaluation of a wide range of data relating to the company's situation), Financial Analysis (which considers the fundamental analysis of financial statements) and Behavioral Analysis (which considers payments of loans and accounts receivable).</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Topics relating to Credit Review cover aspects such as Profitability Analysis, Reciprocity Analysis and Analysis of the Customer's Relationship with the Organization.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">CreditChat's performance was evaluated based on answers to dozens of questions regarding registration, financial and behavioral aspects of the company, in addition to Central Bank regulations and sectoral analyses.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Furthermore, by modifying some standard questions, several trap questions were generated to test your cognitive flexibility.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Based on the Revised Bloom's Taxonomy (2), questions have been categorized into four difficulty levels:</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• </span><span class=\"font-[700]\">Level 1</span><span class=\"\"> - Basic Knowledge</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Consists of understanding and explaining concepts relating to credit analysis;</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• </span><span class=\"font-[700]\">Level 2</span><span class=\"\"> - Application of Knowledge</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">It consists of choosing, employing, illustrating and demonstrating concepts to appropriately solve specific problems.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• </span><span class=\"font-[700]\">Level 3</span><span class=\"\"> - Critical Analysis</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">It consists of dividing information into groups of components to understand its nature or determine characteristics.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">• </span><span class=\"font-[700]\">Level 4</span><span class=\"\"> - Extension of Knowledge</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">It consists of the synthesis of new knowledge and implementation of inferences.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Then, the responses generated were evaluated in relation to four qualitative characteristics: correctness, relevance, clarity and comparability.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">While correctness and relevance are two basic requirements, clarity and comparability represent desirable requirements.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In cases where ChatGPT did not meet basic requirements, its performance was considered low, indicating that it is not yet ready to perform relevant human work.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In cases where ChatGPT met the four requirements, its performance was considered high, that is, comparable to that of human analysts.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In other cases, its performance was considered average, which means it achieved acceptable performance, but there is still room for improvement.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The following figure shows an overview of the test results. In a set of questions divided into four categories, in turn divided into six topics, ChatGPT achieved high performance in 15 of the 24 categories and average performance in 9 categories.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQFGUV9IVBB-Mw/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1714998999452?e=**********&amp;v=beta&amp;t=y3zATNG5lMOWhPWDxMGjPHFWn0N7fMnKPxIDZ3gNNbQ\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Our tests were quite satisfactory and indicated that CreditChat was adequately trained with technical knowledge in credit analysis, and can be used to substantially boost productivity in the Organizations' Credit Areas.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">As CreditChat is connected to CreditFlow, a conventional Credit Automation Platform, the knowledge encoded in policies, rules, logic and models, accessed by CreditFlow are made available to ChatGPT, increasing its knowledge and simplifying the tracking process for verification.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Our tests also show that the reliability of responses is closely related to the construction of the submitted prompts. For the same question, different answers can be obtained if we interact with ChatGPT differently.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <!----><br>\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">CreditChat implementation</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">To fully leverage the benefits of ChatGPT while mitigating its limitations, SIACorp implemented a three-tier model for implementing CreditChat proposed by Xingzhi Wanga, Nabil Anwerb and Yun Daic Ang Liua in (1), as illustrated in the following figure .</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In each layer, specific tasks must be performed according to their level of complexity or priority.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n    \n\n    \n      <div class=\"flex flex-col mt-2\" data-test-id=\"publishing-image-block\" rel=\"ugc\">\n        \n      <figure>\n        <img alt=\"Article content\" class=\"lazy-load block w-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQFQiVzDYBP5Pw/article-inline_image-shrink_1000_1488/article-inline_image-shrink_1000_1488/0/1714999036844?e=**********&amp;v=beta&amp;t=sRjqH4e6vy3CC0xxQRHBMUIZOpDjGqxeZuVLzLHDmXI\">\n<!---->      </figure>\n    \n      </div>\n  \n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">\n      </span></p><ul>\n        \n    <li><span class=\"\">Knowledge Layer</span><!----></li>\n\n      </ul>\n  <!---->\n        <p></p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">The knowledge layer is the core of the model and consists of loading the knowledge base with specialized technical knowledge in credit analysis, organization standards and procedures and regulatory documents, enabling the generation of robust and consistent analyses.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">\n      </span></p><ul>\n        \n    <li><span class=\"\">Reasoning Layer</span><!----></li>\n\n      </ul>\n  <!---->\n        <p></p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">It consists of configuring the analysis models and inference engines used by CreditChat, based on methodologies such as forest trees, logistic regression and Bayesian networks.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">\n      </span></p><ul>\n        \n    <li><span class=\"\">Collaboration Layer</span><!----></li>\n\n      </ul>\n  <!---->\n        <p></p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">This layer represents the biggest challenge for organizations, which will have to restructure the roles of credit professionals and operational procedures to allow more effective collaboration with ChatGPT, involving them in the process of verifying the models that are gradually being refined.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">&nbsp;</span><span class=\"font-[700]\">Conclusion</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In this article, we present CreditChat, a Chatbot for Credit Analysis fully integrated with the CreditFlow Credit Automation Platform, which adds immense value to the process of producing credit analysis reports in a comprehensive, creative and objective way, thus showing its potential for use for this purpose.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">We conclude that implementing Generative AI in credit risk assessment offers benefits such as a dramatic increase in the speed and efficiency of analyzing large data sets, minimizing the potential for bias and human error, and presenting objective and consistent risk assessments. Finally, it allows proactive risk management, identifying potential compliances or non-compliances at an early stage, allowing credit managers to take the necessary measures to mitigate risks.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Our conclusion is that ChatGPT is impressively efficient in providing information, generating coherent and structured content. We will soon publish studies on topics such as embedding, abductive reasoning and training in the knowledge and reasoning layers.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">In the coming years, the evolution of LLM models will enable a dramatic increase in productivity in the credit automation and risk management segment and SIACorp is very prepared and available to your organization to assist you in this transition.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">&nbsp;</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">Published by:</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">&nbsp;Alexandre Marinho is President of SIACorp and has been working in Information Technology and Artificial Intelligence for over 45 years and implementing Credit Automation Systems for over 35 years.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Alexandre Fazolin is a Partner at SIACorp, working in the IT area for 18 years, leading the implementation of CreditFlow in dozens of projects in Brazil and abroad.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Joseph Guarese is a Partner at SIACorp and a specialist in distributed cloud architectures for Credit Automation.</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">&nbsp;</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"font-[700]\">About SIACorp</span><!---->\n        </p>\n    </div>\n  \n<!----><!---->                  \n\n    <div class=\"article-main__content\" data-test-id=\"publishing-text-block\">\n        <p>\n          <span class=\"\">Working since 1998 in the Implementation of Credit Automation Systems, SIACorp (</span><span class=\"\"><a href=\"http://www.siacorp.com.br?trk=article-ssr-frontend-pulse_little-text-block\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_little-text-block\" data-tracking-will-navigate=\"\" data-test-link=\"\">www.siacorp.com.br<!----></a></span><span class=\"\">)  is the Company with the highest number of implementations of Credit Automation Systems in Brazil. </span><!---->\n        </p>\n    </div>\n  \n<!---->              </div>\n<!----><!----><!----><!----><!---->          \n  \n<!---->    </article>\n  \n<!---->            \n    \n    \n\n      <code id=\"ugcPostUrl\" style=\"display: none\"><!--\"https://www.linkedin.com/feed/update/urn:li:ugcPost:7193222795072303104\"--></code>\n      <code id=\"ugcPost\" style=\"display: none\"><!--\"urn:li:ugcPost:7193222795072303104\"--></code>\n      \n    \n      \n      <code id=\"ugcPostUrn\" style=\"display: none\"><!--\"urn:li:ugcPost:7193222795072303104\"--></code>\n      <code id=\"signInUrl\" style=\"display: none\"><!--\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\"--></code>\n\n<!---->  \n    <div class=\"article-social-activity z-[201]\n        x-social-activity-wrapper xs:sticky bottom-0 flex justify-center border-t-1 border-solid border-color-border-faint bg-color-background-container\n        \">\n<!---->      <div class=\"x-social-activity social-details x-social-activity-left flex flex-wrap w-full max-w-x-article-width-small justify-between lg:max-w-x-article-width\">\n        <div class=\"flex x-social-activity-left flex-wrap\">\n          \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    \n    \n    \n\n    <code id=\"i18n_reaction_selection_state\" style=\"display: none\"><!--\"%reactionType% %selectionState%\"--></code>\n\n    <div class=\"reactions-menu flex flex-row relative items-center justify-center select-none [-webkit-user-select:none] pointer-events-auto\" data-feed-control=\"article-ssr-frontend-x-article_x-social-details_like-toggle_reactions-cta\">\n        <a class=\"reactions-menu__link text-color-text social-action-bar__button relative h-6 cursor-pointer w-full\" href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_like-toggle_like-cta\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_like-toggle_reactions-cta\" data-parent-urn=\"urn:li:ugcPost:7193222795072303104\" data-tracking-will-navigate=\"\" data-feed-control=\"article-ssr-frontend-x-article_x-social-details_like-toggle_reactions-cta\">\n          \n\n<!---->    <div class=\"like-button-default flex flex-row items-center \">\n      <icon class=\"social-action-bar__icon lazy-loaded\" data-svg-class-name=\"social-action-bar__icon--svg\" aria-hidden=\"true\" aria-busy=\"false\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" id=\"thumbs-up-outline-medium\" aria-hidden=\"true\" role=\"none\" data-supported-dps=\"24x24\" fill=\"currentColor\" focusable=\"false\" class=\"social-action-bar__icon--svg lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M19.46 11l-3.91-3.91a7 7 0 01-1.69-2.74l-.49-1.47A2.76 2.76 0 0010.76 1 2.75 2.75 0 008 3.74v1.12a9.19 9.19 0 00.46 2.85L8.89 9H4.12A2.12 2.12 0 002 11.12a2.16 2.16 0 00.92 1.76A2.11 2.11 0 002 14.62a2.14 2.14 0 001.28 2 2 2 0 00-.28 1 2.12 2.12 0 002 2.12v.14A2.12 2.12 0 007.12 22h7.49a8.08 8.08 0 003.58-.84l.31-.16H21V11zM19 19h-1l-.73.37a6.14 6.14 0 01-2.69.63H7.72a1 1 0 01-1-.72l-.25-.87-.85-.41A1 1 0 015 17l.17-1-.76-.74A1 1 0 014.27 14l.66-1.09-.73-1.1a.49.49 0 01.08-.7.48.48 0 01.34-.11h7.05l-1.31-3.92A7 7 0 0110 4.86V3.75a.77.77 0 01.75-.75.75.75 0 01.71.51L12 5a9 9 0 002.13 3.5l4.5 4.5H19z\"></path>\n</svg></icon>\n      <span class=\"social-action-bar__button-text\">Like</span>\n    </div>\n\n        </a>\n    </div>\n      <template id=\"selected-reaction\">\n        <div>\n            \n  <div class=\"selected-reaction--LIKE flex flex-row items-center\">\n    <img class=\"inline-block align-middle relative w-3 h-3 mr-1 babybear:mr-0 mb-0.5\" alt=\"Like\" aria-hidden=\"true\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/3tsxb73co15a9ze36f0y0vmng\">\n    <span class=\"social-action-bar__button-text\">Like</span>\n  </div>\n\n            \n  <div class=\"selected-reaction--PRAISE flex flex-row items-center\">\n    <img class=\"inline-block align-middle relative w-3 h-3 mr-1 babybear:mr-0 mb-0.5\" alt=\"Celebrate\" aria-hidden=\"true\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/5tec0l9hykk5gezv56e7coyow\">\n    <span class=\"social-action-bar__button-text\">Celebrate</span>\n  </div>\n\n            \n  <div class=\"selected-reaction--APPRECIATION flex flex-row items-center\">\n    <img class=\"inline-block align-middle relative w-3 h-3 mr-1 babybear:mr-0 mb-0.5\" alt=\"Support\" aria-hidden=\"true\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/7savup3gmhirulv2h5v8snx2x\">\n    <span class=\"social-action-bar__button-text\">Support</span>\n  </div>\n\n            \n  <div class=\"selected-reaction--EMPATHY flex flex-row items-center\">\n    <img class=\"inline-block align-middle relative w-3 h-3 mr-1 babybear:mr-0 mb-0.5\" alt=\"Love\" aria-hidden=\"true\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/f4lj1wks3jt476fe5defirzg9\">\n    <span class=\"social-action-bar__button-text\">Love</span>\n  </div>\n\n            \n  <div class=\"selected-reaction--INTEREST flex flex-row items-center\">\n    <img class=\"inline-block align-middle relative w-3 h-3 mr-1 babybear:mr-0 mb-0.5\" alt=\"Insightful\" aria-hidden=\"true\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/br7ludovnq3n7137fvqsqwljx\">\n    <span class=\"social-action-bar__button-text\">Insightful</span>\n  </div>\n\n            \n  <div class=\"selected-reaction--ENTERTAINMENT flex flex-row items-center\">\n    <img class=\"inline-block align-middle relative w-3 h-3 mr-1 babybear:mr-0 mb-0.5\" alt=\"Funny\" aria-hidden=\"true\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/6ga7ypv66xprsgreohwklur3y\">\n    <span class=\"social-action-bar__button-text\">Funny</span>\n  </div>\n\n        </div>\n      </template>\n  \n  \n            <div class=\"relative flex select-none flex-row items-center justify-center\">\n              <a class=\"social-action-bar__button !rounded-none \" href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_comment-cta\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comment-cta\" data-tracking-will-navigate=\"\">\n                <icon class=\"social-action-bar__icon lazy-loaded\" data-svg-class-name=\"social-action-bar__icon--svg\" aria-hidden=\"true\" aria-busy=\"false\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 16 16\" id=\"comment-small\" aria-hidden=\"true\" role=\"none\" data-supported-dps=\"16x16\" fill=\"currentColor\" focusable=\"false\" class=\"social-action-bar__icon--svg lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M5 8h5v1H5zm11-.5v.08a6 6 0 01-2.75 5L8 16v-3H5.5A5.51 5.51 0 010 7.5 5.62 5.62 0 015.74 2h4.76A5.5 5.5 0 0116 7.5zm-2 0A3.5 3.5 0 0010.5 4H5.74A3.62 3.62 0 002 7.5 3.53 3.53 0 005.5 11H10v1.33l2.17-1.39A4 4 0 0014 7.58zM5 7h6V6H5z\"></path>\n</svg></icon>\n                <span class=\"social-action-bar__button-text\">\n                  Comment\n                </span>\n              </a>\n            </div>\n          \n      \n\n    \n    \n    \n    \n\n    <code id=\"i18n_copy_cta_message_success\" style=\"display: none\"><!--\"Link copied to clipboard.\"--></code>\n    <code id=\"i18n_copy_cta_message_error\" style=\"display: none\"><!--\"Something went wrong while copying to clipboard.\"--></code>\n\n    \n\n    \n\n    <div class=\"collapsible-dropdown flex items-center relative hyphens-auto social-share inline-block relative select-none x-social-share \" data-share-message=\"Share\" data-share-url=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf?utm_source=share&amp;utm_medium=guest_desktop\" data-feed-control=\"article-ssr-frontend-x-article_x-social-details_social-share-intent_social-share_main_button\" data-feed-action-category=\"EXPAND\" data-feed-action-type=\"openShareMenu\">\n<!---->\n        <ul class=\"collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-[100%] top-auto\" role=\"menu\" tabindex=\"-1\">\n          \n        \n  <li role=\"presentation\">\n    <button data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_social-share-intent_social-share_copy\" class=\"social-share__item social-share-item\" data-share-service=\"copy\" role=\"menuitem\">\n      <icon class=\"social-share__item-icon lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n<path d=\"M11.32 13.85C10.27 13.85 9.21 13.45 8.41 12.66L7.87 12.12C7.48 11.73 7.48 11.1 7.87 10.71C8.26 10.32 8.89 10.32 9.28 10.71L9.81 11.25C10.64 12.07 11.98 12.07 12.81 11.25L17.13 6.97003C17.68 6.42003 17.99 5.69003 17.99 4.91003C17.99 4.13003 17.69 3.40003 17.13 2.85003C16.02 1.75003 14.21 1.75003 13.1 2.85003L11.48 4.46003C11.09 4.85003 10.46 4.85003 10.07 4.46003C9.68 4.07003 9.68 3.44003 10.07 3.05003L11.69 1.44003C13.57 -0.439971 16.65 -0.439971 18.54 1.44003C19.47 2.37003 19.99 3.60003 19.99 4.92003C19.99 6.24003 19.48 7.48003 18.54 8.40003L14.22 12.68C13.42 13.48 12.37 13.87 11.31 13.87L11.32 13.85ZM8.3 18.6L9.92 16.99C10.31 16.6 10.31 15.97 9.92 15.58C9.53 15.19 8.9 15.19 8.51 15.58L6.89 17.19C5.78 18.3 3.97 18.3 2.86 17.19C2.31 16.64 2 15.91 2 15.13C2 14.35 2.3 13.62 2.86 13.07L7.18 8.79003C8.01 7.97003 9.35 7.97003 10.18 8.79003L10.71 9.32003C11.1 9.71003 11.73 9.71003 12.12 9.32003C12.51 8.93003 12.51 8.30003 12.12 7.91003L11.58 7.37003C9.98 5.78003 7.37 5.78003 5.76 7.37003L1.45 11.63C0.52 12.56 0 13.79 0 15.11C0 16.43 0.51 17.67 1.45 18.59C2.39 19.53 3.63 19.99 4.87 19.99C6.11 19.99 7.35 19.52 8.3 18.58V18.6Z\" fill=\"currentColor\" fill-opacity=\"0.75\"></path>\n</svg></icon>\n      <span class=\"social-share__item-text pl-1\">Copy</span>\n    </button>\n  </li>\n\n\n        \n  <li role=\"presentation\">\n    <button data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_social-share-intent_social-share_linkedin\" class=\"social-share__item social-share-item\" data-share-service=\"linkedin\" role=\"menuitem\">\n      <icon class=\"social-share__item-icon lazy-loaded\" data-svg-class-name=\"text-color-text\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" focusable=\"false\" class=\"text-color-text lazy-loaded\" aria-busy=\"false\">\n<path d=\"M18.5 0H1.5C0.7 0 0 0.7 0 1.5V18.5C0 19.3 0.7 20 1.5 20H18.5C19.3 20 20 19.3 20 18.5V1.5C20 0.7 19.3 0 18.5 0ZM6 17H3V8H6V17ZM4.5 6.2C3.5 6.2 2.7 5.4 2.7 4.4C2.7 3.4 3.5 2.6 4.5 2.6C5.5 2.6 6.3 3.4 6.3 4.4C6.3 5.5 5.5 6.2 4.5 6.2ZM17 17H14V12.3C14 10.9 13.4 10.4 12.6 10.4C11.8 10.4 11 11.1 11 12.4V17H8V8H10.9V9.3C11.3 8.7 12.2 7.9 13.6 7.9C15.1 7.9 17 8.8 17 11.6V17Z\" fill=\"currentColor\" fill-opacity=\"0.75\"></path>\n</svg></icon>\n      <span class=\"social-share__item-text pl-1\">LinkedIn</span>\n    </button>\n  </li>\n\n          \n  <li role=\"presentation\">\n    <button data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_social-share-intent_social-share_facebook\" class=\"social-share__item social-share-item\" data-share-service=\"facebook\" role=\"menuitem\">\n      <icon class=\"social-share__item-icon lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n<path d=\"M20 10.0553C20 15.0729 16.3418 19.2358 11.5642 19.9899V13.0015H13.8931L14.3328 10.1156H11.5642V8.24535C11.5642 7.45098 11.954 6.68678 13.1934 6.68678H14.4528V4.22323C14.4528 4.22323 13.3133 4.03218 12.2139 4.03218C9.93503 4.03218 8.43578 5.41981 8.43578 7.92358V10.1257H5.89705V13.0116H8.43578V20C3.65817 19.2459 0 15.083 0 10.0654C0.009995 4.50478 4.48776 0 10.005 0C15.5222 0 20 4.50478 20 10.0553Z\" fill=\"currentColor\" fill-opacity=\"0.75\"></path>\n</svg></icon>\n      <span class=\"social-share__item-text pl-1\">Facebook</span>\n    </button>\n  </li>\n\n          \n  <li role=\"presentation\">\n    <button data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_social-share-intent_social-share_twitter\" class=\"social-share__item social-share-item\" data-share-service=\"twitter\" role=\"menuitem\">\n      <icon class=\"social-share__item-icon lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"21\" height=\"17\" viewBox=\"0 0 21 17\" fill=\"none\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n<path d=\"M18.78 4.22999C18.79 4.40999 18.79 4.59999 18.79 4.78999C18.79 10.46 14.47 17 6.58 17C4.25 17 1.96 16.33 0 15.07C0.34 15.11 0.68 15.13 1.02 15.13C2.95 15.13 4.83 14.48 6.35 13.29C4.51 13.26 2.9 12.06 2.34 10.31C2.98 10.43 3.65 10.41 4.28 10.24C2.28 9.83999 0.84 8.07999 0.84 6.02999V5.97999C1.44 6.30999 2.1 6.49999 2.79 6.51999C0.9 5.25999 0.32 2.74999 1.46 0.789994C3.64 3.46999 6.85 5.09999 10.31 5.26999C9.96 3.77999 10.44 2.21999 11.55 1.16999C13.28 -0.460006 16 -0.370006 17.62 1.35999C18.58 1.16999 19.5 0.819994 20.35 0.319994C20.03 1.30999 19.36 2.15999 18.46 2.68999C19.31 2.58999 20.14 2.35999 20.92 2.00999C20.34 2.86999 19.62 3.62999 18.78 4.22999Z\" fill=\"currentColor\" fill-opacity=\"0.75\"></path>\n</svg></icon>\n      <span class=\"social-share__item-text pl-1\">Twitter</span>\n    </button>\n  </li>\n\n<!---->      \n        </ul>\n\n          \n        <button aria-expanded=\"false\" class=\"social-share__button social-share__button-square\n            social-share-button social-action-bar__button\n            \n            \n            social-share-button--square\n            \" aria-label=\"\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_social-share-intent_social-share_main_button\">\n          <icon class=\"social-share__button-icon lazy-loaded\" data-svg-class-name=\"social-share-button-icon social-action-bar__icon--svg \" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" focusable=\"false\" class=\"social-share-button-icon social-action-bar__icon--svg lazy-loaded\" aria-busy=\"false\"><path d=\"M18.4 5H16l3.9 6H8c-3.4 0-6 2.8-6 5.9 0 .6.1 1.2.3 1.8L3 21h2.1l-.9-2.8c-.1-.4-.2-.8-.2-1.2 0-2.1 1.6-4 3.9-4h12L16 19h2.4l4.6-7-4.6-7z\" fill=\"currentColor\"></path></svg></icon>\n            <span class=\"social-share__button-text-square\n                social-action-bar__button-text\n                \">\n              Share\n            </span>\n        </button>\n      \n    </div>\n  \n  \n  \n        </div>\n          \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs\" data-sap-comment-urn=\"urn:li:ugcPost:7193222795072303104\">\n            \n      <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_likes-count_social-actions-reactions\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_likes-count_social-actions-reactions\" data-tracking-will-navigate=\"\" class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"15 Reactions\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"15\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" data-entity-urn=\"urn:li:ugcPost:7193222795072303104\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" class=\"lazy-loaded\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    15\n              </span>\n            \n      </a>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!---->            \n      <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_likes-count_social-actions-comments\" target=\"_blank\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_likes-count_social-actions-comments\" data-tracking-will-navigate=\"\" class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis\n                before:middot\n                my-1\" data-test-id=\"social-actions__comments\" data-id=\"social-actions__comments\" data-num-comments=\"2\" data-singular=\"%numComments% Comment\" data-plural=\"%numComments% Comments\" data-entity-urn=\"urn:li:ugcPost:7193222795072303104\">\n        \n                2 Comments\n            \n      </a>\n  \n<!---->      </div>\n  \n      </div>\n    </div>\n      <div class=\"comments__container\">\n        \n    \n    \n    \n\n    <div class=\"comments\">\n<!---->\n      \n            \n\n    \n    \n    \n    \n    \n    \n\n<!---->\n    \n\n    \n    \n    \n    \n\n      <section class=\"comment flex grow-1 items-stretch leading-[16px] comment-with-action my-1.5\">\n        <div>\n          <a href=\"https://br.linkedin.com/in/walter-koch-msc-2732109?trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment_actor-image\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_actor-image\" data-tracking-will-navigate=\"\">\n            \n      <img class=\"inline-block relative\n          rounded-[50%]\n          w-4 h-4\n          bg-color-entity-ghost-background\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2\" data-ghost-classes=\"bg-color-entity-ghost-background\" data-ghost-url=\"https://static.licdn.com/aero-v1/sc/h/9c8pery4andzj6ohjkjp54ma2\" alt=\"Walter Koch, MSc, graphic\">\n      \n          </a>\n        </div>\n        <div class=\"w-full min-w-0\">\n          <div class=\"comment__body ml-0.5 mb-1 pt-1 pr-1.5 pb-1 pl-1.5 rounded-[8px] rounded-tl-none relative\">\n            <div class=\"comment__header flex flex-col\">\n                  <a class=\"text-sm link-styled no-underline leading-open comment__author truncate pr-6\" href=\"https://br.linkedin.com/in/walter-koch-msc-2732109?trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment_actor-name\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_actor-name\" data-tracking-will-navigate=\"\">\n                    Walter Koch, MSc\n                  </a>\n                  <p class=\"!text-xs text-color-text-low-emphasis leading-[1.33333] mb-0.5 truncate comment__author-headline\">AIIM Fellow, international consultant, information architect, CEO at I-Ware</p>\n              \n        <span class=\"text-xs text-color-text-low-emphasis comment__duration-since absolute right-6 top-1 inline-block\">\n          \n\n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n      1y\n  \n        </span>\n\n          \n\n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n      <div class=\"ellipsis-menu absolute right-0 top-0 !mr-0.5\" data-test-id=\"comment__ellipsis-menu\" data-feed-action=\"show-comment-dropdown\">\n        \n\n    \n\n    <div class=\"collapsible-dropdown flex items-center relative hyphens-auto\">\n          \n            <button class=\"ellipsis-menu__trigger\n                collapsible-dropdown__button btn-md btn-tertiary cursor-pointer\n                !py-[6px] !px-1 flex items-center rounded-[50%]\n                ellipsis-menu__trigger--small !w-4 !h-4 !min-h-4 justify-center\n                 babybear:rotate-90 mamabear:!h-4 mamabear:!min-h-[32px] papabear:!h-4 papabear:!min-h-[32px]\" aria-expanded=\"false\" aria-label=\"Open menu\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_ellipsis-menu-trigger\" tabindex=\"0\">\n              <icon class=\"ellipsis-menu__trigger-icon m-0 p-0 centered-icon\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/c26vw8uiog92lvrqov98hvbxr\"></icon>\n            </button>\n          \n\n        <ul class=\"collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-auto top-[100%]\" role=\"menu\" tabindex=\"-1\">\n          \n              \n\n                <li class=\"ellipsis-menu__item border-t-1 border-solid border-color-border-low-emphasis first-of-type:border-none flex\" role=\"presentation\">\n                  \n\n    \n    \n\n    \n\n    \n\n    <a href=\"/uas/login?session_redirect=https%3A%2F%2Fwww.linkedin.com%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment_ellipsis-menu-semaphore-sign-in-redirect&amp;guestReportContentType=COMMENT&amp;_f=guest-reporting\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_ellipsis-menu-semaphore-sign-in-redirect\" data-tracking-will-navigate=\"\" data-item-type=\"semaphore\" data-semaphore-content-type=\"COMMENT\" data-semaphore-content-urn=\"urn:li:comment:(urn:li:ugcPost:7193222795072303104,7193230828871569409)\" data-semaphore-tracking-prefix=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_ellipsis-menu-semaphore\" data-is-logged-in=\"false\" data-modal=\"semaphore__toggle\" class=\"semaphore__toggle visited:text-color-text-secondary ellipsis-menu__semaphore ellipsis-menu__item-button flex items-center w-full p-1 cursor-pointer font-sans text-sm font-bold link-styled focus:link-styled link:no-underline active:bg-color-background-container-tint focus:bg-color-background-container-tint hover:bg-color-background-container-tint outline-offset-[-2px]\" role=\"menuitem\">\n<!---->        \n                      <icon class=\"ellipsis-menu__item-icon text-color-text h-[24px] w-[24px] mr-1 lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" data-supported-dps=\"24x24\" fill=\"currentColor\" focusable=\"false\" class=\"lazy-loaded\" aria-busy=\"false\">\n  <path d=\"M13.82 5L14 4a1 1 0 00-1-1H5V2H3v20h2v-7h4.18L9 16a1 1 0 001 1h8.87L21 5h-7.18zM5 13V5h6.94l-1.41 8H5zm12.35 2h-6.3l1.42-8h6.29z\"></path>\n</svg></icon>\n                      Report this comment\n                    \n    </a>\n\n<!---->  \n                </li>\n<!---->          \n        </ul>\n\n<!---->    </div>\n  \n\n      </div>\n  \n      \n            </div>\n            \n  <div class=\"attributed-text-segment-list__container relative mt-1 mb-1.5 babybear:mt-0 babybear:mb-0.5\">\n    <p class=\"attributed-text-segment-list__content text-color-text !text-sm whitespace-pre-wrap break-words\n         comment__text babybear:mt-0.5\" dir=\"ltr\"><a class=\"link\" href=\"https://br.linkedin.com/in/alexandremarinho?trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment-text\" target=\"_self\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment-text\" data-tracking-will-navigate=\"\">Alexandre Marinho</a> estava na equipe que desenvolveu alguns dos primeiros workflow de crédito com document imaging na década 90. Alguns destes projetos receberam premiações. Parabéns pela evolução tecnológica e que continues sendo premiado em todos os sentidos!</p>\n<!---->  </div>\n\n            \n<!---->      \n<!---->          </div>\n          \n        <div class=\"comment__actions flex flex-row flex-wrap items-center ml-2\" aria-label=\"Comment Controls\">\n\n              <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment_like\" class=\"comment__action inline-block font-sans text-xs text-color-text-low-emphasis cursor-pointer\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_like\" data-tracking-will-navigate=\"\">\n                \n  <div class=\"flex flex-row items-center \">\n    <icon class=\"action-btn__icon w-2 mr-0.5\" data-svg-class-name=\"action-btn__icon--svg\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/70k0g8kmgdfjjymflqqzipzxj\">\n    </icon>\n    <span class=\"action-btn__button-text inline-block align-baseline\">\n      Like\n    </span>\n  </div>\n\n              </a>\n\n                <span class=\"before:middot font-sans text-s text-color-text-low-emphasis\"></span>\n                <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment_reply\" class=\"comment__action inline-block font-sans text-xs text-color-text-low-emphasis cursor-pointer comment__reply\" data-feed-action=\"replyToComment\" data-feed-control=\"reply\" data-feed-action-category=\"EXPAND\" data-feed-action-type=\"expandReplyBox\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_reply\" data-tracking-will-navigate=\"\">\n                  \n  <div class=\"flex flex-row items-center \">\n    <icon class=\"action-btn__icon w-2 mr-0.5\" data-svg-class-name=\"action-btn__icon--svg\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/4ol9mo4lxvobj5ww3va90wz1o\">\n    </icon>\n    <span class=\"action-btn__button-text inline-block align-baseline\">\n      Reply\n    </span>\n  </div>\n\n                </a>\n                <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_comments-action_comment_reactions\" class=\"comment__reactions-count font-sans text-xs text-color-text-low-emphasis border-l-1 border-solid border-color-border-low-emphasis pl-1 mx-1\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments-action_comment_reactions\" data-tracking-will-navigate=\"\">\n                  1&nbsp;Reaction\n                </a>\n              <span class=\"comment__reactions-count font-sans text-xs text-color-text-low-emphasis border-l-1 border-solid border-color-border-low-emphasis pl-1 mx-1 hidden\">\n                2&nbsp;Reactions\n              </span>\n        </div>\n      \n        </div>\n      </section>\n  \n  \n        \n        <div class=\"comments__see-more-wrapper\">\n          <a class=\"comments__see-more\" href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_comments_comment-see-more\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_comments_comment-see-more\" data-tracking-will-navigate=\"\">\n            See more comments\n          </a>\n        </div>\n    </div>\n  \n      </div>\n      \n\n    \n    \n    \n    \n    \n    \n\n    <p class=\"feed-cta-banner__text border-t-1 border-color-surface-border border-solid p-3 text-center text-md text-color-text-low-emphasis\">\n        To view or add a comment, <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=article-ssr-frontend-pulse_x-social-details_feed-cta-banner-cta\" target=\"\" aria-label=\"To view or add a comment, sign in\" data-tracking-control-name=\"article-ssr-frontend-pulse_x-social-details_feed-cta-banner-cta\" data-tracking-will-navigate=\"true\" class=\"feed-cta-banner__link link\">sign in</a>\n    </p>\n  \n  \n               \n    \n\n    \n    <section class=\"core-section-container\n        my-3 stacked-articles\">\n<!---->\n          \n            <h2 class=\"core-section-container__title section-title\">\n              More articles by Alexandre Marinho\n            </h2>\n         \n        \n<!---->\n      <div class=\"core-section-container__content break-words\">\n        \n          \n    \n    \n    \n    \n\n    \n    \n    \n\n      <div class=\"show-more-less\">\n<!---->\n        <ul data-max-num-to-show=\"4\" class=\"show-more-less__list show-more-less__list--hide-after-4\" data-impression-id=\"article-ssr-frontend-pulse_show-more-less\">\n          \n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://www.linkedin.com/pulse/role-credit-analyst-age-artificial-intelligence-alexandre-marinho-ndfwf\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        The Role of the Credit Analyst in the Age of Artificial Intelligence\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D5612AQFyXnEUAQFvbg/article-cover_image-shrink_600_2000/B56ZYlhbTdGsAQ-/0/1744386242626?e=**********&amp;v=beta&amp;t=HGKmiE2VGTth9-dVB9KwannFmxNTCGyUaR00a9VM654\" alt=\"\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">Apr 11, 2025</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        The Role of the Credit Analyst in the Age of Artificial Intelligence\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          The role of the Credit Analyst in the era of Artificial Intelligence (AI) is undergoing a profound transformation…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"7 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"7\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" height=\"16px\" width=\"16px\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" height=\"16px\" width=\"16px\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    7\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!----><!----><!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://www.linkedin.com/pulse/o-analista-de-cr%C3%A9dito-40-como-intelig%C3%AAncia-artificial-marinho-5hhaf\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        O Analista de Crédito 4.0: Como a Inteligência Artificial Está Redefinindo o Futuro da Profissão\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQGN4ezNmnBRHg/article-cover_image-shrink_600_2000/B4DZYfh.AEHAAQ-/0/1744285721200?e=**********&amp;v=beta&amp;t=9H5T6hKq7E7PAlxKTOJhodrbNAozZ1qVZtTwjBTlhm0\" alt=\"\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">Apr 10, 2025</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        O Analista de Crédito 4.0: Como a Inteligência Artificial Está Redefinindo o Futuro da Profissão\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          Da análise manual à tomada de decisão inteligente com agentes e copilotos de IA O papel do Analista de Crédito na era…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"16 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"16\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" height=\"16px\" width=\"16px\">\n                    <img alt=\"\" data-reaction-type=\"EMPATHY\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\" height=\"16px\" width=\"16px\">\n                    <img alt=\"\" data-reaction-type=\"INTEREST\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\" height=\"16px\" width=\"16px\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    16\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!----><!----><!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://www.linkedin.com/pulse/evolution-artificial-intelligence-models-credit-analysis-marinho-tup1f\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        The Evolution of Artificial Intelligence Models in Credit Analysis: From Weighted Scoring to Generative AI\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQEyvkT14c02Xw/article-cover_image-shrink_600_2000/article-cover_image-shrink_600_2000/0/1732544864800?e=**********&amp;v=beta&amp;t=xN8elmxXkbUlhXyt-HkkDv7x85gXUzNjeSCWiJY1L3s\" alt=\"\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">Nov 25, 2024</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        The Evolution of Artificial Intelligence Models in Credit Analysis: From Weighted Scoring to Generative AI\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          There has been a lot of talk about Artificial Intelligence in recent years, mainly due to innovations associated with…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"15 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"15\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" height=\"16px\" width=\"16px\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" height=\"16px\" width=\"16px\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    15\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!---->            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis\n                before:middot\n                my-1\" data-separate-ctas=\"false\" data-test-id=\"social-actions__comments\" data-id=\"social-actions__comments\" data-num-comments=\"3\" data-singular=\"%numComments% Comment\" data-plural=\"%numComments% Comments\">\n        \n                3 Comments\n            \n      </div>\n  \n<!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://pt.linkedin.com/pulse/evolu%C3%A7%C3%A3o-dos-modelos-de-intelig%C3%AAncia-artificial-em-an%C3%A1lise-marinho-i7m9f\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        A Evolução dos Modelos de Inteligência Artificial em Análise de Crédito: Do Score Ponderado à IA Generativa\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full\" data-delayed-url=\"https://media.licdn.com/dms/image/v2/D4D12AQGQ5n_9g2-FKQ/article-cover_image-shrink_600_2000/article-cover_image-shrink_600_2000/0/1732221178234?e=**********&amp;v=beta&amp;t=e2B74_YpdYWRpmODs8En9b1EEaNRlagFRVxDQZQ0Mz4\" alt=\"\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">Nov 21, 2024</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        A Evolução dos Modelos de Inteligência Artificial em Análise de Crédito: Do Score Ponderado à IA Generativa\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          Muito tem se falado sobre Inteligência Artificial nos últimos anos, principalmente devido às inovações associadas a…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"20 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"20\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" height=\"16px\" width=\"16px\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" height=\"16px\" width=\"16px\">\n                    <img alt=\"\" data-reaction-type=\"EMPATHY\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\" height=\"16px\" width=\"16px\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    20\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!---->            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis\n                before:middot\n                my-1\" data-separate-ctas=\"false\" data-test-id=\"social-actions__comments\" data-id=\"social-actions__comments\" data-num-comments=\"3\" data-singular=\"%numComments% Comment\" data-plural=\"%numComments% Comments\">\n        \n                3 Comments\n            \n      </div>\n  \n<!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://www.linkedin.com/pulse/credit-vision-reading-reclassifying-balance-sheets-using-marinho-2phqf\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        CreditVision: Reading and Reclassifying Balance Sheets using Transformers and Generative AI\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full lazy-loaded\" alt=\"\" aria-busy=\"false\" src=\"https://media.licdn.com/dms/image/v2/D4D12AQFnI-dAaBGHGw/article-cover_image-shrink_600_2000/article-cover_image-shrink_600_2000/0/1719431007310?e=**********&amp;v=beta&amp;t=nG190nJx-YiBoepe4qYZpELktQx9_q5j8XMFRsa0nBA\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">Jun 26, 2024</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        CreditVision: Reading and Reclassifying Balance Sheets using Transformers and Generative AI\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          Financial statement analysis is an activity that usually involves typing and reclassifying balance sheets. Since there…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"10 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"10\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"INTEREST\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\" class=\"lazy-loaded\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    10\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!----><!----><!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://pt.linkedin.com/pulse/credit-vision-leitura-e-reclassifica%C3%A7%C3%A3o-de-utilizando-marinho-fkftf\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        Credit Vision:  Leitura e Reclassificação de Balanços utilizando Transformers e IA Generativa\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full lazy-loaded\" alt=\"\" aria-busy=\"false\" src=\"https://media.licdn.com/dms/image/v2/D4D12AQFlENTbbQkYBg/article-cover_image-shrink_600_2000/article-cover_image-shrink_600_2000/0/1719355984646?e=**********&amp;v=beta&amp;t=Q6bN4OaTxP2I0ClO9kDdG8DHuUn44t3bXl7nyrPsXOg\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">Jun 25, 2024</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        Credit Vision:  Leitura e Reclassificação de Balanços utilizando Transformers e IA Generativa\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          A análise de demonstrativos financeiros é uma atividade que costumeiramente envolve a digitação e reclassificação dos…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"17 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"17\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"EMPATHY\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\" class=\"lazy-loaded\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    17\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!---->            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis\n                before:middot\n                my-1\" data-separate-ctas=\"false\" data-test-id=\"social-actions__comments\" data-id=\"social-actions__comments\" data-num-comments=\"2\" data-singular=\"%numComments% Comment\" data-plural=\"%numComments% Comments\">\n        \n                2 Comments\n            \n      </div>\n  \n<!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://www.linkedin.com/pulse/revolution-credit-analysis-techniques-legal-entities-era-marinho-yiekf\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        The (R)evolution of Credit Analysis Techniques for Legal Entities in the era of Generative AI: Far Beyond Historical Balance Sheet\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full lazy-loaded\" alt=\"\" aria-busy=\"false\" src=\"https://media.licdn.com/dms/image/v2/D4D12AQFvR9mie-Dmzw/article-cover_image-shrink_600_2000/article-cover_image-shrink_600_2000/0/1716371914771?e=**********&amp;v=beta&amp;t=-eKd5qtXrTarqMaV8WbJ_FWUEyRt9jvvIieAwq41Ods\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">May 22, 2024</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        The (R)evolution of Credit Analysis Techniques for Legal Entities in the era of Generative AI: Far Beyond Historical Balance Sheet\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          The field of credit analysis for legal entities is a perfect example of how current processes in companies will be…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"13 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"13\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" class=\"lazy-loaded\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    13\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!----><!----><!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n          <li>\n            \n    \n    \n    \n    \n\n    \n    \n    \n      <div class=\"base-card relative w-full hover:no-underline focus:no-underline\n        base-card--link\n         base-main-card flex flex-wrap py-2 pr-2 babybear:pr-0\n        \n        base-main-card--link\n        babybear:py-1 main-article-card\">\n        \n\n        <a class=\"base-card__full-link absolute top-0 right-0 bottom-0 left-0 p-0 z-[2] outline-offset-[4px]\" href=\"https://www.linkedin.com/pulse/revolu%C3%A7%C3%A3o-das-t%C3%A9cnicas-de-an%C3%A1lise-cr%C3%A9dito-pj-na-era-da-marinho-wjmuf\" data-tracking-control-name=\"article-ssr-frontend-pulse\" data-tracking-will-navigate=\"\">\n          \n          <span class=\"sr-only\">\n              \n          \n        A (R)evolução das Técnicas de Análise de Crédito PJ na era da Inteligência Artificial Generativa : Muito Além da Análise Histórica de Balanços\n      \n      \n          </span>\n        </a>\n\n      \n          <div class=\"base-main-card__media relative w-[228px] block overflow-hidden flex-shrink-0 rounded-md\n              \n              h-[129px] babybear:w-[105px] babybear:h-[59px]\">\n            \n        <img class=\"h-full lazy-loaded\" alt=\"\" aria-busy=\"false\" src=\"https://media.licdn.com/dms/image/v2/D4D12AQElwtWcl56qOw/article-cover_image-shrink_600_2000/article-cover_image-shrink_600_2000/0/1716231482920?e=**********&amp;v=beta&amp;t=229qbusX3T29Dx-3q3HTO7hN9L_uu-vxvpO3wA3WbJc\">\n      \n<!---->          </div>\n\n        <div class=\"base-main-card__info self-center ml-1 flex-1 relative break-words papabear:min-w-0 mamabear:min-w-0 babybear:w-full\n            article-card-social-actions\">\n              <div class=\"body-text text-color-text-low-emphasis base-main-card__metadata mb-0 babybear:mb-0 babybear:-mt-0.5\">\n                \n          <span class=\"body-text text-color-text-low-emphasis base-main-card__metadata-item babybear:text-xs\">May 20, 2024</span>\n<!---->      \n              </div>\n          <h3 class=\"base-main-card__title font-sans text-[18px] font-bold text-color-text overflow-hidden\n              \n              babybear:text-sm\n              base-main-card__title--link\">\n            \n        A (R)evolução das Técnicas de Análise de Crédito PJ na era da Inteligência Artificial Generativa : Muito Além da Análise Histórica de Balanços\n      \n          </h3>\n          \n\n<!---->\n                <p class=\"base-main-card__description body-text text-color-text -mt-0.5\">\n                  \n          A área de análise de crédito a pessoas jurídicas é um exemplo perfeito de como os processos atuais nas empresas serão…\n      \n                </p>\n\n<!---->            \n          \n        \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    <code id=\"i18n_reaction_singular\" style=\"display: none\"><!--\"%numReactions% Reaction\"--></code>\n    <code id=\"i18n_reactions_plural\" style=\"display: none\"><!--\"%numReactions% Reactions\"--></code>\n\n      <div class=\"flex items-center font-sans text-sm babybear:text-xs -mt-1\">\n            \n      <div class=\"flex items-center font-normal text-color-text-low-emphasis !no-underline visited:text-color-text-low-emphasis  my-1\" aria-label=\"17 Reactions\" data-separate-ctas=\"false\" data-test-id=\"social-actions__reactions\" data-id=\"social-actions__reactions\" data-num-reactions=\"17\" data-singular=\"%numReactions%\" data-plural=\"%numReactions%\" tabindex=\"0\">\n        \n                    <img alt=\"\" data-reaction-type=\"LIKE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"PRAISE\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\" class=\"lazy-loaded\">\n                    <img alt=\"\" data-reaction-type=\"INTEREST\" height=\"16px\" width=\"16px\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\" class=\"lazy-loaded\">\n\n              <span aria-hidden=\"true\" class=\"font-normal ml-0.5\" data-test-id=\"social-actions__reaction-count\">\n                    17\n              </span>\n            \n      </div>\n  \n                  <code id=\"social-actions__reaction-image-APPRECIATION\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cyfai5zw4nrqhyyhl0p7so58v\"--></code>\n<!----><!---->                  <code id=\"social-actions__reaction-image-EMPATHY\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/asiqslyf4ooq7ggllg4fyo4o2\"--></code>\n                  <code id=\"social-actions__reaction-image-ENTERTAINMENT\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/22ifp2etz8kb9tgjqn65s9ics\"--></code>\n<!---->                  <code id=\"social-actions__reaction-image-INTEREST\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/a0e8rff6djeoq8iympcysuqfu\"--></code>\n                  <code id=\"social-actions__reaction-image-LIKE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/bn39hirwzjqj18ej1fkz55671\"--></code>\n                  <code id=\"social-actions__reaction-image-MAYBE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/cryzkreqrh52ja5bc6njlrupa\"--></code>\n                  <code id=\"social-actions__reaction-image-PRAISE\" style=\"display: none\"><!--\"https://static.licdn.com/aero-v1/sc/h/2tzoeodxy0zug4455msr0oq0v\"--></code>\n<!----><!----><!----><!---->      </div>\n  \n      \n      \n        </div>\n\n<!---->      \n    \n      </div>\n  \n  \n  \n  \n  \n          </li>\n      \n        </ul>\n\n            \n        <button class=\"show-more-less-button show-more-less__button show-more-less__more-button !py-1.5\" data-tracking-control-name=\"_show_more\" aria-label=\"Show more articles\">\n          Show more\n          <icon class=\"show-more-less-button-icon\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo\"></icon></button>\n      \n\n            \n        <div class=\"pt-1\">\n          <a data-tracking-control-name=\"_see_all_articles\" data-tracking-will-navigate=\"\" href=\"https://br.linkedin.com/in/alexandremarinho/recent-activity/articles/\" class=\"show-more-less-button\n              see-more-link show-more-less__less-button show-more-less__button--hide !text-color-link !border-color-link !shadow-none border-[1px] border-solid !py-1.5\">\n            See all articles\n          </a>\n        </div>\n      \n      </div>\n  \n  \n        \n      </div>\n    </section>\n  \n  \n          \n      \n        </div>\n      </section>\n      <section class=\"right-rail papabear:w-right-rail-width papabear:ml-column-gutter mamabear:max-w-[790px] mamabear:px-mobile-container-padding babybear:max-w-[790px] babybear:px-mobile-container-padding\">\n        \n        \n<!----><!----><!----><!---->              \n    \n    \n    \n\n    \n      <section class=\"aside-section-container mb-4 content-hub-topics\">\n\n            <h2 class=\"aside-section-container__title section-title\">\n              Explore topics\n            </h2>\n\n<!---->\n        <div class=\"aside-section-container__content break-words\">\n          \n        <ul class=\"career-advice-hub-topics__list\">\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/sales-s5/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                Sales\n              </a>\n            </li>\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/marketing-s2461/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                Marketing\n              </a>\n            </li>\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/it-services-s57547/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                IT Services\n              </a>\n            </li>\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/business-administration-s50111/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                Business Administration\n              </a>\n            </li>\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/hr-management-s50359/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                HR Management\n              </a>\n            </li>\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/engineering-s166/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                Engineering\n              </a>\n            </li>\n            <li class=\"career-advice-hub-topics__list-item mb-1\">\n              <a href=\"https://www.linkedin.com/pulse/topics/soft-skills-s2976/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary flex w-fit mr-1 mb-1 hover:no-underline\">\n                Soft Skills\n              </a>\n            </li>\n          <li class=\"career-advice-hub-topics__list-item mb-1\">\n            <a href=\"https://www.linkedin.com/pulse/topics/home/\" data-tracking-control-name=\"article-ssr-frontend-pulse_content-hub-topic\" data-tracking-will-navigate=\"\" class=\"btn-sm btn-secondary-emphasis flex w-fit mr-[6px] mb-1 hover:no-underline\" aria-label=\"See All, Explore topics\">\n              See All\n            </a>\n          </li>\n        </ul>\n      \n        </div>\n      </section>\n  \n  \n          \n      \n      </section>\n    </main>\n\n      <div class=\"pre-footer \" aria-hidden=\"true\">\n        \n        \n<!---->          \n      \n      </div>\n\n    \n\n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    \n    \n    \n    \n\n    <footer class=\"li-footer bg-transparent w-full \" aria-hidden=\"true\">\n      <ul class=\"li-footer__list flex flex-wrap flex-row items-start justify-start w-full h-auto min-h-[50px] my-[0px] mx-auto py-3 px-2 papabear:w-[1128px] papabear:p-0\">\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        \n          <span class=\"sr-only\">LinkedIn</span>\n          <icon class=\"li-footer__copy-logo text-color-logo-brand-alt inline-block self-center h-[14px] w-[56px] mr-1\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/5mebydpuuijm3uhv1q375inqh\"></icon>\n          <span class=\"li-footer__copy-text flex items-center\">© 2025</span>\n        \n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://about.linkedin.com?trk=d_flagship2_pulse_read_footer-about\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-about\" data-tracking-will-navigate=\"\">\n          \n          About\n        \n        </a>\n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/accessibility?trk=d_flagship2_pulse_read_footer-accessibility\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-accessibility\" data-tracking-will-navigate=\"\">\n          \n          Accessibility\n        \n        </a>\n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/legal/user-agreement?trk=d_flagship2_pulse_read_footer-user-agreement\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-user-agreement\" data-tracking-will-navigate=\"\">\n          \n          User Agreement\n        \n        </a>\n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/legal/privacy-policy?trk=d_flagship2_pulse_read_footer-privacy-policy\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-privacy-policy\" data-tracking-will-navigate=\"\">\n          \n          Privacy Policy\n        \n        </a>\n  </li>\n\n<!---->        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/legal/cookie-policy?trk=d_flagship2_pulse_read_footer-cookie-policy\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-cookie-policy\" data-tracking-will-navigate=\"\">\n          \n          Cookie Policy\n        \n        </a>\n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/legal/copyright-policy?trk=d_flagship2_pulse_read_footer-copyright-policy\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-copyright-policy\" data-tracking-will-navigate=\"\">\n          \n          Copyright Policy\n        \n        </a>\n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://brand.linkedin.com/policies?trk=d_flagship2_pulse_read_footer-brand-policy\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-brand-policy\" data-tracking-will-navigate=\"\">\n          \n          Brand Policy\n        \n        </a>\n  </li>\n\n          \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/psettings/guest-controls?trk=d_flagship2_pulse_read_footer-guest-controls\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-guest-controls\" data-tracking-will-navigate=\"\">\n          \n            Guest Controls\n          \n        </a>\n  </li>\n\n        \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        <a class=\"li-footer__item-link flex items-center font-sans text-xs font-bold text-color-text-solid-secondary hover:text-color-link-hover focus:text-color-link-focus\" href=\"https://www.linkedin.com/legal/professional-community-policies?trk=d_flagship2_pulse_read_footer-community-guide\" data-tracking-control-name=\"d_flagship2_pulse_read_footer-community-guide\" data-tracking-will-navigate=\"\">\n          \n          Community Guidelines\n        \n        </a>\n  </li>\n\n        \n<!---->\n          \n          \n  <li class=\"li-footer__item font-sans text-xs text-color-text-solid-secondary flex flex-shrink-0 justify-start p-1 relative w-50% papabear:justify-center papabear:w-auto\">\n        \n              \n\n    \n    \n\n    \n\n    \n\n    <div class=\"collapsible-dropdown collapsible-dropdown--footer collapsible-dropdown--up flex items-center relative hyphens-auto language-selector z-2\">\n<!---->\n        <ul class=\"collapsible-dropdown__list hidden container-raised absolute w-auto overflow-y-auto flex-col items-stretch z-1 bottom-[100%] top-auto\" role=\"menu\" tabindex=\"-1\">\n          \n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"العربية (Arabic)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-ar_AE\" data-locale=\"ar_AE\" role=\"menuitem\" lang=\"ar_AE\">\n                العربية (Arabic)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"বাংলা (Bangla)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-bn_IN\" data-locale=\"bn_IN\" role=\"menuitem\" lang=\"bn_IN\">\n                বাংলা (Bangla)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Čeština (Czech)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-cs_CZ\" data-locale=\"cs_CZ\" role=\"menuitem\" lang=\"cs_CZ\">\n                Čeština (Czech)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Dansk (Danish)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-da_DK\" data-locale=\"da_DK\" role=\"menuitem\" lang=\"da_DK\">\n                Dansk (Danish)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Deutsch (German)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-de_DE\" data-locale=\"de_DE\" role=\"menuitem\" lang=\"de_DE\">\n                Deutsch (German)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Ελληνικά (Greek)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-el_GR\" data-locale=\"el_GR\" role=\"menuitem\" lang=\"el_GR\">\n                Ελληνικά (Greek)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"English (English) selected\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link--selected\" data-tracking-control-name=\"language-selector-en_US\" data-locale=\"en_US\" role=\"menuitem\" lang=\"en_US\">\n                <strong>English (English)</strong>\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Español (Spanish)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-es_ES\" data-locale=\"es_ES\" role=\"menuitem\" lang=\"es_ES\">\n                Español (Spanish)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"فارسی (Persian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-fa_IR\" data-locale=\"fa_IR\" role=\"menuitem\" lang=\"fa_IR\">\n                فارسی (Persian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Suomi (Finnish)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-fi_FI\" data-locale=\"fi_FI\" role=\"menuitem\" lang=\"fi_FI\">\n                Suomi (Finnish)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Français (French)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-fr_FR\" data-locale=\"fr_FR\" role=\"menuitem\" lang=\"fr_FR\">\n                Français (French)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"हिंदी (Hindi)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-hi_IN\" data-locale=\"hi_IN\" role=\"menuitem\" lang=\"hi_IN\">\n                हिंदी (Hindi)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Magyar (Hungarian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-hu_HU\" data-locale=\"hu_HU\" role=\"menuitem\" lang=\"hu_HU\">\n                Magyar (Hungarian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Bahasa Indonesia (Indonesian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-in_ID\" data-locale=\"in_ID\" role=\"menuitem\" lang=\"in_ID\">\n                Bahasa Indonesia (Indonesian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Italiano (Italian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-it_IT\" data-locale=\"it_IT\" role=\"menuitem\" lang=\"it_IT\">\n                Italiano (Italian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"עברית (Hebrew)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-iw_IL\" data-locale=\"iw_IL\" role=\"menuitem\" lang=\"iw_IL\">\n                עברית (Hebrew)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"日本語 (Japanese)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-ja_JP\" data-locale=\"ja_JP\" role=\"menuitem\" lang=\"ja_JP\">\n                日本語 (Japanese)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"한국어 (Korean)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-ko_KR\" data-locale=\"ko_KR\" role=\"menuitem\" lang=\"ko_KR\">\n                한국어 (Korean)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"मराठी (Marathi)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-mr_IN\" data-locale=\"mr_IN\" role=\"menuitem\" lang=\"mr_IN\">\n                मराठी (Marathi)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Bahasa Malaysia (Malay)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-ms_MY\" data-locale=\"ms_MY\" role=\"menuitem\" lang=\"ms_MY\">\n                Bahasa Malaysia (Malay)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Nederlands (Dutch)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-nl_NL\" data-locale=\"nl_NL\" role=\"menuitem\" lang=\"nl_NL\">\n                Nederlands (Dutch)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Norsk (Norwegian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-no_NO\" data-locale=\"no_NO\" role=\"menuitem\" lang=\"no_NO\">\n                Norsk (Norwegian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"ਪੰਜਾਬੀ (Punjabi)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-pa_IN\" data-locale=\"pa_IN\" role=\"menuitem\" lang=\"pa_IN\">\n                ਪੰਜਾਬੀ (Punjabi)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Polski (Polish)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-pl_PL\" data-locale=\"pl_PL\" role=\"menuitem\" lang=\"pl_PL\">\n                Polski (Polish)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Português (Portuguese)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-pt_BR\" data-locale=\"pt_BR\" role=\"menuitem\" lang=\"pt_BR\">\n                Português (Portuguese)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Română (Romanian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-ro_RO\" data-locale=\"ro_RO\" role=\"menuitem\" lang=\"ro_RO\">\n                Română (Romanian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Русский (Russian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-ru_RU\" data-locale=\"ru_RU\" role=\"menuitem\" lang=\"ru_RU\">\n                Русский (Russian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Svenska (Swedish)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-sv_SE\" data-locale=\"sv_SE\" role=\"menuitem\" lang=\"sv_SE\">\n                Svenska (Swedish)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"తెలుగు (Telugu)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-te_IN\" data-locale=\"te_IN\" role=\"menuitem\" lang=\"te_IN\">\n                తెలుగు (Telugu)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"ภาษาไทย (Thai)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-th_TH\" data-locale=\"th_TH\" role=\"menuitem\" lang=\"th_TH\">\n                ภาษาไทย (Thai)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Tagalog (Tagalog)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-tl_PH\" data-locale=\"tl_PH\" role=\"menuitem\" lang=\"tl_PH\">\n                Tagalog (Tagalog)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Türkçe (Turkish)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-tr_TR\" data-locale=\"tr_TR\" role=\"menuitem\" lang=\"tr_TR\">\n                Türkçe (Turkish)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Українська (Ukrainian)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-uk_UA\" data-locale=\"uk_UA\" role=\"menuitem\" lang=\"uk_UA\">\n                Українська (Ukrainian)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"Tiếng Việt (Vietnamese)\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-vi_VN\" data-locale=\"vi_VN\" role=\"menuitem\" lang=\"vi_VN\">\n                Tiếng Việt (Vietnamese)\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"简体中文 (Chinese (Simplified))\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-zh_CN\" data-locale=\"zh_CN\" role=\"menuitem\" lang=\"zh_CN\">\n                简体中文 (Chinese (Simplified))\n            </button>\n          </li>\n          <li class=\"language-selector__item\" role=\"presentation\">\n            <!-- Adding aria-label to both the li and the button because screen reader focus goes to button on desktop and li on mobile-->\n            <button aria-label=\"正體中文 (Chinese (Traditional))\" class=\"font-sans text-xs link block py-[5px] px-2 w-full hover:cursor-pointer hover:bg-color-action hover:text-color-text-on-dark focus:bg-color-action focus:text-color-text-on-dark\n                language-selector__link !font-regular\" data-tracking-control-name=\"language-selector-zh_TW\" data-locale=\"zh_TW\" role=\"menuitem\" lang=\"zh_TW\">\n                正體中文 (Chinese (Traditional))\n            </button>\n          </li>\n<!---->      \n        </ul>\n\n          \n        <button class=\"language-selector__button select-none relative pr-2 font-sans text-xs font-bold text-color-text-low-emphasis hover:text-color-link-hover hover:cursor-pointer focus:text-color-link-focus focus:outline-dotted focus:outline-1\" aria-expanded=\"false\" data-tracking-control-name=\"footer-lang-dropdown_trigger\">\n          <span class=\"language-selector__label-text mr-0.5 break-words\">\n            Language\n          </span>\n          <icon class=\"language-selector__label-chevron w-2 h-2 absolute top-0 right-0\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/cyolgscd0imw2ldqppkrb84vo\"></icon>\n        </button>\n      \n    </div>\n  \n  \n          \n  </li>\n\n      </ul>\n\n<!---->    </footer>\n  \n  \n  \n      \n            \n  \n    \n\n    <div id=\"toasts\" class=\"toasts fixed z-8 babybear:right-4 mamabear:right-4 papabear:min-h-[96px] invisible\n        top:auto bottom-4 left-4 papabear:w-[400px]\n        toasts--bottom\" type=\"bottom\" aria-hidden=\"true\">\n    \n\n    <template id=\"toast-template\">\n      <div class=\"toast container-raised flex\n          toast--bottom\n          transition ease-accelerate duration-xxslow\n          \" data-id=\"toast\">\n        <div class=\"toast__message flex flex-1 babybear:items-center mamabear:items-center papabear:items-start py-2 px-1.5\" data-id=\"toast__message\" role=\"alert\" tabindex=\"-1\">\n          <div class=\"toast__message-content-container\" data-id=\"toast__message-content-container\">\n            <p class=\"toast__message-content font-sans text-sm opacity-90 inline babybear:self-center mamabear:self-center papabear:self-start\" data-id=\"toast__message-content\"></p>\n          </div>\n        </div>\n        <button class=\"toast__dismiss-btn cursor-pointer babybear:self-center mamabear:self-center papabear:self-start pt-3 pb-2 papabear:py-2 pl-0.5 pr-0\" data-id=\"toast__dismiss-btn\" aria-label=\"Close\">\n          <svg width=\"24\" height=\"24\" class=\"fill-color-icon\"><path d=\"M13 4.32 9.31 8 13 11.69 11.69 13 8 9.31 4.31 13 3 11.69 6.69 8 3 4.31 4.31 3 8 6.69 11.68 3Z\"></path></svg>\n        </button>\n      </div>\n    </template>\n      <template id=\"toast-icon-caution\">\n        <icon class=\"text-color-icon-caution toast__icon w-3 h-3 shrink-0 mr-2\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/bk9xca6x9l1fga1tbzame3i3l\"></icon>\n      </template>\n      <template id=\"toast-icon-error\">\n        <icon class=\"text-color-icon-negative toast__icon w-3 h-3 shrink-0 mr-2\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/8c0098stai8lcqypn95r47dew\"></icon>\n      </template>\n      <template id=\"toast-icon-gdpr\">\n        <icon class=\"text-color-icon-neutral toast__icon w-3 h-3 shrink-0 mr-2\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/a9phzx7id2abubo45lgookfd\"></icon>\n      </template>\n      <template id=\"toast-icon-notify\">\n        <icon class=\"text-color-icon-neutral toast__icon w-3 h-3 shrink-0 mr-2\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/4xg53nt8deu1y7tb1t3km8tfh\"></icon>\n      </template>\n      <template id=\"toast-icon-success\">\n        <icon class=\"text-color-icon-positive toast__icon w-3 h-3 shrink-0 mr-2\" data-delayed-url=\"https://static.licdn.com/aero-v1/sc/h/9zhm3eh2dq7vh2muo8xnfikxh\"></icon>\n      </template>\n    <template id=\"toast-cta\">\n      <a class=\"toast__cta cta-link font-medium ml-0.5 text-sm text-inherit inline\" target=\"_blank\"></a>\n    </template>\n  </div>\n  \n\n      \n\n            <script src=\"https://static.licdn.com/aero-v1/sc/h/11w2cyeco40g892agkqchdc8a\" async=\"\" aria-hidden=\"true\"></script>\n<!---->          \n        \n        <script src=\"https://static.licdn.com/aero-v1/sc/h/c7as2hp5uwkulwqp3ncswy16n\" async=\"\" defer=\"\" aria-hidden=\"true\"></script>\n        <script data-module-id=\"media-player\" aria-hidden=\"true\" src=\"https://static.licdn.com/aero-v1/sc/h/19m2m2iij3pcbxe4bkogyzklj\" class=\"lazy-loaded\"></script>\n      \n      \n          \n<!----><!---->  \n      \n    \n  \n  \n  <code id=\"apiRoute\" style=\"display: none;\" aria-hidden=\"true\"><!--\"/pulse/api/runQuery\"--></code><div class=\"top-level-modal-container\"><div id=\"base-contextual-sign-in-modal\" class=\"modal modal--contextual-sign-in\" data-outlet=\"base-contextual-sign-in-modal\">\n<!---->        <div class=\"modal__overlay flex items-center bg-color-background-scrim justify-center fixed bottom-0 left-0 right-0 top-0 opacity-0 invisible pointer-events-none z-[1000] transition-[opacity] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.17s] py-4 modal__overlay--visible\" aria-hidden=\"false\">\n          <section aria-modal=\"true\" role=\"dialog\" aria-labelledby=\"base-contextual-sign-in-modal-modal-header\" tabindex=\"-1\" class=\"max-h-full modal__wrapper overflow-auto p-0 bg-color-surface max-w-[1128px] min-h-[160px] relative scale-[0.25] shadow-sm shadow-color-border-faint transition-[transform] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.33s] focus:outline-0\n              \n              w-[1128px] mamabear:w-[744px] babybear:w-[360px]\n              \n              rounded-md\">\n              \n              <button class=\"modal__dismiss btn-tertiary h-[40px] w-[40px] p-0 rounded-full indent-0\n                  contextual-sign-in-modal__modal-dismiss absolute right-0 m-[20px] cursor-pointer\" aria-label=\"Dismiss\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_modal_dismiss\">\n                <icon class=\"contextual-sign-in-modal__modal-dismiss-icon lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" class=\"artdeco-icon lazy-loaded\" focusable=\"false\" aria-busy=\"false\">\n  <path d=\"M20,5.32L13.32,12,20,18.68,18.66,20,12,13.33,5.34,20,4,18.68,10.68,12,4,5.32,5.32,4,12,10.69,18.68,4Z\" fill=\"currentColor\"></path>\n</svg></icon>\n              </button>\n          \n            <div class=\"modal__main w-full\">\n              \n              <div class=\"contextual-sign-in-modal__screen contextual-sign-in-modal__context-screen flex flex-col my-4 mx-3\">\n                  \n                  \n      <img class=\"inline-block relative w-16 h-16 contextual-sign-in-modal__img m-auto lazy-loaded\" data-ghost-classes=\"bg-color-entity-ghost-background\" data-ghost-url=\"https://static.licdn.com/aero-v1/sc/h/9l8dv1r8a09nem281grvopn9l\" alt=\"\" aria-busy=\"false\" src=\"https://static.licdn.com/aero-v1/sc/h/5k9cgtx8rhoyqkcxfoncu1svl\">\n      \n                <h2 class=\"contextual-sign-in-modal__context-screen-title font-bold font-sans text-xl text-color-text my-2 mx-4 text-center\" id=\"base-contextual-sign-in-modal-modal-header\">\n                  Sign in to view more content\n                </h2>\n                  <p class=\"contextual-sign-in-modal__subtitle mb-2 mx-12 babybear:mx-0 text-center font-sans text-md text-color-text\">\n                    Create your free account or sign in to continue your search\n                  </p>\n<!---->                <div class=\"contextual-sign-in-modal__btn-container m-auto w-[320px] babybear:w-full\">\n<!---->                  \n    \n                      \n    \n    \n    \n    \n    \n    \n    \n\n    <div class=\"sign-in-modal\">\n        \n    \n    \n      \n          <button class=\"sign-in-modal__outlet-btn cursor-pointer btn-md btn-primary btn-secondary\" data-tracking-client-ingraph=\"\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_outlet-button\" data-modal=\"base-sign-in-modal\">\n<!---->              Sign in\n          </button>\n        \n  \n\n      \n\n    \n    <div class=\"\">\n<!---->\n      <div id=\"base-sign-in-modal\" class=\"modal modal--sign-in\" data-outlet=\"base-sign-in-modal\">\n<!---->        <div class=\"modal__overlay flex items-center bg-color-background-scrim justify-center fixed bottom-0 left-0 right-0 top-0 opacity-0 invisible pointer-events-none z-[1000] transition-[opacity] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.17s]\n            py-4\n            \" aria-hidden=\"true\">\n          <section aria-modal=\"true\" role=\"dialog\" aria-labelledby=\"base-sign-in-modal-modal-header\" tabindex=\"-1\" class=\"max-h-full modal__wrapper overflow-auto p-0 bg-color-surface max-w-[1128px] min-h-[160px] relative scale-[0.25] shadow-sm shadow-color-border-faint transition-[transform] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] duration-[0.33s] focus:outline-0\n              \n              w-[1128px] mamabear:w-[744px] babybear:w-[360px]\n              \n              rounded-md\">\n              \n            <button class=\"modal__dismiss btn-tertiary h-[40px] w-[40px] p-0 rounded-full indent-0 sign-in-modal__dismiss absolute right-0 cursor-pointer m-[20px]\" aria-label=\"Dismiss\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_dismiss\">\n              <icon class=\"sign-in-modal__dismiss-icon lazy-loaded\" aria-hidden=\"true\" aria-busy=\"false\"><svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"24px\" height=\"24px\" class=\"artdeco-icon lazy-loaded\" focusable=\"false\" aria-busy=\"false\">\n  <path d=\"M20,5.32L13.32,12,20,18.68,18.66,20,12,13.33,5.34,20,4,18.68,10.68,12,4,5.32,5.32,4,12,10.69,18.68,4Z\" fill=\"currentColor\"></path>\n</svg></icon>\n            </button>\n        \n            <div class=\"modal__main w-full\">\n              \n          <div class=\"sign-in-modal__screen flex flex-col py-4 w-[513px] babybear:w-full\n              px-3\">\n            <h2 class=\"sign-in-modal__header font-sans text-display-md text-color-text\n                \">\n                Welcome back\n                          </h2>\n            \n    \n    \n    \n    \n    \n    \n    \n    \n    \n    \n\n    \n    \n    \n    \n\n    \n    <code id=\"i18n_username_error_empty\" style=\"display: none\"><!--\"Please enter an email address or phone number\"--></code>\n    \n    <code id=\"i18n_username_error_too_long\" style=\"display: none\"><!--\"Email or phone number must be between 3 to 128 characters\"--></code>\n    <code id=\"i18n_username_error_too_short\" style=\"display: none\"><!--\"Email or phone number must be between 3 to 128 characters\"--></code>\n\n    \n    <code id=\"i18n_password_error_empty\" style=\"display: none\"><!--\"Please enter a password\"--></code>\n    \n    <code id=\"i18n_password_error_too_short\" style=\"display: none\"><!--\"The password you provided must have at least 6 characters\"--></code>\n    \n    <code id=\"i18n_password_error_too_long\" style=\"display: none\"><!--\"The password you provided must have at most 400 characters\"--></code>\n\n<!---->    <form data-id=\"sign-in-form\" action=\"https://www.linkedin.com/uas/login-submit\" method=\"post\" novalidate=\"\" class=\"mt-1.5 mb-2\">\n      <input name=\"loginCsrfParam\" value=\"934c66ce-701f-43a1-8dda-c0f89c0deaa9\" type=\"hidden\">\n\n      <div class=\"flex flex-col\">\n        \n    <div class=\"mt-1.5\" data-js-module-id=\"guest-input\">\n      <div class=\"flex flex-col\">\n        <label class=\"input-label mb-1\" for=\"base-sign-in-modal_session_key\">\n          Email or phone\n        </label>\n        <div class=\"text-input flex\">\n          <input class=\"text-color-text font-sans text-md outline-0 bg-color-transparent w-full\" autocomplete=\"username\" id=\"base-sign-in-modal_session_key\" name=\"session_key\" required=\"\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_sign-in-session-key\" data-tracking-client-ingraph=\"\" type=\"text\">\n          \n        </div>\n      </div>\n\n      <p class=\"input-helper mt-1.5\" for=\"base-sign-in-modal_session_key\" role=\"alert\" data-js-module-id=\"guest-input__message\"></p>\n    </div>\n  \n\n        \n    <div class=\"mt-1.5\" data-js-module-id=\"guest-input\">\n      <div class=\"flex flex-col\">\n        <label class=\"input-label mb-1\" for=\"base-sign-in-modal_session_password\">\n          Password\n        </label>\n        <div class=\"text-input flex\">\n          <input class=\"text-color-text font-sans text-md outline-0 bg-color-transparent w-full\" autocomplete=\"current-password\" id=\"base-sign-in-modal_session_password\" name=\"session_password\" required=\"\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_sign-in-password\" data-tracking-client-ingraph=\"\" type=\"password\">\n          \n            <button aria-live=\"assertive\" aria-relevant=\"text\" data-id=\"sign-in-form__password-visibility-toggle\" class=\"font-sans text-md font-bold text-color-action z-10 ml-[12px] hover:cursor-pointer\" aria-label=\"Show your LinkedIn password\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_sign-in-password-visibility-toggle-btn\" type=\"button\">Show</button>\n          \n        </div>\n      </div>\n\n      <p class=\"input-helper mt-1.5\" for=\"base-sign-in-modal_session_password\" role=\"alert\" data-js-module-id=\"guest-input__message\"></p>\n    </div>\n  \n\n        <input name=\"session_redirect\" value=\"https://www.linkedin.com/pulse/creditchat-benefits-integrating-generative-ai-credit-analysis-eswmf\" type=\"hidden\">\n\n<!---->      </div>\n\n      <div data-id=\"sign-in-form__footer\" class=\"flex justify-between\n          sign-in-form__footer--full-width\">\n        <a data-id=\"sign-in-form__forgot-password\" class=\"font-sans text-md font-bold link leading-regular\n            sign-in-form__forgot-password--full-width\" href=\"https://www.linkedin.com/uas/request-password-reset?trk=pulse-article_contextual-sign-in-modal_sign-in-modal_forgot_password\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_forgot_password\" data-tracking-will-navigate=\"\">Forgot password?</a>\n\n<!---->\n        <input name=\"trk\" value=\"pulse-article_contextual-sign-in-modal_sign-in-modal_sign-in-submit\" type=\"hidden\">\n        <button class=\"btn-md btn-primary flex-shrink-0 cursor-pointer\n            sign-in-form__submit-btn--full-width\" data-id=\"sign-in-form__submit-btn\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_sign-in-submit-btn\" data-tracking-client-ingraph=\"\" data-tracking-litms=\"\" type=\"submit\">\n          Sign in\n        </button>\n      </div>\n          <div class=\"sign-in-form__divider left-right-divider pt-2 pb-3\">\n            <p class=\"sign-in-form__divider-text font-sans text-sm text-color-text px-2\">\n              or\n            </p>\n          </div>\n    <input type=\"hidden\" name=\"controlId\" value=\"d_flagship2_pulse_read-pulse-article_contextual-sign-in-modal_sign-in-modal_sign-in-submit-btn\"><input type=\"hidden\" name=\"pageInstance\" value=\"urn:li:page:d_flagship2_pulse_read_jsbeacon;pvyuGzUFR9yaYQadR+Rndw==\"></form>\n        <div class=\"w-full max-w-[400px] mx-auto\">\n          \n    \n\n    <div class=\"google-auth-button\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_google-auth-button\" data-tracking-client-ingraph=\"\" data-google-auth-iframe-initialized=\"\">\n        \n    \n    \n    <p class=\"linkedin-tc__text text-color-text-low-emphasis text-xs pb-2\">\n      By clicking Continue to join or sign in, you agree to LinkedIn’s <a href=\"/legal/user-agreement?trk=pulse-article_contextual-sign-in-modal_sign-in-modal_auth-button_user-agreement\" target=\"_blank\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_auth-button_user-agreement\" data-tracking-will-navigate=\"true\">User Agreement</a>, <a href=\"/legal/privacy-policy?trk=pulse-article_contextual-sign-in-modal_sign-in-modal_auth-button_privacy-policy\" target=\"_blank\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_auth-button_privacy-policy\" data-tracking-will-navigate=\"true\">Privacy Policy</a>, and <a href=\"/legal/cookie-policy?trk=pulse-article_contextual-sign-in-modal_sign-in-modal_auth-button_cookie-policy\" target=\"_blank\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_auth-button_cookie-policy\" data-tracking-will-navigate=\"true\">Cookie Policy</a>.\n    </p>\n  \n      <div class=\"google-auth-button__placeholder mx-auto\n          google-auth-button__placeholder--black-border\" data-theme=\"outline\" data-logo-alignment=\"center\" data-locale=\"en_US\" role=\"button\" aria-label=\"Continue with google\" data-safe-to-skip-tnc-redirect=\"\"><div class=\"S9gUrf-YoZ4jf\" style=\"position: relative;\"><div></div><iframe src=\"https://accounts.google.com/gsi/button?logo_alignment=center&amp;shape=pill&amp;size=large&amp;text=continue_with&amp;theme=outline&amp;type=undefined&amp;width=400px&amp;client_id=************-k6nqn1tpmitg8pui82bfaun3jrpmiuhs.apps.googleusercontent.com&amp;iframe_id=gsi_665821_982896&amp;as=t4u54QKtVOhGudjcODjS1g&amp;hl=en_US\" allow=\"identity-credentials-get\" id=\"gsi_665821_982896\" title=\"Sign in with Google Button\" style=\"display: block; position: relative; top: 0px; left: 0px; height: 44px; width: 420px; border: 0px; margin: -2px -10px;\"></iframe></div></div>\n<!---->    </div>\n  \n        </div>\n<!---->  \n              <p class=\"sign-in-modal__join-now m-auto font-sans text-md text-color-text\n                  mt-2\">\n                New to LinkedIn? <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=pulse-article_contextual-sign-in-modal_sign-in-modal_join-link\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_sign-in-modal_join-link\" data-tracking-will-navigate=\"true\" class=\"sign-in-modal__join-link\">Join now</a>\n              </p>\n          </div>\n        \n            </div>\n\n<!---->          </section>\n        </div>\n      </div>\n    </div>\n  \n<!---->    </div>\n  \n                    \n    \n                      <div class=\"contextual-sign-in-modal__divider left-right-divider\">\n                        <p class=\"contextual-sign-in-modal__divider-text font-sans text-sm text-color-text px-2\">\n                          or\n                        </p>\n                      </div>\n                    \n    \n                        <div class=\"w-full max-w-[400px] mx-auto\">\n                          \n    \n\n    <div class=\"google-auth-button\" data-google-auth-iframe-initialized=\"\">\n        \n    \n    \n    <p class=\"linkedin-tc__text text-color-text-low-emphasis text-xs pb-2\">\n      By clicking Continue to join or sign in, you agree to LinkedIn’s <a href=\"/legal/user-agreement?trk=pulse-article_auth-button_user-agreement\" target=\"_blank\" data-tracking-control-name=\"pulse-article_auth-button_user-agreement\" data-tracking-will-navigate=\"true\">User Agreement</a>, <a href=\"/legal/privacy-policy?trk=pulse-article_auth-button_privacy-policy\" target=\"_blank\" data-tracking-control-name=\"pulse-article_auth-button_privacy-policy\" data-tracking-will-navigate=\"true\">Privacy Policy</a>, and <a href=\"/legal/cookie-policy?trk=pulse-article_auth-button_cookie-policy\" target=\"_blank\" data-tracking-control-name=\"pulse-article_auth-button_cookie-policy\" data-tracking-will-navigate=\"true\">Cookie Policy</a>.\n    </p>\n  \n      <div class=\"google-auth-button__placeholder mx-auto\n          \" data-theme=\"filled_blue\" data-logo-alignment=\"center\" data-locale=\"en_US\" role=\"button\" aria-label=\"Continue with google\" data-safe-to-skip-tnc-redirect=\"\"><div class=\"S9gUrf-YoZ4jf\" style=\"position: relative;\"><div></div><iframe src=\"https://accounts.google.com/gsi/button?logo_alignment=center&amp;shape=pill&amp;size=large&amp;text=continue_with&amp;theme=filled_blue&amp;type=undefined&amp;width=320px&amp;client_id=************-k6nqn1tpmitg8pui82bfaun3jrpmiuhs.apps.googleusercontent.com&amp;iframe_id=gsi_665827_395907&amp;as=t4u54QKtVOhGudjcODjS1g&amp;hl=en_US\" allow=\"identity-credentials-get\" id=\"gsi_665827_395907\" title=\"Sign in with Google Button\" style=\"display: block; position: relative; top: 0px; left: 0px; height: 44px; width: 340px; border: 0px; margin: -2px -10px;\"></iframe></div></div>\n<!---->    </div>\n  \n                        </div>\n                    \n  \n                </div>\n                  <p class=\"contextual-sign-in-modal__join-now m-auto font-sans text-md text-color-text my-1\">\n                    New to LinkedIn? <a href=\"https://www.linkedin.com/signup/cold-join?session_redirect=%2Fpulse%2Fcreditchat-benefits-integrating-generative-ai-credit-analysis-eswmf&amp;trk=pulse-article_contextual-sign-in-modal_join-link\" data-tracking-control-name=\"pulse-article_contextual-sign-in-modal_join-link\" data-tracking-will-navigate=\"true\" class=\"contextual-sign-in-modal__join-link\">Join now</a>\n                  </p>\n<!---->              </div>\n          \n            </div>\n\n<!---->          </section>\n        </div>\n      </div></div><iframe id=\"humanThirdPartyIframe\" src=\"https://li.protechts.net/index.html?ts=1751723665431&amp;r_id=AAY5LvK9t4JO7AhDT%2FdkSw%3D%3D&amp;app_id=PXdOjV695v&amp;uc=scraping&amp;d_id=bc08509dc1ba5bba99193c43c697ab81f533af0ea067bcf3530c33d6ed5c6583\" sandbox=\"allow-same-origin allow-scripts\" aria-hidden=\"true\" style=\"height: 0px; width: 0px; border: none; position: absolute; left: -9999px;\"></iframe></body></html>", "images": [], "success": true, "screenshot": "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"}}